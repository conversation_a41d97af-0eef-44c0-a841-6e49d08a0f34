'use client';

// MySQLAi.de - 知识库统一数据管理Hook
// 提供统一的数据获取、缓存、错误处理和加载状态管理

import { useState, useEffect, useCallback, useRef } from 'react';
import { articlesApi, categoriesApi, codeExamplesApi } from '@/lib/api/knowledge';
import type { Database } from '@/lib/database.types';

type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];
type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];
type CodeExample = Database['public']['Tables']['code_examples']['Row'];

// 创建和更新类型
type KnowledgeArticleInsert = Database['public']['Tables']['knowledge_articles']['Insert'];
type KnowledgeCategoryInsert = Database['public']['Tables']['knowledge_categories']['Insert'];
type CodeExampleInsert = Database['public']['Tables']['code_examples']['Insert'];

// 移除联合类型，使用具体的泛型类型

// 数据类型枚举
export type DataType = 'articles' | 'categories' | 'codeExamples';

// API响应类型
interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  details?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Hook配置选项
interface UseKnowledgeDataOptions {
  // 自动获取数据
  autoFetch?: boolean;
  // 缓存时间（毫秒）
  cacheTime?: number;
  // 重试次数
  retryCount?: number;
  // 重试延迟（毫秒）
  retryDelay?: number;
  // 乐观更新
  optimisticUpdates?: boolean;
  // 后台刷新
  backgroundRefresh?: boolean;
  // 调试模式
  debug?: boolean;
}

// Hook状态类型
interface KnowledgeDataState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  lastFetch: Date | null;
  isStale: boolean;
  retryCount: number;
}

// Hook返回类型
interface UseKnowledgeDataReturn<T> {
  // 数据状态
  data: T[];
  loading: boolean;
  error: string | null;
  isStale: boolean;
  
  // 操作方法
  refetch: () => Promise<void>;
  refresh: () => Promise<void>;
  invalidate: () => void;
  retry: () => Promise<void>;
  
  // CRUD操作
  create: (item: Partial<T>) => Promise<boolean>;
  update: (id: string, item: Partial<T>) => Promise<boolean>;
  remove: (id: string) => Promise<boolean>;

  // 批量操作
  batchCreate: (items: Partial<T>[]) => Promise<boolean>;
  batchUpdate: (items: { id: string; data: Partial<T> }[]) => Promise<boolean>;
  batchRemove: (ids: string[]) => Promise<boolean>;

  // 工具方法
  findById: (id: string) => T | undefined;
  findByField: (field: keyof T, value: unknown) => T[];
  count: number;
}

// 默认配置（完全禁用自动功能以停止无限循环）
const DEFAULT_OPTIONS: UseKnowledgeDataOptions = {
  autoFetch: false, // 禁用自动获取
  cacheTime: 0, // 禁用缓存
  retryCount: 0, // 禁用重试
  retryDelay: 5000, // 增加重试延迟
  optimisticUpdates: false, // 禁用乐观更新
  backgroundRefresh: false, // 禁用后台刷新
  debug: false
};

// API映射
const API_MAP = {
  articles: articlesApi,
  categories: categoriesApi,
  codeExamples: codeExamplesApi
};

export function useKnowledgeData<T = any>(
  dataType: DataType,
  params?: any,
  options: UseKnowledgeDataOptions = {}
): UseKnowledgeDataReturn<T> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const api = API_MAP[dataType];

  // 稳定化 params 引用，避免无限循环
  const stableParams = useRef(params);
  const [paramsKey, setParamsKey] = useState(JSON.stringify(params || {}));
  
  // 状态管理
  const [state, setState] = useState<KnowledgeDataState<T>>({
    data: [],
    loading: false,
    error: null,
    lastFetch: null,
    isStale: false,
    retryCount: 0
  });

  // 引用管理
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cacheTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 调试日志
  const log = useCallback((message: string, data?: unknown) => {
    if (opts.debug) {
      console.log(`[useKnowledgeData:${dataType}] ${message}`, data);
    }
  }, [dataType, opts.debug]);

  // 设置缓存过期（增加更长的缓存时间以减少请求频率）
  const setCacheTimeout = useCallback(() => {
    if (cacheTimeoutRef.current) {
      clearTimeout(cacheTimeoutRef.current);
    }

    // 使用更长的缓存时间，最少15分钟
    const cacheTime = Math.max(opts.cacheTime || 0, 15 * 60 * 1000);

    if (cacheTime > 0) {
      cacheTimeoutRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, isStale: true }));
        log('Cache expired, data marked as stale');
      }, cacheTime);
    }
  }, [opts.cacheTime, log]);

  // 更新 params 引用
  useEffect(() => {
    const newParamsKey = JSON.stringify(params || {});
    if (newParamsKey !== paramsKey) {
      stableParams.current = params;
      setParamsKey(newParamsKey);
    }
  }, [params, paramsKey]);

  // 获取数据
  const fetchData = useCallback(async (isRetry = false): Promise<void> => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      loading: true,
      error: isRetry ? prev.error : null
    }));

    const currentParams = stableParams.current;
    log('Fetching data', { params: currentParams, isRetry });

    try {
      let response: any;

      // 根据数据类型调用相应的API
      switch (dataType) {
        case 'articles':
          response = await api.getAll(currentParams);
          break;
        case 'categories':
          response = await api.getAll(currentParams?.includeStats);
          break;
        case 'codeExamples':
          response = await api.getAll(currentParams);
          break;
        default:
          throw new Error(`Unsupported data type: ${dataType}`);
      }

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          data: response.data as T[],
          loading: false,
          error: null,
          lastFetch: new Date(),
          isStale: false,
          retryCount: 0
        }));

        setCacheTimeout();
        log('Data fetched successfully', response.data);
      } else {
        throw new Error(response.error || 'Failed to fetch data');
      }
    } catch (error: unknown) {
      const err = error as Error;
      if (err.name === 'AbortError') {
        log('Request aborted');
        return;
      }

      const errorMessage = err.message || 'Unknown error occurred';

      setState(prev => {
        const newRetryCount = prev.retryCount + 1;
        log('Fetch error', { error: errorMessage, retryCount: newRetryCount });

        // 自动重试（使用最新的retryCount）
        if (newRetryCount < opts.retryCount! && !isRetry) {
          retryTimeoutRef.current = setTimeout(() => {
            fetchData(true);
          }, opts.retryDelay);
        }

        return {
          ...prev,
          loading: false,
          error: errorMessage,
          retryCount: newRetryCount
        };
      });
    }
  }, [dataType, api, log, setCacheTimeout, opts.retryCount, opts.retryDelay]);

  // 刷新数据
  const refresh = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, isStale: false }));
    await fetchData();
  }, [fetchData]);

  // 重新获取数据
  const refetch = useCallback(async (): Promise<void> => {
    await fetchData();
  }, [fetchData]);

  // 使缓存失效
  const invalidate = useCallback(() => {
    setState(prev => ({ ...prev, isStale: true }));
    if (cacheTimeoutRef.current) {
      clearTimeout(cacheTimeoutRef.current);
    }
    log('Cache invalidated');
  }, [log]);

  // 重试
  const retry = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, retryCount: 0 }));
    await fetchData(true);
  }, [fetchData]);

  // 创建项目
  const create = useCallback(async (item: Partial<T>): Promise<boolean> => {
    try {
      log('Creating item', item);
      
      // 乐观更新
      if (opts.optimisticUpdates) {
        const tempId = `temp_${Date.now()}`;
        const tempItem = { ...item, id: tempId } as T;
        setState(prev => ({
          ...prev,
          data: [...prev.data, tempItem]
        }));
      }

      const response = await api.create(item as any);
      
      if (response.success) {
        // 刷新数据以获取最新状态
        await refetch();
        log('Item created successfully');
        return true;
      } else {
        throw new Error(response.error || 'Failed to create item');
      }
    } catch (error: unknown) {
      const err = error as Error;
      log('Create error', err.message);

      // 回滚乐观更新
      if (opts.optimisticUpdates) {
        await refetch();
      }

      setState(prev => ({ ...prev, error: err.message }));
      return false;
    }
  }, [api, opts.optimisticUpdates, refetch, log]);

  // 更新项目
  const update = useCallback(async (id: string, item: Partial<T>): Promise<boolean> => {
    try {
      log('Updating item', { id, item });
      
      // 乐观更新
      if (opts.optimisticUpdates) {
        setState(prev => ({
          ...prev,
          data: prev.data.map(d => 
            (d as any).id === id ? { ...d, ...item } : d
          )
        }));
      }

      const response = await api.update(id, item);
      
      if (response.success) {
        // 刷新数据以获取最新状态
        await refetch();
        log('Item updated successfully');
        return true;
      } else {
        throw new Error(response.error || 'Failed to update item');
      }
    } catch (error: any) {
      log('Update error', error.message);
      
      // 回滚乐观更新
      if (opts.optimisticUpdates) {
        await refetch();
      }
      
      setState(prev => ({ ...prev, error: error.message }));
      return false;
    }
  }, [api, opts.optimisticUpdates, refetch, log]);

  // 删除项目
  const remove = useCallback(async (id: string): Promise<boolean> => {
    try {
      log('Removing item', { id });
      
      // 乐观更新
      if (opts.optimisticUpdates) {
        setState(prev => ({
          ...prev,
          data: prev.data.filter(d => (d as any).id !== id)
        }));
      }

      const response = await api.delete(id);
      
      if (response.success) {
        // 刷新数据以获取最新状态
        await refetch();
        log('Item removed successfully');
        return true;
      } else {
        throw new Error(response.error || 'Failed to remove item');
      }
    } catch (error: any) {
      log('Remove error', error.message);
      
      // 回滚乐观更新
      if (opts.optimisticUpdates) {
        await refetch();
      }
      
      setState(prev => ({ ...prev, error: error.message }));
      return false;
    }
  }, [api, opts.optimisticUpdates, refetch, log]);

  // 批量创建
  const batchCreate = useCallback(async (items: any[]): Promise<boolean> => {
    try {
      log('Batch creating items', { count: items.length });
      
      const results = await Promise.all(
        items.map(item => api.create(item))
      );
      
      const allSuccess = results.every(r => r.success);
      
      if (allSuccess) {
        await refetch();
        log('Batch create successful');
        return true;
      } else {
        throw new Error('Some items failed to create');
      }
    } catch (error: any) {
      log('Batch create error', error.message);
      setState(prev => ({ ...prev, error: error.message }));
      return false;
    }
  }, [api, refetch, log]);

  // 批量更新
  const batchUpdate = useCallback(async (items: { id: string; data: any }[]): Promise<boolean> => {
    try {
      log('Batch updating items', { count: items.length });
      
      const results = await Promise.all(
        items.map(item => api.update(item.id, item.data))
      );
      
      const allSuccess = results.every(r => r.success);
      
      if (allSuccess) {
        await refetch();
        log('Batch update successful');
        return true;
      } else {
        throw new Error('Some items failed to update');
      }
    } catch (error: any) {
      log('Batch update error', error.message);
      setState(prev => ({ ...prev, error: error.message }));
      return false;
    }
  }, [api, refetch, log]);

  // 批量删除
  const batchRemove = useCallback(async (ids: string[]): Promise<boolean> => {
    try {
      log('Batch removing items', { count: ids.length });
      
      const results = await Promise.all(
        ids.map(id => api.delete(id))
      );
      
      const allSuccess = results.every(r => r.success);
      
      if (allSuccess) {
        await refetch();
        log('Batch remove successful');
        return true;
      } else {
        throw new Error('Some items failed to remove');
      }
    } catch (error: any) {
      log('Batch remove error', error.message);
      setState(prev => ({ ...prev, error: error.message }));
      return false;
    }
  }, [api, refetch, log]);

  // 根据ID查找
  const findById = useCallback((id: string): T | undefined => {
    return state.data.find(item => (item as any).id === id);
  }, [state.data]);

  // 根据字段查找
  const findByField = useCallback((field: keyof T, value: any): T[] => {
    return state.data.filter(item => (item as any)[field] === value);
  }, [state.data]);

  // 自动获取数据（暂时完全禁用以停止无限循环）
  useEffect(() => {
    // 完全禁用自动获取以停止无限循环
    // if (opts.autoFetch) {
    //   fetchData();
    // }

    // 清理函数
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (cacheTimeoutRef.current) {
        clearTimeout(cacheTimeoutRef.current);
      }
    };
  }, []); // 移除所有依赖以停止循环

  // 后台刷新（禁用以避免无限循环）
  useEffect(() => {
    if (opts.backgroundRefresh && state.isStale && !state.loading) {
      log('Background refresh triggered');
      // 暂时禁用自动后台刷新以避免无限循环
      // fetchData();
    }
  }, [opts.backgroundRefresh, state.isStale, state.loading, log]);

  return {
    // 数据状态
    data: state.data,
    loading: state.loading,
    error: state.error,
    isStale: state.isStale,
    
    // 操作方法
    refetch,
    refresh,
    invalidate,
    retry,
    
    // CRUD操作
    create,
    update,
    remove,
    
    // 批量操作
    batchCreate,
    batchUpdate,
    batchRemove,
    
    // 工具方法
    findById,
    findByField,
    count: state.data.length
  };
}
