'use client';

// MySQLAi.de - 管理系统仪表板
// 显示系统概览和快速操作

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  FolderOpen,
  Code,
  BarChart3,
  TrendingUp,
  Search,
  Plus,
  Database
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { statsApi } from '@/lib/api/knowledge';
import Link from 'next/link';

// 统计数据类型定义
interface StatsData {
  overview: {
    totalCategories: number;
    totalArticles: number;
    totalCodeExamples: number;
    totalRelations: number;
  };
  categoryStats: Array<{
    id: string;
    name: string;
    articleCount: number;
  }>;
  difficultyStats: Record<string, number>;
  languageStats: Record<string, number>;
  recentArticles: Array<{
    id: string;
    title: string;
    last_updated: string;
    knowledge_categories?: {
      name: string;
    };
  }>;
  searchStats?: {
    totalSearches: number;
    popularQueries: Array<{
      query: string;
      count: number;
    }>;
  };
}

// 统计卡片组件
interface StatsCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'primary' | 'accent' | 'success' | 'warning';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

function StatsCard({ title, value, description, icon: IconComponent, color, trend }: StatsCardProps) {
  const colorClasses = {
    primary: {
      bg: 'bg-mysql-primary',
      light: 'bg-mysql-primary-light',
      text: 'text-mysql-primary'
    },
    accent: {
      bg: 'bg-mysql-accent',
      light: 'bg-blue-50',
      text: 'text-mysql-accent'
    },
    success: {
      bg: 'bg-mysql-success',
      light: 'bg-green-50',
      text: 'text-mysql-success'
    },
    warning: {
      bg: 'bg-mysql-warning',
      light: 'bg-yellow-50',
      text: 'text-mysql-warning'
    }
  };

  const colors = colorClasses[color];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-xl shadow-lg border border-mysql-border p-6 hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-4">
            <div className={cn('p-3 rounded-lg', colors.light)}>
              <IconComponent className={cn('w-6 h-6', colors.text)} />
            </div>
            <div>
              <h3 className="text-sm font-medium text-mysql-text-light">
                {title}
              </h3>
              <p className="text-2xl font-bold text-mysql-text">
                {value}
              </p>
            </div>
          </div>
          
          <p className="text-sm text-mysql-text-light mb-2">
            {description}
          </p>

          {trend && (
            <div className="flex items-center space-x-1">
              <TrendingUp className={cn(
                'w-4 h-4',
                trend.isPositive ? 'text-mysql-success' : 'text-mysql-error'
              )} />
              <span className={cn(
                'text-sm font-medium',
                trend.isPositive ? 'text-mysql-success' : 'text-mysql-error'
              )}>
                {trend.isPositive ? '+' : ''}{trend.value}%
              </span>
              <span className="text-sm text-mysql-text-light">
                vs 上月
              </span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}

// 快速操作卡片组件
interface QuickActionProps {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

function QuickActionCard({ title, description, href, icon: IconComponent, color }: QuickActionProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.05, y: -2 }}
      transition={{ duration: 0.3 }}
    >
      <Link
        href={href}
        className="block bg-white rounded-xl shadow-lg border border-mysql-border p-6 hover:shadow-xl transition-all duration-300 group"
      >
        <div className="flex items-center space-x-4">
          <div className={cn(
            'p-3 rounded-lg group-hover:scale-110 transition-transform duration-300',
            `bg-${color}-light`
          )}>
            <IconComponent className={cn('w-6 h-6', `text-${color}`)} />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300">
              {title}
            </h3>
            <p className="text-sm text-mysql-text-light">
              {description}
            </p>
          </div>
          <Plus className="w-5 h-5 text-mysql-text-light group-hover:text-mysql-primary transition-colors duration-300" />
        </div>
      </Link>
    </motion.div>
  );
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);

  // 获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await statsApi.get({ includeSearchStats: true });
        if (response.success && response.data) {
          setStats(response.data as StatsData);
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-mysql-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-mysql-text-light">加载统计数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold text-mysql-text mb-2">
          管理仪表板
        </h1>
        <p className="text-mysql-text-light">
          欢迎回来！这里是您的知识库管理中心概览。
        </p>
      </motion.div>

      {/* 统计卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="总文章数"
          value={stats?.overview?.totalArticles || 0}
          description="知识库文章总数"
          icon={FileText}
          color="primary"
          trend={{ value: 12, isPositive: true }}
        />
        <StatsCard
          title="分类数量"
          value={stats?.overview?.totalCategories || 0}
          description="知识库分类总数"
          icon={FolderOpen}
          color="accent"
          trend={{ value: 5, isPositive: true }}
        />
        <StatsCard
          title="代码示例"
          value={stats?.overview?.totalCodeExamples || 0}
          description="代码片段总数"
          icon={Code}
          color="success"
          trend={{ value: 8, isPositive: true }}
        />
        <StatsCard
          title="搜索次数"
          value={stats?.searchStats?.totalSearches || 0}
          description="本月搜索总次数"
          icon={Search}
          color="warning"
          trend={{ value: 15, isPositive: true }}
        />
      </div>

      {/* 快速操作区域 */}
      <div>
        <h2 className="text-xl font-semibold text-mysql-text mb-6">
          快速操作
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <QuickActionCard
            title="知识库管理"
            description="进入知识库管理中心"
            href="/admin/knowledge"
            icon={Database}
            color="mysql-primary"
          />
          <QuickActionCard
            title="创建新文章"
            description="添加新的知识库文章"
            href="/admin/knowledge/articles/new"
            icon={FileText}
            color="mysql-accent"
          />
          <QuickActionCard
            title="管理分类"
            description="组织和管理知识库分类"
            href="/admin/knowledge/categories"
            icon={FolderOpen}
            color="mysql-success"
          />
          <QuickActionCard
            title="代码示例"
            description="管理代码片段和示例"
            href="/admin/knowledge/code-examples"
            icon={Code}
            color="mysql-warning"
          />
          <QuickActionCard
            title="查看统计"
            description="详细的数据分析和报告"
            href="/admin/stats"
            icon={BarChart3}
            color="mysql-info"
          />
        </div>
      </div>

      {/* 最近活动 */}
      <div>
        <h2 className="text-xl font-semibold text-mysql-text mb-6">
          最近更新的文章
        </h2>
        <div className="bg-white rounded-xl shadow-lg border border-mysql-border">
          {stats?.recentArticles && stats.recentArticles.length > 0 ? (
            <div className="divide-y divide-mysql-border">
              {stats.recentArticles.slice(0, 5).map((article, index: number) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-4 hover:bg-mysql-primary-light/30 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-mysql-text">
                        {article.title}
                      </h3>
                      <p className="text-sm text-mysql-text-light">
                        分类: {article.knowledge_categories?.name || '未分类'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-mysql-text-light">
                        {article.last_updated}
                      </p>
                      <Link
                        href={`/admin/articles/${article.id}`}
                        className="text-sm text-mysql-primary hover:text-mysql-primary-dark transition-colors duration-200"
                      >
                        编辑
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <FileText className="w-12 h-12 text-mysql-text-light mx-auto mb-4" />
              <p className="text-mysql-text-light">
                暂无最近更新的文章
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
