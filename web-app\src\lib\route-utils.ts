// MySQLAi.de - 路由工具函数
// 提供路由验证、权限检查和导航辅助功能

import { NextRequest } from 'next/server';
import { 
  KNOWLEDGE_ROUTES, 
  ROUTE_PERMISSIONS, 
  checkRoutePermission,
  validateRouteParams,
  getRedirectRoute
} from './knowledge-routes';

// 路由匹配结果
interface RouteMatch {
  isKnowledgeRoute: boolean;
  isLegacyRoute: boolean;
  needsRedirect: boolean;
  redirectPath?: string;
  requiredPermissions: string[];
  isValid: boolean;
}

// 检查是否为知识库路由
export function isKnowledgeRoute(pathname: string): boolean {
  return pathname.startsWith('/admin/knowledge');
}

// 检查是否为旧的知识库路由
export function isLegacyKnowledgeRoute(pathname: string): boolean {
  const legacyPaths = [
    '/admin/articles',
    '/admin/categories',
    '/admin/code-examples'
  ];
  
  return legacyPaths.some(path => pathname.startsWith(path));
}

// 分析路由
export function analyzeRoute(pathname: string, _searchParams?: URLSearchParams): RouteMatch {
  const isKnowledge = isKnowledgeRoute(pathname);
  const isLegacy = isLegacyKnowledgeRoute(pathname);
  
  let needsRedirect = false;
  let redirectPath: string | undefined;
  let requiredPermissions: string[] = [];
  let isValid = true;

  // 处理旧路由重定向
  if (isLegacy) {
    needsRedirect = true;
    
    // 提取路由参数
    const pathSegments = pathname.split('/');
    const params: Record<string, string> = {};
    
    // 检查是否有ID参数
    if (pathSegments.length > 3) {
      params.id = pathSegments[3];
    }
    
    redirectPath = getRedirectRoute(pathname, params) || undefined;
  }

  // 获取权限要求
  if (isKnowledge || isLegacy) {
    const targetPath = redirectPath || pathname;
    requiredPermissions = ROUTE_PERMISSIONS[targetPath] || [];
  }

  // 验证路由参数
  if (isKnowledge) {
    const pathSegments = pathname.split('/');
    const params: Record<string, string> = {};
    
    // 提取参数
    if (pathSegments.includes('articles') || pathSegments.includes('code-examples')) {
      const lastSegment = pathSegments[pathSegments.length - 1];
      if (lastSegment !== 'articles' && lastSegment !== 'code-examples' && lastSegment !== 'new') {
        params.id = lastSegment;
      }
    }
    
    isValid = validateRouteParams(pathname, params);
  }

  return {
    isKnowledgeRoute: isKnowledge,
    isLegacyRoute: isLegacy,
    needsRedirect,
    redirectPath,
    requiredPermissions,
    isValid
  };
}

// 中间件路由处理
export function handleRouteInMiddleware(request: NextRequest): Response | null {
  const pathname = request.nextUrl.pathname;
  const searchParams = request.nextUrl.searchParams;
  
  const routeAnalysis = analyzeRoute(pathname, searchParams);
  
  // 处理无效路由
  if (!routeAnalysis.isValid) {
    return new Response('Invalid route parameters', { status: 400 });
  }
  
  // 处理重定向
  if (routeAnalysis.needsRedirect && routeAnalysis.redirectPath) {
    const redirectUrl = new URL(routeAnalysis.redirectPath, request.url);
    
    // 保持查询参数
    searchParams.forEach((value, key) => {
      redirectUrl.searchParams.set(key, value);
    });
    
    return Response.redirect(redirectUrl, 301); // 永久重定向
  }
  
  return null; // 继续正常处理
}

// 客户端路由导航辅助
export class KnowledgeRouter {
  // 导航到文章列表
  static toArticles(): string {
    return KNOWLEDGE_ROUTES.ARTICLES.ROOT;
  }
  
  // 导航到创建文章
  static toCreateArticle(): string {
    return KNOWLEDGE_ROUTES.ARTICLES.NEW;
  }
  
  // 导航到编辑文章
  static toEditArticle(id: string): string {
    return KNOWLEDGE_ROUTES.ARTICLES.EDIT(id);
  }
  
  // 导航到查看文章
  static toViewArticle(id: string): string {
    return KNOWLEDGE_ROUTES.ARTICLES.VIEW(id);
  }
  
  // 导航到分类列表
  static toCategories(): string {
    return KNOWLEDGE_ROUTES.CATEGORIES.ROOT;
  }
  
  // 导航到查看分类
  static toViewCategory(id: string): string {
    return KNOWLEDGE_ROUTES.CATEGORIES.VIEW(id);
  }
  
  // 导航到代码示例列表
  static toCodeExamples(): string {
    return KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT;
  }
  
  // 导航到创建代码示例
  static toCreateCodeExample(): string {
    return KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW;
  }
  
  // 导航到编辑代码示例
  static toEditCodeExample(id: string): string {
    return KNOWLEDGE_ROUTES.CODE_EXAMPLES.EDIT(id);
  }
  
  // 导航到查看代码示例
  static toViewCodeExample(id: string): string {
    return KNOWLEDGE_ROUTES.CODE_EXAMPLES.VIEW(id);
  }
  
  // 导航到知识库主页
  static toKnowledgeHome(): string {
    return KNOWLEDGE_ROUTES.ROOT;
  }
}

// URL构建辅助函数
export function buildKnowledgeUrl(
  basePath: string, 
  params?: Record<string, string>, 
  searchParams?: Record<string, string>
): string {
  let url = basePath;
  
  // 替换路径参数
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`[${key}]`, value);
    });
  }
  
  // 添加查询参数
  if (searchParams) {
    const urlParams = new URLSearchParams(searchParams);
    const queryString = urlParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }
  
  return url;
}

// 路由匹配器
export function matchKnowledgeRoute(pathname: string): {
  route: string | null;
  params: Record<string, string>;
} {
  const params: Record<string, string> = {};
  
  // 匹配文章路由
  if (pathname.startsWith('/admin/knowledge/articles/')) {
    const segments = pathname.split('/');
    if (segments[4] === 'new') {
      return { route: KNOWLEDGE_ROUTES.ARTICLES.NEW, params };
    } else if (segments[4]) {
      params.id = segments[4];
      return { route: '/admin/knowledge/articles/[id]', params };
    }
    return { route: KNOWLEDGE_ROUTES.ARTICLES.ROOT, params };
  }
  
  // 匹配代码示例路由
  if (pathname.startsWith('/admin/knowledge/code-examples/')) {
    const segments = pathname.split('/');
    if (segments[4] === 'new') {
      return { route: KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW, params };
    } else if (segments[4]) {
      params.id = segments[4];
      return { route: '/admin/knowledge/code-examples/[id]', params };
    }
    return { route: KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT, params };
  }
  
  // 匹配分类路由
  if (pathname === '/admin/knowledge/categories') {
    return { route: KNOWLEDGE_ROUTES.CATEGORIES.ROOT, params };
  }
  
  // 匹配知识库主页
  if (pathname === '/admin/knowledge') {
    return { route: KNOWLEDGE_ROUTES.ROOT, params };
  }
  
  return { route: null, params };
}

// 权限检查辅助函数
export function requireKnowledgePermission(
  pathname: string, 
  userPermissions: string[]
): boolean {
  return checkRoutePermission(pathname, userPermissions);
}

// 导出类型
export type { RouteMatch };
