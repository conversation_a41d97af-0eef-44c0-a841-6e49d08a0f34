/**
 * MySQL下载源配置和安装步骤常量
 * 包含官方源、镜像源、安装步骤和错误消息等
 */

// ===== 下载源配置 =====

/**
 * MySQL官方下载源
 */
export const OFFICIAL_DOWNLOAD_SOURCES = {
  MYSQL_OFFICIAL: 'https://dev.mysql.com/downloads/mysql/',
  MYSQL_ARCHIVES: 'https://downloads.mysql.com/archives/',
  MYSQL_CDN: 'https://cdn.mysql.com/Downloads/'
} as const;

/**
 * 国内镜像源
 */
export const CHINA_MIRROR_SOURCES = {
  ALIYUN: 'https://mirrors.aliyun.com/mysql/',
  HUAWEI: 'https://mirrors.huaweicloud.com/mysql/',
  TSINGHUA: 'https://mirrors.tuna.tsinghua.edu.cn/mysql/',
  USTC: 'https://mirrors.ustc.edu.cn/mysql-ftp/'
} as const;

/**
 * 国际镜像源
 */
export const INTERNATIONAL_MIRROR_SOURCES = {
  UBUNTU: 'http://archive.ubuntu.com/ubuntu/pool/universe/m/mysql-8.0/',
  DEBIAN: 'http://ftp.debian.org/debian/pool/main/m/mysql-8.0/',
  CENTOS: 'http://mirror.centos.org/centos/8/AppStream/x86_64/os/Packages/'
} as const;

// ===== 安装步骤模板 =====

/**
 * 通用安装步骤ID
 */
export const INSTALLATION_STEP_IDS = {
  DOWNLOAD: 'download',
  EXTRACT: 'extract',
  CONFIGURE: 'configure',
  INITIALIZE: 'initialize',
  START_SERVICE: 'start_service',
  VERIFY: 'verify',
  SECURE: 'secure'
} as const;

/**
 * 默认安装步骤预计时间（分钟）
 */
export const ESTIMATED_TIMES = {
  [INSTALLATION_STEP_IDS.DOWNLOAD]: 5,
  [INSTALLATION_STEP_IDS.EXTRACT]: 2,
  [INSTALLATION_STEP_IDS.CONFIGURE]: 3,
  [INSTALLATION_STEP_IDS.INITIALIZE]: 5,
  [INSTALLATION_STEP_IDS.START_SERVICE]: 2,
  [INSTALLATION_STEP_IDS.VERIFY]: 2,
  [INSTALLATION_STEP_IDS.SECURE]: 3
} as const;

// ===== 错误消息常量 =====

/**
 * 常见错误消息
 */
export const ERROR_MESSAGES = {
  UNSUPPORTED_OS: '不支持的操作系统',
  INSUFFICIENT_PERMISSIONS: '权限不足，请以管理员身份运行',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  DOWNLOAD_FAILED: '下载失败，请重试或选择其他下载源',
  CONFIG_GENERATION_FAILED: '配置文件生成失败',
  INVALID_PATH: '无效的安装路径',
  PORT_IN_USE: '端口已被占用，请选择其他端口',
  DISK_SPACE_INSUFFICIENT: '磁盘空间不足',
  ACTIVATION_REQUIRED: '需要激活许可证才能继续',
  ACTIVATION_EXPIRED: '许可证已过期',
  TRIAL_EXPIRED: '试用期已结束',
  INVALID_LICENSE: '无效的许可证密钥'
} as const;

// ===== 成功消息常量 =====

/**
 * 成功消息
 */
export const SUCCESS_MESSAGES = {
  SYSTEM_DETECTED: '系统检测完成',
  CONFIG_GENERATED: '配置文件生成成功',
  DOWNLOAD_READY: '下载准备就绪',
  INSTALLATION_COMPLETE: 'MySQL安装完成',
  ACTIVATION_SUCCESS: '许可证激活成功',
  DOWNLOAD_COMPLETE: '下载完成',
  SERVICE_STARTED: '服务启动成功'
} as const;

// ===== 桌面应用特有常量 =====

/**
 * 激活相关常量
 */
export const ACTIVATION_CONSTANTS = {
  /** 试用次数限制 */
  TRIAL_LIMIT: 3,
  /** 激活验证超时时间（毫秒） */
  ACTIVATION_TIMEOUT: 30000,
  /** 许可证缓存过期时间（小时） */
  LICENSE_CACHE_HOURS: 24,
  /** 机器指纹长度 */
  FINGERPRINT_LENGTH: 32
} as const;

/**
 * 下载相关常量
 */
export const DOWNLOAD_CONSTANTS = {
  /** 下载超时时间（毫秒） */
  DOWNLOAD_TIMEOUT: 300000,
  /** 重试次数 */
  MAX_RETRIES: 3,
  /** 分块大小（字节） */
  CHUNK_SIZE: 1024 * 1024, // 1MB
  /** 进度更新间隔（毫秒） */
  PROGRESS_INTERVAL: 1000
} as const;

/**
 * 安装相关常量
 */
export const INSTALLATION_CONSTANTS = {
  /** 安装超时时间（毫秒） */
  INSTALLATION_TIMEOUT: 600000, // 10分钟
  /** 服务启动等待时间（毫秒） */
  SERVICE_START_WAIT: 30000,
  /** 配置文件备份后缀 */
  CONFIG_BACKUP_SUFFIX: '.backup',
  /** 临时目录名称 */
  TEMP_DIR_NAME: 'mysqlai-temp'
} as const;