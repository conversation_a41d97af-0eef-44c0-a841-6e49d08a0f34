# MySQLAi.de - Supabase 配置
# 请在 Supabase Dashboard 中获取这些值

# Supabase 项目配置
NEXT_PUBLIC_SUPABASE_URL=https://rlwppfewjevxzhqeupdq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJsd3BwZmV3amV2eHpocWV1cGRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMDY1NjksImV4cCI6MjA2Njc4MjU2OX0.gDrcpdWqA8gdUIBw-5OLN2iBSkeHIDvmNdLwV67NJvc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJsd3BwZmV3amV2eHpocWV1cGRxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTIwNjU2OSwiZXhwIjoyMDY2NzgyNTY5fQ.0B07_F_Ay_sDWFU-kQ9MN1SlZHvLMmb_AZgf5fIwtMk

# 数据库直连 URL (可选，用于 Prisma 等工具)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# 开发环境配置
NODE_ENV=development
