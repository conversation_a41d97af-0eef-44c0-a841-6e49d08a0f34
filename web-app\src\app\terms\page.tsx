// MySQLAi.de - 服务条款页面
// 展示平台服务使用条款和用户协议

import { Metadata } from 'next';
import { getLegalContent } from '@/lib/legal';
import { PAGE_METADATA } from '@/lib/constants';
import { generatePageMetadata } from '@/app/metadata';
import LegalPageLayout from '@/components/layout/LegalPageLayout';

// 生成页面元数据
export function generateMetadata(): Metadata {
  const pageData = PAGE_METADATA.terms;
  return generatePageMetadata(
    pageData.title,
    pageData.description,
    '/terms'
  );
}

export default function TermsPage() {
  // 获取服务条款内容
  const termsContent = getLegalContent('terms');

  return (
    <LegalPageLayout
      type="terms"
      title={termsContent.title}
      lastUpdated={termsContent.lastUpdated}
      pathname="/terms"
    >
      {/* 渲染服务条款内容 */}
      {termsContent.sections.map((section) => (
        <div key={section.id} className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {section.title}
          </h2>
          <p className="text-gray-700 leading-relaxed mb-4">
            {section.content}
          </p>
          
          {/* 渲染子章节 */}
          {section.subsections && section.subsections.length > 0 && (
            <div className="ml-4 space-y-4">
              {section.subsections.map((subsection) => (
                <div key={subsection.id}>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">
                    {subsection.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {subsection.content}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </LegalPageLayout>
  );
}
