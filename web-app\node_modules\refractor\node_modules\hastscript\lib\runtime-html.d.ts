export * from './jsx-automatic.js'
export const Fragment: null
export const jsx: {
  (
    type: null | undefined,
    props: {
      children?: import('./core.js').HChild
    },
    key?: string | undefined
  ): import('hast').Root
  (
    type: string,
    props: Record<
      string,
      | string
      | number
      | boolean
      | import('hast').Root
      | import('hast').Comment
      | import('hast').DocType
      | import('hast').Element
      | import('hast').Text
      | import('./core.js').HStyle
      | import('./core.js').HArrayValue
      | import('./core.js').HArrayChild
      | null
      | undefined
    >,
    key?: string | undefined
  ): import('hast').Element
}
export const jsxs: {
  (
    type: null | undefined,
    props: {
      children?: import('./core.js').HChild
    },
    key?: string | undefined
  ): import('hast').Root
  (
    type: string,
    props: Record<
      string,
      | string
      | number
      | boolean
      | import('hast').Root
      | import('hast').Comment
      | import('hast').DocType
      | import('hast').Element
      | import('hast').Text
      | import('./core.js').HStyle
      | import('./core.js').HArrayValue
      | import('./core.js').HArrayChild
      | null
      | undefined
    >,
    key?: string | undefined
  ): import('hast').Element
}
export const jsxDEV: {
  (
    type: null | undefined,
    props: {
      children?: import('./core.js').HChild
    },
    key?: string | undefined
  ): import('hast').Root
  (
    type: string,
    props: Record<
      string,
      | string
      | number
      | boolean
      | import('hast').Root
      | import('hast').Comment
      | import('hast').DocType
      | import('hast').Element
      | import('hast').Text
      | import('./core.js').HStyle
      | import('./core.js').HArrayValue
      | import('./core.js').HArrayChild
      | null
      | undefined
    >,
    key?: string | undefined
  ): import('hast').Element
}
