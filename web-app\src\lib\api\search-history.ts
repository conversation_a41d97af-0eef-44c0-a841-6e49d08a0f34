// MySQLAi.de - 搜索历史 API 客户端
// 提供类型安全的搜索历史 API 调用方法

import { SearchHistoryItem } from '@/lib/types';

// API 响应类型
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  total?: number;
}

// API 基础配置
const API_BASE = '/api/knowledge/search-history';

// 通用 API 调用函数
async function apiCall<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API调用失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误',
    };
  }
}

// 搜索历史 API
export const searchHistoryApi = {
  // 获取搜索历史
  getHistory: async (options: {
    limit?: number;
    offset?: number;
    query?: string;
  } = {}): Promise<ApiResponse<SearchHistoryItem[]>> => {
    const params = new URLSearchParams();
    
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());
    if (options.query) params.append('query', options.query);

    const queryString = params.toString();
    const endpoint = queryString ? `?${queryString}` : '';
    
    return apiCall<SearchHistoryItem[]>(endpoint);
  },

  // 添加搜索记录
  addSearchRecord: async (query: string, resultsCount: number = 0): Promise<ApiResponse<SearchHistoryItem>> => {
    return apiCall<SearchHistoryItem>('', {
      method: 'POST',
      body: JSON.stringify({
        query: query.trim(),
        results_count: resultsCount
      }),
    });
  },

  // 删除特定搜索记录
  deleteRecord: async (id: string): Promise<ApiResponse<void>> => {
    return apiCall<void>(`?id=${id}`, {
      method: 'DELETE',
    });
  },

  // 清除所有搜索历史
  clearAllHistory: async (): Promise<ApiResponse<void>> => {
    return apiCall<void>('?clearAll=true', {
      method: 'DELETE',
    });
  },

  // 搜索历史记录（在历史中查找）
  searchInHistory: async (query: string, limit: number = 10): Promise<ApiResponse<SearchHistoryItem[]>> => {
    return apiCall<SearchHistoryItem[]>(`?query=${encodeURIComponent(query)}&limit=${limit}`);
  },
};

// 本地存储管理（作为备用方案）
export const localSearchHistory = {
  // 本地存储键名
  STORAGE_KEY: 'mysqlai_search_history',
  
  // 获取本地搜索历史
  getLocal: (): SearchHistoryItem[] => {
    try {
      const stored = localStorage.getItem(localSearchHistory.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取本地搜索历史失败:', error);
      return [];
    }
  },

  // 保存到本地存储
  saveLocal: (items: SearchHistoryItem[]): void => {
    try {
      localStorage.setItem(localSearchHistory.STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('保存本地搜索历史失败:', error);
    }
  },

  // 添加搜索记录到本地
  addLocal: (query: string, resultsCount: number = 0): void => {
    const items = localSearchHistory.getLocal();
    const newItem: SearchHistoryItem = {
      id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      query: query.trim(),
      results_count: resultsCount,
      created_at: new Date().toISOString()
    };

    // 检查是否已存在相同查询（最近10条）
    const recentItems = items.slice(0, 10);
    const existingIndex = recentItems.findIndex(item => 
      item.query.toLowerCase() === query.toLowerCase().trim()
    );

    if (existingIndex >= 0) {
      // 如果存在，移动到最前面
      const existingItem = items.splice(existingIndex, 1)[0];
      items.unshift({
        ...existingItem,
        results_count: resultsCount,
        created_at: new Date().toISOString()
      });
    } else {
      // 如果不存在，添加到最前面
      items.unshift(newItem);
    }

    // 限制最多保存50条记录
    const limitedItems = items.slice(0, 50);
    localSearchHistory.saveLocal(limitedItems);
  },

  // 删除本地搜索记录
  deleteLocal: (id: string): void => {
    const items = localSearchHistory.getLocal();
    const filteredItems = items.filter(item => item.id !== id);
    localSearchHistory.saveLocal(filteredItems);
  },

  // 清除所有本地搜索历史
  clearLocal: (): void => {
    try {
      localStorage.removeItem(localSearchHistory.STORAGE_KEY);
    } catch (error) {
      console.error('清除本地搜索历史失败:', error);
    }
  },
};

// 混合搜索历史管理（优先使用API，降级到本地存储）
export const hybridSearchHistory = {
  // 获取搜索历史（API优先，本地降级）
  getHistory: async (limit: number = 10): Promise<SearchHistoryItem[]> => {
    try {
      const response = await searchHistoryApi.getHistory({ limit });
      if (response.success && response.data) {
        return response.data;
      }
    } catch (error) {
      console.warn('API获取搜索历史失败，使用本地存储:', error);
    }

    // 降级到本地存储
    return localSearchHistory.getLocal().slice(0, limit);
  },

  // 添加搜索记录（API优先，本地降级）
  addRecord: async (query: string, resultsCount: number = 0): Promise<void> => {
    try {
      const response = await searchHistoryApi.addSearchRecord(query, resultsCount);
      if (response.success) {
        return;
      }
    } catch (error) {
      console.warn('API保存搜索历史失败，使用本地存储:', error);
    }

    // 降级到本地存储
    localSearchHistory.addLocal(query, resultsCount);
  },

  // 删除搜索记录（API优先，本地降级）
  deleteRecord: async (id: string): Promise<void> => {
    try {
      if (id.startsWith('local_')) {
        // 本地记录直接删除
        localSearchHistory.deleteLocal(id);
        return;
      }

      const response = await searchHistoryApi.deleteRecord(id);
      if (response.success) {
        return;
      }
    } catch (error) {
      console.warn('API删除搜索记录失败:', error);
    }
  },

  // 清除所有搜索历史（API优先，本地降级）
  clearAll: async (): Promise<void> => {
    try {
      const response = await searchHistoryApi.clearAllHistory();
      if (response.success) {
        // API成功后也清除本地存储
        localSearchHistory.clearLocal();
        return;
      }
    } catch (error) {
      console.warn('API清除搜索历史失败，使用本地存储:', error);
    }

    // 降级到本地存储
    localSearchHistory.clearLocal();
  },
};
