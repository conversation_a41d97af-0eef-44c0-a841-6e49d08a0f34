'use client';

/**
 * 状态组件集合
 * 从Web版本移植，适配桌面应用环境
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Loader2, 
  AlertCircle, 
  RefreshCw, 
  FileX, 
  Search,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { LoadingStateProps, ErrorStateProps, EmptyStateProps } from '../../lib/types';
import Button from './Button';

/**
 * 加载状态组件
 */
export function LoadingState({
  message = '正在加载...',
  size = 'md',
  variant = 'spinner',
  itemCount = 6,
  className
}: LoadingStateProps) {
  const sizeConfig = {
    xs: { spinner: 'w-4 h-4', text: 'text-xs', spacing: 'space-y-2' },
    sm: { spinner: 'w-4 h-4', text: 'text-sm', spacing: 'space-y-2' },
    md: { spinner: 'w-6 h-6', text: 'text-base', spacing: 'space-y-3' },
    lg: { spinner: 'w-8 h-8', text: 'text-lg', spacing: 'space-y-4' },
    xl: { spinner: 'w-10 h-10', text: 'text-xl', spacing: 'space-y-4' }
  };

  const config = sizeConfig[size];

  // 旋转加载器
  const SpinnerLoader = () => (
    <div className={cn('flex flex-col items-center justify-center', config.spacing, className)}>
      <Loader2 className={cn(config.spinner, 'animate-spin text-mysql-primary')} />
      <p className={cn(config.text, 'text-mysql-text-light')}>{message}</p>
    </div>
  );

  // 点状加载器
  const DotsLoader = () => (
    <div className={cn('flex flex-col items-center justify-center', config.spacing, className)}>
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-mysql-primary rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
      <p className={cn(config.text, 'text-mysql-text-light')}>{message}</p>
    </div>
  );

  // 脉冲加载器
  const PulseLoader = () => (
    <div className={cn('flex flex-col items-center justify-center', config.spacing, className)}>
      <motion.div
        className={cn(
          'rounded-full bg-mysql-primary',
          size === 'xs' ? 'w-8 h-8' :
          size === 'sm' ? 'w-10 h-10' :
          size === 'md' ? 'w-12 h-12' :
          size === 'lg' ? 'w-16 h-16' : 'w-20 h-20'
        )}
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.7, 1, 0.7],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <p className={cn(config.text, 'text-mysql-text-light')}>{message}</p>
    </div>
  );

  // 骨架屏加载器
  const SkeletonLoader = () => (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: itemCount }).map((_, i) => (
        <motion.div
          key={i}
          className="animate-pulse"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: i * 0.1 }}
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              <div className="h-3 bg-gray-300 rounded w-1/2"></div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );

  switch (variant) {
    case 'dots':
      return <DotsLoader />;
    case 'pulse':
      return <PulseLoader />;
    case 'skeleton':
      return <SkeletonLoader />;
    default:
      return <SpinnerLoader />;
  }
}

/**
 * 错误状态组件
 */
export function ErrorState({
  title = '出现错误',
  message,
  actionText = '重试',
  onAction,
  showIcon = true,
  className
}: ErrorStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex flex-col items-center justify-center text-center p-8',
        className
      )}
    >
      {showIcon && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
          className="mb-4"
        >
          <AlertCircle className="w-16 h-16 text-red-500" />
        </motion.div>
      )}
      
      <h3 className="text-lg font-semibold text-mysql-text mb-2">
        {title}
      </h3>
      
      <p className="text-mysql-text-light mb-6 max-w-md">
        {message}
      </p>
      
      {onAction && (
        <Button
          variant="outline"
          onClick={onAction}
          icon={<RefreshCw className="w-4 h-4" />}
        >
          {actionText}
        </Button>
      )}
    </motion.div>
  );
}

/**
 * 空状态组件
 */
export function EmptyState({
  title = '暂无数据',
  message,
  actionText,
  onAction,
  icon,
  className
}: EmptyStateProps) {
  const defaultIcon = <FileX className="w-16 h-16 text-gray-400" />;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex flex-col items-center justify-center text-center p-8',
        className
      )}
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
        className="mb-4"
      >
        {icon || defaultIcon}
      </motion.div>
      
      <h3 className="text-lg font-semibold text-mysql-text mb-2">
        {title}
      </h3>
      
      <p className="text-mysql-text-light mb-6 max-w-md">
        {message}
      </p>
      
      {onAction && actionText && (
        <Button
          variant="primary"
          onClick={onAction}
        >
          {actionText}
        </Button>
      )}
    </motion.div>
  );
}

/**
 * 网络状态组件
 */
interface NetworkStateProps {
  isOnline: boolean;
  className?: string;
}

export function NetworkState({ isOnline, className }: NetworkStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        'flex items-center space-x-2 px-3 py-2 rounded-lg',
        isOnline 
          ? 'bg-green-50 text-green-700 border border-green-200'
          : 'bg-red-50 text-red-700 border border-red-200',
        className
      )}
    >
      {isOnline ? (
        <Wifi className="w-4 h-4" />
      ) : (
        <WifiOff className="w-4 h-4" />
      )}
      <span className="text-sm font-medium">
        {isOnline ? '已连接' : '网络断开'}
      </span>
    </motion.div>
  );
}

/**
 * 搜索状态组件
 */
interface SearchStateProps {
  query: string;
  resultCount: number;
  isSearching: boolean;
  className?: string;
}

export function SearchState({ 
  query, 
  resultCount, 
  isSearching, 
  className 
}: SearchStateProps) {
  if (isSearching) {
    return (
      <LoadingState
        message="正在搜索..."
        size="sm"
        variant="dots"
        className={className}
      />
    );
  }

  if (query && resultCount === 0) {
    return (
      <EmptyState
        title="未找到结果"
        message={`没有找到与 "${query}" 相关的内容`}
        icon={<Search className="w-16 h-16 text-gray-400" />}
        className={className}
      />
    );
  }

  if (query && resultCount > 0) {
    return (
      <div className={cn('text-sm text-mysql-text-light p-4', className)}>
        找到 {resultCount} 个与 "{query}" 相关的结果
      </div>
    );
  }

  return null;
}

/**
 * 通用状态包装器
 */
interface StateWrapperProps {
  loading?: boolean;
  error?: string;
  empty?: boolean;
  children: React.ReactNode;
  loadingProps?: Partial<LoadingStateProps>;
  errorProps?: Partial<ErrorStateProps>;
  emptyProps?: Partial<EmptyStateProps>;
  className?: string;
}

export function StateWrapper({
  loading,
  error,
  empty,
  children,
  loadingProps,
  errorProps,
  emptyProps,
  className
}: StateWrapperProps) {
  if (loading) {
    return (
      <div className={className}>
        <LoadingState {...loadingProps} />
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <ErrorState
          message={error}
          {...errorProps}
        />
      </div>
    );
  }

  if (empty) {
    return (
      <div className={className}>
        <EmptyState
          message="暂无数据"
          {...emptyProps}
        />
      </div>
    );
  }

  return <div className={className}>{children}</div>;
}

/**
 * 连接状态指示器
 */
interface ConnectionStatusProps {
  status: 'connected' | 'connecting' | 'disconnected' | 'error';
  className?: string;
}

export function ConnectionStatus({ status, className }: ConnectionStatusProps) {
  const statusConfig = {
    connected: {
      color: 'bg-green-500',
      text: '已连接',
      textColor: 'text-green-700',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    connecting: {
      color: 'bg-yellow-500',
      text: '连接中...',
      textColor: 'text-yellow-700',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    disconnected: {
      color: 'bg-gray-500',
      text: '未连接',
      textColor: 'text-gray-700',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    },
    error: {
      color: 'bg-red-500',
      text: '连接错误',
      textColor: 'text-red-700',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    }
  };

  const config = statusConfig[status];

  return (
    <div className={cn(
      'flex items-center space-x-2 px-3 py-2 rounded-lg border',
      config.bgColor,
      config.borderColor,
      className
    )}>
      <motion.div
        className={cn('w-2 h-2 rounded-full', config.color)}
        animate={status === 'connecting' ? {
          scale: [1, 1.2, 1],
          opacity: [0.7, 1, 0.7],
        } : {}}
        transition={status === 'connecting' ? {
          duration: 1,
          repeat: Infinity,
        } : {}}
      />
      <span className={cn('text-sm font-medium', config.textColor)}>
        {config.text}
      </span>
    </div>
  );
}