/**
 * Tauri API Mock for Browser Development
 * 在浏览器环境中模拟Tauri API，用于开发和测试
 */

// 检查是否在Tauri环境中
export const isTauriEnvironment = () => {
  return typeof window !== 'undefined' && window.__TAURI_IPC__ !== undefined;
};

// Mock的系统信息
const mockSystemInfo = {
  platform: 'win32',
  arch: 'x64',
  version: '10.0.19042',
  hostname: 'DEVELOPMENT-PC',
  totalMemory: 16777216000,
  freeMemory: **********,
  cpuCount: 8,
  cpuModel: 'Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz',
};

// Mock的机器指纹
const mockFingerprint = {
  machineId: 'dev-machine-12345',
  cpuId: 'mock-cpu-id',
  diskId: 'mock-disk-id',
  macAddress: '00:11:22:33:44:55',
  motherboardId: 'mock-motherboard-id',
};

// Mock的激活状态
let mockActivationState = {
  isActivated: false,
  licenseKey: '',
  expiresAt: '',
  machineId: mockFingerprint.machineId,
  isTrialMode: true,
  remainingTrials: 30,
};

// Mock函数
export const mockTauriAPI = {
  // 基础问候函数
  greet: async (name: string) => {
    return `Hello ${name}, you've been greeted from Rust! (Mock Mode)`;
  },

  // 系统信息
  get_system_info: async () => {
    return JSON.stringify(mockSystemInfo);
  },

  // 测试连接
  test_connection: async () => {
    return { success: true, message: 'Mock connection successful' };
  },

  // 机器指纹
  generate_fingerprint: async () => {
    return mockFingerprint;
  },

  get_machine_features: async () => {
    return {
      cpu: mockSystemInfo.cpuModel,
      memory: `${Math.round(mockSystemInfo.totalMemory / 1024 / 1024 / 1024)}GB`,
      platform: mockSystemInfo.platform,
      arch: mockSystemInfo.arch,
    };
  },

  get_features_summary: async () => {
    return {
      hardware: 'Intel i7-8700K, 16GB RAM',
      system: 'Windows 10 x64',
      network: 'Connected',
    };
  },

  get_system_performance: async () => {
    return {
      cpuUsage: Math.random() * 100,
      memoryUsage: Math.random() * 100,
      diskUsage: Math.random() * 100,
    };
  },

  // 激活系统
  init_activation_system: async () => {
    return { success: true, message: 'Mock activation system initialized' };
  },

  check_activation_status: async () => {
    // 计算剩余时间
    let remainingHours = 0;
    if (mockActivationState.isActivated && mockActivationState.expiresAt) {
      const now = new Date();
      const expiryDate = new Date(mockActivationState.expiresAt);
      const diffMs = expiryDate.getTime() - now.getTime();
      remainingHours = Math.max(0, diffMs / (1000 * 60 * 60)); // 转换为小时
    }

    return {
      ...mockActivationState,
      remainingHours,
    };
  },

  validate_license: async (licenseKey: string) => {
    // 支持的测试激活码列表
    const validLicenses = {
      'MOCK-LICENSE-KEY-12345': {
        type: 'Professional',
        expiresAt: '2025-12-31',
        features: ['mysql_download', 'mysql_install', 'premium_support'],
        duration: 8760, // 1年
      },
      'MYSQL-TEST-2024-DEMO-0001': {
        type: 'Demo',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时后
        features: ['mysql_download', 'mysql_install'],
        duration: 24,
      },
      'MYSQL-PROF-2024-FULL-0001': {
        type: 'Professional',
        expiresAt: new Date(Date.now() + 8760 * 60 * 60 * 1000).toISOString(), // 1年后
        features: ['mysql_download', 'mysql_install', 'premium_support'],
        duration: 8760,
      },
      'MYSQL-EVAL-2024-TRIAL-001': {
        type: 'Evaluation',
        expiresAt: new Date(Date.now() + 168 * 60 * 60 * 1000).toISOString(), // 1周后
        features: ['mysql_download', 'mysql_install'],
        duration: 168,
      },
    };

    const licenseInfo = validLicenses[licenseKey as keyof typeof validLicenses];
    if (licenseInfo) {
      return {
        valid: true,
        licenseInfo,
      };
    }
    return { valid: false, error: 'License validation failed\nPlease confirm that the license key is valid, or contact technical support.' };
  },

  activate_license_key: async (licenseKey: string) => {
    const validation = await mockTauriAPI.validate_license(licenseKey);
    if (validation.valid) {
      mockActivationState = {
        ...mockActivationState,
        isActivated: true,
        licenseKey,
        expiresAt: validation.licenseInfo?.expiresAt || '',
        isTrialMode: false,
      };
      return { success: true, message: 'License activated successfully' };
    }
    return { success: false, error: 'Invalid license key' };
  },

  deactivate_license_key: async () => {
    mockActivationState = {
      ...mockActivationState,
      isActivated: false,
      licenseKey: '',
      expiresAt: '',
      isTrialMode: true,
    };
    return { success: true, message: 'License deactivated successfully' };
  },

  check_is_activated: async () => {
    return mockActivationState.isActivated;
  },

  // 位置检测
  detect_user_location: async () => {
    return {
      country: 'CN',
      region: 'Beijing',
      city: 'Beijing',
      timezone: 'Asia/Shanghai',
    };
  },

  // MySQL相关
  get_mysql_package: async (version: string) => {
    return {
      version,
      downloadUrl: `https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-${version}-winx64.msi`,
      size: 123456789,
      checksum: 'mock-checksum-12345',
    };
  },

  get_supported_versions: async () => {
    return [
      { version: '8.0.35', stable: true, recommended: true },
      { version: '8.0.34', stable: true, recommended: false },
      { version: '5.7.44', stable: true, recommended: false },
    ];
  },

  download_mysql_package: async (packageInfo: any) => {
    // Mock下载进度
    return new Promise((resolve) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        console.log(`Mock download progress: ${progress}%`);
        if (progress >= 100) {
          clearInterval(interval);
          resolve({
            success: true,
            filePath: 'C:\\Downloads\\mysql-8.0.35-winx64.msi',
            message: 'Download completed successfully',
          });
        }
      }, 500);
    });
  },

  // MySQL安装
  detect_mysql_components: async () => {
    return {
      hasExistingInstallation: false,
      installedVersion: null,
      installPath: null,
      services: [],
    };
  },

  uninstall_old_mysql: async () => {
    return { success: true, message: 'No existing MySQL installation found' };
  },

  install_mysql: async (config: any) => {
    // Mock安装进度
    return new Promise((resolve) => {
      let progress = 0;
      const steps = [
        'Preparing installation...',
        'Extracting files...',
        'Installing MySQL Server...',
        'Configuring MySQL...',
        'Starting MySQL service...',
        'Installation completed!',
      ];
      
      const interval = setInterval(() => {
        console.log(`Mock install: ${steps[Math.floor(progress / 20)]} (${progress}%)`);
        progress += 20;
        if (progress > 100) {
          clearInterval(interval);
          resolve({
            success: true,
            installPath: 'C:\\Program Files\\MySQL\\MySQL Server 8.0',
            message: 'MySQL installed successfully',
          });
        }
      }, 1000);
    });
  },

  verify_mysql_installation: async () => {
    return {
      isInstalled: true,
      version: '8.0.35',
      isRunning: true,
      installPath: 'C:\\Program Files\\MySQL\\MySQL Server 8.0',
    };
  },

  set_mysql_config: async (config: any) => {
    return { success: true, message: 'MySQL configuration updated' };
  },

  get_mysql_config: async () => {
    return {
      port: 3306,
      rootPassword: '***',
      dataDir: 'C:\\ProgramData\\MySQL\\MySQL Server 8.0\\Data',
      logDir: 'C:\\ProgramData\\MySQL\\MySQL Server 8.0\\Logs',
    };
  },
};

// 创建统一的API接口
export const createTauriAPI = () => {
  if (isTauriEnvironment()) {
    // 在Tauri环境中，使用真实的API
    return import('@tauri-apps/api/tauri').then(({ invoke }) => {
      const api: any = {};
      Object.keys(mockTauriAPI).forEach(key => {
        api[key] = (...args: any[]) => invoke(key, ...args);
      });
      return api;
    });
  } else {
    // 在浏览器环境中，使用mock API
    return Promise.resolve(mockTauriAPI);
  }
};

// 默认导出
export default mockTauriAPI;