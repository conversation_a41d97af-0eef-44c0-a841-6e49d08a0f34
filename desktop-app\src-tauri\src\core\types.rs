use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 支持的操作系统
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum SupportedOS {
    Windows,
    Macos,
    Linux,
    Unknown,
}

/// 系统架构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum SystemArchitecture {
    X64,
    ARM64,
    X86,
    Unknown,
}

/// 安装模式
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum InstallationMode {
    Quick,
    Advanced,
}

/// 安装状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum InstallationStatus {
    Detecting,
    Ready,
    #[serde(rename = "generating")]
    GeneratingConfig,
    Downloading,
    Installing,
    Completed,
    Error,
}

/// MySQL版本信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum MySQLVersion {
    #[serde(rename = "8.0.28")]
    V8_0_28,
    #[serde(rename = "8.0.36")]
    V8_0_36,
}

/// 系统信息接口
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SystemInfo {
    /// 操作系统
    pub os: SupportedOS,
    /// 系统架构
    pub architecture: SystemArchitecture,
    /// 操作系统版本
    pub os_version: Option<String>,
    /// 浏览器信息
    pub browser: Option<String>,
    /// 是否支持MySQL安装
    pub is_supported: bool,
    /// 系统要求检查结果
    pub requirements: Vec<SystemRequirement>,
}

/// 系统要求检查
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemRequirement {
    /// 要求名称
    pub name: String,
    /// 是否满足要求
    pub satisfied: bool,
    /// 描述信息
    pub description: String,
    /// 建议操作
    pub suggestion: Option<String>,
}

/// 一键安装配置接口
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuickInstallConfig {
    /// MySQL版本
    pub version: MySQLVersion,
    /// 目标操作系统
    pub target_os: SupportedOS,
    /// 安装路径
    pub install_path: String,
    /// 数据目录路径
    pub data_path: String,
    /// 端口号
    pub port: u16,
    /// root密码
    pub root_password: String,
    /// 字符集
    pub charset: String,
    /// 排序规则
    pub collation: String,
    /// 是否启用二进制日志
    pub enable_bin_log: bool,
    /// 是否创建Windows服务
    pub create_service: bool,
}

/// 默认安装模板接口
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DefaultTemplate {
    /// 操作系统
    pub os: SupportedOS,
    /// 默认安装路径
    pub default_install_path: String,
    /// 默认数据路径
    pub default_data_path: String,
    /// 默认端口
    pub default_port: u16,
    /// 默认字符集
    pub default_charset: String,
    /// 默认排序规则
    pub default_collation: String,
    /// 配置文件名
    pub config_file_name: String,
    /// 服务名称
    pub service_name: Option<String>,
}

/// 下载源信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadSource {
    /// 源名称
    pub name: String,
    /// 下载URL
    pub url: String,
    /// 地区
    pub region: String,
    /// 优先级
    pub priority: u8,
    /// 下载速度（KB/s）
    pub speed: Option<u64>,
    /// 是否可用
    pub available: bool,
}

/// MySQL安装包信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MySQLPackage {
    /// 版本
    pub version: MySQLVersion,
    /// 操作系统
    pub os: SupportedOS,
    /// 系统架构
    pub architecture: SystemArchitecture,
    /// 文件名
    pub filename: String,
    /// 文件大小（字节）
    pub file_size: u64,
    /// 下载源列表
    pub download_sources: Vec<DownloadSource>,
    /// 校验和
    pub checksum: Option<String>,
    /// 发布日期
    pub release_date: String,
}

/// 安装步骤
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InstallationStep {
    /// 步骤ID
    pub id: String,
    /// 步骤标题
    pub title: String,
    /// 步骤描述
    pub description: String,
    /// 执行命令
    pub command: Option<String>,
    /// 是否必需
    pub required: bool,
    /// 是否已完成
    pub completed: bool,
    /// 预计耗时（分钟）
    pub estimated_time: Option<u32>,
}

/// 安装结果接口
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InstallationResult {
    /// 安装状态
    pub status: InstallationStatus,
    /// 安装配置
    pub config: QuickInstallConfig,
    /// 生成的配置文件内容
    pub config_file_content: Option<String>,
    /// 安装指导步骤
    pub installation_steps: Vec<InstallationStep>,
    /// 下载链接
    pub download_url: Option<String>,
    /// 错误信息
    pub error: Option<String>,
    /// 安装时间
    pub timestamp: String,
}

/// 高级配置选项
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AdvancedConfig {
    /// 基础配置
    #[serde(flatten)]
    pub base: QuickInstallConfig,
    /// InnoDB缓冲池大小
    pub innodb_buffer_pool_size: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 查询缓存大小
    pub query_cache_size: String,
    /// 慢查询日志
    pub slow_query_log: bool,
    /// 慢查询时间阈值
    pub long_query_time: u32,
    /// 自定义配置项
    pub custom_config: HashMap<String, String>,
}

/// 激活状态接口（桌面应用专用）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ActivationStatus {
    /// 是否已激活
    pub is_activated: bool,
    /// 许可证密钥
    pub license_key: Option<String>,
    /// 过期时间
    pub expires_at: Option<String>,
    /// 机器ID
    pub machine_id: String,
    /// 试用模式
    pub is_trial_mode: Option<bool>,
    /// 剩余试用次数
    pub remaining_trials: Option<u32>,
}

/// 下载进度接口（桌面应用专用）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DownloadProgress {
    /// 总字节数
    pub total_bytes: u64,
    /// 已下载字节数
    pub downloaded_bytes: u64,
    /// 下载百分比
    pub percentage: f64,
    /// 下载速度（字节/秒）
    pub speed_bps: u64,
    /// 剩余时间（秒）
    pub remaining_time: Option<u64>,
    /// 当前下载源
    pub current_source: Option<String>,
}

/// 安装进度接口（桌面应用专用）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InstallationProgress {
    /// 当前步骤
    pub current_step: String,
    /// 步骤索引
    pub step_index: u32,
    /// 总步骤数
    pub total_steps: u32,
    /// 当前步骤进度百分比
    pub step_progress: f64,
    /// 整体进度百分比
    pub overall_progress: f64,
    /// 状态消息
    pub message: String,
    /// 是否可以取消
    pub cancellable: bool,
}

/// 错误详情接口（桌面应用专用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorDetails {
    /// 错误代码
    pub code: String,
    /// 错误消息
    pub message: String,
    /// 详细描述
    pub details: Option<String>,
    /// 建议解决方案
    pub suggestions: Option<Vec<String>>,
    /// 错误发生时间
    pub timestamp: String,
    /// 错误堆栈（调试用）
    pub stack: Option<String>,
}