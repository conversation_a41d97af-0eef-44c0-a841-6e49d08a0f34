'use client';

// MySQLAi.de - 管理系统根布局
// 为管理后台提供完全独立的布局结构，不继承主网站布局

import React from 'react';
import { AdminAuth } from '@/lib/admin-auth';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminRootLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  // 检查认证状态
  useEffect(() => {
    // 检查当前路径，如果是登录页面就不进行认证检查
    if (pathname === '/admin/login') {
      return;
    }

    const checkAuth = async () => {
      const authenticated = AdminAuth.isAuthenticated();

      if (authenticated) {
        // 简化认证检查，先设置为已认证
        setIsAuthenticated(true);

        // 异步验证令牌，如果失败再处理
        try {
          const isValid = await AdminAuth.verifyAuth();
          if (!isValid) {
            setIsAuthenticated(false);
            router.replace('/admin/login');
          }
        } catch (error) {
          console.error('认证验证失败:', error);
          // 如果验证失败，但本地有令牌，暂时保持认证状态
          // 让用户能够使用管理系统，后续API调用会处理认证问题
        }
      } else {
        setIsAuthenticated(false);
        router.replace('/admin/login');
      }
    };

    checkAuth();
  }, [router, pathname]);

  // 如果是登录页面，使用独立的登录布局
  if (pathname === '/admin/login') {
    return (
      <html lang="zh-CN" className="scroll-smooth">
        <body className="antialiased bg-white text-mysql-text">
          {children}
        </body>
      </html>
    );
  }

  // 加载状态 - 使用独立布局
  if (isAuthenticated === null) {
    return (
      <html lang="zh-CN" className="scroll-smooth">
        <body className="antialiased bg-white text-mysql-text">
          <div className="min-h-screen bg-mysql-primary-light flex items-center justify-center">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-mysql-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-mysql-text">验证管理员身份...</p>
            </div>
          </div>
        </body>
      </html>
    );
  }

  // 未认证状态 - 使用独立布局
  if (!isAuthenticated) {
    return (
      <html lang="zh-CN" className="scroll-smooth">
        <body className="antialiased bg-white text-mysql-text">
          <div className="min-h-screen bg-mysql-primary-light flex items-center justify-center">
            <div className="text-center">
              <p className="text-mysql-text">重定向到登录页面...</p>
            </div>
          </div>
        </body>
      </html>
    );
  }

  // 已认证，显示管理界面 - 使用独立布局
  return (
    <html lang="zh-CN" className="scroll-smooth">
      <body className="antialiased bg-white text-mysql-text">
        <div className="bg-gray-50 min-h-screen">
          <AdminLayout>{children}</AdminLayout>
        </div>
      </body>
    </html>
  );
}
