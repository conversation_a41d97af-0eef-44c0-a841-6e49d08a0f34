// MySQLAi.de - MySQL安装指导 API
// 提供系统特定的安装指导和验证脚本

import { NextRequest, NextResponse } from 'next/server';
import { 
  SupportedOS, 
  QuickInstallConfig 
} from '@/app/tools/mysql-installer/types/mysql-installer';
import { 
  generateInstallationGuide,
  getPackageManagerCommands,
  getCommonIssues,
  generateVerificationScript 
} from '@/app/tools/mysql-installer/lib/installation-guide';

/**
 * 验证操作系统参数
 */
function validateOS(os: string): { isValid: boolean; parsedOS?: SupportedOS } {
  if (!os || !Object.values(SupportedOS).includes(os as SupportedOS)) {
    return { isValid: false };
  }
  return { isValid: true, parsedOS: os as SupportedOS };
}

// GET /api/mysql-installer/installation-guide - 获取安装指导
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const os = searchParams.get('os');
    const includePackageManager = searchParams.get('includePackageManager') === 'true';
    const includeCommonIssues = searchParams.get('includeCommonIssues') === 'true';
    const action = searchParams.get('action') || 'get-guide';
    const format = searchParams.get('format') || 'json';

    // 处理常见问题请求
    if (action === 'common-issues') {
      const commonIssues = getCommonIssues();

      return NextResponse.json({
        success: true,
        data: {
          issues: commonIssues,
          total: commonIssues.reduce((sum, category) => sum + category.issues.length, 0),
          categories: commonIssues.length,
          generatedAt: new Date().toISOString()
        }
      });
    }

    // 验证操作系统参数
    const osValidation = validateOS(os || '');
    if (!osValidation.isValid) {
      return NextResponse.json(
        { success: false, error: '无效的操作系统参数' },
        { status: 400 }
      );
    }

    const targetOS = osValidation.parsedOS!;

    // 创建默认配置用于生成指导
    const defaultConfig: QuickInstallConfig = {
      version: 'mysql-8.0.36',
      targetOS,
      installPath: targetOS === SupportedOS.WINDOWS
        ? 'C:\\Program Files\\MySQL\\MySQL Server 8.0'
        : '/usr/local/mysql',
      dataPath: targetOS === SupportedOS.WINDOWS
        ? 'C:\\ProgramData\\MySQL\\MySQL Server 8.0\\Data'
        : '/usr/local/mysql/data',
      logPath: targetOS === SupportedOS.WINDOWS
        ? 'C:\\ProgramData\\MySQL\\MySQL Server 8.0\\Logs'
        : '/var/log/mysql',
      port: 3306,
      charset: 'utf8mb4',
      collation: 'utf8mb4_unicode_ci',
      rootPassword: 'MySecurePassword123!',
      enableSSL: true
    };

    // 处理验证脚本请求
    if (action === 'verification-script') {
      const verificationScript = generateVerificationScript(defaultConfig);
      const filename = targetOS === SupportedOS.WINDOWS ? 'verify_mysql.bat' : 'verify_mysql.sh';

      if (format === 'file') {
        // 返回文件下载
        return new NextResponse(verificationScript.script, {
          status: 200,
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
      } else {
        // 返回JSON响应
        return NextResponse.json({
          success: true,
          data: {
            script: verificationScript.script,
            description: verificationScript.description,
            filename,
            os: targetOS,
            size: Buffer.byteLength(verificationScript.script, 'utf8'),
            generatedAt: new Date().toISOString()
          }
        });
      }
    }
    
    // 生成安装指导
    const guide = generateInstallationGuide(targetOS, defaultConfig);

    const baseResponse = {
      success: true,
      data: {
        guide,
        os: targetOS,
        generatedAt: new Date().toISOString()
      }
    };

    let responseData = { ...baseResponse.data };

    // 添加包管理器命令（如果支持）
    if (includePackageManager) {
      const packageManagerCommands = getPackageManagerCommands(targetOS);
      responseData = {
        ...responseData,
        packageManager: packageManagerCommands
      };
    }

    // 添加常见问题
    if (includeCommonIssues) {
      const commonIssues = getCommonIssues();
      responseData = {
        ...responseData,
        commonIssues
      };
    }

    return NextResponse.json({
      success: true,
      data: responseData
    });
    
  } catch (error) {
    console.error('安装指导API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取安装指导失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// POST /api/mysql-installer/installation-guide - 生成自定义安装指导
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { config, includeVerificationScript = false } = body;
    
    if (!config) {
      return NextResponse.json(
        { success: false, error: '缺少配置参数' },
        { status: 400 }
      );
    }
    
    // 验证配置中的操作系统
    const osValidation = validateOS(config.targetOS);
    if (!osValidation.isValid) {
      return NextResponse.json(
        { success: false, error: '配置中的操作系统无效' },
        { status: 400 }
      );
    }
    
    // 生成自定义安装指导
    const guide = generateInstallationGuide(config.targetOS, config);

    const baseResponse = {
      success: true,
      data: {
        guide,
        config: {
          version: config.version,
          targetOS: config.targetOS,
          installPath: config.installPath,
          dataPath: config.dataPath,
          port: config.port
        },
        generatedAt: new Date().toISOString()
      }
    };

    // 生成验证脚本（如果需要）
    if (includeVerificationScript) {
      const verificationScript = generateVerificationScript(config);
      const responseWithScript = {
        ...baseResponse,
        data: {
          ...baseResponse.data,
          verificationScript
        }
      };
      return NextResponse.json(responseWithScript);
    }

    return NextResponse.json(baseResponse);
    
  } catch (error) {
    console.error('自定义安装指导API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '生成自定义安装指导失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}


