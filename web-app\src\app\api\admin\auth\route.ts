// MySQLAi.de - 管理员认证 API
// 实现硬编码管理员认证系统

import { NextRequest, NextResponse } from 'next/server';

// 硬编码管理员凭据
const ADMIN_CREDENTIALS = {
  username: '17742495540',
  password: '123456'
};

// POST /api/admin/auth - 管理员登录
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // 验证必需字段
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: '用户名和密码不能为空' },
        { status: 400 }
      );
    }

    // 验证管理员凭据
    if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
      // 生成简单的认证令牌（基于时间戳）
      const token = Buffer.from(`${username}:${Date.now()}`).toString('base64');

      // 创建响应并设置cookie
      const response = NextResponse.json({
        success: true,
        data: {
          token,
          user: {
            username: ADMIN_CREDENTIALS.username,
            role: 'admin'
          }
        },
        message: '登录成功'
      });

      // 设置认证cookie（24小时过期）
      response.cookies.set('mysql_admin_token', token, {
        httpOnly: false, // 允许前端JavaScript访问
        secure: process.env.NODE_ENV === 'production', // 生产环境使用HTTPS
        sameSite: 'lax',
        maxAge: 24 * 60 * 60 * 1000, // 24小时
        path: '/'
      });

      return response;
    } else {
      return NextResponse.json(
        { success: false, error: '用户名或密码错误' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('管理员认证错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// GET /api/admin/auth - 验证管理员认证状态
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: '未提供认证令牌' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    
    try {
      // 解析令牌
      const decoded = Buffer.from(token, 'base64').toString();
      const [username, timestamp] = decoded.split(':');
      
      // 验证令牌格式和用户名
      if (username !== ADMIN_CREDENTIALS.username) {
        return NextResponse.json(
          { success: false, error: '无效的认证令牌' },
          { status: 401 }
        );
      }

      // 检查令牌是否过期（24小时）
      const tokenTime = parseInt(timestamp);
      const now = Date.now();
      const twentyFourHours = 24 * 60 * 60 * 1000;
      
      if (now - tokenTime > twentyFourHours) {
        return NextResponse.json(
          { success: false, error: '认证令牌已过期' },
          { status: 401 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          user: {
            username: ADMIN_CREDENTIALS.username,
            role: 'admin'
          }
        }
      });

    } catch {
      return NextResponse.json(
        { success: false, error: '无效的认证令牌格式' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('认证验证错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/auth - 管理员登出
export async function DELETE() {
  try {
    // 对于基于sessionStorage的认证，服务端不需要特殊处理
    // 客户端会清除本地存储的令牌
    return NextResponse.json({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('登出错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
