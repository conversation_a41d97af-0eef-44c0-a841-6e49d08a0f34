// MySQLAi.de - 联系我们页面
// 展示专业的联系方式和技术支持信息，使用统一的LegalPageLayout风格
// 更新：2025-06-28

import { Metadata } from 'next';
import { PAGE_METADATA } from '@/lib/constants';
import { generatePageMetadata } from '@/app/metadata';
import ContactPageLayout from '@/components/layout/ContactPageLayout';

// 生成页面元数据
export function generateMetadata(): Metadata {
  const pageData = PAGE_METADATA.contact;
  return generatePageMetadata(
    pageData.title,
    pageData.description,
    '/contact'
  );
}

export default function ContactPage() {
  return (
    <ContactPageLayout
      title="联系我们"
      description="专业的MySQL技术支持团队，随时为您服务"
      pathname="/contact"
    />
  );
}
