'use client';

// MySQLAi.de - 知识库搜索功能Hook
// 提供智能搜索、筛选、排序和历史记录功能

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { searchApi } from '@/lib/api/knowledge';
import { useKnowledgeCache, generateCacheKey } from './useKnowledgeCache';
import type { Database } from '@/lib/database.types';

type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];

// 搜索参数类型
interface SearchParams {
  query: string;
  category?: string;
  tags?: string;
  difficulty?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 搜索结果类型
interface SearchResult {
  articles: KnowledgeArticle[];
  total: number;
  page: number;
  totalPages: number;
  query: SearchParams;
  searchTime: number;
  suggestions?: string[];
}

// 搜索历史项
interface SearchHistoryItem {
  query: string;
  timestamp: number;
  resultCount: number;
  params: SearchParams;
}

// 搜索建议类型
interface SearchSuggestion {
  text: string;
  type: 'query' | 'category' | 'tag' | 'difficulty';
  count?: number;
  highlight?: boolean;
}

// Hook配置选项
interface UseKnowledgeSearchOptions {
  // 自动搜索延迟（毫秒）
  debounceDelay?: number;
  // 最小查询长度
  minQueryLength?: number;
  // 最大历史记录数
  maxHistoryItems?: number;
  // 启用搜索建议
  enableSuggestions?: boolean;
  // 启用搜索历史
  enableHistory?: boolean;
  // 启用缓存
  enableCache?: boolean;
  // 缓存时间（毫秒）
  cacheTime?: number;
  // 调试模式
  debug?: boolean;
}

// Hook返回类型
interface UseKnowledgeSearchReturn {
  // 搜索状态
  results: SearchResult | null;
  loading: boolean;
  error: string | null;
  
  // 搜索参数
  params: SearchParams;
  setParams: (params: Partial<SearchParams>) => void;
  
  // 搜索操作
  search: (params?: Partial<SearchParams>) => Promise<void>;
  clearSearch: () => void;
  retry: () => Promise<void>;
  
  // 查询管理
  query: string;
  setQuery: (query: string) => void;
  
  // 筛选管理
  filters: {
    category: string;
    tags: string;
    difficulty: string;
  };
  setFilter: (key: string, value: string) => void;
  clearFilters: () => void;
  
  // 排序管理
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  setSorting: (sortBy: string, sortOrder?: 'asc' | 'desc') => void;
  
  // 分页管理
  page: number;
  limit: number;
  totalPages: number;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  
  // 搜索历史
  history: SearchHistoryItem[];
  addToHistory: (item: SearchHistoryItem) => void;
  removeFromHistory: (index: number) => void;
  clearHistory: () => void;
  searchFromHistory: (item: SearchHistoryItem) => void;
  
  // 搜索建议
  suggestions: SearchSuggestion[];
  getSuggestions: (query: string) => SearchSuggestion[];
  
  // 工具方法
  hasResults: boolean;
  isEmpty: boolean;
  isSearching: boolean;
  canLoadMore: boolean;
  loadMore: () => Promise<void>;
}

// 默认配置
const DEFAULT_OPTIONS: UseKnowledgeSearchOptions = {
  debounceDelay: 300,
  minQueryLength: 2,
  maxHistoryItems: 20,
  enableSuggestions: true,
  enableHistory: true,
  enableCache: true,
  cacheTime: 5 * 60 * 1000, // 5分钟
  debug: false
};

// 默认搜索参数
const DEFAULT_PARAMS: SearchParams = {
  query: '',
  page: 1,
  limit: 10,
  sortBy: 'relevance',
  sortOrder: 'desc'
};

export function useKnowledgeSearch(
  options: UseKnowledgeSearchOptions = {}
): UseKnowledgeSearchReturn {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const cache = useKnowledgeCache({ 
    defaultTTL: opts.cacheTime,
    debug: opts.debug 
  });
  
  // 状态管理
  const [results, setResults] = useState<SearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [params, setParamsState] = useState<SearchParams>(DEFAULT_PARAMS);
  const [history, setHistory] = useState<SearchHistoryItem[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);

  // 引用管理
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 调试日志
  const log = useCallback((message: string, data?: any) => {
    if (opts.debug) {
      console.log(`[useKnowledgeSearch] ${message}`, data);
    }
  }, [opts.debug]);

  // 执行搜索
  const performSearch = useCallback(async (searchParams: SearchParams): Promise<void> => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    
    // 检查最小查询长度
    if (searchParams.query.length < opts.minQueryLength!) {
      setResults(null);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);
    
    const startTime = Date.now();
    log('Starting search', searchParams);

    try {
      // 检查缓存
      const cacheKey = generateCacheKey('articles', searchParams);
      let cachedResult: SearchResult | null = null;
      
      if (opts.enableCache) {
        cachedResult = cache.get<SearchResult>(cacheKey);
        if (cachedResult) {
          setResults(cachedResult);
          setLoading(false);
          log('Search result from cache', { cacheKey });
          return;
        }
      }

      // 执行API搜索
      const response = await searchApi.search(searchParams);
      
      if (response.success && response.data) {
        const searchTime = Date.now() - startTime;
        const result: SearchResult = {
          articles: response.data,
          total: response.pagination?.total || 0,
          page: response.pagination?.page || 1,
          totalPages: response.pagination?.totalPages || 1,
          query: searchParams,
          searchTime,
          suggestions: [] // 可以从API获取建议
        };

        setResults(result);
        
        // 缓存结果
        if (opts.enableCache) {
          cache.set(cacheKey, result, opts.cacheTime);
        }
        
        // 添加到历史记录
        if (opts.enableHistory && searchParams.query.trim()) {
          const historyItem: SearchHistoryItem = {
            query: searchParams.query,
            timestamp: Date.now(),
            resultCount: result.total,
            params: searchParams
          };
          addToHistory(historyItem);
        }
        
        log('Search completed', { 
          query: searchParams.query, 
          results: result.total, 
          time: searchTime 
        });
      } else {
        throw new Error(response.error || 'Search failed');
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        log('Search aborted');
        return;
      }

      const errorMessage = error.message || 'Search failed';
      setError(errorMessage);
      log('Search error', { error: errorMessage });
    } finally {
      setLoading(false);
    }
  }, [opts.minQueryLength, opts.enableCache, opts.enableHistory, opts.cacheTime, cache, log]);

  // 防抖搜索
  const debouncedSearch = useCallback((searchParams: SearchParams) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      performSearch(searchParams);
    }, opts.debounceDelay);
  }, [performSearch, opts.debounceDelay]);

  // 搜索方法
  const search = useCallback(async (newParams: Partial<SearchParams> = {}): Promise<void> => {
    const searchParams = { ...params, ...newParams };
    setParamsState(searchParams);
    await performSearch(searchParams);
  }, [params, performSearch]);

  // 清除搜索
  const clearSearch = useCallback(() => {
    setResults(null);
    setError(null);
    setParamsState(DEFAULT_PARAMS);
    
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    log('Search cleared');
  }, [log]);

  // 重试搜索
  const retry = useCallback(async (): Promise<void> => {
    await performSearch(params);
  }, [params, performSearch]);

  // 设置参数
  const setParams = useCallback((newParams: Partial<SearchParams>) => {
    const updatedParams = { ...params, ...newParams };
    setParamsState(updatedParams);
    debouncedSearch(updatedParams);
  }, [params, debouncedSearch]);

  // 设置查询
  const setQuery = useCallback((query: string) => {
    setParams({ query, page: 1 });
  }, [setParams]);

  // 设置筛选
  const setFilter = useCallback((key: string, value: string) => {
    setParams({ [key]: value, page: 1 });
  }, [setParams]);

  // 清除筛选
  const clearFilters = useCallback(() => {
    setParams({
      category: undefined,
      tags: undefined,
      difficulty: undefined,
      page: 1
    });
  }, [setParams]);

  // 设置排序
  const setSorting = useCallback((sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') => {
    setParams({ sortBy, sortOrder, page: 1 });
  }, [setParams]);

  // 设置页码
  const setPage = useCallback((page: number) => {
    setParams({ page });
  }, [setParams]);

  // 设置页面大小
  const setLimit = useCallback((limit: number) => {
    setParams({ limit, page: 1 });
  }, [setParams]);

  // 添加到历史记录
  const addToHistory = useCallback((item: SearchHistoryItem) => {
    setHistory(prev => {
      // 去重
      const filtered = prev.filter(h => h.query !== item.query);
      // 添加到开头
      const newHistory = [item, ...filtered];
      // 限制数量
      return newHistory.slice(0, opts.maxHistoryItems);
    });
  }, [opts.maxHistoryItems]);

  // 从历史记录中删除
  const removeFromHistory = useCallback((index: number) => {
    setHistory(prev => prev.filter((_, i) => i !== index));
  }, []);

  // 清除历史记录
  const clearHistory = useCallback(() => {
    setHistory([]);
    log('Search history cleared');
  }, [log]);

  // 从历史记录搜索
  const searchFromHistory = useCallback((item: SearchHistoryItem) => {
    setParamsState(item.params);
    performSearch(item.params);
  }, [performSearch]);

  // 获取搜索建议
  const getSuggestions = useCallback((query: string): SearchSuggestion[] => {
    if (!opts.enableSuggestions || query.length < 2) {
      return [];
    }

    const suggestions: SearchSuggestion[] = [];
    
    // 从历史记录生成建议
    history.forEach(item => {
      if (item.query.toLowerCase().includes(query.toLowerCase())) {
        suggestions.push({
          text: item.query,
          type: 'query',
          count: item.resultCount
        });
      }
    });

    // 可以添加更多建议来源（分类、标签等）
    
    return suggestions.slice(0, 5); // 限制建议数量
  }, [opts.enableSuggestions, history]);

  // 加载更多结果
  const loadMore = useCallback(async (): Promise<void> => {
    if (!results || loading || (params.page || 1) >= results.totalPages) {
      return;
    }

    const nextPage = (params.page || 1) + 1;
    const searchParams = { ...params, page: nextPage };
    
    setLoading(true);
    
    try {
      const response = await searchApi.search(searchParams);
      
      if (response.success && response.data) {
        setResults(prev => prev ? {
          ...prev,
          articles: [...prev.articles, ...response.data!],
          page: nextPage
        } : null);
        
        setParamsState(searchParams);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to load more results');
    } finally {
      setLoading(false);
    }
  }, [results, loading, params]);

  // 计算属性
  const filters = useMemo(() => ({
    category: params.category || '',
    tags: params.tags || '',
    difficulty: params.difficulty || ''
  }), [params]);

  const hasResults = useMemo(() => {
    return results !== null && results.articles.length > 0;
  }, [results]);

  const isEmpty = useMemo(() => {
    return results !== null && results.articles.length === 0;
  }, [results]);

  const isSearching = useMemo(() => {
    return loading && params.query.length >= opts.minQueryLength!;
  }, [loading, params.query, opts.minQueryLength]);

  const canLoadMore = useMemo(() => {
    return results !== null && (params.page || 1) < results.totalPages && !loading;
  }, [results, params.page, loading]);

  // 更新搜索建议
  useEffect(() => {
    if (opts.enableSuggestions) {
      const newSuggestions = getSuggestions(params.query);
      setSuggestions(newSuggestions);
    }
  }, [params.query, opts.enableSuggestions, getSuggestions]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    // 搜索状态
    results,
    loading,
    error,
    
    // 搜索参数
    params,
    setParams,
    
    // 搜索操作
    search,
    clearSearch,
    retry,
    
    // 查询管理
    query: params.query,
    setQuery,
    
    // 筛选管理
    filters,
    setFilter,
    clearFilters,
    
    // 排序管理
    sortBy: params.sortBy || 'relevance',
    sortOrder: params.sortOrder || 'desc',
    setSorting,
    
    // 分页管理
    page: params.page || 1,
    limit: params.limit || 10,
    totalPages: results?.totalPages || 1,
    setPage,
    setLimit,
    
    // 搜索历史
    history,
    addToHistory,
    removeFromHistory,
    clearHistory,
    searchFromHistory,
    
    // 搜索建议
    suggestions,
    getSuggestions,
    
    // 工具方法
    hasResults,
    isEmpty,
    isSearching,
    canLoadMore,
    loadMore
  };
}
