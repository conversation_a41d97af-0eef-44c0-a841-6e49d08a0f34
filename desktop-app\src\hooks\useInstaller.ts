import { useState, useEffect, useCallback } from 'react';
import { createTauriAPI } from '../lib/tauri-mock';

// MySQL安装器相关类型定义
export interface MySQLComponent {
  componentType: MySQLComponentType;
  location: string;
  description: string;
  uninstallString?: string;
}

export enum MySQLComponentType {
  Directory = 'Directory',
  SpecialDirectory = 'SpecialDirectory',
  Service = 'Service',
  UninstallRegistry = 'UninstallRegistry',
  EnvironmentVariable = 'EnvironmentVariable',
  Process = 'Process',
}

export interface InstallationProgress {
  currentStep: string;
  progressPercentage: number;
  status: InstallationStatus;
  message: string;
  estimatedTimeRemaining?: number;
}

export enum InstallationStatus {
  Pending = 'Pending',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
}

export interface MySQLConfig {
  installPath: string;
  dataPath: string;
  port: number;
  rootPassword: string;
  characterSet: string;
  sqlMode: string;
  maxConnections: number;
  innodbBufferPoolSize: string;
}

export interface UseInstallerReturn {
  // 状态
  mysqlComponents: MySQLComponent[];
  mysqlConfig: MySQLConfig | null;
  installationProgress: InstallationProgress | null;
  isDetecting: boolean;
  isUninstalling: boolean;
  isInstalling: boolean;
  isVerifying: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 方法
  detectMySQLComponents: () => Promise<void>;
  uninstallOldMySQL: (components: MySQLComponent[]) => Promise<void>;
  installMySQL: (zipFilePath: string) => Promise<void>;
  verifyInstallation: () => Promise<boolean>;
  setMySQLConfig: (config: MySQLConfig) => Promise<void>;
  getMySQLConfig: () => Promise<void>;
  refreshAll: () => Promise<void>;
}

/**
 * MySQL安装器管理Hook
 * 提供MySQL检测、卸载、安装、配置等功能
 */
export function useInstaller(): UseInstallerReturn {
  // 状态管理
  const [mysqlComponents, setMysqlComponents] = useState<MySQLComponent[]>([]);
  const [mysqlConfig, setMysqlConfigState] = useState<MySQLConfig | null>(null);
  const [installationProgress, setInstallationProgress] = useState<InstallationProgress | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [isUninstalling, setIsUninstalling] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 检测MySQL组件
  const detectMySQLComponents = useCallback(async () => {
    try {
      setError(null);
      setIsDetecting(true);

      const api = await createTauriAPI();
      const components = await api.detect_mysql_components();
      setMysqlComponents(components);

      console.log('MySQL components detected:', components);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '检测MySQL组件失败';
      setError(errorMessage);
      console.error('Failed to detect MySQL components:', err);
    } finally {
      setIsDetecting(false);
    }
  }, []);

  // 卸载旧版本MySQL
  const uninstallOldMySQL = useCallback(async (components: MySQLComponent[]) => {
    try {
      setError(null);
      setIsUninstalling(true);
      setInstallationProgress({
        currentStep: '卸载旧版本',
        progressPercentage: 0,
        status: InstallationStatus.InProgress,
        message: '正在卸载旧版本MySQL...',
      });

      const api = await createTauriAPI();
      await api.uninstall_old_mysql(components);
      
      setInstallationProgress({
        currentStep: '卸载完成',
        progressPercentage: 100,
        status: InstallationStatus.Completed,
        message: '旧版本MySQL卸载完成',
      });

      // 重新检测组件
      await detectMySQLComponents();
      
      console.log('Old MySQL uninstalled successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '卸载MySQL失败';
      setError(errorMessage);
      setInstallationProgress({
        currentStep: '卸载失败',
        progressPercentage: 0,
        status: InstallationStatus.Failed,
        message: errorMessage,
      });
      console.error('Failed to uninstall old MySQL:', err);
    } finally {
      setIsUninstalling(false);
    }
  }, [detectMySQLComponents]);

  // 安装MySQL
  const installMySQL = useCallback(async (zipFilePath: string) => {
    try {
      setError(null);
      setIsInstalling(true);
      setInstallationProgress({
        currentStep: '开始安装',
        progressPercentage: 0,
        status: InstallationStatus.InProgress,
        message: '正在准备MySQL安装...',
      });

      const api = await createTauriAPI();
      await api.install_mysql(zipFilePath);
      
      setInstallationProgress({
        currentStep: '安装完成',
        progressPercentage: 100,
        status: InstallationStatus.Completed,
        message: 'MySQL安装成功完成！',
      });

      console.log('MySQL installed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '安装MySQL失败';
      setError(errorMessage);
      setInstallationProgress({
        currentStep: '安装失败',
        progressPercentage: 0,
        status: InstallationStatus.Failed,
        message: errorMessage,
      });
      console.error('Failed to install MySQL:', err);
    } finally {
      setIsInstalling(false);
    }
  }, []);

  // 验证安装
  const verifyInstallation = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      setIsVerifying(true);
      
      const api = await createTauriAPI();
      const isValid = await api.verify_mysql_installation();
      
      console.log('MySQL installation verification:', isValid);
      return isValid;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证MySQL安装失败';
      setError(errorMessage);
      console.error('Failed to verify MySQL installation:', err);
      return false;
    } finally {
      setIsVerifying(false);
    }
  }, []);

  // 设置MySQL配置
  const setMySQLConfig = useCallback(async (config: MySQLConfig) => {
    try {
      setError(null);
      
      const api = await createTauriAPI();
      await api.set_mysql_config(config);
      setMysqlConfigState(config);
      
      console.log('MySQL config set:', config);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '设置MySQL配置失败';
      setError(errorMessage);
      console.error('Failed to set MySQL config:', err);
    }
  }, []);

  // 获取MySQL配置
  const getMySQLConfig = useCallback(async () => {
    try {
      setError(null);
      
      const api = await createTauriAPI();
      const config = await api.get_mysql_config();
      setMysqlConfigState(config);
      
      console.log('MySQL config retrieved:', config);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取MySQL配置失败';
      setError(errorMessage);
      console.error('Failed to get MySQL config:', err);
    }
  }, []);

  // 刷新所有信息
  const refreshAll = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        detectMySQLComponents(),
        getMySQLConfig(),
      ]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刷新信息失败';
      setError(errorMessage);
      console.error('Failed to refresh all info:', err);
    } finally {
      setIsLoading(false);
    }
  }, [detectMySQLComponents, getMySQLConfig]);

  // 组件挂载时自动获取信息
  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  return {
    // 状态
    mysqlComponents,
    mysqlConfig,
    installationProgress,
    isDetecting,
    isUninstalling,
    isInstalling,
    isVerifying,
    isLoading,
    error,
    
    // 方法
    detectMySQLComponents,
    uninstallOldMySQL,
    installMySQL,
    verifyInstallation,
    setMySQLConfig,
    getMySQLConfig,
    refreshAll,
  };
}

/**
 * 获取组件类型显示名称
 */
export function getComponentTypeDisplayName(type: MySQLComponentType): string {
  switch (type) {
    case MySQLComponentType.Directory:
      return '目录';
    case MySQLComponentType.SpecialDirectory:
      return '特殊目录';
    case MySQLComponentType.Service:
      return '服务';
    case MySQLComponentType.UninstallRegistry:
      return '卸载注册表';
    case MySQLComponentType.EnvironmentVariable:
      return '环境变量';
    case MySQLComponentType.Process:
      return '进程';
    default:
      return '未知';
  }
}

/**
 * 获取安装状态显示文本
 */
export function getInstallationStatusText(status: InstallationStatus): string {
  switch (status) {
    case InstallationStatus.Pending:
      return '等待中';
    case InstallationStatus.InProgress:
      return '进行中';
    case InstallationStatus.Completed:
      return '已完成';
    case InstallationStatus.Failed:
      return '失败';
    case InstallationStatus.Cancelled:
      return '已取消';
    default:
      return '未知状态';
  }
}

/**
 * 获取安装状态颜色类
 */
export function getInstallationStatusColor(status: InstallationStatus): string {
  switch (status) {
    case InstallationStatus.Pending:
      return 'text-gray-600';
    case InstallationStatus.InProgress:
      return 'text-blue-600';
    case InstallationStatus.Completed:
      return 'text-green-600';
    case InstallationStatus.Failed:
      return 'text-red-600';
    case InstallationStatus.Cancelled:
      return 'text-gray-600';
    default:
      return 'text-gray-600';
  }
}

/**
 * 获取组件类型颜色类
 */
export function getComponentTypeColor(type: MySQLComponentType): string {
  switch (type) {
    case MySQLComponentType.Directory:
      return 'text-blue-600';
    case MySQLComponentType.SpecialDirectory:
      return 'text-purple-600';
    case MySQLComponentType.Service:
      return 'text-green-600';
    case MySQLComponentType.UninstallRegistry:
      return 'text-orange-600';
    case MySQLComponentType.EnvironmentVariable:
      return 'text-yellow-600';
    case MySQLComponentType.Process:
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
}

/**
 * 创建默认MySQL配置
 */
export function createDefaultMySQLConfig(): MySQLConfig {
  return {
    installPath: 'C:\\MySQL',
    dataPath: 'C:\\MySQL\\data',
    port: 3306,
    rootPassword: '123456',
    characterSet: 'utf8mb4',
    sqlMode: 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
    maxConnections: 151,
    innodbBufferPoolSize: '128M',
  };
}