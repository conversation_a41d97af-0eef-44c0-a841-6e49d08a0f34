/**
 * 页面路由组件
 * 根据当前页面状态渲染对应的页面组件
 * 注意：激活功能已集成到ConfigPanel中，不再需要独立的ActivationPanel
 */

// import React from 'react';
import { useCurrentPage } from '../store/appStore';
import { DownloadPanel } from './DownloadPanel';
import { InstallerPanel } from './InstallerPanel';
import { SystemInfoPanel } from './SystemInfoPanel';
import SettingsPanel from './SettingsPanel';
import ConfigPanel from './panels/ConfigPanel';

export default function PageRouter() {
  const currentPage = useCurrentPage();

  const renderPage = () => {
    switch (currentPage) {
      case 'activation':
        // 激活功能已集成到ConfigPanel中，重定向到配置页面
        return <ConfigPanel />;

      case 'download':
        return <DownloadPanel className="h-full" />;

      case 'installer':
        return <InstallerPanel className="h-full" />;

      case 'system':
        return <SystemInfoPanel className="h-full" />;

      case 'settings':
        return <SettingsPanel className="h-full" />;

      default:
        // 默认显示配置页面（包含激活功能）
        return <ConfigPanel />;
    }
  };

  return (
    <div className="h-full">
      {renderPage()}
    </div>
  );
}