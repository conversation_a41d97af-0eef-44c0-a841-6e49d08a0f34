/**
 * MySQL一键安装工具的默认配置和常量定义
 * 包含各操作系统的默认安装路径、配置参数和模板
 */

import {
  SupportedOS,
  MySQLVersion,
  DefaultTemplate,
  QuickInstallConfig
} from '../types/mysql-installer';

// ===== 默认安装路径配置 =====

/**
 * 各系统默认安装路径
 */
export const DEFAULT_INSTALL_PATHS: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'C:\\MySQL\\8.0',
  [SupportedOS.MACOS]: '/usr/local/mysql',
  [SupportedOS.LINUX]: '/opt/mysql',
  [SupportedOS.UNKNOWN]: '/opt/mysql'
};

/**
 * 各系统默认数据目录路径
 */
export const DEFAULT_DATA_PATHS: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'C:\\MySQL\\8.0\\data',
  [SupportedOS.MACOS]: '/usr/local/mysql/data',
  [SupportedOS.LINUX]: '/var/lib/mysql',
  [SupportedOS.UNKNOWN]: '/var/lib/mysql'
};

/**
 * 各系统配置文件名
 */
export const CONFIG_FILE_NAMES: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'my.ini',
  [SupportedOS.MACOS]: 'my.cnf',
  [SupportedOS.LINUX]: 'my.cnf',
  [SupportedOS.UNKNOWN]: 'my.cnf'
};

/**
 * 各系统服务名称
 */
export const SERVICE_NAMES: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'MySQL80',
  [SupportedOS.MACOS]: 'com.mysql.mysqld',
  [SupportedOS.LINUX]: 'mysqld',
  [SupportedOS.UNKNOWN]: 'mysqld'
};

// ===== 默认MySQL配置参数 =====

/**
 * 默认MySQL配置
 */
export const DEFAULT_MYSQL_CONFIG = {
  /** 默认端口 */
  PORT: 3306,
  /** 默认root密码 */
  ROOT_PASSWORD: '123456',
  /** 默认字符集 */
  CHARSET: 'utf8mb4',
  /** 默认排序规则 */
  COLLATION: 'utf8mb4_0900_ai_ci',
  /** 默认存储引擎 */
  STORAGE_ENGINE: 'InnoDB',
  /** 默认最大连接数 */
  MAX_CONNECTIONS: 200,
  /** 默认InnoDB缓冲池大小 */
  INNODB_BUFFER_POOL_SIZE: '256M',
  /** 默认查询缓存大小 */
  QUERY_CACHE_SIZE: '64M',
  /** 默认慢查询时间阈值（秒） */
  LONG_QUERY_TIME: 2
} as const;

/**
 * 支持的MySQL版本列表
 */
export const SUPPORTED_MYSQL_VERSIONS = [
  MySQLVersion.V8_0_36,
  MySQLVersion.V8_0_28
] as const;

/**
 * 默认MySQL版本
 */
export const DEFAULT_MYSQL_VERSION = MySQLVersion.V8_0_36;

// ===== 系统模板配置 =====

/**
 * 各系统的默认安装模板
 */
export const DEFAULT_TEMPLATES: Record<SupportedOS, DefaultTemplate> = {
  [SupportedOS.WINDOWS]: {
    os: SupportedOS.WINDOWS,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.WINDOWS],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.WINDOWS],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.WINDOWS],
    serviceName: SERVICE_NAMES[SupportedOS.WINDOWS]
  },
  [SupportedOS.MACOS]: {
    os: SupportedOS.MACOS,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.MACOS],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.MACOS],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.MACOS],
    serviceName: SERVICE_NAMES[SupportedOS.MACOS]
  },
  [SupportedOS.LINUX]: {
    os: SupportedOS.LINUX,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.LINUX],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.LINUX],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.LINUX],
    serviceName: SERVICE_NAMES[SupportedOS.LINUX]
  },
  [SupportedOS.UNKNOWN]: {
    os: SupportedOS.UNKNOWN,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.UNKNOWN],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.UNKNOWN],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.UNKNOWN]
  }
};

// ===== 一键安装默认配置生成器 =====

/**
 * 生成指定操作系统的默认一键安装配置
 */
export function createDefaultQuickInstallConfig(os: SupportedOS): QuickInstallConfig {
  const template = DEFAULT_TEMPLATES[os];
  
  return {
    version: DEFAULT_MYSQL_VERSION,
    targetOS: os,
    installPath: template.defaultInstallPath,
    dataPath: template.defaultDataPath,
    port: template.defaultPort,
    rootPassword: DEFAULT_MYSQL_CONFIG.ROOT_PASSWORD,
    charset: template.defaultCharset,
    collation: template.defaultCollation,
    enableBinLog: false, // 默认不启用二进制日志
    createService: os === SupportedOS.WINDOWS // 仅Windows默认创建服务
  };
}

// ===== 系统要求配置 =====

/**
 * 各系统的最低要求
 */
export const SYSTEM_REQUIREMENTS = {
  [SupportedOS.WINDOWS]: {
    minVersion: 'Windows 10',
    minRAM: '4GB',
    minDisk: '2GB',
    requiredSoftware: ['.NET Framework 4.7.2+', 'Visual C++ Redistributable']
  },
  [SupportedOS.MACOS]: {
    minVersion: 'macOS 10.15',
    minRAM: '4GB',
    minDisk: '2GB',
    requiredSoftware: ['Xcode Command Line Tools']
  },
  [SupportedOS.LINUX]: {
    minVersion: 'Ubuntu 18.04+ / CentOS 7+ / Debian 9+',
    minRAM: '2GB',
    minDisk: '2GB',
    requiredSoftware: ['glibc 2.17+', 'libssl']
  }
} as const;

// ===== 下载源配置 =====

/**
 * MySQL官方下载源
 */
export const OFFICIAL_DOWNLOAD_SOURCES = {
  MYSQL_OFFICIAL: 'https://dev.mysql.com/downloads/mysql/',
  MYSQL_ARCHIVES: 'https://downloads.mysql.com/archives/',
  MYSQL_CDN: 'https://cdn.mysql.com/Downloads/'
} as const;

/**
 * 国内镜像源
 */
export const CHINA_MIRROR_SOURCES = {
  ALIYUN: 'https://mirrors.aliyun.com/mysql/',
  HUAWEI: 'https://mirrors.huaweicloud.com/mysql/',
  TSINGHUA: 'https://mirrors.tuna.tsinghua.edu.cn/mysql/',
  USTC: 'https://mirrors.ustc.edu.cn/mysql-ftp/'
} as const;

// ===== 安装步骤模板 =====

/**
 * 通用安装步骤ID
 */
export const INSTALLATION_STEP_IDS = {
  DOWNLOAD: 'download',
  EXTRACT: 'extract',
  CONFIGURE: 'configure',
  INITIALIZE: 'initialize',
  START_SERVICE: 'start_service',
  VERIFY: 'verify',
  SECURE: 'secure'
} as const;

/**
 * 默认安装步骤预计时间（分钟）
 */
export const ESTIMATED_TIMES = {
  [INSTALLATION_STEP_IDS.DOWNLOAD]: 5,
  [INSTALLATION_STEP_IDS.EXTRACT]: 2,
  [INSTALLATION_STEP_IDS.CONFIGURE]: 3,
  [INSTALLATION_STEP_IDS.INITIALIZE]: 5,
  [INSTALLATION_STEP_IDS.START_SERVICE]: 2,
  [INSTALLATION_STEP_IDS.VERIFY]: 2,
  [INSTALLATION_STEP_IDS.SECURE]: 3
} as const;

// ===== 错误消息常量 =====

/**
 * 常见错误消息
 */
export const ERROR_MESSAGES = {
  UNSUPPORTED_OS: '不支持的操作系统',
  INSUFFICIENT_PERMISSIONS: '权限不足，请以管理员身份运行',
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  DOWNLOAD_FAILED: '下载失败，请重试或选择其他下载源',
  CONFIG_GENERATION_FAILED: '配置文件生成失败',
  INVALID_PATH: '无效的安装路径',
  PORT_IN_USE: '端口已被占用，请选择其他端口',
  DISK_SPACE_INSUFFICIENT: '磁盘空间不足'
} as const;

// ===== 成功消息常量 =====

/**
 * 成功消息
 */
export const SUCCESS_MESSAGES = {
  SYSTEM_DETECTED: '系统检测完成',
  CONFIG_GENERATED: '配置文件生成成功',
  DOWNLOAD_READY: '下载准备就绪',
  INSTALLATION_COMPLETE: 'MySQL安装完成'
} as const;