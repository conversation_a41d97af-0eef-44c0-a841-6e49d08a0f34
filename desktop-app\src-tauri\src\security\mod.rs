/**
 * 安全防护模块
 * 提供反调试、完整性检查、运行时保护等安全功能
 */

pub mod anti_debug;
pub mod integrity;
pub mod obfuscation;
pub mod protection;

// 重新导出主要功能
pub use anti_debug::{
    check_debugger_presence, 
    check_execution_timing, 
    check_virtual_environment,
    AntiDebugResult
};

pub use integrity::{
    verify_binary_integrity,
    verify_config_integrity,
    calculate_file_hash,
    IntegrityResult
};

pub use obfuscation::{
    obfuscate_string,
    deobfuscate_string,
    protect_sensitive_data
};

pub use protection::{
    initialize_runtime_protection,
    check_runtime_security,
    SecurityLevel,
    ProtectionStatus
};

/// 安全检查结果
#[derive(Debug, <PERSON>lone)]
pub struct SecurityCheckResult {
    pub is_secure: bool,
    pub threat_level: ThreatLevel,
    pub details: Vec<String>,
    pub recommendations: Vec<String>,
}

/// 威胁级别
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum ThreatLevel {
    None,
    Low,
    Medium,
    High,
    Critical,
}

/// 执行全面安全检查
pub async fn perform_security_check() -> SecurityCheckResult {
    let mut details = Vec::new();
    let mut recommendations = Vec::new();
    let mut threat_level = ThreatLevel::None;

    // 反调试检查
    match check_debugger_presence() {
        AntiDebugResult::Safe => {
            details.push("反调试检查：未检测到调试器".to_string());
        }
        AntiDebugResult::DebuggerDetected => {
            details.push("反调试检查：检测到调试器存在".to_string());
            recommendations.push("应用可能正在被调试，建议终止运行".to_string());
            threat_level = ThreatLevel::High;
        }
        AntiDebugResult::Suspicious => {
            details.push("反调试检查：检测到可疑活动".to_string());
            recommendations.push("检测到可疑的调试活动".to_string());
            threat_level = std::cmp::max(threat_level, ThreatLevel::Medium);
        }
    }

    // 执行时间检查
    match check_execution_timing().await {
        AntiDebugResult::Safe => {
            details.push("执行时间检查：正常".to_string());
        }
        AntiDebugResult::Suspicious => {
            details.push("执行时间检查：检测到异常延迟".to_string());
            recommendations.push("执行时间异常，可能存在调试或分析行为".to_string());
            threat_level = std::cmp::max(threat_level, ThreatLevel::Medium);
        }
        _ => {}
    }

    // 虚拟环境检查
    match check_virtual_environment() {
        AntiDebugResult::Safe => {
            details.push("环境检查：真实环境".to_string());
        }
        AntiDebugResult::Suspicious => {
            details.push("环境检查：检测到虚拟环境".to_string());
            recommendations.push("在虚拟环境中运行，可能用于分析目的".to_string());
            threat_level = std::cmp::max(threat_level, ThreatLevel::Low);
        }
        _ => {}
    }

    // 完整性检查
    match verify_binary_integrity().await {
        IntegrityResult::Valid => {
            details.push("完整性检查：二进制文件完整".to_string());
        }
        IntegrityResult::Invalid => {
            details.push("完整性检查：二进制文件已被修改".to_string());
            recommendations.push("二进制文件完整性验证失败，可能已被篡改".to_string());
            threat_level = ThreatLevel::Critical;
        }
        IntegrityResult::Unknown => {
            details.push("完整性检查：无法验证".to_string());
            recommendations.push("无法验证二进制文件完整性".to_string());
            threat_level = std::cmp::max(threat_level, ThreatLevel::Low);
        }
    }

    let is_secure = threat_level == ThreatLevel::None || threat_level == ThreatLevel::Low;

    SecurityCheckResult {
        is_secure,
        threat_level,
        details,
        recommendations,
    }
}

/// 应用安全策略
pub fn apply_security_policy(result: &SecurityCheckResult) -> bool {
    match result.threat_level {
        ThreatLevel::Critical => {
            log::error!("检测到严重安全威胁，应用将退出");
            false
        }
        ThreatLevel::High => {
            log::warn!("检测到高级安全威胁，限制功能");
            // 可以选择限制某些功能而不是完全退出
            true
        }
        ThreatLevel::Medium => {
            log::warn!("检测到中等安全威胁，增强监控");
            true
        }
        ThreatLevel::Low => {
            log::info!("检测到低级安全威胁，继续监控");
            true
        }
        ThreatLevel::None => {
            log::info!("安全检查通过");
            true
        }
    }
}