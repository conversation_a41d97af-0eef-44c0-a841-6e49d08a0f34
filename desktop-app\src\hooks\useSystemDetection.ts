import { useState, useEffect, useCallback } from 'react';
import { createTauriAPI } from '../lib/tauri-mock';
import type {
  SystemInfo
} from '../types';

// 临时定义MachineFeatures类型，直到共享类型完全集成
export interface MachineFeatures {
  machine_id: string;
  cpu_info?: string;
  cpu_name?: string;
  motherboard_info?: string;
  mac_addresses?: string;
  platform: string;
  machine_arch: string;
  hostname: string;
  bios_info?: string;
  disk_serial?: string;
  system_version: string;
}

export interface SystemPerformanceInfo {
  cpu_count: number;
  total_memory: number;
  available_memory: number;
  cpu_usage: number;
  disk_usage: Array<{
    name: string;
    mount_point: string;
    total_space: number;
    available_space: number;
    used_space: number;
    usage_percent: number;
  }>;
}

export interface UseSystemDetectionReturn {
  // 系统信息
  systemInfo: SystemInfo | null;
  machineFeatures: MachineFeatures | null;
  systemPerformance: SystemPerformanceInfo | null;
  machineFingerprint: string | null;
  featuresummary: Record<string, string> | null;
  
  // 状态
  loading: boolean;
  error: string | null;
  
  // 方法
  refreshSystemInfo: () => Promise<void>;
  generateFingerprint: () => Promise<void>;
  getMachineFeatures: () => Promise<void>;
  getSystemPerformance: () => Promise<void>;
  getFeaturesummary: () => Promise<void>;
  refreshAll: () => Promise<void>;
}

/**
 * 系统检测和机器指纹Hook
 * 提供系统信息检测、机器指纹生成等功能
 */
export function useSystemDetection(): UseSystemDetectionReturn {
  // 状态管理
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [machineFeatures, setMachineFeatures] = useState<MachineFeatures | null>(null);
  const [systemPerformance, setSystemPerformance] = useState<SystemPerformanceInfo | null>(null);
  const [machineFingerprint, setMachineFingerprint] = useState<string | null>(null);
  const [featuresummary, setFeaturesummary] = useState<Record<string, string> | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 刷新系统信息
  const refreshSystemInfo = useCallback(async () => {
    try {
      setError(null);
      const result = await invoke<SystemInfo>('get_system_info');
      setSystemInfo(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取系统信息失败';
      setError(errorMessage);
      console.error('Failed to get system info:', err);
    }
  }, []);

  // 生成机器指纹
  const generateFingerprint = useCallback(async () => {
    try {
      setError(null);
      const result = await invoke<string>('generate_fingerprint');
      setMachineFingerprint(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '生成机器指纹失败';
      setError(errorMessage);
      console.error('Failed to generate fingerprint:', err);
    }
  }, []);

  // 获取机器特征
  const getMachineFeatures = useCallback(async () => {
    try {
      setError(null);
      const result = await invoke<MachineFeatures>('get_machine_features');
      setMachineFeatures(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取机器特征失败';
      setError(errorMessage);
      console.error('Failed to get machine features:', err);
    }
  }, []);

  // 获取系统性能信息
  const getSystemPerformance = useCallback(async () => {
    try {
      setError(null);
      const result = await invoke<SystemPerformanceInfo>('get_system_performance');
      setSystemPerformance(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取系统性能信息失败';
      setError(errorMessage);
      console.error('Failed to get system performance:', err);
    }
  }, []);

  // 获取特征摘要
  const getFeaturesummary = useCallback(async () => {
    try {
      setError(null);
      const result = await invoke<Record<string, string>>('get_features_summary');
      setFeaturesummary(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取特征摘要失败';
      setError(errorMessage);
      console.error('Failed to get features summary:', err);
    }
  }, []);

  // 刷新所有信息
  const refreshAll = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        refreshSystemInfo(),
        generateFingerprint(),
        getMachineFeatures(),
        getSystemPerformance(),
        getFeaturesummary(),
      ]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刷新系统信息失败';
      setError(errorMessage);
      console.error('Failed to refresh all system info:', err);
    } finally {
      setLoading(false);
    }
  }, [refreshSystemInfo, generateFingerprint, getMachineFeatures, getSystemPerformance, getFeaturesummary]);

  // 组件挂载时自动获取系统信息
  useEffect(() => {
    refreshAll();
  }, []);

  return {
    // 数据
    systemInfo,
    machineFeatures,
    systemPerformance,
    machineFingerprint,
    featuresummary,
    
    // 状态
    loading,
    error,
    
    // 方法
    refreshSystemInfo,
    generateFingerprint,
    getMachineFeatures,
    getSystemPerformance,
    getFeaturesummary,
    refreshAll,
  };
}

/**
 * 格式化内存大小
 */
export function formatMemorySize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * 格式化磁盘使用百分比
 */
export function formatDiskUsage(usagePercent: number): string {
  return `${usagePercent.toFixed(1)}%`;
}

/**
 * 获取操作系统显示名称
 */
export function getOSDisplayName(os: string): string {
  switch (os.toLowerCase()) {
    case 'windows':
      return 'Windows';
    case 'macos':
      return 'macOS';
    case 'linux':
      return 'Linux';
    default:
      return '未知系统';
  }
}

/**
 * 获取架构显示名称
 */
export function getArchDisplayName(arch: string): string {
  switch (arch.toLowerCase()) {
    case 'x64':
      return '64位';
    case 'x86':
      return '32位';
    case 'arm64':
      return 'ARM64';
    default:
      return arch;
  }
}

/**
 * 检查系统要求是否全部满足
 */
export function areAllRequirementsSatisfied(systemInfo: SystemInfo | null): boolean {
  if (!systemInfo || !systemInfo.requirements) {
    return false;
  }
  
  return systemInfo.requirements.every(req => req.satisfied);
}

/**
 * 获取未满足的系统要求
 */
export function getUnsatisfiedRequirements(systemInfo: SystemInfo | null) {
  if (!systemInfo || !systemInfo.requirements) {
    return [];
  }
  
  return systemInfo.requirements.filter(req => !req.satisfied);
}