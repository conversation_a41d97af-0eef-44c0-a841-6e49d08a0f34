/**
 * 安装面板组件
 * 右侧面板，包含一键安装按钮、进度显示和日志输出
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Play, Download, Settings, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import {
  useInstallationState,
  useActivationState,
  useAppStore,
  useAppConfig
} from '../../store/appStore';
import { useInstaller } from '../../hooks/useInstaller';
import { invoke } from '@tauri-apps/api/tauri';

export default function InstallPanel() {
  const installation = useInstallationState();
  const activation = useActivationState();
  const config = useAppConfig();
  const {
    setInstallationStatus,
    setInstallationProgress,
    addInstallationLog,
    clearInstallationLogs
  } = useAppStore();

  // 集成安装器
  const {
    installMySQL,
    verifyInstallation,
    isInstalling: realIsInstalling,
    installationProgress: realProgress,
    error: installError
  } = useInstaller();

  // 安装状态检测
  const [isInstalled, setIsInstalled] = useState(false);
  const [isCheckingInstallation, setIsCheckingInstallation] = useState(false);

  // 安装状态检测函数
  const checkInstallationStatus = useCallback(async () => {
    setIsCheckingInstallation(true);
    try {
      const installed = await verifyInstallation();
      setIsInstalled(installed);
      console.log('MySQL installation status:', installed);
    } catch (error) {
      console.error('Failed to check installation status:', error);
      setIsInstalled(false);
    } finally {
      setIsCheckingInstallation(false);
    }
  }, [verifyInstallation]);

  // 同步真实安装状态到UI
  useEffect(() => {
    if (realProgress) {
      setInstallationProgress(
        realProgress.currentStep,
        realProgress.progressPercentage,
        realProgress.message
      );

      // 同步安装状态
      switch (realProgress.status) {
        case 'InProgress':
          if (realProgress.progressPercentage < 50) {
            setInstallationStatus('downloading');
          } else {
            setInstallationStatus('installing');
          }
          break;
        case 'Completed':
          setInstallationStatus('completed');
          break;
        case 'Failed':
          setInstallationStatus('failed');
          break;
      }
    }
  }, [realProgress, setInstallationProgress, setInstallationStatus]);

  // 同步错误状态
  useEffect(() => {
    if (installError) {
      addInstallationLog('error', installError);
    }
  }, [installError, addInstallationLog]);

  const logContainerRef = useRef<HTMLDivElement>(null);

  // 自动滚动到日志底部
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [installation.logs]);

  // 组件挂载时检测安装状态
  useEffect(() => {
    checkInstallationStatus();
  }, [checkInstallationStatus]);

  // 获取安装状态显示
  const getInstallationStatusDisplay = () => {
    switch (installation.status) {
      case 'downloading':
        return {
          icon: <Download className="w-5 h-5 text-blue-500 animate-pulse" />,
          text: '下载中...',
          color: 'text-blue-600'
        };
      case 'installing':
        return {
          icon: <Settings className="w-5 h-5 text-yellow-500 animate-spin" />,
          text: '安装中...',
          color: 'text-yellow-600'
        };
      case 'completed':
        return {
          icon: <CheckCircle className="w-5 h-5 text-green-500" />,
          text: '安装完成',
          color: 'text-green-600'
        };
      case 'failed':
        return {
          icon: <XCircle className="w-5 h-5 text-red-500" />,
          text: '安装失败',
          color: 'text-red-600'
        };
      default:
        return {
          icon: <Play className="w-5 h-5 text-gray-500" />,
          text: '准备安装',
          color: 'text-gray-600'
        };
    }
  };

  // 获取日志级别样式
  const getLogLevelStyle = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600';
      case 'warn':
        return 'text-yellow-600';
      case 'success':
        return 'text-green-600';
      default:
        return 'text-gray-700';
    }
  };

  // 获取日志级别图标
  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <XCircle className="w-3 h-3 text-red-500" />;
      case 'warn':
        return <AlertCircle className="w-3 h-3 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      default:
        return <div className="w-3 h-3 rounded-full bg-blue-500" />;
    }
  };

  // MySQL安装处理
  const handleInstall = async () => {
    // 检查激活状态
    if (activation.status !== 'activated') {
      addInstallationLog('error', '请先激活产品后再进行安装');
      return;
    }

    try {
      setInstallationStatus('downloading');
      clearInstallationLogs();

      addInstallationLog('info', '开始MySQL安装流程...');
      setInstallationProgress('准备安装', 0, '正在准备MySQL安装...');

      // 1. 首先下载MySQL包
      addInstallationLog('info', '开始下载MySQL安装包...');
      setInstallationProgress('下载中', 10, `正在下载MySQL ${config.mysqlVersion}...`);

      const packageInfo = {
        version: config.mysqlVersion,
        platform: 'windows',
        architecture: 'x64'
      };

      const downloadResult = await invoke('download_mysql_package', { packageInfo }) as any;
      addInstallationLog('success', 'MySQL安装包下载完成');
      setInstallationProgress('下载完成', 30, '安装包下载完成，准备安装...');

      // 2. 设置MySQL配置
      const mysqlConfig = {
        version: config.mysqlVersion,
        installPath: config.installPath,
        rootPassword: config.rootPassword,
        port: 3306,
        enableNetworking: true,
        enableSSL: false
      };

      await invoke('set_mysql_config', { config: mysqlConfig });
      addInstallationLog('info', 'MySQL配置已设置');

      // 3. 执行MySQL安装
      addInstallationLog('info', '开始安装MySQL...');
      setInstallationStatus('installing');
      setInstallationProgress('安装中', 50, '正在安装MySQL服务...');

      // 调用Tauri安装命令
      await installMySQL(downloadResult.filePath || downloadResult.zipFilePath || 'mysql-package.zip');

      // 4. 验证安装
      addInstallationLog('info', '验证MySQL安装...');
      setInstallationProgress('验证中', 90, '正在验证MySQL安装...');

      const isValid = await invoke('verify_mysql_installation');
      if (isValid) {
        setInstallationStatus('completed');
        setInstallationProgress('完成', 100, 'MySQL安装成功完成！');
        addInstallationLog('success', 'MySQL安装和验证成功完成');

        // 重新检测安装状态
        await checkInstallationStatus();
      } else {
        throw new Error('MySQL安装验证失败');
      }

    } catch (error) {
      setInstallationStatus('failed');

      // 增强的错误分类和处理
      let errorMessage = '安装失败';
      let errorDetails = '';

      if (error instanceof Error) {
        const errorStr = error.message.toLowerCase();

        if (errorStr.includes('network') || errorStr.includes('download')) {
          errorMessage = '网络下载失败';
          errorDetails = '请检查网络连接，或稍后重试';
        } else if (errorStr.includes('permission') || errorStr.includes('access')) {
          errorMessage = '权限不足';
          errorDetails = '请以管理员身份运行程序';
        } else if (errorStr.includes('space') || errorStr.includes('disk')) {
          errorMessage = '磁盘空间不足';
          errorDetails = '请清理磁盘空间后重试';
        } else if (errorStr.includes('port') || errorStr.includes('3306')) {
          errorMessage = '端口冲突';
          errorDetails = '端口3306已被占用，请关闭相关服务后重试';
        } else if (errorStr.includes('service')) {
          errorMessage = '服务安装失败';
          errorDetails = '请检查系统服务权限';
        } else if (errorStr.includes('verify') || errorStr.includes('validation')) {
          errorMessage = 'MySQL安装验证失败';
          errorDetails = '安装可能不完整，建议重新安装';
        } else {
          errorMessage = error.message;
          errorDetails = '如问题持续，请联系技术支持';
        }
      }

      addInstallationLog('error', `安装失败: ${errorMessage}`);
      if (errorDetails) {
        addInstallationLog('error', `解决建议: ${errorDetails}`);
      }
      setInstallationProgress('失败', 0, `${errorMessage}${errorDetails ? ` - ${errorDetails}` : ''}`);

      console.error('MySQL installation failed:', error);
    }
  };

  const statusDisplay = getInstallationStatusDisplay();
  const isInstalling = installation.status === 'downloading' || installation.status === 'installing' || realIsInstalling;
  const canInstall = activation.status === 'activated' && !isInstalling;

  // 动态按钮文本逻辑
  const getInstallButtonText = useCallback(() => {
    if (isInstalling || realIsInstalling) {
      return '安装中...';
    }
    if (isInstalled) {
      return '重新安装';
    }
    return '一键安装 MySQL';
  }, [isInstalling, realIsInstalling, isInstalled]);

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 面板标题 */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <Play className="w-5 h-5 mr-2 text-green-600" />
          一键安装
        </h2>
      </div>

      {/* 安装控制区域 */}
      <div className="p-6 border-b border-gray-200">
        {/* 安装状态 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            {statusDisplay.icon}
            <span className={`ml-2 font-medium ${statusDisplay.color}`}>
              {statusDisplay.text}
            </span>
          </div>
          <span className="text-sm text-gray-500">
            {installation.progressPercentage}%
          </span>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>{installation.currentStep}</span>
            <span>{installation.message}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${installation.progressPercentage}%` }}
            />
          </div>
        </div>

        {/* 一键安装按钮 */}
        <button
          onClick={handleInstall}
          disabled={!canInstall}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
            canInstall
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {getInstallButtonText()}
        </button>

        {!canInstall && activation.status !== 'activated' && (
          <p className="mt-2 text-sm text-red-600 text-center">
            请先激活产品后再进行安装
          </p>
        )}
      </div>

      {/* 日志输出区域 */}
      <div className="flex-1 flex flex-col">
        <div className="px-6 py-3 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">安装日志</h3>
          <button
            onClick={clearInstallationLogs}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            清空日志
          </button>
        </div>
        
        <div 
          ref={logContainerRef}
          className="flex-1 p-4 overflow-auto bg-gray-50 font-mono text-sm"
        >
          {installation.logs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              暂无日志信息
            </div>
          ) : (
            <div className="space-y-1">
              {installation.logs.map((log, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <span className="text-gray-400 text-xs mt-0.5 flex-shrink-0">
                    {log.timestamp}
                  </span>
                  <div className="flex items-center space-x-1 flex-shrink-0">
                    {getLogLevelIcon(log.level)}
                  </div>
                  <span className={`${getLogLevelStyle(log.level)} break-all`}>
                    {log.message}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
