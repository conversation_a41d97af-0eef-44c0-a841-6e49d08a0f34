/**
 * MySQL安装器应用状态管理
 * 简化的单页面应用状态结构
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 安装状态类型
export type InstallationStatus = 'idle' | 'downloading' | 'installing' | 'completed' | 'failed';

// 激活状态类型
export type ActivationStatus = 'not_activated' | 'activated' | 'expired' | 'checking';

// 应用配置接口
export interface AppConfig {
  // MySQL配置
  mysqlVersion: string;
  installPath: string;
  rootPassword: string;

  // 应用设置
  autoUpdate: boolean;
  enableLogging: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

// 激活状态接口
export interface ActivationState {
  status: ActivationStatus;
  licenseKey?: string;
  expiresAt?: string;
  remainingHours?: number;
  machineId: string;
  lastCheckTime?: number;
}

// 安装进度接口
export interface InstallationProgress {
  status: InstallationStatus;
  currentStep: string;
  progressPercentage: number;
  message: string;
  logs: Array<{
    timestamp: string;
    level: 'info' | 'warn' | 'error' | 'success';
    message: string;
  }>;
}

// 应用状态接口
export interface AppState {
  // 应用配置
  config: AppConfig;

  // 激活状态
  activation: ActivationState;

  // 安装进度
  installation: InstallationProgress;
}

// 应用操作接口
export interface AppActions {
  // 配置操作
  updateConfig: (config: Partial<AppConfig>) => void;
  setMySQLVersion: (version: string) => void;
  setInstallPath: (path: string) => void;
  setRootPassword: (password: string) => void;

  // 激活状态操作
  updateActivation: (activation: Partial<ActivationState>) => void;
  setActivationStatus: (status: ActivationStatus) => void;
  setLicenseKey: (key: string) => void;

  // 安装进度操作
  updateInstallation: (installation: Partial<InstallationProgress>) => void;
  setInstallationStatus: (status: InstallationStatus) => void;
  setInstallationProgress: (step: string, percentage: number, message: string) => void;
  addInstallationLog: (level: 'info' | 'warn' | 'error' | 'success', message: string) => void;
  clearInstallationLogs: () => void;

  // 重置操作
  resetInstallation: () => void;
  resetToDefaults: () => void;

  // 状态恢复和错误处理
  recoverFromError: () => void;
  validateState: () => void;
}

// 默认状态
const defaultState: AppState = {
  config: {
    mysqlVersion: '8.0.36',
    installPath: 'C:\\MySQL',
    rootPassword: '123456',
    autoUpdate: true,
    enableLogging: true,
    logLevel: 'info',
  },
  activation: {
    status: 'not_activated',
    machineId: '',
  },
  installation: {
    status: 'idle',
    currentStep: '',
    progressPercentage: 0,
    message: '准备安装...',
    logs: [],
  },
};

// 创建应用状态存储
export const useAppStore = create<AppState & AppActions>()(
  persist(
    (set, get) => ({
      ...defaultState,

      // 配置操作
      updateConfig: (config) =>
        set((state) => ({
          config: { ...state.config, ...config }
        })),

      setMySQLVersion: (version) =>
        set((state) => ({
          config: { ...state.config, mysqlVersion: version }
        })),

      setInstallPath: (path) =>
        set((state) => ({
          config: { ...state.config, installPath: path }
        })),

      setRootPassword: (password) =>
        set((state) => ({
          config: { ...state.config, rootPassword: password }
        })),

      // 激活状态操作
      updateActivation: (activation) =>
        set((state) => ({
          activation: { ...state.activation, ...activation }
        })),

      setActivationStatus: (status) =>
        set((state) => ({
          activation: { ...state.activation, status }
        })),

      setLicenseKey: (licenseKey) =>
        set((state) => ({
          activation: { ...state.activation, licenseKey }
        })),

      // 安装进度操作
      updateInstallation: (installation) =>
        set((state) => ({
          installation: { ...state.installation, ...installation }
        })),

      setInstallationStatus: (status) =>
        set((state) => ({
          installation: { ...state.installation, status }
        })),

      setInstallationProgress: (step, percentage, message) =>
        set((state) => ({
          installation: {
            ...state.installation,
            currentStep: step,
            progressPercentage: percentage,
            message,
          }
        })),

      addInstallationLog: (level, message) =>
        set((state) => ({
          installation: {
            ...state.installation,
            logs: [
              ...state.installation.logs,
              {
                timestamp: new Date().toLocaleTimeString(),
                level,
                message,
              }
            ]
          }
        })),

      clearInstallationLogs: () =>
        set((state) => ({
          installation: { ...state.installation, logs: [] }
        })),

      // 重置操作
      resetInstallation: () =>
        set((state) => ({
          installation: {
            status: 'idle',
            currentStep: '',
            progressPercentage: 0,
            message: '准备安装...',
            logs: [],
          }
        })),

      resetToDefaults: () => set(defaultState),

      // 状态恢复和错误处理
      recoverFromError: () =>
        set((state) => ({
          installation: {
            ...state.installation,
            status: 'idle',
            message: '准备安装...',
            progressPercentage: 0
          }
        })),

      // 验证状态一致性
      validateState: () => {
        const state = get();

        // 检查激活状态一致性
        if (state.activation.status === 'activated' && !state.activation.licenseKey) {
          console.warn('Activation state inconsistency detected, resetting...');
          set((prevState) => ({
            activation: {
              ...prevState.activation,
              status: 'not_activated'
            }
          }));
        }

        // 检查安装状态一致性
        if (state.installation.status === 'completed' && state.installation.progressPercentage !== 100) {
          console.warn('Installation state inconsistency detected, fixing...');
          set((prevState) => ({
            installation: {
              ...prevState.installation,
              progressPercentage: 100,
              message: 'MySQL安装成功完成！'
            }
          }));
        }
      },
    }),
    {
      name: 'mysql-installer-storage',
      partialize: (state) => ({
        config: state.config,
        activation: {
          status: state.activation.status,
          licenseKey: state.activation.licenseKey,
          expiresAt: state.activation.expiresAt,
          remainingHours: state.activation.remainingHours,
          machineId: state.activation.machineId,
        },
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 在状态恢复后验证一致性
          setTimeout(() => {
            state.validateState();
          }, 100);
        }
      },
      // 处理持久化错误
      storage: {
        getItem: (name) => {
          try {
            return localStorage.getItem(name);
          } catch (error) {
            console.error('Failed to read from localStorage:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            localStorage.setItem(name, value);
          } catch (error) {
            console.error('Failed to write to localStorage:', error);
          }
        },
        removeItem: (name) => {
          try {
            localStorage.removeItem(name);
          } catch (error) {
            console.error('Failed to remove from localStorage:', error);
          }
        },
      },
    }
  )
);

// 选择器函数
export const useAppConfig = () => useAppStore((state) => state.config);
export const useActivationState = () => useAppStore((state) => state.activation);
export const useInstallationState = () => useAppStore((state) => state.installation);

// 便捷的配置选择器
export const useMySQLVersion = () => useAppStore((state) => state.config.mysqlVersion);
export const useInstallPath = () => useAppStore((state) => state.config.installPath);
export const useRootPassword = () => useAppStore((state) => state.config.rootPassword);

// 便捷的激活选择器
export const useActivationStatus = () => useAppStore((state) => state.activation.status);
export const useIsActivated = () => useAppStore((state) => state.activation.status === 'activated');

// 便捷的安装选择器
export const useInstallationStatus = () => useAppStore((state) => state.installation.status);
export const useInstallationProgress = () => useAppStore((state) => state.installation.progressPercentage);
export const useInstallationLogs = () => useAppStore((state) => state.installation.logs);
export const useWindowState = () => {
  const isMaximized = useAppStore((state) => state.isMaximized);
  const isMinimized = useAppStore((state) => state.isMinimized);
  const windowSize = useAppStore((state) => state.windowSize);

  return { isMaximized, isMinimized, windowSize };
};

// 操作函数
export const useAppActions = () => {
  const setTheme = useAppStore((state) => state.setTheme);
  const setLanguage = useAppStore((state) => state.setLanguage);
  const setCurrentPage = useAppStore((state) => state.setCurrentPage);
  const setMaximized = useAppStore((state) => state.setMaximized);
  const setMinimized = useAppStore((state) => state.setMinimized);
  const setWindowSize = useAppStore((state) => state.setWindowSize);
  const updateSettings = useAppStore((state) => state.updateSettings);
  const updateActivationStatus = useAppStore((state) => state.updateActivationStatus);
  const setInstalling = useAppStore((state) => state.setInstalling);
  const setInstallationStep = useAppStore((state) => state.setInstallationStep);
  const setInstallationProgress = useAppStore((state) => state.setInstallationProgress);
  const addInstallationLog = useAppStore((state) => state.addInstallationLog);
  const clearInstallationLogs = useAppStore((state) => state.clearInstallationLogs);
  const resetApp = useAppStore((state) => state.resetApp);

  return {
    setTheme,
    setLanguage,
    setCurrentPage,
    setMaximized,
    setMinimized,
    setWindowSize,
    updateSettings,
    updateActivationStatus,
    setInstalling,
    setInstallationStep,
    setInstallationProgress,
    addInstallationLog,
    clearInstallationLogs,
    resetApp,
  };
};