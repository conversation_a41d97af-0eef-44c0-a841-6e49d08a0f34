'use client';

// MySQLAi.de - About关于我们组件
// 展示5个专业特性，体现平台的专业性和可靠性

import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Users, TrendingUp, FileText, Clock, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AboutProps } from '@/lib/types';

interface AboutComponentProps {
  className?: string;
}

// 专业特性数据配置
const PROFESSIONAL_FEATURES = [
  {
    id: 'ai-analysis',
    title: '智能分析',
    description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',
    icon: Zap,
    color: 'mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
    details: [
      '自动性能瓶颈检测',
      '智能SQL优化建议',
      '实时监控告警',
      '预测性维护方案',
    ],
  },
  {
    id: 'expert-consulting',
    title: '专业咨询',
    description: '资深数据库专家团队，提供一对一的专业咨询服务。',
    icon: Users,
    color: 'mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
    details: [
      '15年+数据库经验专家',
      '一对一专属咨询服务',
      '定制化解决方案',
      '7×24小时技术支持',
    ],
  },
  {
    id: 'efficient-management',
    title: '高效管理',
    description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',
    icon: TrendingUp,
    color: 'mysql-success',
    gradient: 'from-mysql-success to-green-600',
    details: [
      '敏捷项目管理方法',
      '可视化进度跟踪',
      '团队协作工具集成',
      '自动化工作流程',
    ],
  },
  {
    id: 'transparent-reporting',
    title: '透明报告',
    description: '详细的项目报告和数据分析，确保项目进展透明可控。',
    icon: FileText,
    color: 'mysql-warning',
    gradient: 'from-mysql-warning to-yellow-600',
    details: [
      '实时数据可视化',
      '多维度分析报告',
      '自定义报告模板',
      '数据导出多格式',
    ],
  },
  {
    id: '24-7-support',
    title: '7×24支持',
    description: '全天候技术支持服务，确保您的数据库系统稳定运行。',
    icon: Clock,
    color: 'mysql-error',
    gradient: 'from-mysql-error to-red-600',
    details: [
      '全年无休技术支持',
      '5分钟快速响应',
      '远程故障诊断',
      '紧急事件处理',
    ],
  },
  {
    id: 'enterprise-security',
    title: '企业级安全',
    description: '银行级安全保障，多重加密保护您的数据安全。',
    icon: Shield,
    color: 'mysql-info',
    gradient: 'from-blue-500 to-blue-700',
    details: [
      'SSL/TLS加密传输',
      '数据库访问控制',
      '审计日志记录',
      '定期安全评估',
    ],
  },
] as const;

export default function About({ className }: AboutComponentProps) {
  return (
    <section
      className={cn(
        'py-20 px-4 bg-gradient-to-b from-mysql-primary-light/30 to-white',
        className
      )}
    >
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-mysql-text mb-6">
            专业特性
          </h2>
          <p className="text-lg sm:text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed">
            凭借多年的数据库优化经验和先进的AI技术，为您提供最专业、最可靠、最安全的MySQL解决方案
          </p>
        </motion.div>

        {/* 专业特性网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {PROFESSIONAL_FEATURES.map((feature, index) => {
            const IconComponent = feature.icon;

            return (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.15,
                  ease: "easeOut" 
                }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="relative bg-white rounded-2xl p-8 shadow-lg border border-mysql-border hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out overflow-hidden">
                  {/* 渐变背景装饰 */}
                  <div className={cn(
                    'absolute top-0 left-0 right-0 h-1 bg-gradient-to-r',
                    feature.gradient
                  )} />

                  {/* 图标区域 */}
                  <div className="flex items-center justify-center mb-6">
                    <div className={cn(
                      'flex items-center justify-center w-20 h-20 rounded-2xl',
                      'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
                      feature.gradient
                    )}>
                      <IconComponent className="w-10 h-10 text-white" />
                    </div>
                  </div>

                  {/* 标题和描述 */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-mysql-text-light leading-relaxed">
                      {feature.description}
                    </p>
                  </div>

                  {/* 详细特性列表 */}
                  <div className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <motion.div
                        key={detailIndex}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ 
                          duration: 0.3, 
                          delay: (index * 0.15) + (detailIndex * 0.1) + 0.3
                        }}
                        viewport={{ once: true }}
                        className="flex items-center text-sm text-mysql-text"
                      >
                        <div className={cn(
                          'w-2 h-2 rounded-full mr-3 flex-shrink-0',
                          `bg-gradient-to-r ${feature.gradient}`
                        )} />
                        <span>{detail}</span>
                      </motion.div>
                    ))}
                  </div>

                  {/* 悬停时的光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* 底部统计数据 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">1000+</div>
              <div className="text-mysql-text-light">企业客户</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">99.9%</div>
              <div className="text-mysql-text-light">服务可用性</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">5分钟</div>
              <div className="text-mysql-text-light">响应时间</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">15年+</div>
              <div className="text-mysql-text-light">专业经验</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
