Fix and enhance the ER diagram generation tool at http://localhost:3000/tools/er-diagram to properly implement <PERSON>'s ER diagram notation system. The current implementation has multiple issues that need to be corrected:

**Critical ER Diagram Rendering Issues to Fix:**

1. **Line Styling**: All relationship lines MUST be rendered without arrowheads (plain lines only)

2. **Attribute Visualization**: All attributes MUST be displayed as uniform-sized ellipses (ovals), regardless of attribute type

3. **Entity Movement Behavior**: When an entity is dragged/moved, ALL its connected attributes MUST automatically move with it (maintain relative positioning)

4. **Color Scheme**: ALL ER diagram elements (entities, attributes, relationships) MUST use black color as the primary color

5. **Primary Key Notation**: All primary key attributes MUST display with underlined text to indicate their key status

6. **Foreign Key Display**: Foreign key attributes should NOT be displayed as separate visual elements in the diagram

7. **Relationship Cardinality**: Add missing relationship cardinality indicators (1:1, 1:N, N:M) near relationship diamonds

**Implementation Requirements - Follow <PERSON>'s ER Diagram Standards:**

**Entities:**
- Strong entities: Black rectangles
- Weak entities: Double-line black rectangles

**Attributes:**
- Simple attributes: Black ellipses (uniform size)
- Composite attributes: Black ellipses with sub-attribute connections
- Multi-valued attributes: Double-line black ellipses
- Derived attributes: Dashed-line black ellipses
- Key attributes: Black ellipses with underlined text

**Relationships:**
- Basic relationships: Black diamonds
- Weak relationships: Double-line black diamonds
- Cardinality notation: Display 1:1, 1:N, or N:M near relationship connections

**Technical Implementation Notes:**
- Ensure GoJS diagram configuration supports proper Chen notation
- Implement drag behavior that maintains attribute-entity relationships
- Update styling to use consistent black coloring throughout
- Add cardinality labels to relationship connections
- Modify primary key rendering to include text underlining
- Remove or hide foreign key attribute nodes

The goal is to create a standards-compliant Chen ER diagram that accurately represents database relationships with proper academic notation.