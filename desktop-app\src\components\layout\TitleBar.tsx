/**
 * 自定义标题栏组件
 * 实现无边框窗口的标题栏功能，包含窗口控制按钮
 */

// import React from 'react';
import { motion } from 'framer-motion';
import { Minus, Square, X, Database } from 'lucide-react';
import { useWindowState, useAppActions } from '../../store/appStore';
import { cn } from '../../lib/utils';

export default function TitleBar() {
  const { isMaximized } = useWindowState();
  const { setMaximized, setMinimized } = useAppActions();

  // 窗口控制函数
  const handleMinimize = () => {
    setMinimized(true);
    // 这里可以调用Tauri API来最小化窗口
  };

  const handleMaximize = () => {
    setMaximized(!isMaximized);
    // 这里可以调用Tauri API来最大化/还原窗口
  };

  const handleClose = () => {
    // 这里可以调用Tauri API来关闭窗口
    window.close();
  };

  return (
    <div className="flex items-center justify-between h-8 bg-mysql-primary text-white select-none draggable">
      {/* 左侧：应用图标和标题 */}
      <div className="flex items-center space-x-2 px-3">
        <Database className="w-4 h-4" />
        <span className="text-sm font-medium">MySQL安装器 - MySQLAi.de</span>
      </div>

      {/* 右侧：窗口控制按钮 */}
      <div className="flex items-center no-drag">
        {/* 最小化按钮 */}
        <motion.button
          whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
          whileTap={{ scale: 0.95 }}
          onClick={handleMinimize}
          className={cn(
            'flex items-center justify-center w-8 h-8',
            'hover:bg-white/10 transition-colors duration-200'
          )}
          title="最小化"
        >
          <Minus className="w-4 h-4" />
        </motion.button>

        {/* 最大化/还原按钮 */}
        <motion.button
          whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
          whileTap={{ scale: 0.95 }}
          onClick={handleMaximize}
          className={cn(
            'flex items-center justify-center w-8 h-8',
            'hover:bg-white/10 transition-colors duration-200'
          )}
          title={isMaximized ? '还原' : '最大化'}
        >
          <Square className="w-3 h-3" />
        </motion.button>

        {/* 关闭按钮 */}
        <motion.button
          whileHover={{ backgroundColor: '#e53e3e' }}
          whileTap={{ scale: 0.95 }}
          onClick={handleClose}
          className={cn(
            'flex items-center justify-center w-8 h-8',
            'hover:bg-red-600 transition-colors duration-200'
          )}
          title="关闭"
        >
          <X className="w-4 h-4" />
        </motion.button>
      </div>
    </div>
  );
}