'use client';

// MySQLAi.de - 响应式数据表格组件
// 支持移动端和桌面端的自适应数据表格

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  Filter,
  MoreVertical,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { SkeletonLoader } from '@/components/ui/PageLoader';

interface Column {
  key: string;
  title: string;
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
  responsive?: 'always' | 'desktop' | 'tablet' | 'mobile';
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
  rowKey?: string;
  className?: string;
  responsive?: boolean;
  emptyText?: string;
}

export default function DataTable({
  columns,
  data,
  loading = false,
  pagination,
  onSort,
  onFilter,
  rowKey = 'id',
  className,
  responsive = true,
  emptyText = '暂无数据'
}: DataTableProps) {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    columns.map(col => col.key)
  );
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 根据响应式设置过滤列
  const responsiveColumns = useMemo(() => {
    if (!responsive) return columns;
    
    return columns.filter(col => {
      if (!col.responsive || col.responsive === 'always') return true;
      
      if (isMobile) {
        return col.responsive === 'mobile';
      } else if (window.innerWidth < 1024) {
        return col.responsive === 'mobile' || col.responsive === 'tablet';
      } else {
        return true;
      }
    });
  }, [columns, responsive, isMobile]);

  // 处理排序
  const handleSort = (key: string) => {
    if (!columns.find(col => col.key === key)?.sortable) return;
    
    const direction = 
      sortConfig?.key === key && sortConfig.direction === 'asc' 
        ? 'desc' 
        : 'asc';
    
    setSortConfig({ key, direction });
    onSort?.(key, direction);
  };

  // 处理搜索
  const filteredData = useMemo(() => {
    if (!searchQuery) return data;
    
    return data.filter(record =>
      Object.values(record).some(value =>
        String(value).toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  }, [data, searchQuery]);

  // 渲染表头
  const renderHeader = () => (
    <thead className="bg-gray-50">
      <tr>
        {responsiveColumns
          .filter(col => visibleColumns.includes(col.key))
          .map((column) => (
            <th
              key={column.key}
              className={cn(
                'px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                column.sortable && 'cursor-pointer hover:bg-gray-100',
                column.width && `w-${column.width}`
              )}
              onClick={() => column.sortable && handleSort(column.key)}
            >
              <div className="flex items-center space-x-1">
                <span>{column.title}</span>
                {column.sortable && (
                  <div className="flex flex-col">
                    <ChevronUp 
                      className={cn(
                        'w-3 h-3',
                        sortConfig?.key === column.key && sortConfig.direction === 'asc'
                          ? 'text-mysql-primary'
                          : 'text-gray-400'
                      )}
                    />
                    <ChevronDown 
                      className={cn(
                        'w-3 h-3 -mt-1',
                        sortConfig?.key === column.key && sortConfig.direction === 'desc'
                          ? 'text-mysql-primary'
                          : 'text-gray-400'
                      )}
                    />
                  </div>
                )}
              </div>
            </th>
          ))}
      </tr>
    </thead>
  );

  // 渲染表格行
  const renderRow = (record: any, index: number) => (
    <motion.tr
      key={record[rowKey] || index}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className="bg-white hover:bg-gray-50 transition-colors"
    >
      {responsiveColumns
        .filter(col => visibleColumns.includes(col.key))
        .map((column) => (
          <td
            key={column.key}
            className="px-4 py-4 whitespace-nowrap text-sm text-gray-900"
          >
            {column.render 
              ? column.render(record[column.key], record)
              : record[column.key]
            }
          </td>
        ))}
    </motion.tr>
  );

  // 移动端卡片视图
  const renderMobileCard = (record: any, index: number) => (
    <motion.div
      key={record[rowKey] || index}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4"
    >
      {responsiveColumns.map((column) => (
        <div key={column.key} className="flex justify-between items-center py-2">
          <span className="text-sm font-medium text-gray-500">
            {column.title}:
          </span>
          <span className="text-sm text-gray-900">
            {column.render 
              ? column.render(record[column.key], record)
              : record[column.key]
            }
          </span>
        </div>
      ))}
    </motion.div>
  );

  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        <SkeletonLoader lines={5} />
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 工具栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
        {/* 搜索框 */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索数据..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-mysql-primary focus:border-transparent"
          />
        </div>

        {/* 列显示控制 */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            icon={<Filter className="w-4 h-4" />}
          >
            筛选
          </Button>
          
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              icon={<MoreVertical className="w-4 h-4" />}
            >
              列设置
            </Button>
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      {isMobile ? (
        // 移动端卡片视图
        <div className="space-y-4">
          {filteredData.length > 0 ? (
            filteredData.map((record, index) => renderMobileCard(record, index))
          ) : (
            <div className="text-center py-8 text-gray-500">
              {emptyText}
            </div>
          )}
        </div>
      ) : (
        // 桌面端表格视图
        <div className="overflow-x-auto bg-white rounded-lg shadow">
          <table className="min-w-full divide-y divide-gray-200">
            {renderHeader()}
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.length > 0 ? (
                filteredData.map((record, index) => renderRow(record, index))
              ) : (
                <tr>
                  <td 
                    colSpan={responsiveColumns.length}
                    className="px-4 py-8 text-center text-gray-500"
                  >
                    {emptyText}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* 分页 */}
      {pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            共 {pagination.total} 条记录
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.current === 1}
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
            >
              上一页
            </Button>
            <span className="text-sm text-gray-500">
              {pagination.current} / {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
