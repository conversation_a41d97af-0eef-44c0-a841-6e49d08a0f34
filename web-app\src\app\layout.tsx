import type { Metadata } from "next";
import "./globals.css";
// import Header from "@/components/layout/Header";
// import Footer from "@/components/layout/Footer";
import { homeMetadata, structuredData, generateJsonLd } from "./metadata";

// 字体配置已移至globals.css

export const metadata: Metadata = homeMetadata;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" className="scroll-smooth">
      <head>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={generateJsonLd(structuredData)}
        />
        {/* 预连接到外部资源 - 移除Google Fonts */}
        {/* DNS预取 */}
        <link rel="dns-prefetch" href="//github.com" />
        <link rel="dns-prefetch" href="//twitter.com" />
        <link rel="dns-prefetch" href="//linkedin.com" />
      </head>
      <body
        className="antialiased bg-white text-mysql-text"
        suppressHydrationWarning={true}
      >
        {/* 页面加载指示器 - 移除以避免hydration mismatch */}
        {children}
        {/* 页面加载完成脚本 - 移除以避免hydration mismatch */}
      </body>
    </html>
  );
}
