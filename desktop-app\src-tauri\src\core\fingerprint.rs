use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use sysinfo::{System, SystemExt, CpuExt, DiskExt};
use uuid::Uuid;

/// 机器指纹常量
const FINGERPRINT_VERSION: u32 = 2;
const MIN_FEATURES_COUNT: usize = 5;
const HASH_ROUNDS: usize = 3;

/// 机器特征信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MachineFeatures {
    /// 机器ID
    pub machine_id: String,
    /// CPU信息
    pub cpu_info: Option<String>,
    /// CPU名称
    pub cpu_name: Option<String>,
    /// 主板信息
    pub motherboard_info: Option<String>,
    /// MAC地址列表
    pub mac_addresses: Option<String>,
    /// 系统平台
    pub platform: String,
    /// 机器架构
    pub machine_arch: String,
    /// 主机名
    pub hostname: String,
    /// BIOS信息
    pub bios_info: Option<String>,
    /// 磁盘序列号
    pub disk_serial: Option<String>,
    /// 系统版本
    pub system_version: String,
}

/// 增强机器指纹生成器
#[derive(Debug)]
pub struct EnhancedFingerprint {
    features: HashMap<String, String>,
    fingerprint: Option<String>,
}

impl EnhancedFingerprint {
    /// 创建新的机器指纹生成器
    pub fn new() -> Self {
        let mut fingerprint = Self {
            features: HashMap::new(),
            fingerprint: None,
        };
        fingerprint.collect_features();
        fingerprint
    }

    /// 收集机器特征信息
    fn collect_features(&mut self) {
        log::info!("开始收集机器特征信息");

        // 1. 基础机器ID
        self.collect_machine_id();

        // 2. CPU信息
        self.collect_cpu_info();

        // 3. 系统信息
        self.collect_system_info();

        // 4. 网络适配器信息
        self.collect_network_info();

        // 5. 磁盘信息
        self.collect_disk_info();

        // 6. 主机名信息
        self.collect_hostname_info();

        log::info!("收集到 {} 个机器特征", self.features.len());
    }

    /// 收集机器ID
    fn collect_machine_id(&mut self) {
        match self.get_machine_id() {
            Ok(machine_id) => {
                self.features.insert("machine_id".to_string(), machine_id);
            }
            Err(e) => {
                log::warn!("获取机器ID失败: {}", e);
                self.features.insert("machine_id".to_string(), "UNKNOWN".to_string());
            }
        }
    }

    /// 获取机器ID
    fn get_machine_id(&self) -> Result<String, Box<dyn std::error::Error>> {
        // 尝试生成基于硬件的唯一ID
        let mut sys = System::new_all();
        sys.refresh_all();

        let mut id_components = Vec::new();

        // CPU信息
        if let Some(cpu) = sys.cpus().first() {
            id_components.push(cpu.brand().to_string());
        }

        // 系统信息
        if let Some(name) = sys.name() {
            id_components.push(name);
        }

        if let Some(version) = sys.os_version() {
            id_components.push(version);
        }

        // 如果没有足够的信息，使用UUID
        if id_components.is_empty() {
            return Ok(Uuid::new_v4().to_string());
        }

        // 生成基于硬件信息的稳定ID
        let combined = id_components.join("|");
        let mut hasher = Sha256::new();
        hasher.update(combined.as_bytes());
        Ok(format!("{:x}", hasher.finalize()))
    }

    /// 收集CPU信息
    fn collect_cpu_info(&mut self) {
        let mut sys = System::new();
        sys.refresh_cpu();

        if let Some(cpu) = sys.cpus().first() {
            self.features.insert("cpu_name".to_string(), cpu.brand().to_string());
            self.features.insert("cpu_frequency".to_string(), cpu.frequency().to_string());
        }

        // CPU核心数
        self.features.insert("cpu_cores".to_string(), sys.cpus().len().to_string());

        // 尝试获取更详细的CPU信息（平台特定）
        #[cfg(target_os = "windows")]
        self.collect_windows_cpu_info();

        #[cfg(target_os = "linux")]
        self.collect_linux_cpu_info();

        #[cfg(target_os = "macos")]
        self.collect_macos_cpu_info();
    }

    /// Windows平台CPU信息收集
    #[cfg(target_os = "windows")]
    fn collect_windows_cpu_info(&mut self) {
        use std::process::Command;

        // 获取CPU ID
        if let Ok(output) = Command::new("wmic")
            .args(&["cpu", "get", "ProcessorId", "/format:value"])
            .output()
        {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                for line in output_str.lines() {
                    if line.starts_with("ProcessorId=") {
                        let cpu_id = line.split('=').nth(1).unwrap_or("").trim();
                        if !cpu_id.is_empty() {
                            self.features.insert("cpu_id".to_string(), cpu_id.to_string());
                        }
                        break;
                    }
                }
            }
        }

        // 获取主板信息
        if let Ok(output) = Command::new("wmic")
            .args(&["baseboard", "get", "SerialNumber", "/format:value"])
            .output()
        {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                for line in output_str.lines() {
                    if line.starts_with("SerialNumber=") {
                        let serial = line.split('=').nth(1).unwrap_or("").trim();
                        if !serial.is_empty() && serial != "To be filled by O.E.M." {
                            self.features.insert("motherboard_serial".to_string(), serial.to_string());
                        }
                        break;
                    }
                }
            }
        }
    }

    /// Linux平台CPU信息收集
    #[cfg(target_os = "linux")]
    fn collect_linux_cpu_info(&mut self) {
        use std::fs;

        // 读取/proc/cpuinfo
        if let Ok(cpuinfo) = fs::read_to_string("/proc/cpuinfo") {
            for line in cpuinfo.lines() {
                if line.starts_with("processor") {
                    // 可以提取更多CPU信息
                } else if line.starts_with("model name") {
                    if let Some(model) = line.split(':').nth(1) {
                        self.features.insert("cpu_model".to_string(), model.trim().to_string());
                    }
                }
            }
        }

        // 读取机器ID
        if let Ok(machine_id) = fs::read_to_string("/etc/machine-id") {
            self.features.insert("linux_machine_id".to_string(), machine_id.trim().to_string());
        }
    }

    /// macOS平台CPU信息收集
    #[cfg(target_os = "macos")]
    fn collect_macos_cpu_info(&mut self) {
        use std::process::Command;

        // 使用system_profiler获取硬件信息
        if let Ok(output) = Command::new("system_profiler")
            .args(&["SPHardwareDataType"])
            .output()
        {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                // 解析硬件信息
                for line in output_str.lines() {
                    if line.contains("Serial Number") {
                        if let Some(serial) = line.split(':').nth(1) {
                            self.features.insert("hardware_serial".to_string(), serial.trim().to_string());
                        }
                    }
                }
            }
        }
    }

    /// 收集系统信息
    fn collect_system_info(&mut self) {
        let mut sys = System::new();
        sys.refresh_all();

        // 系统名称和版本
        if let Some(name) = sys.name() {
            self.features.insert("system_name".to_string(), name);
        }

        if let Some(version) = sys.os_version() {
            self.features.insert("system_version".to_string(), version);
        }

        if let Some(hostname) = sys.host_name() {
            self.features.insert("hostname".to_string(), hostname);
        }

        // 内存信息
        self.features.insert("total_memory".to_string(), sys.total_memory().to_string());

        // 架构信息
        self.features.insert("arch".to_string(), std::env::consts::ARCH.to_string());
        self.features.insert("os".to_string(), std::env::consts::OS.to_string());
    }

    /// 收集网络信息
    fn collect_network_info(&mut self) {
        // 获取MAC地址
        match mac_address::get_mac_address() {
            Ok(Some(mac)) => {
                self.features.insert("primary_mac".to_string(), mac.to_string());
            }
            Ok(None) => {
                log::warn!("未找到MAC地址");
            }
            Err(e) => {
                log::warn!("获取MAC地址失败: {}", e);
            }
        }

        // 获取所有网络接口的MAC地址
        let mut mac_addresses = Vec::new();
        if let Ok(interfaces) = mac_address::mac_address_by_name("") {
            // 这里需要遍历所有接口，但mac_address crate的API有限
            // 可以考虑使用其他方法获取所有MAC地址
        }

        // 备用方法：使用UUID节点ID
        let node_id = uuid::Uuid::new_v1(uuid::Timestamp::now(uuid::NoContext), &[1, 2, 3, 4, 5, 6]);
        self.features.insert("node_id".to_string(), node_id.to_string());
    }

    /// 收集磁盘信息
    fn collect_disk_info(&mut self) {
        let mut sys = System::new();
        sys.refresh_disks();

        let mut disk_info = Vec::new();
        for disk in sys.disks() {
            let disk_name = disk.name().to_string_lossy();
            let total_space = disk.total_space();
            disk_info.push(format!("{}:{}", disk_name, total_space));
        }

        if !disk_info.is_empty() {
            self.features.insert("disk_info".to_string(), disk_info.join("|"));
        }
    }

    /// 收集主机名信息
    fn collect_hostname_info(&mut self) {
        // 环境变量
        if let Ok(computer_name) = std::env::var("COMPUTERNAME") {
            self.features.insert("computer_name".to_string(), computer_name);
        }

        if let Ok(username) = std::env::var("USERNAME") {
            self.features.insert("username".to_string(), username);
        }

        if let Ok(user) = std::env::var("USER") {
            self.features.insert("user".to_string(), user);
        }
    }

    /// 生成增强机器指纹
    pub fn generate_fingerprint(&mut self) -> String {
        if let Some(ref fingerprint) = self.fingerprint {
            return fingerprint.clone();
        }

        // 检查特征数量
        if self.features.len() < MIN_FEATURES_COUNT {
            log::warn!(
                "收集到的特征数量 ({}) 少于最小要求 ({})",
                self.features.len(),
                MIN_FEATURES_COUNT
            );
        }

        // 构建特征字符串
        let mut feature_parts = Vec::new();
        let mut sorted_keys: Vec<_> = self.features.keys().collect();
        sorted_keys.sort();

        for key in sorted_keys {
            if let Some(value) = self.features.get(key) {
                if !value.is_empty() {
                    feature_parts.push(format!("{}:{}", key, value));
                }
            }
        }

        let feature_string = feature_parts.join("|");

        // 多重哈希增强安全性
        let mut fingerprint = feature_string;
        for round_num in 0..HASH_ROUNDS {
            let salt = format!("fingerprint_v{}_round_{}", FINGERPRINT_VERSION, round_num);
            let mut hasher = Sha256::new();
            hasher.update(format!("{}{}", fingerprint, salt).as_bytes());
            fingerprint = format!("{:x}", hasher.finalize());
        }

        self.fingerprint = Some(fingerprint.clone());
        log::info!("生成增强机器指纹: {}...", &fingerprint[..16]);

        fingerprint
    }

    /// 验证机器指纹
    pub fn verify_fingerprint(&mut self, stored_fingerprint: &str) -> bool {
        let current_fingerprint = self.generate_fingerprint();
        
        // 使用常量时间比较防止时序攻击
        if current_fingerprint.len() != stored_fingerprint.len() {
            return false;
        }

        let mut result = 0u8;
        for (a, b) in current_fingerprint.bytes().zip(stored_fingerprint.bytes()) {
            result |= a ^ b;
        }

        result == 0
    }

    /// 获取特征摘要（用于调试）
    pub fn get_features_summary(&self) -> HashMap<String, String> {
        let mut summary = HashMap::new();
        for (key, value) in &self.features {
            if !value.is_empty() {
                // 对敏感信息进行部分隐藏
                let display_value = if value.len() > 8 {
                    format!("{}...", &value[..8])
                } else {
                    value.clone()
                };
                summary.insert(key.clone(), display_value);
            }
        }
        summary
    }

    /// 获取机器特征信息
    pub fn get_machine_features(&self) -> MachineFeatures {
        MachineFeatures {
            machine_id: self.features.get("machine_id").cloned().unwrap_or_default(),
            cpu_info: self.features.get("cpu_id").cloned(),
            cpu_name: self.features.get("cpu_name").cloned(),
            motherboard_info: self.features.get("motherboard_serial").cloned(),
            mac_addresses: self.features.get("primary_mac").cloned(),
            platform: self.features.get("os").cloned().unwrap_or_default(),
            machine_arch: self.features.get("arch").cloned().unwrap_or_default(),
            hostname: self.features.get("hostname").cloned().unwrap_or_default(),
            bios_info: self.features.get("bios_serial").cloned(),
            disk_serial: self.features.get("disk_info").cloned(),
            system_version: self.features.get("system_version").cloned().unwrap_or_default(),
        }
    }
}

/// 全局指纹实例
static mut ENHANCED_FINGERPRINT: Option<EnhancedFingerprint> = None;
static INIT: std::sync::Once = std::sync::Once::new();

/// 获取全局增强指纹实例
pub fn get_enhanced_fingerprint() -> &'static mut EnhancedFingerprint {
    unsafe {
        INIT.call_once(|| {
            ENHANCED_FINGERPRINT = Some(EnhancedFingerprint::new());
        });
        ENHANCED_FINGERPRINT.as_mut().unwrap()
    }
}

/// 生成机器指纹（便捷函数）
pub fn generate_machine_fingerprint() -> String {
    get_enhanced_fingerprint().generate_fingerprint()
}

/// 验证机器指纹（便捷函数）
pub fn verify_machine_fingerprint(stored_fingerprint: &str) -> bool {
    get_enhanced_fingerprint().verify_fingerprint(stored_fingerprint)
}

/// 获取机器特征摘要（便捷函数）
pub fn get_machine_features_summary() -> HashMap<String, String> {
    get_enhanced_fingerprint().get_features_summary()
}

/// 获取机器特征信息（便捷函数）
pub fn get_machine_features() -> MachineFeatures {
    get_enhanced_fingerprint().get_machine_features()
}