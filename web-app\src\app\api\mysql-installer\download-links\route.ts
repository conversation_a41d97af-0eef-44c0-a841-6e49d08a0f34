// MySQLAi.de - MySQL下载链接 API
// 提供最佳下载链接获取和优化功能

import { NextRequest, NextResponse } from 'next/server';
import { 
  SupportedOS, 
  SystemArchitecture, 
  MySQLVersion 
} from '@/app/tools/mysql-installer/types/mysql-installer';
import { 
  getOptimalDownloadUrl,
  testDownloadSourceSpeed,
  getAllDownloadSources 
} from '@/app/tools/mysql-installer/lib/download-manager';

/**
 * 获取客户端地理位置（简化版）
 */
function getClientRegion(request: NextRequest): string {
  // 从请求头获取地理位置信息
  const cfCountry = request.headers.get('cf-ipcountry');
  const acceptLanguage = request.headers.get('accept-language');
  
  if (cfCountry) {
    return cfCountry.toLowerCase();
  }
  
  if (acceptLanguage) {
    const lang = acceptLanguage.split(',')[0].split('-')[1];
    if (lang) {
      return lang.toLowerCase();
    }
  }
  
  return 'cn'; // 默认中国
}

/**
 * 验证下载参数
 */
function validateDownloadParams(
  version: string, 
  os: string, 
  architecture: string
): {
  isValid: boolean;
  errors: string[];
  parsedParams?: {
    version: MySQLVersion;
    os: SupportedOS;
    architecture: SystemArchitecture;
  };
} {
  const errors: string[] = [];
  
  // 验证版本
  if (!version || !Object.values(MySQLVersion).includes(version as MySQLVersion)) {
    errors.push('无效的MySQL版本');
  }
  
  // 验证操作系统
  if (!os || !Object.values(SupportedOS).includes(os as SupportedOS)) {
    errors.push('无效的操作系统');
  }
  
  // 验证架构
  if (!architecture || !Object.values(SystemArchitecture).includes(architecture as SystemArchitecture)) {
    errors.push('无效的系统架构');
  }
  
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  
  return {
    isValid: true,
    errors: [],
    parsedParams: {
      version: version as MySQLVersion,
      os: os as SupportedOS,
      architecture: architecture as SystemArchitecture
    }
  };
}

// GET /api/mysql-installer/download-links - 获取下载链接
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const version = searchParams.get('version');
    const os = searchParams.get('os');
    const architecture = searchParams.get('architecture');
    const region = searchParams.get('region') || getClientRegion(request);
    const includeAlternatives = searchParams.get('includeAlternatives') === 'true';
    const action = searchParams.get('action') || 'get-links';

    // 验证参数
    const validation = validateDownloadParams(
      version || '',
      os || '',
      architecture || ''
    );

    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: '参数验证失败',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    const { version: mysqlVersion, os: targetOS, architecture: arch } = validation.parsedParams!;

    // 根据action参数处理不同请求
    if (action === 'get-sources') {
      // 获取所有下载源
      const allSources = getAllDownloadSources(mysqlVersion, targetOS, arch);

      return NextResponse.json({
        success: true,
        data: {
          sources: allSources,
          total: allSources.length,
          version: mysqlVersion,
          os: targetOS,
          architecture: arch
        }
      });
    }

    // 默认：获取最佳下载链接
    const downloadInfo = await getOptimalDownloadUrl(mysqlVersion, targetOS, arch, region);

    if (!downloadInfo) {
      return NextResponse.json(
        {
          success: false,
          error: '无法获取下载链接',
          details: '当前版本和系统组合暂不支持'
        },
        { status: 404 }
      );
    }

    const baseResponse = {
      success: true,
      data: {
        recommended: downloadInfo.recommendedSource,
        packageInfo: downloadInfo.packageInfo,
        region,
        generatedAt: new Date().toISOString()
      }
    };

    // 如果需要备选链接
    if (includeAlternatives) {
      const allSources = getAllDownloadSources(mysqlVersion, targetOS, arch);
      const responseWithAlternatives = {
        ...baseResponse,
        data: {
          ...baseResponse.data,
          alternatives: allSources.filter(source =>
            source.url !== downloadInfo.recommendedSource.url
          )
        }
      };
      return NextResponse.json(responseWithAlternatives);
    }

    return NextResponse.json(baseResponse);

  } catch (error) {
    console.error('下载链接API错误:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取下载链接失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// POST /api/mysql-installer/download-links/test-speed - 测试下载源速度
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { urls, timeout = 10000 } = body;
    
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json(
        { success: false, error: '缺少有效的URL列表' },
        { status: 400 }
      );
    }
    
    // 限制测试URL数量
    if (urls.length > 10) {
      return NextResponse.json(
        { success: false, error: 'URL数量不能超过10个' },
        { status: 400 }
      );
    }
    
    // 测试每个URL的速度
    const speedTests = await Promise.allSettled(
      urls.map(async (url: string) => {
        try {
          const result = await testDownloadSourceSpeed(url, timeout);
          return {
            url,
            ...result
          };
        } catch (error) {
          return {
            url,
            isAvailable: false,
            responseTime: -1,
            error: error instanceof Error ? error.message : '测试失败'
          };
        }
      })
    );
    
    // 处理测试结果
    const results = speedTests.map((test, index) => {
      if (test.status === 'fulfilled') {
        return test.value;
      } else {
        return {
          url: urls[index],
          isAvailable: false,
          responseTime: -1,
          error: test.reason?.message || '测试失败'
        };
      }
    });
    
    // 按响应时间排序（可用的优先）
    const sortedResults = results.sort((a, b) => {
      if (a.isAvailable && !b.isAvailable) return -1;
      if (!a.isAvailable && b.isAvailable) return 1;
      if (a.isAvailable && b.isAvailable) {
        return a.responseTime - b.responseTime;
      }
      return 0;
    });
    
    return NextResponse.json({
      success: true,
      data: {
        results: sortedResults,
        fastest: sortedResults.find(r => r.isAvailable),
        testedAt: new Date().toISOString(),
        totalTested: urls.length,
        availableCount: results.filter(r => r.isAvailable).length
      }
    });
    
  } catch (error) {
    console.error('下载速度测试API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '下载速度测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}


