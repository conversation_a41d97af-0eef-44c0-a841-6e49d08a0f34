'use client';

/**
 * MySQLAi.de - MySQL一键安装工具页面
 * 智能MySQL 8.0一键安装工具，支持多系统自动检测和配置
 * 更新：2025-07-01
 */

import React, { useState, useCallback, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Download,
  ArrowLeft,
  Settings,
  Zap,
  Monitor,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { LoadingState, ErrorState } from '@/components/ui/StateComponents';
import QuickInstaller from './components/QuickInstaller';
import AdvancedWizard from './components/AdvancedWizard';
import InstallationGuide from './components/InstallationGuide';
import {
  InstallationStatus,
  SystemInfo,
  AdvancedConfig,
  InstallationResult
} from './types/mysql-installer';
import { getSystemInfo } from './lib/system-detection';
import { generateMySQLConfig } from './lib/config-generator';

/**
 * 安装模式枚举
 */
enum InstallationMode {
  QUICK = 'quick',
  ADVANCED = 'advanced',
  GUIDE = 'guide'
}

/**
 * 页面状态接口
 */
interface PageState {
  mode: InstallationMode;
  systemInfo: SystemInfo | null;
  isLoading: boolean;
  error: string | null;
  installationResult: InstallationResult | null;
}

/**
 * MySQL一键安装工具主页面组件
 */
export default function MySQLInstallerPage() {
  // 状态管理
  const [pageState, setPageState] = useState<PageState>({
    mode: InstallationMode.QUICK,
    systemInfo: null,
    isLoading: true,
    error: null,
    installationResult: null
  });

  // 系统检测
  useEffect(() => {
    const detectSystem = async () => {
      try {
        setPageState(prev => ({ ...prev, isLoading: true, error: null }));

        // 检测系统信息
        const sysInfo = getSystemInfo();

        setPageState(prev => ({
          ...prev,
          systemInfo: sysInfo,
          isLoading: false
        }));
      } catch (error) {
        setPageState(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : '系统检测失败',
          isLoading: false
        }));
      }
    };

    detectSystem();
  }, []);

  // 切换安装模式
  const switchMode = useCallback((mode: InstallationMode) => {
    setPageState(prev => ({ ...prev, mode }));
  }, []);

  // 处理一键安装完成
  const handleQuickInstallComplete = useCallback((result: InstallationResult) => {
    setPageState(prev => ({
      ...prev,
      installationResult: result,
      mode: InstallationMode.GUIDE
    }));
  }, []);

  // 处理高级配置完成
  const handleAdvancedConfigComplete = useCallback((config: AdvancedConfig) => {
    const configContent = generateMySQLConfig(config);
    const result: InstallationResult = {
      status: InstallationStatus.COMPLETED,
      config,
      configFileContent: configContent,
      installationSteps: [],
      timestamp: new Date().toISOString()
    };

    setPageState(prev => ({
      ...prev,
      installationResult: result,
      mode: InstallationMode.GUIDE
    }));
  }, []);

  // 重置状态
  const resetInstallation = useCallback(() => {
    setPageState(prev => ({
      ...prev,
      mode: InstallationMode.QUICK,
      installationResult: null,
      error: null
    }));
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-mysql-primary-light">
      {/* 面包屑导航 */}
      <div className="bg-white border-b border-mysql-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link
              href="/"
              className="text-mysql-text-light hover:text-mysql-primary transition-colors duration-200"
            >
              首页
            </Link>
            <span className="text-mysql-text-light">/</span>
            <Link
              href="/tools"
              className="text-mysql-text-light hover:text-mysql-primary transition-colors duration-200"
            >
              工具集
            </Link>
            <span className="text-mysql-text-light">/</span>
            <span className="text-mysql-text font-medium">MySQL一键安装工具</span>
          </nav>
        </div>
      </div>

      {/* 页面头部 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 返回按钮 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Link
            href="/tools"
            className="inline-flex items-center text-mysql-text-light hover:text-mysql-primary transition-colors duration-200"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回工具集
          </Link>
        </motion.div>

        {/* 页面标题 */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-700 rounded-2xl shadow-lg">
              <Download className="w-8 h-8 text-white" />
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-mysql-text mb-4"
          >
            MySQL 8.0 一键安装工具
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed"
          >
            智能检测系统环境，一键安装和配置MySQL 8.0数据库，支持Windows/macOS/Linux多平台
          </motion.p>
        </div>

        {/* 模式切换按钮 */}
        {pageState.mode !== InstallationMode.GUIDE && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex justify-center space-x-4 mb-8"
          >
            <Button
              variant={pageState.mode === InstallationMode.QUICK ? 'primary' : 'outline'}
              onClick={() => switchMode(InstallationMode.QUICK)}
              icon={<Zap className="w-4 h-4" />}
            >
              一键安装
            </Button>

            <Button
              variant={pageState.mode === InstallationMode.ADVANCED ? 'primary' : 'outline'}
              onClick={() => switchMode(InstallationMode.ADVANCED)}
              icon={<Settings className="w-4 h-4" />}
            >
              高级配置
            </Button>
          </motion.div>
        )}

        {/* 错误状态 */}
        {pageState.error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <ErrorState
              error={pageState.error}
              onRetry={() => window.location.reload()}
            />
          </motion.div>
        )}

        {/* 加载状态 */}
        {pageState.isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-8"
          >
            <LoadingState
              message="正在检测系统环境..."
              size="lg"
              variant="pulse"
              className="py-12"
            />
          </motion.div>
        )}

        {/* 主要内容区域 */}
        {!pageState.isLoading && !pageState.error && (
          <AnimatePresence mode="wait">
            {pageState.mode === InstallationMode.QUICK && (
              <motion.div
                key="quick-installer"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <QuickInstaller
                  onAdvancedMode={() => switchMode(InstallationMode.ADVANCED)}
                  onConfigComplete={handleQuickInstallComplete}
                />
              </motion.div>
            )}

            {pageState.mode === InstallationMode.ADVANCED && (
              <motion.div
                key="advanced-wizard"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <AdvancedWizard
                  onConfigComplete={handleAdvancedConfigComplete}
                  onCancel={() => switchMode(InstallationMode.QUICK)}
                  onBackToQuick={() => switchMode(InstallationMode.QUICK)}
                />
              </motion.div>
            )}

            {pageState.mode === InstallationMode.GUIDE && pageState.installationResult && (
              <motion.div
                key="installation-guide"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-mysql-text">安装指导</h2>
                    <Button
                      variant="outline"
                      onClick={resetInstallation}
                      icon={<ArrowLeft className="w-4 h-4" />}
                    >
                      重新开始
                    </Button>
                  </div>
                </div>

                <InstallationGuide
                  config={pageState.installationResult.config}
                  configFileContent={pageState.installationResult.configFileContent || ''}
                />
              </motion.div>
            )}
          </AnimatePresence>
        )}

        {/* 帮助信息 */}
        {pageState.mode !== InstallationMode.GUIDE && !pageState.isLoading && !pageState.error && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-12 bg-white rounded-xl shadow-lg border border-mysql-border p-6"
          >
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <HelpCircle className="w-6 h-6 text-mysql-primary" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-mysql-text mb-2">使用说明</h3>
                <div className="space-y-2 text-mysql-text-light">
                  <p>• <strong>一键安装</strong>：自动检测系统环境，使用默认配置快速安装MySQL 8.0</p>
                  <p>• <strong>高级配置</strong>：通过向导式界面自定义安装路径、端口、安全设置等参数</p>
                  <p>• <strong>多系统支持</strong>：支持Windows 10+、macOS 11+、Ubuntu 18.04+等主流操作系统</p>
                  <p>• <strong>安全优化</strong>：自动应用MySQL安全最佳实践，保障数据库安全</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* 系统兼容性信息 */}
        {pageState.systemInfo && pageState.mode !== InstallationMode.GUIDE && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-6 bg-blue-50 border border-blue-200 rounded-xl p-6"
          >
            <div className="flex items-center space-x-3 mb-3">
              <Monitor className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-blue-800">检测到的系统环境</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-blue-600">操作系统：</span>
                <span className="font-medium text-blue-800">{pageState.systemInfo.osVersion}</span>
              </div>
              <div>
                <span className="text-blue-600">架构：</span>
                <span className="font-medium text-blue-800">{pageState.systemInfo.architecture}</span>
              </div>
              <div>
                <span className="text-blue-600">兼容性：</span>
                <span className={cn(
                  "font-medium",
                  pageState.systemInfo.isSupported ? "text-green-600" : "text-red-600"
                )}>
                  {pageState.systemInfo.isSupported ? "✓ 支持" : "✗ 不支持"}
                </span>
              </div>
            </div>
          </motion.div>
        )}

        {/* 底部说明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center mt-12"
        >
          <p className="text-mysql-text-light">
            如有任何问题或建议，欢迎
            <Link href="/contact" className="text-mysql-primary hover:underline ml-1">
              联系我们
            </Link>
          </p>
        </motion.div>
      </div>
    </div>
  );
}
