# Icons Directory

This directory should contain the following icon files for the Tauri application:

- `32x32.png` - 32x32 pixel PNG icon
- `128x128.png` - 128x128 pixel PNG icon  
- `<EMAIL>` - 256x256 pixel PNG icon (2x resolution)
- `icon.icns` - macOS icon file
- `icon.ico` - Windows icon file

For now, these are placeholder references. In production, you should:

1. Create proper icon files with the MySQLAi branding
2. Use the MySQL theme colors (#00758F)
3. Ensure icons are optimized for different platforms
4. Test icon display across Windows, macOS, and Linux

You can generate these icons from a single high-resolution source image using tools like:
- Tauri Icon Generator
- Online icon converters
- Design tools like Figma or Adobe Illustrator