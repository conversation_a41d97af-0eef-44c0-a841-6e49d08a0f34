import React from 'react';
import {
  useSystemDetection,
  formatMemorySize,
  formatDiskUsage,
  getOSDisplayName,
  getArchDisplayName,
  areAllRequirementsSatisfied
} from '../hooks/useSystemDetection';
import { LoadingState, ErrorState, Button } from './ui';

interface SystemInfoPanelProps {
  className?: string;
}

export const SystemInfoPanel: React.FC<SystemInfoPanelProps> = ({ className = '' }) => {
  const {
    systemInfo,
    machineFeatures,
    systemPerformance,
    machineFingerprint,
    featuresummary,
    loading,
    error,
    refreshAll
  } = useSystemDetection();

  if (loading) {
    return (
      <div className={`p-6 bg-white rounded-lg shadow-md ${className}`}>
        <LoadingState
          message="正在检测系统信息..."
          size="md"
          variant="spinner"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 bg-white rounded-lg shadow-md ${className}`}>
        <ErrorState
          title="系统检测失败"
          message={error}
          actionText="重新检测"
          onAction={refreshAll}
        />
      </div>
    );
  }

  const allRequirementsSatisfied = areAllRequirementsSatisfied(systemInfo);

  return (
    <div className={`p-6 bg-white rounded-lg shadow-md ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-800">系统信息</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={refreshAll}
        >
          刷新
        </Button>
      </div>

      {/* 系统基本信息 */}
      {systemInfo && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">基本信息</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">操作系统</div>
              <div className="font-medium">
                {getOSDisplayName(systemInfo.os)} {systemInfo.osVersion}
              </div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">系统架构</div>
              <div className="font-medium">
                {getArchDisplayName(systemInfo.architecture)}
              </div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">MySQL支持</div>
              <div className={`font-medium ${systemInfo.isSupported ? 'text-green-600' : 'text-red-600'}`}>
                {systemInfo.isSupported ? '✓ 支持' : '✗ 不支持'}
              </div>
            </div>
            {machineFeatures?.hostname && (
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">主机名</div>
                <div className="font-medium">{machineFeatures.hostname}</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 系统性能信息 */}
      {systemPerformance && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">性能信息</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">CPU核心数</div>
              <div className="font-medium">{systemPerformance.cpu_count} 核</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">CPU使用率</div>
              <div className="font-medium">{systemPerformance.cpu_usage.toFixed(1)}%</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">总内存</div>
              <div className="font-medium">{formatMemorySize(systemPerformance.total_memory)}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <div className="text-sm text-gray-600">可用内存</div>
              <div className="font-medium">{formatMemorySize(systemPerformance.available_memory)}</div>
            </div>
          </div>

          {/* 磁盘信息 */}
          {systemPerformance.disk_usage.length > 0 && (
            <div className="mt-4">
              <div className="text-sm text-gray-600 mb-2">磁盘使用情况</div>
              <div className="space-y-2">
                {systemPerformance.disk_usage.map((disk, index) => (
                  <div key={index} className="bg-gray-50 p-3 rounded">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{disk.name}</div>
                        <div className="text-sm text-gray-600">{disk.mount_point}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatDiskUsage(disk.usage_percent)}</div>
                        <div className="text-sm text-gray-600">
                          {formatMemorySize(disk.used_space)} / {formatMemorySize(disk.total_space)}
                        </div>
                      </div>
                    </div>
                    <div className="mt-2 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          disk.usage_percent > 90 ? 'bg-red-500' :
                          disk.usage_percent > 70 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(disk.usage_percent, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 系统要求检查 */}
      {systemInfo?.requirements && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">系统要求检查</h3>
          <div className={`p-4 rounded-lg ${allRequirementsSatisfied ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className={`flex items-center mb-3 ${allRequirementsSatisfied ? 'text-green-700' : 'text-red-700'}`}>
              <span className="text-lg mr-2">
                {allRequirementsSatisfied ? '✓' : '✗'}
              </span>
              <span className="font-medium">
                {allRequirementsSatisfied ? '系统要求全部满足' : '部分系统要求未满足'}
              </span>
            </div>
            
            <div className="space-y-2">
              {systemInfo.requirements.map((req, index) => (
                <div key={index} className="flex items-start">
                  <span className={`text-sm mr-2 mt-0.5 ${req.satisfied ? 'text-green-600' : 'text-red-600'}`}>
                    {req.satisfied ? '✓' : '✗'}
                  </span>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{req.name}</div>
                    <div className="text-xs text-gray-600">{req.description}</div>
                    {!req.satisfied && req.suggestion && (
                      <div className="text-xs text-red-600 mt-1">
                        建议：{req.suggestion}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 机器指纹信息 */}
      {machineFingerprint && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">机器指纹</h3>
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-sm text-gray-600 mb-1">指纹ID</div>
            <div className="font-mono text-sm break-all">{machineFingerprint}</div>
          </div>
        </div>
      )}

      {/* 特征摘要 */}
      {featuresummary && Object.keys(featuresummary).length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-700">硬件特征</h3>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(featuresummary).map(([key, value]) => (
              <div key={key} className="bg-gray-50 p-2 rounded">
                <div className="text-xs text-gray-600">{key}</div>
                <div className="text-sm font-medium truncate">{value}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};