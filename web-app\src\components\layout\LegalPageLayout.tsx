'use client';

// MySQLAi.de - LegalPageLayout法律页面布局组件
// 为法律声明页面提供统一的布局结构和视觉风格

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowLeft, Scale, Clock, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { LegalPageProps } from '@/lib/types';
import { getLegalNavigationLinks, formatLastUpdated } from '@/lib/legal';
import Breadcrumb from '@/components/ui/Breadcrumb';

interface LegalPageLayoutProps extends LegalPageProps {
  children: React.ReactNode;
  pathname: string;
}

const LegalPageLayout: React.FC<LegalPageLayoutProps> = React.memo(({
  type,
  title,
  lastUpdated,
  children,
  pathname,
  className,
  ...props
}) => {
  // 获取其他法律页面的导航链接
  const navigationLinks = React.useMemo(() => getLegalNavigationLinks(type), [type]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
      className={cn(
        'min-h-screen bg-gradient-to-br from-gray-50 to-white',
        className
      )}
      {...props}
    >
      {/* 页面容器 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* 面包屑导航 */}
        <div className="mb-6">
          <Breadcrumb pathname={pathname} />
        </div>

        {/* 返回首页链接 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="mb-8"
        >
          <Link
            href="/"
            className={cn(
              'inline-flex items-center space-x-2',
              'text-mysql-primary hover:text-mysql-primary-dark',
              'transition-colors duration-200',
              'group'
            )}
          >
            <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
            <span className="text-sm font-medium">返回首页</span>
          </Link>
        </motion.div>

        {/* 页面标题区域 */}
        <motion.header
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="mb-12 text-center"
        >
          {/* 法律图标 */}
          <div className="flex justify-center mb-6">
            <div className={cn(
              'w-16 h-16 rounded-full',
              'bg-mysql-primary/10 flex items-center justify-center',
              'border-2 border-mysql-primary/20'
            )}>
              <Scale className="w-8 h-8 text-mysql-primary" />
            </div>
          </div>

          {/* 页面标题 */}
          <h1 className={cn(
            'text-3xl md:text-4xl lg:text-5xl font-bold',
            'text-gray-900 mb-4',
            'leading-tight'
          )}>
            {title}
          </h1>

          {/* 最后更新时间 */}
          <div className="flex items-center justify-center space-x-2 text-gray-600">
            <Clock className="w-4 h-4" />
            <span className="text-sm">
              最后更新：{formatLastUpdated(lastUpdated)}
            </span>
          </div>
        </motion.header>

        {/* 主要内容区域 */}
        <motion.main
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
          className={cn(
            'bg-white rounded-xl shadow-lg',
            'border border-gray-200',
            'p-6 md:p-8 lg:p-12',
            'mb-12'
          )}
        >
          <div className={cn(
            'prose prose-lg max-w-none',
            'prose-headings:text-gray-900 prose-headings:font-semibold',
            'prose-p:text-gray-700 prose-p:leading-relaxed',
            'prose-a:text-mysql-primary prose-a:no-underline hover:prose-a:underline',
            'prose-strong:text-gray-900',
            'prose-ul:text-gray-700 prose-ol:text-gray-700',
            'prose-li:marker:text-mysql-primary'
          )}>
            {children}
          </div>
        </motion.main>

        {/* 法律页面间导航 */}
        {navigationLinks.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.4 }}
            className="mb-12"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              其他法律声明
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {navigationLinks.map((link) => (
                <motion.div
                  key={link.type}
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    href={link.href}
                    className={cn(
                      'block p-4 rounded-lg border border-gray-200',
                      'bg-white hover:bg-gray-50',
                      'transition-all duration-200',
                      'group'
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900 group-hover:text-mysql-primary">
                          {link.title}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {link.description}
                        </p>
                      </div>
                      <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-mysql-primary transition-colors" />
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.section>
        )}

        {/* 底部提示信息 */}
        <motion.footer
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.4 }}
          className={cn(
            'text-center py-8 px-6',
            'bg-gray-50 rounded-lg',
            'border border-gray-200'
          )}
        >
          <p className="text-gray-600 text-sm leading-relaxed">
            如您对本{title}有任何疑问，请通过
            <Link 
              href="/contact" 
              className="text-mysql-primary hover:text-mysql-primary-dark mx-1"
            >
              联系我们
            </Link>
            页面与我们取得联系。
          </p>
          <p className="text-gray-500 text-xs mt-2">
            MySQLAi.de 致力于为用户提供透明、公正的服务条款
          </p>
        </motion.footer>
      </div>
    </motion.div>
  );
});

LegalPageLayout.displayName = 'LegalPageLayout';

export default LegalPageLayout;
