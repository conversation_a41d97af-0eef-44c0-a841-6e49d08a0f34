// MySQLAi.de - 代码示例 API
// 提供代码示例的 CRUD 操作

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type CodeExampleInsert = Database['public']['Tables']['code_examples']['Insert'];

// GET /api/knowledge/code-examples - 获取代码示例列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const articleId = searchParams.get('articleId');
    const language = searchParams.get('language');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    // 构建查询
    let query = supabase
      .from('code_examples')
      .select(`
        *,
        knowledge_articles(id, title)
      `);

    // 按文章筛选
    if (articleId) {
      query = query.eq('article_id', articleId);
    }

    // 按语言筛选
    if (language) {
      query = query.eq('language', language);
    }

    // 排序和分页
    const offset = (page - 1) * limit;
    query = query
      .order('order_index', { ascending: true })
      .range(offset, offset + limit - 1);

    const { data: examples, error, count } = await query;

    if (error) {
      console.error('获取代码示例失败:', error);
      return NextResponse.json(
        { success: false, error: '获取代码示例失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: examples,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/code-examples - 创建新代码示例
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    if (!body.id || !body.article_id || !body.title || !body.code) {
      return NextResponse.json(
        { success: false, error: '缺少必需字段: id, article_id, title, code' },
        { status: 400 }
      );
    }

    // 验证文章是否存在
    const { data: article, error: articleError } = await supabase
      .from('knowledge_articles')
      .select('id')
      .eq('id', body.article_id)
      .single();

    if (articleError || !article) {
      return NextResponse.json(
        { success: false, error: '指定的文章不存在' },
        { status: 400 }
      );
    }

    const exampleData: CodeExampleInsert = {
      id: body.id,
      article_id: body.article_id,
      title: body.title,
      code: body.code,
      language: body.language || 'sql',
      description: body.description || null,
      order_index: body.order_index || 0
    };

    const { data, error } = await supabase
      .from('code_examples')
      .insert(exampleData)
      .select()
      .single();

    if (error) {
      console.error('创建代码示例失败:', error);
      
      if (error.code === '23505') {
        return NextResponse.json(
          { success: false, error: '代码示例ID已存在' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { success: false, error: '创建代码示例失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: '代码示例创建成功'
    }, { status: 201 });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PUT /api/knowledge/code-examples - 批量更新代码示例排序
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!Array.isArray(body.examples)) {
      return NextResponse.json(
        { success: false, error: '请提供代码示例数组' },
        { status: 400 }
      );
    }

    // 批量更新排序
    const updates = body.examples.map((example: { id: string }, index: number) =>
      supabase
        .from('code_examples')
        .update({ order_index: index })
        .eq('id', example.id)
    );

    const results = await Promise.all(updates);
    
    // 检查是否有错误
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      console.error('批量更新失败:', errors);
      return NextResponse.json(
        { success: false, error: '批量更新失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '代码示例排序更新成功'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
