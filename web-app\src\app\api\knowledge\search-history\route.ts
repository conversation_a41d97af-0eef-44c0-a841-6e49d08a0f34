// MySQLAi.de - 搜索历史API路由
// 提供搜索历史的CRUD操作

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - 获取搜索历史
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const query = searchParams.get('query');

    let queryBuilder = supabase
      .from('search_history')
      .select('*')
      .order('created_at', { ascending: false });

    // 如果提供了查询参数，过滤结果
    if (query) {
      queryBuilder = queryBuilder.ilike('query', `%${query}%`);
    }

    // 应用分页
    queryBuilder = queryBuilder.range(offset, offset + limit - 1);

    const { data: searchHistory, error } = await queryBuilder;

    if (error) {
      console.error('获取搜索历史失败:', error);
      return NextResponse.json(
        { success: false, error: '获取搜索历史失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: searchHistory || [],
      total: searchHistory?.length || 0
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST - 添加搜索历史记录
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, results_count = 0 } = body;

    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '搜索查询不能为空' },
        { status: 400 }
      );
    }

    // 获取客户端信息
    const ip_address = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      '127.0.0.1';
    const user_agent = request.headers.get('user-agent') || '';

    // 检查是否已存在相同的搜索记录（最近1小时内）
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const { data: existingRecord } = await supabase
      .from('search_history')
      .select('id')
      .eq('query', query.trim())
      .eq('ip_address', ip_address)
      .gte('created_at', oneHourAgo)
      .limit(1);

    // 如果最近1小时内已有相同搜索，不重复记录
    if (existingRecord && existingRecord.length > 0) {
      return NextResponse.json({
        success: true,
        message: '搜索记录已存在',
        data: { id: existingRecord[0].id }
      });
    }

    // 插入新的搜索记录
    const { data: newRecord, error } = await supabase
      .from('search_history')
      .insert({
        query: query.trim(),
        results_count,
        ip_address,
        user_agent
      })
      .select()
      .single();

    if (error) {
      console.error('保存搜索历史失败:', error);
      return NextResponse.json(
        { success: false, error: '保存搜索历史失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: newRecord,
      message: '搜索历史已保存'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// DELETE - 清除搜索历史
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const clearAll = searchParams.get('clearAll') === 'true';

    if (clearAll) {
      // 清除所有搜索历史（可选择性地基于IP地址）
      const ip_address = request.headers.get('x-forwarded-for') || 
                        request.headers.get('x-real-ip') || 
                        '127.0.0.1';

      const { error } = await supabase
        .from('search_history')
        .delete()
        .eq('ip_address', ip_address);

      if (error) {
        console.error('清除搜索历史失败:', error);
        return NextResponse.json(
          { success: false, error: '清除搜索历史失败' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: '搜索历史已清除'
      });
    }

    if (id) {
      // 删除特定的搜索记录
      const { error } = await supabase
        .from('search_history')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('删除搜索记录失败:', error);
        return NextResponse.json(
          { success: false, error: '删除搜索记录失败' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: '搜索记录已删除'
      });
    }

    return NextResponse.json(
      { success: false, error: '请提供要删除的记录ID或设置clearAll=true' },
      { status: 400 }
    );

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
