[{"D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\articles\\page.tsx": "1", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\articles\\[id]\\page.tsx": "2", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\categories\\page.tsx": "3", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\code-examples\\page.tsx": "4", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\new\\page.tsx": "5", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\page.tsx": "6", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\[id]\\page.tsx": "7", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\[id]\\preview\\page.tsx": "8", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\categories\\page.tsx": "9", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\code-examples\\page.tsx": "10", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\layout.tsx": "11", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\page.tsx": "12", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\layout.tsx": "13", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\login\\page.tsx": "14", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\page.tsx": "15", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\stats\\page.tsx": "16", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\admin\\auth\\route.ts": "17", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\articles\\route.ts": "18", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\articles\\[id]\\route.ts": "19", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\categories\\route.ts": "20", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\categories\\[id]\\route.ts": "21", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\code-examples\\route.ts": "22", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\search\\route.ts": "23", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\search-history\\route.ts": "24", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\stats\\route.ts": "25", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\contact\\page.tsx": "26", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\cookies\\page.tsx": "27", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\disclaimer\\page.tsx": "28", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\layout.tsx": "29", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\page.tsx": "30", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\[category]\\page.tsx": "31", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\[category]\\[item]\\page.tsx": "32", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\layout.tsx": "33", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\metadata.ts": "34", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\page.tsx": "35", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\privacy\\page.tsx": "36", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\terms\\page.tsx": "37", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\DiagramViewer.tsx": "38", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\ErrorBoundary.tsx": "39", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\ExportDialog.tsx": "40", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\index.ts": "41", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\SqlEditor.tsx": "42", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\lib\\diagram-config.ts": "43", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\lib\\export-utils.ts": "44", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\lib\\sql-parser.ts": "45", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\page.tsx": "46", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\types\\er-diagram.ts": "47", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\layout.tsx": "48", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\defaults.ts": "49", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\page.tsx": "50", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\types\\mysql-installer.ts": "51", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\page.tsx": "52", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\AdminLayout.tsx": "53", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\AdminSidebar.tsx": "54", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\ArticleForm.tsx": "55", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\BatchOperations.tsx": "56", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\CategoryForm.tsx": "57", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\CategoryList.tsx": "58", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\CodeExampleForm.tsx": "59", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\ConfirmDialog.tsx": "60", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\DataTable.tsx": "61", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\LoadingSpinner.tsx": "62", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\MarkdownEditor.tsx": "63", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\StatsChart.tsx": "64", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\StatsOverview.tsx": "65", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\CategoryPageClient.tsx": "66", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\index.ts": "67", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\ItemPageClient.tsx": "68", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeBreadcrumb.tsx": "69", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeCard.tsx": "70", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeFilter.tsx": "71", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeForm.tsx": "72", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeLayout.tsx": "73", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgePageClient.tsx": "74", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgePagination.tsx": "75", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeSearchBar.tsx": "76", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeSidebarWrapper.tsx": "77", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeTable.tsx": "78", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\RelatedItems.tsx": "79", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\RouteRedirect.tsx": "80", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\SearchBox.tsx": "81", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\SearchHistory.tsx": "82", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\ContactPageLayout.tsx": "83", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\Footer.tsx": "84", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\Header.tsx": "85", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\LegalPageLayout.tsx": "86", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\MainLayout.tsx": "87", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\About.tsx": "88", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Advantages.tsx": "89", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Contact.tsx": "90", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Features.tsx": "91", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Hero.tsx": "92", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\AdvantageCard.tsx": "93", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\AnimatedText.tsx": "94", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\Breadcrumb.tsx": "95", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\Button.tsx": "96", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\CodeBlock.tsx": "97", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\ContactButton.tsx": "98", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\DropdownMenu.tsx": "99", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\FeatureCard.tsx": "100", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\FeatureItem.tsx": "101", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\MarkdownRenderer.tsx": "102", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\OptimizedImage.tsx": "103", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\PageLoader.tsx": "104", "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\StateComponents.tsx": "105", "D:\\MysqlAi.De\\mysqlai-de\\src\\contexts\\NavigationContext.tsx": "106", "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\index.ts": "107", "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\useKnowledgeCache.ts": "108", "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\useKnowledgeData.ts": "109", "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\useKnowledgeSearch.ts": "110", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\about.ts": "111", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\admin-auth.ts": "112", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\advantages.ts": "113", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\api\\knowledge.ts": "114", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\api\\search-history.ts": "115", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\constants.ts": "116", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\database.types.ts": "117", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\features.ts": "118", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\footer.ts": "119", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\knowledge-routes.ts": "120", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\knowledge.ts": "121", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\legal.ts": "122", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\navigation.ts": "123", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\route-utils.ts": "124", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\scroll.ts": "125", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\supabase.ts": "126", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\types.ts": "127", "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\utils.ts": "128", "D:\\MysqlAi.De\\mysqlai-de\\src\\tests\\playwright-knowledge-real.spec.ts": "129", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\config-generator.ts": "130", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\download-manager.ts": "131", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\system-detection.ts": "132", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\ProgressTracker.tsx": "133", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\QuickInstaller.tsx": "134", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\InstallationGuide.tsx": "135", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\installation-guide.ts": "136", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\AdvancedWizard.tsx": "137", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\ConfigPreview.tsx": "138", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\detect-system\\route.ts": "139", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\download-links\\route.ts": "140", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\generate-config\\route.ts": "141", "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\installation-guide\\route.ts": "142", "D:\\MysqlAi.De\\mysqlai-de\\src\\tests\\mysql-installer-playwright.spec.ts": "143"}, {"size": 438, "mtime": 1751242882868, "results": "144", "hashOfConfig": "145"}, {"size": 7262, "mtime": 1751302370491, "results": "146", "hashOfConfig": "145"}, {"size": 441, "mtime": 1751243008814, "results": "147", "hashOfConfig": "145"}, {"size": 467, "mtime": 1751243129372, "results": "148", "hashOfConfig": "145"}, {"size": 4698, "mtime": 1751287929161, "results": "149", "hashOfConfig": "145"}, {"size": 12556, "mtime": 1751302355792, "results": "150", "hashOfConfig": "145"}, {"size": 7048, "mtime": 1751302314623, "results": "151", "hashOfConfig": "145"}, {"size": 8075, "mtime": 1751302477668, "results": "152", "hashOfConfig": "145"}, {"size": 14362, "mtime": 1751297693628, "results": "153", "hashOfConfig": "145"}, {"size": 17902, "mtime": 1751294406479, "results": "154", "hashOfConfig": "145"}, {"size": 469, "mtime": 1751296108034, "results": "155", "hashOfConfig": "145"}, {"size": 8658, "mtime": 1751290672568, "results": "156", "hashOfConfig": "145"}, {"size": 3420, "mtime": 1751294001170, "results": "157", "hashOfConfig": "145"}, {"size": 8437, "mtime": 1751213953785, "results": "158", "hashOfConfig": "145"}, {"size": 11382, "mtime": 1751290816536, "results": "159", "hashOfConfig": "145"}, {"size": 8031, "mtime": 1751290837834, "results": "160", "hashOfConfig": "145"}, {"size": 4108, "mtime": 1751295787195, "results": "161", "hashOfConfig": "145"}, {"size": 6083, "mtime": 1751292151151, "results": "162", "hashOfConfig": "145"}, {"size": 5805, "mtime": 1751290869253, "results": "163", "hashOfConfig": "145"}, {"size": 4645, "mtime": 1751292188801, "results": "164", "hashOfConfig": "145"}, {"size": 4987, "mtime": 1751290888136, "results": "165", "hashOfConfig": "145"}, {"size": 5093, "mtime": 1751292227519, "results": "166", "hashOfConfig": "145"}, {"size": 5872, "mtime": 1751294053910, "results": "167", "hashOfConfig": "145"}, {"size": 5314, "mtime": 1751284171800, "results": "168", "hashOfConfig": "145"}, {"size": 7550, "mtime": 1751295232288, "results": "169", "hashOfConfig": "145"}, {"size": 788, "mtime": 1751093959215, "results": "170", "hashOfConfig": "145"}, {"size": 9486, "mtime": 1751291752509, "results": "171", "hashOfConfig": "145"}, {"size": 6411, "mtime": 1751292556356, "results": "172", "hashOfConfig": "145"}, {"size": 1260, "mtime": 1751283168662, "results": "173", "hashOfConfig": "145"}, {"size": 827, "mtime": 1751099789680, "results": "174", "hashOfConfig": "145"}, {"size": 2182, "mtime": 1751292293048, "results": "175", "hashOfConfig": "145"}, {"size": 2775, "mtime": 1751292609287, "results": "176", "hashOfConfig": "145"}, {"size": 1256, "mtime": 1751292353583, "results": "177", "hashOfConfig": "145"}, {"size": 5999, "mtime": 1751294145359, "results": "178", "hashOfConfig": "145"}, {"size": 2544, "mtime": 1751292643222, "results": "179", "hashOfConfig": "145"}, {"size": 3570, "mtime": 1751292569889, "results": "180", "hashOfConfig": "145"}, {"size": 1829, "mtime": 1751292583350, "results": "181", "hashOfConfig": "145"}, {"size": 27339, "mtime": 1751295275030, "results": "182", "hashOfConfig": "145"}, {"size": 5590, "mtime": 1751295307457, "results": "183", "hashOfConfig": "145"}, {"size": 15871, "mtime": 1751295331036, "results": "184", "hashOfConfig": "145"}, {"size": 272, "mtime": 1751133199661, "results": "185", "hashOfConfig": "145"}, {"size": 8676, "mtime": 1751191166006, "results": "186", "hashOfConfig": "145"}, {"size": 21825, "mtime": 1751247972535, "results": "187", "hashOfConfig": "145"}, {"size": 7298, "mtime": 1751170765442, "results": "188", "hashOfConfig": "145"}, {"size": 31484, "mtime": 1751176187661, "results": "189", "hashOfConfig": "145"}, {"size": 18245, "mtime": 1751207202906, "results": "190", "hashOfConfig": "145"}, {"size": 5981, "mtime": 1751170650020, "results": "191", "hashOfConfig": "145"}, {"size": 399, "mtime": 1751192064563, "results": "192", "hashOfConfig": "145"}, {"size": 7498, "mtime": 1751303811129, "results": "193", "hashOfConfig": "145"}, {"size": 13942, "mtime": 1751341948006, "results": "194", "hashOfConfig": "145"}, {"size": 6920, "mtime": 1751303822161, "results": "195", "hashOfConfig": "145"}, {"size": 7036, "mtime": 1751292692787, "results": "196", "hashOfConfig": "145"}, {"size": 5668, "mtime": 1751294255842, "results": "197", "hashOfConfig": "145"}, {"size": 9932, "mtime": 1751294283421, "results": "198", "hashOfConfig": "145"}, {"size": 14829, "mtime": 1751295188917, "results": "199", "hashOfConfig": "145"}, {"size": 5476, "mtime": 1751295215716, "results": "200", "hashOfConfig": "145"}, {"size": 14411, "mtime": 1751300841304, "results": "201", "hashOfConfig": "145"}, {"size": 9753, "mtime": 1751297383173, "results": "202", "hashOfConfig": "145"}, {"size": 16345, "mtime": 1751288020725, "results": "203", "hashOfConfig": "145"}, {"size": 5256, "mtime": 1751244949169, "results": "204", "hashOfConfig": "145"}, {"size": 10030, "mtime": 1751216458697, "results": "205", "hashOfConfig": "145"}, {"size": 4288, "mtime": 1751249944062, "results": "206", "hashOfConfig": "145"}, {"size": 6067, "mtime": 1751249957004, "results": "207", "hashOfConfig": "145"}, {"size": 6715, "mtime": 1751288332314, "results": "208", "hashOfConfig": "145"}, {"size": 5582, "mtime": 1751215015884, "results": "209", "hashOfConfig": "145"}, {"size": 4941, "mtime": 1751112662306, "results": "210", "hashOfConfig": "145"}, {"size": 798, "mtime": 1751220309735, "results": "211", "hashOfConfig": "145"}, {"size": 13556, "mtime": 1751289500888, "results": "212", "hashOfConfig": "145"}, {"size": 3098, "mtime": 1751220260841, "results": "213", "hashOfConfig": "145"}, {"size": 9468, "mtime": 1751289278383, "results": "214", "hashOfConfig": "145"}, {"size": 9582, "mtime": 1751217968209, "results": "215", "hashOfConfig": "145"}, {"size": 11943, "mtime": 1751244889222, "results": "216", "hashOfConfig": "145"}, {"size": 1281, "mtime": 1751296120554, "results": "217", "hashOfConfig": "145"}, {"size": 8606, "mtime": 1751301242923, "results": "218", "hashOfConfig": "145"}, {"size": 7862, "mtime": 1751244716841, "results": "219", "hashOfConfig": "145"}, {"size": 4609, "mtime": 1751217929019, "results": "220", "hashOfConfig": "145"}, {"size": 10888, "mtime": 1751301768000, "results": "221", "hashOfConfig": "145"}, {"size": 9061, "mtime": 1751296403071, "results": "222", "hashOfConfig": "145"}, {"size": 10984, "mtime": 1751289422208, "results": "223", "hashOfConfig": "145"}, {"size": 5536, "mtime": 1751220295114, "results": "224", "hashOfConfig": "145"}, {"size": 14385, "mtime": 1751289448118, "results": "225", "hashOfConfig": "145"}, {"size": 7910, "mtime": 1751284283613, "results": "226", "hashOfConfig": "145"}, {"size": 9574, "mtime": 1751094005792, "results": "227", "hashOfConfig": "145"}, {"size": 6046, "mtime": 1751088541635, "results": "228", "hashOfConfig": "145"}, {"size": 10785, "mtime": 1751121794233, "results": "229", "hashOfConfig": "145"}, {"size": 7008, "mtime": 1751091569849, "results": "230", "hashOfConfig": "145"}, {"size": 449, "mtime": 1751214055399, "results": "231", "hashOfConfig": "145"}, {"size": 8567, "mtime": 1751084743626, "results": "232", "hashOfConfig": "145"}, {"size": 9915, "mtime": 1751085091309, "results": "233", "hashOfConfig": "145"}, {"size": 7905, "mtime": 1751094613799, "results": "234", "hashOfConfig": "145"}, {"size": 10169, "mtime": 1751119832248, "results": "235", "hashOfConfig": "145"}, {"size": 11193, "mtime": 1751117820296, "results": "236", "hashOfConfig": "145"}, {"size": 8742, "mtime": 1751085446187, "results": "237", "hashOfConfig": "145"}, {"size": 7296, "mtime": 1751083346234, "results": "238", "hashOfConfig": "145"}, {"size": 4079, "mtime": 1751091596893, "results": "239", "hashOfConfig": "145"}, {"size": 3624, "mtime": 1751249930946, "results": "240", "hashOfConfig": "145"}, {"size": 8153, "mtime": 1751110908195, "results": "241", "hashOfConfig": "145"}, {"size": 8532, "mtime": 1751085832224, "results": "242", "hashOfConfig": "145"}, {"size": 6894, "mtime": 1751121313987, "results": "243", "hashOfConfig": "145"}, {"size": 7658, "mtime": 1751083862259, "results": "244", "hashOfConfig": "145"}, {"size": 8355, "mtime": 1751084405556, "results": "245", "hashOfConfig": "145"}, {"size": 8170, "mtime": 1751302551136, "results": "246", "hashOfConfig": "145"}, {"size": 9376, "mtime": 1751119935548, "results": "247", "hashOfConfig": "145"}, {"size": 8146, "mtime": 1751086785876, "results": "248", "hashOfConfig": "145"}, {"size": 8179, "mtime": 1751277089847, "results": "249", "hashOfConfig": "145"}, {"size": 6027, "mtime": 1751100551212, "results": "250", "hashOfConfig": "145"}, {"size": 354, "mtime": 1751219845583, "results": "251", "hashOfConfig": "145"}, {"size": 12239, "mtime": 1751288373365, "results": "252", "hashOfConfig": "145"}, {"size": 14970, "mtime": 1751293565506, "results": "253", "hashOfConfig": "145"}, {"size": 13974, "mtime": 1751288495220, "results": "254", "hashOfConfig": "145"}, {"size": 8661, "mtime": 1751090306466, "results": "255", "hashOfConfig": "145"}, {"size": 4747, "mtime": 1751295831010, "results": "256", "hashOfConfig": "145"}, {"size": 8551, "mtime": 1751085046795, "results": "257", "hashOfConfig": "145"}, {"size": 10888, "mtime": 1751301465537, "results": "258", "hashOfConfig": "145"}, {"size": 6895, "mtime": 1751284247835, "results": "259", "hashOfConfig": "145"}, {"size": 11949, "mtime": 1751291085065, "results": "260", "hashOfConfig": "145"}, {"size": 7530, "mtime": 1751250486578, "results": "261", "hashOfConfig": "145"}, {"size": 8597, "mtime": 1751083907126, "results": "262", "hashOfConfig": "145"}, {"size": 7825, "mtime": 1751086268629, "results": "263", "hashOfConfig": "145"}, {"size": 7454, "mtime": 1751302244222, "results": "264", "hashOfConfig": "145"}, {"size": 7499, "mtime": 1751291106174, "results": "265", "hashOfConfig": "145"}, {"size": 19762, "mtime": 1751089804642, "results": "266", "hashOfConfig": "145"}, {"size": 8857, "mtime": 1751122886844, "results": "267", "hashOfConfig": "145"}, {"size": 6943, "mtime": 1751291120326, "results": "268", "hashOfConfig": "145"}, {"size": 8776, "mtime": 1751086986953, "results": "269", "hashOfConfig": "145"}, {"size": 3708, "mtime": 1751207532911, "results": "270", "hashOfConfig": "145"}, {"size": 7836, "mtime": 1751287516816, "results": "271", "hashOfConfig": "145"}, {"size": 13239, "mtime": 1751288600026, "results": "272", "hashOfConfig": "145"}, {"size": 12416, "mtime": 1751261324224, "results": "273", "hashOfConfig": "145"}, {"size": 13160, "mtime": 1751304385918, "results": "274", "hashOfConfig": "145"}, {"size": 16331, "mtime": 1751341124479, "results": "275", "hashOfConfig": "145"}, {"size": 10101, "mtime": 1751304090291, "results": "276", "hashOfConfig": "145"}, {"size": 8515, "mtime": 1751304635773, "results": "277", "hashOfConfig": "145"}, {"size": 18550, "mtime": 1751341895651, "results": "278", "hashOfConfig": "145"}, {"size": 18128, "mtime": 1751305142807, "results": "279", "hashOfConfig": "145"}, {"size": 20012, "mtime": 1751305182853, "results": "280", "hashOfConfig": "145"}, {"size": 21660, "mtime": 1751340378977, "results": "281", "hashOfConfig": "145"}, {"size": 9186, "mtime": 1751340305715, "results": "282", "hashOfConfig": "145"}, {"size": 8191, "mtime": 1751341141808, "results": "283", "hashOfConfig": "145"}, {"size": 7331, "mtime": 1751341158317, "results": "284", "hashOfConfig": "145"}, {"size": 7104, "mtime": 1751340548876, "results": "285", "hashOfConfig": "145"}, {"size": 6802, "mtime": 1751341193918, "results": "286", "hashOfConfig": "145"}, {"size": 8300, "mtime": 1751343091402, "results": "287", "hashOfConfig": "145"}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p7qscd", {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 23, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\articles\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\articles\\[id]\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\categories\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\code-examples\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\new\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\[id]\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\articles\\[id]\\preview\\page.tsx", ["717", "718"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\categories\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\code-examples\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\layout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\knowledge\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\layout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\login\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\admin\\stats\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\admin\\auth\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\articles\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\articles\\[id]\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\categories\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\categories\\[id]\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\code-examples\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\search\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\search-history\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\knowledge\\stats\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\contact\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\cookies\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\disclaimer\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\layout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\[category]\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\knowledge\\[category]\\[item]\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\layout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\metadata.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\privacy\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\terms\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\DiagramViewer.tsx", ["719", "720", "721"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\ErrorBoundary.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\ExportDialog.tsx", ["722", "723", "724"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\index.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\components\\SqlEditor.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\lib\\diagram-config.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\lib\\export-utils.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\lib\\sql-parser.ts", ["725", "726", "727", "728", "729", "730", "731", "732", "733"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\page.tsx", ["734", "735", "736"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\er-diagram\\types\\er-diagram.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\layout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\defaults.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\types\\mysql-installer.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\page.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\AdminLayout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\AdminSidebar.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\ArticleForm.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\BatchOperations.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\CategoryForm.tsx", ["737"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\CategoryList.tsx", ["738", "739", "740", "741", "742"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\CodeExampleForm.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\ConfirmDialog.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\DataTable.tsx", ["743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\LoadingSpinner.tsx", ["757"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\MarkdownEditor.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\StatsChart.tsx", ["758", "759", "760", "761"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\admin\\StatsOverview.tsx", ["762"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\CategoryPageClient.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\index.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\ItemPageClient.tsx", ["763", "764", "765", "766", "767"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeBreadcrumb.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeCard.tsx", ["768", "769"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeFilter.tsx", ["770", "771", "772", "773"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeForm.tsx", ["774", "775", "776", "777", "778", "779"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeLayout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgePageClient.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgePagination.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeSearchBar.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeSidebarWrapper.tsx", ["780", "781", "782", "783", "784", "785", "786", "787"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\KnowledgeTable.tsx", ["788", "789", "790", "791", "792", "793", "794", "795", "796", "797"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\RelatedItems.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\RouteRedirect.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\SearchBox.tsx", ["798", "799", "800", "801"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\knowledge\\SearchHistory.tsx", ["802"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\ContactPageLayout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\Footer.tsx", ["803", "804"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\Header.tsx", ["805", "806", "807", "808"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\LegalPageLayout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\layout\\MainLayout.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\About.tsx", ["809"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Advantages.tsx", ["810"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Contact.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Features.tsx", ["811"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\sections\\Hero.tsx", ["812", "813"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\AdvantageCard.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\AnimatedText.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\Breadcrumb.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\Button.tsx", ["814", "815", "816", "817"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\CodeBlock.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\ContactButton.tsx", ["818"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\DropdownMenu.tsx", ["819"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\FeatureCard.tsx", ["820"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\FeatureItem.tsx", ["821"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\MarkdownRenderer.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\OptimizedImage.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\PageLoader.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\components\\ui\\StateComponents.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\contexts\\NavigationContext.tsx", ["822", "823"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\index.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\useKnowledgeCache.ts", ["824", "825", "826", "827", "828", "829", "830", "831"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\useKnowledgeData.ts", ["832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\hooks\\useKnowledgeSearch.ts", ["855", "856", "857", "858"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\about.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\admin-auth.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\advantages.ts", ["859", "860"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\api\\knowledge.ts", ["861", "862"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\api\\search-history.ts", ["863"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\constants.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\database.types.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\features.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\footer.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\knowledge-routes.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\knowledge.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\legal.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\navigation.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\route-utils.ts", ["864"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\scroll.ts", ["865", "866", "867", "868"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\supabase.ts", ["869", "870", "871"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\types.ts", ["872"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\lib\\utils.ts", ["873", "874", "875", "876", "877", "878", "879", "880", "881"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\tests\\playwright-knowledge-real.spec.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\config-generator.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\download-manager.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\system-detection.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\ProgressTracker.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\QuickInstaller.tsx", ["882"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\InstallationGuide.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\lib\\installation-guide.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\AdvancedWizard.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\tools\\mysql-installer\\components\\ConfigPreview.tsx", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\detect-system\\route.ts", ["883"], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\download-links\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\generate-config\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\app\\api\\mysql-installer\\installation-guide\\route.ts", [], [], "D:\\MysqlAi.De\\mysqlai-de\\src\\tests\\mysql-installer-playwright.spec.ts", ["884", "885"], [], {"ruleId": "886", "severity": 2, "message": "887", "line": 13, "column": 3, "nodeType": null, "messageId": "888", "endLine": 13, "endColumn": 9}, {"ruleId": "886", "severity": 2, "message": "889", "line": 16, "column": 3, "nodeType": null, "messageId": "888", "endLine": 16, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "891", "line": 195, "column": 6, "nodeType": "892", "endLine": 195, "endColumn": 8, "suggestions": "893"}, {"ruleId": "890", "severity": 1, "message": "894", "line": 443, "column": 6, "nodeType": "892", "endLine": 443, "endColumn": 8, "suggestions": "895"}, {"ruleId": "890", "severity": 1, "message": "896", "line": 605, "column": 6, "nodeType": "892", "endLine": 605, "endColumn": 32, "suggestions": "897"}, {"ruleId": "890", "severity": 1, "message": "898", "line": 107, "column": 6, "nodeType": "892", "endLine": 107, "endColumn": 27, "suggestions": "899"}, {"ruleId": "890", "severity": 1, "message": "898", "line": 129, "column": 6, "nodeType": "892", "endLine": 129, "endColumn": 27, "suggestions": "900"}, {"ruleId": "901", "severity": 1, "message": "902", "line": 263, "column": 19, "nodeType": "903", "endLine": 263, "endColumn": 60}, {"ruleId": "904", "severity": 2, "message": "905", "line": 340, "column": 70, "nodeType": "906", "messageId": "907", "endLine": 340, "endColumn": 73, "suggestions": "908"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 342, "column": 40, "nodeType": "906", "messageId": "907", "endLine": 342, "endColumn": 43, "suggestions": "909"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 406, "column": 47, "nodeType": "906", "messageId": "907", "endLine": 406, "endColumn": 50, "suggestions": "910"}, {"ruleId": "886", "severity": 2, "message": "911", "line": 411, "column": 14, "nodeType": null, "messageId": "888", "endLine": 411, "endColumn": 19}, {"ruleId": "886", "severity": 2, "message": "911", "line": 432, "column": 14, "nodeType": null, "messageId": "888", "endLine": 432, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "905", "line": 454, "column": 36, "nodeType": "906", "messageId": "907", "endLine": 454, "endColumn": 39, "suggestions": "912"}, {"ruleId": "886", "severity": 2, "message": "913", "line": 482, "column": 51, "nodeType": null, "messageId": "888", "endLine": 482, "endColumn": 59}, {"ruleId": "904", "severity": 2, "message": "905", "line": 552, "column": 38, "nodeType": "906", "messageId": "907", "endLine": 552, "endColumn": 41, "suggestions": "914"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 819, "column": 44, "nodeType": "906", "messageId": "907", "endLine": 819, "endColumn": 47, "suggestions": "915"}, {"ruleId": "886", "severity": 2, "message": "916", "line": 152, "column": 9, "nodeType": null, "messageId": "888", "endLine": 152, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "917", "line": 226, "column": 6, "nodeType": "892", "endLine": 226, "endColumn": 15, "suggestions": "918"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 259, "column": 57, "nodeType": "906", "messageId": "907", "endLine": 259, "endColumn": 60, "suggestions": "919"}, {"ruleId": "886", "severity": 2, "message": "920", "line": 8, "column": 10, "nodeType": null, "messageId": "888", "endLine": 8, "endColumn": 16}, {"ruleId": "886", "severity": 2, "message": "921", "line": 14, "column": 3, "nodeType": null, "messageId": "888", "endLine": 14, "endColumn": 15}, {"ruleId": "886", "severity": 2, "message": "922", "line": 15, "column": 3, "nodeType": null, "messageId": "888", "endLine": 15, "endColumn": 6}, {"ruleId": "904", "severity": 2, "message": "905", "line": 136, "column": 26, "nodeType": "906", "messageId": "907", "endLine": 136, "endColumn": 29, "suggestions": "923"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 138, "column": 25, "nodeType": "906", "messageId": "907", "endLine": 138, "endColumn": 28, "suggestions": "924"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 140, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 140, "endColumn": 24, "suggestions": "925"}, {"ruleId": "886", "severity": 2, "message": "926", "line": 7, "column": 18, "nodeType": null, "messageId": "888", "endLine": 7, "endColumn": 33}, {"ruleId": "886", "severity": 2, "message": "922", "line": 14, "column": 3, "nodeType": null, "messageId": "888", "endLine": 14, "endColumn": 6}, {"ruleId": "886", "severity": 2, "message": "887", "line": 15, "column": 3, "nodeType": null, "messageId": "888", "endLine": 15, "endColumn": 9}, {"ruleId": "904", "severity": 2, "message": "905", "line": 27, "column": 20, "nodeType": "906", "messageId": "907", "endLine": 27, "endColumn": 23, "suggestions": "927"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 27, "column": 33, "nodeType": "906", "messageId": "907", "endLine": 27, "endColumn": 36, "suggestions": "928"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 33, "column": 9, "nodeType": "906", "messageId": "907", "endLine": 33, "endColumn": 12, "suggestions": "929"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 42, "column": 39, "nodeType": "906", "messageId": "907", "endLine": 42, "endColumn": 42, "suggestions": "930"}, {"ruleId": "886", "severity": 2, "message": "931", "line": 55, "column": 3, "nodeType": null, "messageId": "888", "endLine": 55, "endColumn": 11}, {"ruleId": "886", "severity": 2, "message": "932", "line": 65, "column": 10, "nodeType": null, "messageId": "888", "endLine": 65, "endColumn": 17}, {"ruleId": "886", "severity": 2, "message": "933", "line": 65, "column": 19, "nodeType": null, "messageId": "888", "endLine": 65, "endColumn": 29}, {"ruleId": "904", "severity": 2, "message": "905", "line": 65, "column": 57, "nodeType": "906", "messageId": "907", "endLine": 65, "endColumn": 60, "suggestions": "934"}, {"ruleId": "886", "severity": 2, "message": "935", "line": 67, "column": 26, "nodeType": null, "messageId": "888", "endLine": 67, "endColumn": 43}, {"ruleId": "904", "severity": 2, "message": "905", "line": 170, "column": 30, "nodeType": "906", "messageId": "907", "endLine": 170, "endColumn": 33, "suggestions": "936"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 195, "column": 37, "nodeType": "906", "messageId": "907", "endLine": 195, "endColumn": 40, "suggestions": "937"}, {"ruleId": "886", "severity": 2, "message": "938", "line": 8, "column": 19, "nodeType": null, "messageId": "888", "endLine": 8, "endColumn": 28}, {"ruleId": "904", "severity": 2, "message": "905", "line": 31, "column": 18, "nodeType": "906", "messageId": "907", "endLine": 31, "endColumn": 21, "suggestions": "939"}, {"ruleId": "886", "severity": 2, "message": "940", "line": 45, "column": 7, "nodeType": null, "messageId": "888", "endLine": 45, "endColumn": 21}, {"ruleId": "904", "severity": 2, "message": "905", "line": 79, "column": 54, "nodeType": "906", "messageId": "907", "endLine": 79, "endColumn": 57, "suggestions": "941"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 84, "column": 32, "nodeType": "906", "messageId": "907", "endLine": 84, "endColumn": 35, "suggestions": "942"}, {"ruleId": "886", "severity": 2, "message": "943", "line": 14, "column": 3, "nodeType": null, "messageId": "888", "endLine": 14, "endColumn": 8}, {"ruleId": "886", "severity": 2, "message": "944", "line": 11, "column": 3, "nodeType": null, "messageId": "888", "endLine": 11, "endColumn": 11}, {"ruleId": "886", "severity": 2, "message": "945", "line": 12, "column": 3, "nodeType": null, "messageId": "888", "endLine": 12, "endColumn": 7}, {"ruleId": "886", "severity": 2, "message": "889", "line": 14, "column": 3, "nodeType": null, "messageId": "888", "endLine": 14, "endColumn": 7}, {"ruleId": "886", "severity": 2, "message": "946", "line": 17, "column": 3, "nodeType": null, "messageId": "888", "endLine": 17, "endColumn": 12}, {"ruleId": "886", "severity": 2, "message": "911", "line": 62, "column": 16, "nodeType": null, "messageId": "888", "endLine": 62, "endColumn": 21}, {"ruleId": "886", "severity": 2, "message": "945", "line": 11, "column": 3, "nodeType": null, "messageId": "888", "endLine": 11, "endColumn": 7}, {"ruleId": "886", "severity": 2, "message": "947", "line": 21, "column": 30, "nodeType": null, "messageId": "888", "endLine": 21, "endColumn": 43}, {"ruleId": "886", "severity": 2, "message": "948", "line": 12, "column": 3, "nodeType": null, "messageId": "888", "endLine": 12, "endColumn": 11}, {"ruleId": "886", "severity": 2, "message": "949", "line": 13, "column": 3, "nodeType": null, "messageId": "888", "endLine": 13, "endColumn": 6}, {"ruleId": "886", "severity": 2, "message": "950", "line": 14, "column": 3, "nodeType": null, "messageId": "888", "endLine": 14, "endColumn": 13}, {"ruleId": "886", "severity": 2, "message": "951", "line": 15, "column": 3, "nodeType": null, "messageId": "888", "endLine": 15, "endColumn": 12}, {"ruleId": "886", "severity": 2, "message": "952", "line": 10, "column": 3, "nodeType": null, "messageId": "888", "endLine": 10, "endColumn": 14}, {"ruleId": "886", "severity": 2, "message": "953", "line": 14, "column": 3, "nodeType": null, "messageId": "888", "endLine": 14, "endColumn": 4}, {"ruleId": "904", "severity": 2, "message": "905", "line": 34, "column": 22, "nodeType": "906", "messageId": "907", "endLine": 34, "endColumn": 25, "suggestions": "954"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 44, "column": 26, "nodeType": "906", "messageId": "907", "endLine": 44, "endColumn": 29, "suggestions": "955"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 46, "column": 35, "nodeType": "906", "messageId": "907", "endLine": 46, "endColumn": 38, "suggestions": "956"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 150, "column": 50, "nodeType": "906", "messageId": "907", "endLine": 150, "endColumn": 53, "suggestions": "957"}, {"ruleId": "886", "severity": 2, "message": "958", "line": 8, "column": 18, "nodeType": null, "messageId": "888", "endLine": 8, "endColumn": 23}, {"ruleId": "886", "severity": 2, "message": "959", "line": 12, "column": 10, "nodeType": null, "messageId": "888", "endLine": 12, "endColumn": 41}, {"ruleId": "886", "severity": 2, "message": "960", "line": 27, "column": 10, "nodeType": null, "messageId": "888", "endLine": 27, "endColumn": 23}, {"ruleId": "904", "severity": 2, "message": "905", "line": 28, "column": 75, "nodeType": "906", "messageId": "907", "endLine": 28, "endColumn": 78, "suggestions": "961"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 57, "column": 29, "nodeType": "906", "messageId": "907", "endLine": 57, "endColumn": 32, "suggestions": "962"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 58, "column": 17, "nodeType": "906", "messageId": "907", "endLine": 58, "endColumn": 20, "suggestions": "963"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 58, "column": 25, "nodeType": "906", "messageId": "907", "endLine": 58, "endColumn": 28, "suggestions": "964"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 142, "column": 46, "nodeType": "906", "messageId": "907", "endLine": 142, "endColumn": 49, "suggestions": "965"}, {"ruleId": "886", "severity": 2, "message": "966", "line": 6, "column": 17, "nodeType": null, "messageId": "888", "endLine": 6, "endColumn": 25}, {"ruleId": "886", "severity": 2, "message": "920", "line": 7, "column": 10, "nodeType": null, "messageId": "888", "endLine": 7, "endColumn": 16}, {"ruleId": "904", "severity": 2, "message": "905", "line": 18, "column": 27, "nodeType": "906", "messageId": "907", "endLine": 18, "endColumn": 30, "suggestions": "967"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 23, "column": 20, "nodeType": "906", "messageId": "907", "endLine": 23, "endColumn": 23, "suggestions": "968"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 28, "column": 27, "nodeType": "906", "messageId": "907", "endLine": 28, "endColumn": 30, "suggestions": "969"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 43, "column": 35, "nodeType": "906", "messageId": "907", "endLine": 43, "endColumn": 38, "suggestions": "970"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 58, "column": 44, "nodeType": "906", "messageId": "907", "endLine": 58, "endColumn": 47, "suggestions": "971"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 78, "column": 23, "nodeType": "906", "messageId": "907", "endLine": 78, "endColumn": 26, "suggestions": "972"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 221, "column": 42, "nodeType": "906", "messageId": "907", "endLine": 221, "endColumn": 45, "suggestions": "973"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 222, "column": 28, "nodeType": "906", "messageId": "907", "endLine": 222, "endColumn": 31, "suggestions": "974"}, {"ruleId": "886", "severity": 2, "message": "975", "line": 9, "column": 16, "nodeType": null, "messageId": "888", "endLine": 9, "endColumn": 26}, {"ruleId": "886", "severity": 2, "message": "976", "line": 12, "column": 29, "nodeType": null, "messageId": "888", "endLine": 12, "endColumn": 49}, {"ruleId": "886", "severity": 2, "message": "977", "line": 48, "column": 4, "nodeType": null, "messageId": "888", "endLine": 48, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "978", "line": 127, "column": 27, "nodeType": "979", "endLine": 127, "endColumn": 38}, {"ruleId": "890", "severity": 1, "message": "980", "line": 96, "column": 6, "nodeType": "892", "endLine": 96, "endColumn": 16, "suggestions": "981"}, {"ruleId": "886", "severity": 2, "message": "982", "line": 56, "column": 7, "nodeType": null, "messageId": "888", "endLine": 56, "endColumn": 19}, {"ruleId": "886", "severity": 2, "message": "983", "line": 84, "column": 7, "nodeType": null, "messageId": "888", "endLine": 84, "endColumn": 19}, {"ruleId": "886", "severity": 2, "message": "984", "line": 9, "column": 29, "nodeType": null, "messageId": "888", "endLine": 9, "endColumn": 40}, {"ruleId": "886", "severity": 2, "message": "985", "line": 12, "column": 10, "nodeType": null, "messageId": "888", "endLine": 12, "endColumn": 25}, {"ruleId": "886", "severity": 2, "message": "986", "line": 13, "column": 27, "nodeType": null, "messageId": "888", "endLine": 13, "endColumn": 41}, {"ruleId": "886", "severity": 2, "message": "987", "line": 13, "column": 43, "nodeType": null, "messageId": "888", "endLine": 13, "endColumn": 53}, {"ruleId": "886", "severity": 2, "message": "988", "line": 10, "column": 10, "nodeType": null, "messageId": "888", "endLine": 10, "endColumn": 20}, {"ruleId": "886", "severity": 2, "message": "989", "line": 10, "column": 10, "nodeType": null, "messageId": "888", "endLine": 10, "endColumn": 25}, {"ruleId": "886", "severity": 2, "message": "990", "line": 10, "column": 10, "nodeType": null, "messageId": "888", "endLine": 10, "endColumn": 23}, {"ruleId": "886", "severity": 2, "message": "991", "line": 10, "column": 10, "nodeType": null, "messageId": "888", "endLine": 10, "endColumn": 21}, {"ruleId": "886", "severity": 2, "message": "992", "line": 11, "column": 10, "nodeType": null, "messageId": "888", "endLine": 11, "endColumn": 19}, {"ruleId": "904", "severity": 2, "message": "905", "line": 120, "column": 25, "nodeType": "906", "messageId": "907", "endLine": 120, "endColumn": 28, "suggestions": "993"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 121, "column": 23, "nodeType": "906", "messageId": "907", "endLine": 121, "endColumn": 26, "suggestions": "994"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 138, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 138, "endColumn": 24, "suggestions": "995"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 139, "column": 19, "nodeType": "906", "messageId": "907", "endLine": 139, "endColumn": 22, "suggestions": "996"}, {"ruleId": "886", "severity": 2, "message": "997", "line": 222, "column": 3, "nodeType": null, "messageId": "888", "endLine": 222, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "998", "line": 134, "column": 6, "nodeType": "892", "endLine": 134, "endColumn": 14, "suggestions": "999"}, {"ruleId": "886", "severity": 2, "message": "1000", "line": 27, "column": 3, "nodeType": null, "messageId": "888", "endLine": 27, "endColumn": 8}, {"ruleId": "886", "severity": 2, "message": "1000", "line": 31, "column": 3, "nodeType": null, "messageId": "888", "endLine": 31, "endColumn": 8}, {"ruleId": "886", "severity": 2, "message": "1001", "line": 7, "column": 10, "nodeType": null, "messageId": "888", "endLine": 7, "endColumn": 27}, {"ruleId": "886", "severity": 2, "message": "947", "line": 7, "column": 29, "nodeType": null, "messageId": "888", "endLine": 7, "endColumn": 42}, {"ruleId": "904", "severity": 2, "message": "905", "line": 10, "column": 25, "nodeType": "906", "messageId": "907", "endLine": 10, "endColumn": 28, "suggestions": "1002"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 64, "column": 59, "nodeType": "906", "messageId": "907", "endLine": 64, "endColumn": 62, "suggestions": "1003"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 107, "column": 56, "nodeType": "906", "messageId": "907", "endLine": 107, "endColumn": 59, "suggestions": "1004"}, {"ruleId": "886", "severity": 2, "message": "1005", "line": 132, "column": 9, "nodeType": null, "messageId": "888", "endLine": 132, "endColumn": 23}, {"ruleId": "904", "severity": 2, "message": "905", "line": 135, "column": 52, "nodeType": "906", "messageId": "907", "endLine": 135, "endColumn": 55, "suggestions": "1006"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 170, "column": 16, "nodeType": "906", "messageId": "907", "endLine": 170, "endColumn": 19, "suggestions": "1007"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 339, "column": 55, "nodeType": "906", "messageId": "907", "endLine": 339, "endColumn": 58, "suggestions": "1008"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 374, "column": 55, "nodeType": "906", "messageId": "907", "endLine": 374, "endColumn": 58, "suggestions": "1009"}, {"ruleId": "886", "severity": 2, "message": "1010", "line": 10, "column": 6, "nodeType": null, "messageId": "888", "endLine": 10, "endColumn": 22}, {"ruleId": "886", "severity": 2, "message": "1001", "line": 11, "column": 6, "nodeType": null, "messageId": "888", "endLine": 11, "endColumn": 23}, {"ruleId": "886", "severity": 2, "message": "1011", "line": 12, "column": 6, "nodeType": null, "messageId": "888", "endLine": 12, "endColumn": 17}, {"ruleId": "886", "severity": 2, "message": "1012", "line": 15, "column": 6, "nodeType": null, "messageId": "888", "endLine": 15, "endColumn": 28}, {"ruleId": "886", "severity": 2, "message": "1013", "line": 16, "column": 6, "nodeType": null, "messageId": "888", "endLine": 16, "endColumn": 29}, {"ruleId": "886", "severity": 2, "message": "1014", "line": 17, "column": 6, "nodeType": null, "messageId": "888", "endLine": 17, "endColumn": 23}, {"ruleId": "886", "severity": 2, "message": "1015", "line": 25, "column": 11, "nodeType": null, "messageId": "888", "endLine": 25, "endColumn": 22}, {"ruleId": "904", "severity": 2, "message": "905", "line": 115, "column": 38, "nodeType": "906", "messageId": "907", "endLine": 115, "endColumn": 41, "suggestions": "1016"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 117, "column": 12, "nodeType": "906", "messageId": "907", "endLine": 117, "endColumn": 15, "suggestions": "1017"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 194, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 194, "endColumn": 24, "suggestions": "1018"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 298, "column": 49, "nodeType": "906", "messageId": "907", "endLine": 298, "endColumn": 52, "suggestions": "1019"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 332, "column": 19, "nodeType": "906", "messageId": "907", "endLine": 332, "endColumn": 22, "suggestions": "1020"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 347, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 347, "endColumn": 24, "suggestions": "1021"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 369, "column": 45, "nodeType": "906", "messageId": "907", "endLine": 369, "endColumn": 48, "suggestions": "1022"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 383, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 383, "endColumn": 24, "suggestions": "1023"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 397, "column": 49, "nodeType": "906", "messageId": "907", "endLine": 397, "endColumn": 52, "suggestions": "1024"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 414, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 414, "endColumn": 24, "suggestions": "1025"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 422, "column": 69, "nodeType": "906", "messageId": "907", "endLine": 422, "endColumn": 72, "suggestions": "1026"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 439, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 439, "endColumn": 24, "suggestions": "1027"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 464, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 464, "endColumn": 24, "suggestions": "1028"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 473, "column": 45, "nodeType": "906", "messageId": "907", "endLine": 473, "endColumn": 48, "suggestions": "1029"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 477, "column": 59, "nodeType": "906", "messageId": "907", "endLine": 477, "endColumn": 62, "suggestions": "1030"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 478, "column": 47, "nodeType": "906", "messageId": "907", "endLine": 478, "endColumn": 50, "suggestions": "1031"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 175, "column": 52, "nodeType": "906", "messageId": "907", "endLine": 175, "endColumn": 55, "suggestions": "1032"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 259, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 259, "endColumn": 24, "suggestions": "1033"}, {"ruleId": "890", "severity": 1, "message": "1034", "line": 271, "column": 6, "nodeType": "892", "endLine": 271, "endColumn": 93, "suggestions": "1035"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 431, "column": 21, "nodeType": "906", "messageId": "907", "endLine": 431, "endColumn": 24, "suggestions": "1036"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 311, "column": 50, "nodeType": "906", "messageId": "907", "endLine": 311, "endColumn": 53, "suggestions": "1037"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 319, "column": 53, "nodeType": "906", "messageId": "907", "endLine": 319, "endColumn": 56, "suggestions": "1038"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 11, "column": 27, "nodeType": "906", "messageId": "907", "endLine": 11, "endColumn": 30, "suggestions": "1039"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 341, "column": 80, "nodeType": "906", "messageId": "907", "endLine": 341, "endColumn": 83, "suggestions": "1040"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 7, "column": 27, "nodeType": "906", "messageId": "907", "endLine": 7, "endColumn": 30, "suggestions": "1041"}, {"ruleId": "886", "severity": 2, "message": "1042", "line": 40, "column": 48, "nodeType": null, "messageId": "888", "endLine": 40, "endColumn": 61}, {"ruleId": "886", "severity": 2, "message": "1043", "line": 242, "column": 3, "nodeType": null, "messageId": "888", "endLine": 242, "endColumn": 11}, {"ruleId": "886", "severity": 2, "message": "1044", "line": 243, "column": 3, "nodeType": null, "messageId": "888", "endLine": 243, "endColumn": 7}, {"ruleId": "904", "severity": 2, "message": "905", "line": 243, "column": 9, "nodeType": "906", "messageId": "907", "endLine": 243, "endColumn": 12, "suggestions": "1045"}, {"ruleId": "886", "severity": 2, "message": "1046", "line": 368, "column": 3, "nodeType": null, "messageId": "888", "endLine": 368, "endColumn": 8}, {"ruleId": "904", "severity": 2, "message": "905", "line": 56, "column": 77, "nodeType": "906", "messageId": "907", "endLine": 56, "endColumn": 80, "suggestions": "1047"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 94, "column": 23, "nodeType": "906", "messageId": "907", "endLine": 94, "endColumn": 26, "suggestions": "1048"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 95, "column": 35, "nodeType": "906", "messageId": "907", "endLine": 95, "endColumn": 38, "suggestions": "1049"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 183, "column": 34, "nodeType": "906", "messageId": "907", "endLine": 183, "endColumn": 37, "suggestions": "1050"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 52, "column": 46, "nodeType": "906", "messageId": "907", "endLine": 52, "endColumn": 49, "suggestions": "1051"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 52, "column": 56, "nodeType": "906", "messageId": "907", "endLine": 52, "endColumn": 59, "suggestions": "1052"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 64, "column": 46, "nodeType": "906", "messageId": "907", "endLine": 64, "endColumn": 49, "suggestions": "1053"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 64, "column": 56, "nodeType": "906", "messageId": "907", "endLine": 64, "endColumn": 59, "suggestions": "1054"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 165, "column": 37, "nodeType": "906", "messageId": "907", "endLine": 165, "endColumn": 40, "suggestions": "1055"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 165, "column": 70, "nodeType": "906", "messageId": "907", "endLine": 165, "endColumn": 73, "suggestions": "1056"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 165, "column": 88, "nodeType": "906", "messageId": "907", "endLine": 165, "endColumn": 91, "suggestions": "1057"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 180, "column": 37, "nodeType": "906", "messageId": "907", "endLine": 180, "endColumn": 40, "suggestions": "1058"}, {"ruleId": "904", "severity": 2, "message": "905", "line": 180, "column": 63, "nodeType": "906", "messageId": "907", "endLine": 180, "endColumn": 66, "suggestions": "1059"}, {"ruleId": "890", "severity": 1, "message": "1060", "line": 167, "column": 6, "nodeType": "892", "endLine": 167, "endColumn": 33, "suggestions": "1061"}, {"ruleId": "886", "severity": 2, "message": "1062", "line": 107, "column": 31, "nodeType": null, "messageId": "888", "endLine": 107, "endColumn": 34}, {"ruleId": "886", "severity": 2, "message": "911", "line": 157, "column": 14, "nodeType": null, "messageId": "888", "endLine": 157, "endColumn": 19}, {"ruleId": "886", "severity": 2, "message": "1063", "line": 218, "column": 31, "nodeType": null, "messageId": "888", "endLine": 218, "endColumn": 35}, "@typescript-eslint/no-unused-vars", "'EyeOff' is defined but never used.", "unusedVar", "'User' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'isInitialized'. Either include it or remove the dependency array.", "ArrayExpression", ["1064"], "React Hook useCallback has a missing dependency: 'analyzeDirectRelationships'. Either include it or remove the dependency array.", ["1065"], "React Hook useEffect has a missing dependency: 'updateDiagram'. Either include it or remove the dependency array.", ["1066"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["1067"], ["1068"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1069", "1070"], ["1071", "1072"], ["1073", "1074"], "'error' is defined but never used.", ["1075", "1076"], "'_columns' is defined but never used.", ["1077", "1078"], ["1079", "1080"], "'debouncedParseSql' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'scrollToERDiagram'. Either include it or remove the dependency array.", ["1081"], ["1082", "1083"], "'motion' is defined but never used.", "'MoreVertical' is defined but never used.", "'Eye' is defined but never used.", ["1084", "1085"], ["1086", "1087"], ["1088", "1089"], "'AnimatePresence' is defined but never used.", ["1090", "1091"], ["1092", "1093"], ["1094", "1095"], ["1096", "1097"], "'onFilter' is defined but never used.", "'filters' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", ["1098", "1099"], "'setVisibleColumns' is assigned a value but never used.", ["1100", "1101"], ["1102", "1103"], "'RefreshCw' is defined but never used.", ["1104", "1105"], "'DEFAULT_COLORS' is assigned a value but never used.", ["1106", "1107"], ["1108", "1109"], "'Users' is defined but never used.", "'BookOpen' is defined but never used.", "'Star' is defined but never used.", "'ArrowLeft' is defined but never used.", "'KnowledgeItem' is defined but never used.", "'Calendar' is defined but never used.", "'Tag' is defined but never used.", "'FolderOpen' is defined but never used.", "'BarChart3' is defined but never used.", "'CheckCircle' is defined but never used.", "'X' is defined but never used.", ["1110", "1111"], ["1112", "1113"], ["1114", "1115"], ["1116", "1117"], "'Clock' is defined but never used.", "'mapDatabaseCategoriesToFrontend' is defined but never used.", "'searchFocused' is assigned a value but never used.", ["1118", "1119"], ["1120", "1121"], ["1122", "1123"], ["1124", "1125"], ["1126", "1127"], "'useState' is defined but never used.", ["1128", "1129"], ["1130", "1131"], ["1132", "1133"], ["1134", "1135"], ["1136", "1137"], ["1138", "1139"], ["1140", "1141"], ["1142", "1143"], "'FuseResult' is defined but never used.", "'searchKnowledgeItems' is defined but never used.", "'ref' is defined but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", "React Hook useEffect has a missing dependency: 'loadHistory'. Either include it or remove the dependency array.", ["1144"], "'SOCIAL_LINKS' is assigned a value but never used.", "'CONTACT_INFO' is assigned a value but never used.", "'ChevronDown' is defined but never used.", "'NavigationProps' is defined but never used.", "'NAV_ANIMATIONS' is defined but never used.", "'NAV_STYLES' is defined but never used.", "'AboutProps' is defined but never used.", "'AdvantagesProps' is defined but never used.", "'FeaturesProps' is defined but never used.", "'SITE_CONFIG' is defined but never used.", "'HeroProps' is defined but never used.", ["1145", "1146"], ["1147", "1148"], ["1149", "1150"], ["1151", "1152"], "'size' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'closeMenu'. Either include it or remove the dependency array.", ["1153"], "'color' is assigned a value but never used.", "'KnowledgeCategory' is defined but never used.", ["1154", "1155"], ["1156", "1157"], ["1158", "1159"], "'syncTimeoutRef' is assigned a value but never used.", ["1160", "1161"], ["1162", "1163"], ["1164", "1165"], ["1166", "1167"], "'KnowledgeArticle' is defined but never used.", "'CodeExample' is defined but never used.", "'KnowledgeArticleInsert' is defined but never used.", "'KnowledgeCategoryInsert' is defined but never used.", "'CodeExampleInsert' is defined but never used.", "'ApiResponse' is defined but never used.", ["1168", "1169"], ["1170", "1171"], ["1172", "1173"], ["1174", "1175"], ["1176", "1177"], ["1178", "1179"], ["1180", "1181"], ["1182", "1183"], ["1184", "1185"], ["1186", "1187"], ["1188", "1189"], ["1190", "1191"], ["1192", "1193"], ["1194", "1195"], ["1196", "1197"], ["1198", "1199"], ["1200", "1201"], ["1202", "1203"], "React Hook useCallback has a missing dependency: 'addToHistory'. Either include it or remove the dependency array.", ["1204"], ["1205", "1206"], ["1207", "1208"], ["1209", "1210"], ["1211", "1212"], ["1213", "1214"], ["1215", "1216"], "'_searchParams' is defined but never used.", "'throttle' is assigned a value but never used.", "'deps' is assigned a value but never used.", ["1217", "1218"], "'delay' is assigned a value but never used.", ["1219", "1220"], ["1221", "1222"], ["1223", "1224"], ["1225", "1226"], ["1227", "1228"], ["1229", "1230"], ["1231", "1232"], ["1233", "1234"], ["1235", "1236"], ["1237", "1238"], ["1239", "1240"], ["1241", "1242"], ["1243", "1244"], "React Hook useCallback has a missing dependency: 'onConfigComplete'. Either include it or remove the dependency array. If 'onConfigComplete' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1245"], "'_ip' is defined but never used.", "'page' is defined but never used.", {"desc": "1246", "fix": "1247"}, {"desc": "1248", "fix": "1249"}, {"desc": "1250", "fix": "1251"}, {"desc": "1252", "fix": "1253"}, {"desc": "1252", "fix": "1254"}, {"messageId": "1255", "fix": "1256", "desc": "1257"}, {"messageId": "1258", "fix": "1259", "desc": "1260"}, {"messageId": "1255", "fix": "1261", "desc": "1257"}, {"messageId": "1258", "fix": "1262", "desc": "1260"}, {"messageId": "1255", "fix": "1263", "desc": "1257"}, {"messageId": "1258", "fix": "1264", "desc": "1260"}, {"messageId": "1255", "fix": "1265", "desc": "1257"}, {"messageId": "1258", "fix": "1266", "desc": "1260"}, {"messageId": "1255", "fix": "1267", "desc": "1257"}, {"messageId": "1258", "fix": "1268", "desc": "1260"}, {"messageId": "1255", "fix": "1269", "desc": "1257"}, {"messageId": "1258", "fix": "1270", "desc": "1260"}, {"desc": "1271", "fix": "1272"}, {"messageId": "1255", "fix": "1273", "desc": "1257"}, {"messageId": "1258", "fix": "1274", "desc": "1260"}, {"messageId": "1255", "fix": "1275", "desc": "1257"}, {"messageId": "1258", "fix": "1276", "desc": "1260"}, {"messageId": "1255", "fix": "1277", "desc": "1257"}, {"messageId": "1258", "fix": "1278", "desc": "1260"}, {"messageId": "1255", "fix": "1279", "desc": "1257"}, {"messageId": "1258", "fix": "1280", "desc": "1260"}, {"messageId": "1255", "fix": "1281", "desc": "1257"}, {"messageId": "1258", "fix": "1282", "desc": "1260"}, {"messageId": "1255", "fix": "1283", "desc": "1257"}, {"messageId": "1258", "fix": "1284", "desc": "1260"}, {"messageId": "1255", "fix": "1285", "desc": "1257"}, {"messageId": "1258", "fix": "1286", "desc": "1260"}, {"messageId": "1255", "fix": "1287", "desc": "1257"}, {"messageId": "1258", "fix": "1288", "desc": "1260"}, {"messageId": "1255", "fix": "1289", "desc": "1257"}, {"messageId": "1258", "fix": "1290", "desc": "1260"}, {"messageId": "1255", "fix": "1291", "desc": "1257"}, {"messageId": "1258", "fix": "1292", "desc": "1260"}, {"messageId": "1255", "fix": "1293", "desc": "1257"}, {"messageId": "1258", "fix": "1294", "desc": "1260"}, {"messageId": "1255", "fix": "1295", "desc": "1257"}, {"messageId": "1258", "fix": "1296", "desc": "1260"}, {"messageId": "1255", "fix": "1297", "desc": "1257"}, {"messageId": "1258", "fix": "1298", "desc": "1260"}, {"messageId": "1255", "fix": "1299", "desc": "1257"}, {"messageId": "1258", "fix": "1300", "desc": "1260"}, {"messageId": "1255", "fix": "1301", "desc": "1257"}, {"messageId": "1258", "fix": "1302", "desc": "1260"}, {"messageId": "1255", "fix": "1303", "desc": "1257"}, {"messageId": "1258", "fix": "1304", "desc": "1260"}, {"messageId": "1255", "fix": "1305", "desc": "1257"}, {"messageId": "1258", "fix": "1306", "desc": "1260"}, {"messageId": "1255", "fix": "1307", "desc": "1257"}, {"messageId": "1258", "fix": "1308", "desc": "1260"}, {"messageId": "1255", "fix": "1309", "desc": "1257"}, {"messageId": "1258", "fix": "1310", "desc": "1260"}, {"messageId": "1255", "fix": "1311", "desc": "1257"}, {"messageId": "1258", "fix": "1312", "desc": "1260"}, {"messageId": "1255", "fix": "1313", "desc": "1257"}, {"messageId": "1258", "fix": "1314", "desc": "1260"}, {"messageId": "1255", "fix": "1315", "desc": "1257"}, {"messageId": "1258", "fix": "1316", "desc": "1260"}, {"messageId": "1255", "fix": "1317", "desc": "1257"}, {"messageId": "1258", "fix": "1318", "desc": "1260"}, {"messageId": "1255", "fix": "1319", "desc": "1257"}, {"messageId": "1258", "fix": "1320", "desc": "1260"}, {"messageId": "1255", "fix": "1321", "desc": "1257"}, {"messageId": "1258", "fix": "1322", "desc": "1260"}, {"messageId": "1255", "fix": "1323", "desc": "1257"}, {"messageId": "1258", "fix": "1324", "desc": "1260"}, {"messageId": "1255", "fix": "1325", "desc": "1257"}, {"messageId": "1258", "fix": "1326", "desc": "1260"}, {"messageId": "1255", "fix": "1327", "desc": "1257"}, {"messageId": "1258", "fix": "1328", "desc": "1260"}, {"messageId": "1255", "fix": "1329", "desc": "1257"}, {"messageId": "1258", "fix": "1330", "desc": "1260"}, {"messageId": "1255", "fix": "1331", "desc": "1257"}, {"messageId": "1258", "fix": "1332", "desc": "1260"}, {"messageId": "1255", "fix": "1333", "desc": "1257"}, {"messageId": "1258", "fix": "1334", "desc": "1260"}, {"desc": "1335", "fix": "1336"}, {"messageId": "1255", "fix": "1337", "desc": "1257"}, {"messageId": "1258", "fix": "1338", "desc": "1260"}, {"messageId": "1255", "fix": "1339", "desc": "1257"}, {"messageId": "1258", "fix": "1340", "desc": "1260"}, {"messageId": "1255", "fix": "1341", "desc": "1257"}, {"messageId": "1258", "fix": "1342", "desc": "1260"}, {"messageId": "1255", "fix": "1343", "desc": "1257"}, {"messageId": "1258", "fix": "1344", "desc": "1260"}, {"desc": "1345", "fix": "1346"}, {"messageId": "1255", "fix": "1347", "desc": "1257"}, {"messageId": "1258", "fix": "1348", "desc": "1260"}, {"messageId": "1255", "fix": "1349", "desc": "1257"}, {"messageId": "1258", "fix": "1350", "desc": "1260"}, {"messageId": "1255", "fix": "1351", "desc": "1257"}, {"messageId": "1258", "fix": "1352", "desc": "1260"}, {"messageId": "1255", "fix": "1353", "desc": "1257"}, {"messageId": "1258", "fix": "1354", "desc": "1260"}, {"messageId": "1255", "fix": "1355", "desc": "1257"}, {"messageId": "1258", "fix": "1356", "desc": "1260"}, {"messageId": "1255", "fix": "1357", "desc": "1257"}, {"messageId": "1258", "fix": "1358", "desc": "1260"}, {"messageId": "1255", "fix": "1359", "desc": "1257"}, {"messageId": "1258", "fix": "1360", "desc": "1260"}, {"messageId": "1255", "fix": "1361", "desc": "1257"}, {"messageId": "1258", "fix": "1362", "desc": "1260"}, {"messageId": "1255", "fix": "1363", "desc": "1257"}, {"messageId": "1258", "fix": "1364", "desc": "1260"}, {"messageId": "1255", "fix": "1365", "desc": "1257"}, {"messageId": "1258", "fix": "1366", "desc": "1260"}, {"messageId": "1255", "fix": "1367", "desc": "1257"}, {"messageId": "1258", "fix": "1368", "desc": "1260"}, {"messageId": "1255", "fix": "1369", "desc": "1257"}, {"messageId": "1258", "fix": "1370", "desc": "1260"}, {"messageId": "1255", "fix": "1371", "desc": "1257"}, {"messageId": "1258", "fix": "1372", "desc": "1260"}, {"messageId": "1255", "fix": "1373", "desc": "1257"}, {"messageId": "1258", "fix": "1374", "desc": "1260"}, {"messageId": "1255", "fix": "1375", "desc": "1257"}, {"messageId": "1258", "fix": "1376", "desc": "1260"}, {"messageId": "1255", "fix": "1377", "desc": "1257"}, {"messageId": "1258", "fix": "1378", "desc": "1260"}, {"messageId": "1255", "fix": "1379", "desc": "1257"}, {"messageId": "1258", "fix": "1380", "desc": "1260"}, {"messageId": "1255", "fix": "1381", "desc": "1257"}, {"messageId": "1258", "fix": "1382", "desc": "1260"}, {"messageId": "1255", "fix": "1383", "desc": "1257"}, {"messageId": "1258", "fix": "1384", "desc": "1260"}, {"messageId": "1255", "fix": "1385", "desc": "1257"}, {"messageId": "1258", "fix": "1386", "desc": "1260"}, {"messageId": "1255", "fix": "1387", "desc": "1257"}, {"messageId": "1258", "fix": "1388", "desc": "1260"}, {"messageId": "1255", "fix": "1389", "desc": "1257"}, {"messageId": "1258", "fix": "1390", "desc": "1260"}, {"messageId": "1255", "fix": "1391", "desc": "1257"}, {"messageId": "1258", "fix": "1392", "desc": "1260"}, {"messageId": "1255", "fix": "1393", "desc": "1257"}, {"messageId": "1258", "fix": "1394", "desc": "1260"}, {"messageId": "1255", "fix": "1395", "desc": "1257"}, {"messageId": "1258", "fix": "1396", "desc": "1260"}, {"desc": "1397", "fix": "1398"}, {"messageId": "1255", "fix": "1399", "desc": "1257"}, {"messageId": "1258", "fix": "1400", "desc": "1260"}, {"messageId": "1255", "fix": "1401", "desc": "1257"}, {"messageId": "1258", "fix": "1402", "desc": "1260"}, {"messageId": "1255", "fix": "1403", "desc": "1257"}, {"messageId": "1258", "fix": "1404", "desc": "1260"}, {"messageId": "1255", "fix": "1405", "desc": "1257"}, {"messageId": "1258", "fix": "1406", "desc": "1260"}, {"messageId": "1255", "fix": "1407", "desc": "1257"}, {"messageId": "1258", "fix": "1408", "desc": "1260"}, {"messageId": "1255", "fix": "1409", "desc": "1257"}, {"messageId": "1258", "fix": "1410", "desc": "1260"}, {"messageId": "1255", "fix": "1411", "desc": "1257"}, {"messageId": "1258", "fix": "1412", "desc": "1260"}, {"messageId": "1255", "fix": "1413", "desc": "1257"}, {"messageId": "1258", "fix": "1414", "desc": "1260"}, {"messageId": "1255", "fix": "1415", "desc": "1257"}, {"messageId": "1258", "fix": "1416", "desc": "1260"}, {"messageId": "1255", "fix": "1417", "desc": "1257"}, {"messageId": "1258", "fix": "1418", "desc": "1260"}, {"messageId": "1255", "fix": "1419", "desc": "1257"}, {"messageId": "1258", "fix": "1420", "desc": "1260"}, {"messageId": "1255", "fix": "1421", "desc": "1257"}, {"messageId": "1258", "fix": "1422", "desc": "1260"}, {"messageId": "1255", "fix": "1423", "desc": "1257"}, {"messageId": "1258", "fix": "1424", "desc": "1260"}, {"messageId": "1255", "fix": "1425", "desc": "1257"}, {"messageId": "1258", "fix": "1426", "desc": "1260"}, {"messageId": "1255", "fix": "1427", "desc": "1257"}, {"messageId": "1258", "fix": "1428", "desc": "1260"}, {"messageId": "1255", "fix": "1429", "desc": "1257"}, {"messageId": "1258", "fix": "1430", "desc": "1260"}, {"messageId": "1255", "fix": "1431", "desc": "1257"}, {"messageId": "1258", "fix": "1432", "desc": "1260"}, {"messageId": "1255", "fix": "1433", "desc": "1257"}, {"messageId": "1258", "fix": "1434", "desc": "1260"}, {"messageId": "1255", "fix": "1435", "desc": "1257"}, {"messageId": "1258", "fix": "1436", "desc": "1260"}, {"messageId": "1255", "fix": "1437", "desc": "1257"}, {"messageId": "1258", "fix": "1438", "desc": "1260"}, {"desc": "1439", "fix": "1440"}, "Update the dependencies array to be: [isInitialized]", {"range": "1441", "text": "1442"}, "Update the dependencies array to be: [analyzeDirectRelationships]", {"range": "1443", "text": "1444"}, "Update the dependencies array to be: [tableData, isInitialized, updateDiagram]", {"range": "1445", "text": "1446"}, "Update the dependencies array to be: [isOpen, isExporting, handleClose]", {"range": "1447", "text": "1448"}, {"range": "1449", "text": "1448"}, "suggestUnknown", {"range": "1450", "text": "1451"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1452", "text": "1453"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1454", "text": "1451"}, {"range": "1455", "text": "1453"}, {"range": "1456", "text": "1451"}, {"range": "1457", "text": "1453"}, {"range": "1458", "text": "1451"}, {"range": "1459", "text": "1453"}, {"range": "1460", "text": "1451"}, {"range": "1461", "text": "1453"}, {"range": "1462", "text": "1451"}, {"range": "1463", "text": "1453"}, "Update the dependencies array to be: [scrollToERDiagram, sqlText]", {"range": "1464", "text": "1465"}, {"range": "1466", "text": "1451"}, {"range": "1467", "text": "1453"}, {"range": "1468", "text": "1451"}, {"range": "1469", "text": "1453"}, {"range": "1470", "text": "1451"}, {"range": "1471", "text": "1453"}, {"range": "1472", "text": "1451"}, {"range": "1473", "text": "1453"}, {"range": "1474", "text": "1451"}, {"range": "1475", "text": "1453"}, {"range": "1476", "text": "1451"}, {"range": "1477", "text": "1453"}, {"range": "1478", "text": "1451"}, {"range": "1479", "text": "1453"}, {"range": "1480", "text": "1451"}, {"range": "1481", "text": "1453"}, {"range": "1482", "text": "1451"}, {"range": "1483", "text": "1453"}, {"range": "1484", "text": "1451"}, {"range": "1485", "text": "1453"}, {"range": "1486", "text": "1451"}, {"range": "1487", "text": "1453"}, {"range": "1488", "text": "1451"}, {"range": "1489", "text": "1453"}, {"range": "1490", "text": "1451"}, {"range": "1491", "text": "1453"}, {"range": "1492", "text": "1451"}, {"range": "1493", "text": "1453"}, {"range": "1494", "text": "1451"}, {"range": "1495", "text": "1453"}, {"range": "1496", "text": "1451"}, {"range": "1497", "text": "1453"}, {"range": "1498", "text": "1451"}, {"range": "1499", "text": "1453"}, {"range": "1500", "text": "1451"}, {"range": "1501", "text": "1453"}, {"range": "1502", "text": "1451"}, {"range": "1503", "text": "1453"}, {"range": "1504", "text": "1451"}, {"range": "1505", "text": "1453"}, {"range": "1506", "text": "1451"}, {"range": "1507", "text": "1453"}, {"range": "1508", "text": "1451"}, {"range": "1509", "text": "1453"}, {"range": "1510", "text": "1451"}, {"range": "1511", "text": "1453"}, {"range": "1512", "text": "1451"}, {"range": "1513", "text": "1453"}, {"range": "1514", "text": "1451"}, {"range": "1515", "text": "1453"}, {"range": "1516", "text": "1451"}, {"range": "1517", "text": "1453"}, {"range": "1518", "text": "1451"}, {"range": "1519", "text": "1453"}, {"range": "1520", "text": "1451"}, {"range": "1521", "text": "1453"}, {"range": "1522", "text": "1451"}, {"range": "1523", "text": "1453"}, {"range": "1524", "text": "1451"}, {"range": "1525", "text": "1453"}, {"range": "1526", "text": "1451"}, {"range": "1527", "text": "1453"}, "Update the dependencies array to be: [loadHistory, maxItems]", {"range": "1528", "text": "1529"}, {"range": "1530", "text": "1451"}, {"range": "1531", "text": "1453"}, {"range": "1532", "text": "1451"}, {"range": "1533", "text": "1453"}, {"range": "1534", "text": "1451"}, {"range": "1535", "text": "1453"}, {"range": "1536", "text": "1451"}, {"range": "1537", "text": "1453"}, "Update the dependencies array to be: [closeMenu, isOpen]", {"range": "1538", "text": "1539"}, {"range": "1540", "text": "1451"}, {"range": "1541", "text": "1453"}, {"range": "1542", "text": "1451"}, {"range": "1543", "text": "1453"}, {"range": "1544", "text": "1451"}, {"range": "1545", "text": "1453"}, {"range": "1546", "text": "1451"}, {"range": "1547", "text": "1453"}, {"range": "1548", "text": "1451"}, {"range": "1549", "text": "1453"}, {"range": "1550", "text": "1451"}, {"range": "1551", "text": "1453"}, {"range": "1552", "text": "1451"}, {"range": "1553", "text": "1453"}, {"range": "1554", "text": "1451"}, {"range": "1555", "text": "1453"}, {"range": "1556", "text": "1451"}, {"range": "1557", "text": "1453"}, {"range": "1558", "text": "1451"}, {"range": "1559", "text": "1453"}, {"range": "1560", "text": "1451"}, {"range": "1561", "text": "1453"}, {"range": "1562", "text": "1451"}, {"range": "1563", "text": "1453"}, {"range": "1564", "text": "1451"}, {"range": "1565", "text": "1453"}, {"range": "1566", "text": "1451"}, {"range": "1567", "text": "1453"}, {"range": "1568", "text": "1451"}, {"range": "1569", "text": "1453"}, {"range": "1570", "text": "1451"}, {"range": "1571", "text": "1453"}, {"range": "1572", "text": "1451"}, {"range": "1573", "text": "1453"}, {"range": "1574", "text": "1451"}, {"range": "1575", "text": "1453"}, {"range": "1576", "text": "1451"}, {"range": "1577", "text": "1453"}, {"range": "1578", "text": "1451"}, {"range": "1579", "text": "1453"}, {"range": "1580", "text": "1451"}, {"range": "1581", "text": "1453"}, {"range": "1582", "text": "1451"}, {"range": "1583", "text": "1453"}, {"range": "1584", "text": "1451"}, {"range": "1585", "text": "1453"}, {"range": "1586", "text": "1451"}, {"range": "1587", "text": "1453"}, {"range": "1588", "text": "1451"}, {"range": "1589", "text": "1453"}, "Update the dependencies array to be: [opts.minQueryLength, opts.enableCache, opts.enableHistory, opts.cacheTime, log, cache, addToHistory]", {"range": "1590", "text": "1591"}, {"range": "1592", "text": "1451"}, {"range": "1593", "text": "1453"}, {"range": "1594", "text": "1451"}, {"range": "1595", "text": "1453"}, {"range": "1596", "text": "1451"}, {"range": "1597", "text": "1453"}, {"range": "1598", "text": "1451"}, {"range": "1599", "text": "1453"}, {"range": "1600", "text": "1451"}, {"range": "1601", "text": "1453"}, {"range": "1602", "text": "1451"}, {"range": "1603", "text": "1453"}, {"range": "1604", "text": "1451"}, {"range": "1605", "text": "1453"}, {"range": "1606", "text": "1451"}, {"range": "1607", "text": "1453"}, {"range": "1608", "text": "1451"}, {"range": "1609", "text": "1453"}, {"range": "1610", "text": "1451"}, {"range": "1611", "text": "1453"}, {"range": "1612", "text": "1451"}, {"range": "1613", "text": "1453"}, {"range": "1614", "text": "1451"}, {"range": "1615", "text": "1453"}, {"range": "1616", "text": "1451"}, {"range": "1617", "text": "1453"}, {"range": "1618", "text": "1451"}, {"range": "1619", "text": "1453"}, {"range": "1620", "text": "1451"}, {"range": "1621", "text": "1453"}, {"range": "1622", "text": "1451"}, {"range": "1623", "text": "1453"}, {"range": "1624", "text": "1451"}, {"range": "1625", "text": "1453"}, {"range": "1626", "text": "1451"}, {"range": "1627", "text": "1453"}, {"range": "1628", "text": "1451"}, {"range": "1629", "text": "1453"}, {"range": "1630", "text": "1451"}, {"range": "1631", "text": "1453"}, "Update the dependencies array to be: [systemInfo, installConfig, onConfigComplete]", {"range": "1632", "text": "1633"}, [5128, 5130], "[isInitialized]", [13138, 13140], "[analyzeDirectRelationships]", [17924, 17950], "[tableData, isInitialized, updateDiagram]", [2438, 2459], "[isOpen, isExporting, handleClose]", [2931, 2952], [9325, 9328], "unknown", [9325, 9328], "never", [9457, 9460], [9457, 9460], [11462, 11465], [11462, 11465], [12728, 12731], [12728, 12731], [15820, 15823], [15820, 15823], [24865, 24868], [24865, 24868], [6877, 6886], "[scrollToERDiagram, sqlText]", [7792, 7795], [7792, 7795], [3467, 3470], [3467, 3470], [3585, 3588], [3585, 3588], [3702, 3705], [3702, 3705], [561, 564], [561, 564], [574, 577], [574, 577], [717, 720], [717, 720], [984, 987], [984, 987], [1464, 1467], [1464, 1467], [4694, 4697], [4694, 4697], [5444, 5447], [5444, 5447], [439, 442], [439, 442], [1356, 1359], [1356, 1359], [1624, 1627], [1624, 1627], [784, 787], [784, 787], [1044, 1047], [1044, 1047], [1118, 1121], [1118, 1121], [4341, 4344], [4341, 4344], [1223, 1226], [1223, 1226], [1857, 1860], [1857, 1860], [1880, 1883], [1880, 1883], [1888, 1891], [1888, 1891], [3983, 3986], [3983, 3986], [339, 342], [339, 342], [437, 440], [437, 440], [566, 569], [566, 569], [888, 891], [888, 891], [1290, 1293], [1290, 1293], [1690, 1693], [1690, 1693], [6124, 6127], [6124, 6127], [6184, 6187], [6184, 6187], [2438, 2448], "[loadHistory, maxItems]", [2953, 2956], [2953, 2956], [2981, 2984], [2981, 2984], [3367, 3370], [3367, 3370], [3391, 3394], [3391, 3394], [3408, 3416], "[closeMenu, isOpen]", [213, 216], [213, 216], [1172, 1175], [1172, 1175], [2227, 2230], [2227, 2230], [3015, 3018], [3015, 3018], [3933, 3936], [3933, 3936], [8860, 8863], [8860, 8863], [9776, 9779], [9776, 9779], [2827, 2830], [2827, 2830], [2866, 2869], [2866, 2869], [4999, 5002], [4999, 5002], [7871, 7874], [7871, 7874], [8754, 8757], [8754, 8757], [9137, 9140], [9137, 9140], [9699, 9702], [9699, 9702], [10041, 10044], [10041, 10044], [10386, 10389], [10386, 10389], [10866, 10869], [10866, 10869], [11114, 11117], [11114, 11117], [11610, 11613], [11610, 11613], [12311, 12314], [12311, 12314], [12601, 12604], [12601, 12604], [12709, 12712], [12709, 12712], [12770, 12773], [12770, 12773], [4028, 4031], [4028, 4031], [6384, 6387], [6384, 6387], [6683, 6770], "[opts.min<PERSON><PERSON><PERSON><PERSON><PERSON>, opts.enableCache, opts.enableHistory, opts.cacheTime, log, cache, addToHistory]", [11003, 11006], [11003, 11006], [6783, 6786], [6783, 6786], [7021, 7024], [7021, 7024], [385, 388], [385, 388], [10371, 10374], [10371, 10374], [142, 145], [142, 145], [5204, 5207], [5204, 5207], [1584, 1587], [1584, 1587], [2773, 2776], [2773, 2776], [2861, 2864], [2861, 2864], [3657, 3660], [3657, 3660], [1261, 1264], [1261, 1264], [1271, 1274], [1271, 1274], [1552, 1555], [1552, 1555], [1562, 1565], [1562, 1565], [4137, 4140], [4137, 4140], [4170, 4173], [4170, 4173], [4188, 4191], [4188, 4191], [4483, 4486], [4483, 4486], [4509, 4512], [4509, 4512], [4618, 4645], "[systemInfo, installConfig, onConfigComplete]"]