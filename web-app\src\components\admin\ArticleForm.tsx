'use client';

// MySQLAi.de - 文章表单组件
// 用于创建和编辑文章的表单

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Save, X, Eye, FileText, FolderOpen, Tag, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';
// import { articlesApi, categoriesApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import MarkdownEditor from './MarkdownEditor';
import type { Database } from '@/lib/database.types';

type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];
type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];

interface ArticleFormData {
  id: string;
  title: string;
  description: string;
  content: string;
  category_id: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  order_index: number;
}

interface ArticleFormProps {
  article?: KnowledgeArticle | null;
  onSave: (data: ArticleFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

// 难度选项
const DIFFICULTY_OPTIONS = [
  { value: 'beginner', label: '初级', color: 'text-green-600 bg-green-50' },
  { value: 'intermediate', label: '中级', color: 'text-yellow-600 bg-yellow-50' },
  { value: 'advanced', label: '高级', color: 'text-red-600 bg-red-50' }
] as const;

export default function ArticleForm({ article, onSave, onCancel, loading = false }: ArticleFormProps) {
  const [categories, setCategories] = useState<KnowledgeCategory[]>([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [tagInput, setTagInput] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<ArticleFormData>({
    defaultValues: {
      id: article?.id || '',
      title: article?.title || '',
      description: article?.description || '',
      content: article?.content || '',
      category_id: article?.category_id || '',
      tags: article?.tags || [],
      difficulty: article?.difficulty || 'beginner',
      order_index: article?.order_index || 0
    }
  });

  const watchedContent = watch('content');
  const watchedTags = watch('tags');

  // 获取分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await categoriesApi.getAll();
        if (response.success) {
          setCategories(response.data || []);
        }
      } catch (error) {
        console.error('获取分类失败:', error);
      }
    };

    fetchCategories();
  }, []);

  // 处理表单提交
  const onSubmit = async (data: ArticleFormData) => {
    try {
      await onSave(data);
    } catch (error) {
      console.error('保存文章失败:', error);
    }
  };

  // 添加标签
  const addTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      const newTags = [...watchedTags, tagInput.trim()];
      setValue('tags', newTags, { shouldDirty: true });
      setTagInput('');
    }
  };

  // 删除标签
  const removeTag = (tagToRemove: string) => {
    const newTags = watchedTags.filter(tag => tag !== tagToRemove);
    setValue('tags', newTags, { shouldDirty: true });
  };

  // 处理标签输入键盘事件
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-xl shadow-lg border border-mysql-border"
    >
      {/* 表单头部 */}
      <div className="flex items-center justify-between p-6 border-b border-mysql-border">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-mysql-primary-light rounded-lg">
            <FileText className="w-5 h-5 text-mysql-primary" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-mysql-text">
              {article ? '编辑文章' : '创建新文章'}
            </h2>
            <p className="text-sm text-mysql-text-light">
              {article ? '修改现有文章内容' : '添加新的知识库文章'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            icon={<Eye className="w-4 h-4" />}
            className="text-mysql-text-light hover:text-mysql-primary"
          >
            {isPreviewMode ? '编辑模式' : '预览模式'}
          </Button>
        </div>
      </div>

      {/* 表单内容 */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* 基本信息 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 文章ID */}
          <div>
            <label htmlFor="id" className="block text-sm font-medium text-mysql-text mb-2">
              文章ID *
            </label>
            <input
              id="id"
              type="text"
              {...register('id', { 
                required: '文章ID不能为空',
                pattern: {
                  value: /^[a-zA-Z0-9_-]+$/,
                  message: '文章ID只能包含字母、数字、下划线和连字符'
                }
              })}
              placeholder="例如: mysql-basics-intro"
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200',
                errors.id && 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
              )}
              disabled={loading || !!article}
            />
            {errors.id && (
              <p className="mt-1 text-sm text-red-600">{errors.id.message}</p>
            )}
          </div>

          {/* 文章标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-mysql-text mb-2">
              文章标题 *
            </label>
            <input
              id="title"
              type="text"
              {...register('title', { required: '文章标题不能为空' })}
              placeholder="请输入文章标题"
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200',
                errors.title && 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
              )}
              disabled={loading}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>
        </div>

        {/* 文章描述 */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-mysql-text mb-2">
            文章描述
          </label>
          <textarea
            id="description"
            {...register('description')}
            placeholder="请输入文章的简短描述..."
            rows={3}
            className={cn(
              'w-full px-4 py-3 text-base',
              'bg-white border-2 border-mysql-border rounded-lg',
              'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
              'placeholder-mysql-text-light text-mysql-text',
              'transition-all duration-200 resize-none'
            )}
            disabled={loading}
          />
        </div>

        {/* 分类和难度 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 分类选择 */}
          <div>
            <label htmlFor="category_id" className="block text-sm font-medium text-mysql-text mb-2">
              <FolderOpen className="w-4 h-4 inline mr-1" />
              文章分类
            </label>
            <select
              id="category_id"
              {...register('category_id')}
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'text-mysql-text transition-all duration-200'
              )}
              disabled={loading}
            >
              <option value="">选择分类</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* 难度选择 */}
          <div>
            <label htmlFor="difficulty" className="block text-sm font-medium text-mysql-text mb-2">
              <BarChart3 className="w-4 h-4 inline mr-1" />
              难度等级
            </label>
            <select
              id="difficulty"
              {...register('difficulty')}
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'text-mysql-text transition-all duration-200'
              )}
              disabled={loading}
            >
              {DIFFICULTY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 排序索引 */}
          <div>
            <label htmlFor="order_index" className="block text-sm font-medium text-mysql-text mb-2">
              排序索引
            </label>
            <input
              id="order_index"
              type="number"
              {...register('order_index', { valueAsNumber: true })}
              placeholder="0"
              min="0"
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200'
              )}
              disabled={loading}
            />
          </div>
        </div>

        {/* 标签管理 */}
        <div>
          <label className="block text-sm font-medium text-mysql-text mb-2">
            <Tag className="w-4 h-4 inline mr-1" />
            文章标签
          </label>
          
          {/* 标签输入 */}
          <div className="flex space-x-2 mb-3">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={handleTagKeyPress}
              placeholder="输入标签后按回车添加"
              className={cn(
                'flex-1 px-4 py-2 text-sm',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200'
              )}
              disabled={loading}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addTag}
              disabled={!tagInput.trim() || loading}
            >
              添加
            </Button>
          </div>

          {/* 标签列表 */}
          <div className="flex flex-wrap gap-2">
            {watchedTags.map((tag, index) => (
              <motion.span
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-mysql-primary-light text-mysql-primary"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  aria-label={`删除标签 ${tag}`}
                  className="ml-2 text-mysql-primary hover:text-mysql-primary-dark"
                  disabled={loading}
                >
                  <X className="w-3 h-3" />
                </button>
              </motion.span>
            ))}
          </div>
        </div>

        {/* Markdown编辑器 */}
        <div>
          <label className="block text-sm font-medium text-mysql-text mb-2">
            文章内容 *
          </label>
          <MarkdownEditor
            value={watchedContent}
            onChange={(value) => setValue('content', value, { shouldDirty: true })}
            placeholder="请输入文章内容，支持Markdown语法..."
            height={500}
            disabled={loading}
            error={errors.content?.message}
          />
          {errors.content && (
            <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
          )}
        </div>

        {/* 表单操作按钮 */}
        <div className="flex items-center justify-between pt-6 border-t border-mysql-border">
          <div className="text-sm text-mysql-text-light">
            {isDirty && '* 表单有未保存的更改'}
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              icon={<X className="w-4 h-4" />}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
              icon={<Save className="w-4 h-4" />}
            >
              {loading ? '保存中...' : (article ? '更新文章' : '创建文章')}
            </Button>
          </div>
        </div>
      </form>
    </motion.div>
  );
}
