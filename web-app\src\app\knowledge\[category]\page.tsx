// MySQLAi.de - MySQL知识库分类页面
// 展示特定分类下的所有知识点，支持筛选和排序功能

import React from 'react';
import { notFound } from 'next/navigation';
// import { PAGE_METADATA } from '@/lib/constants';
import { getKnowledgeCategory, getKnowledgeItemsByCategory } from '@/lib/knowledge';
import CategoryPageClient from '@/components/knowledge/CategoryPageClient';

// 生成页面元数据
export async function generateMetadata({ params }: { params: Promise<{ category: string }> }) {
  const { category: categoryId } = await params;
  const category = await getKnowledgeCategory(categoryId);

  if (!category) {
    return {
      title: '分类不存在 - MySQLAi.de',
      description: '您访问的知识库分类不存在。',
    };
  }

  return {
    title: `${category.name} - MySQL知识库 - MySQLAi.de`,
    description: `${category.description || ''} - 深入学习${category.name}相关的MySQL知识点和最佳实践。`,
    keywords: ['MySQL', category.name, '知识库', '教程', '最佳实践'],
    openGraph: {
      title: `${category.name} - MySQL知识库`,
      description: category.description || '',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${category.name} - MySQL知识库`,
      description: category.description || '',
    },
  };
}

// 生成静态路径（可选，用于静态生成）
export async function generateStaticParams() {
  // 这里可以返回所有可能的分类路径
  // 暂时返回空数组，使用动态生成
  return [];
}

interface CategoryPageProps {
  params: Promise<{
    category: string;
  }>;
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { category: categoryId } = await params;

  // 获取分类信息
  const category = await getKnowledgeCategory(categoryId);

  // 如果分类不存在，返回404
  if (!category) {
    notFound();
  }

  // 获取该分类下的所有知识点
  const items = await getKnowledgeItemsByCategory(categoryId);

  return (
    <CategoryPageClient
      category={category}
      items={items}
      categorySlug={categoryId}
    />
  );
}
