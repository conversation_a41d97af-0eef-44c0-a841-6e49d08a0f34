'use client';

// MySQLAi.de - 主页面
// 专业的MySQL智能分析平台首页，整合所有核心组件

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import Hero from "@/components/sections/Hero";
import Features from "@/components/sections/Features";
import About from "@/components/sections/About";
import Advantages from "@/components/sections/Advantages";
// import Contact from "@/components/sections/Contact";
import { handleAnchorNavigation, ScrollListener, ScrollData } from '@/lib/scroll';

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);

  // 页面加载和滚动效果
  useEffect(() => {
    // 初始化锚点导航
    handleAnchorNavigation();

    // 页面加载完成
    setIsLoaded(true);

    // 创建滚动监听器
    const scrollListener = new ScrollListener();
    const removeScrollListener = scrollListener.addListener((scrollData: ScrollData) => {
      setScrollProgress(scrollData.progress);
    });

    return () => {
      removeScrollListener();
    };
  }, []);

  return (
    <MainLayout>
      {/* 滚动进度条 */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-mysql-primary to-mysql-accent z-50 origin-left"
        style={{ scaleX: scrollProgress }}
        initial={{ scaleX: 0 }}
        animate={{ scaleX: scrollProgress }}
        transition={{ duration: 0.1, ease: "easeOut" }}
      />

      {/* 页面内容 */}
      <motion.div
        className="min-h-screen bg-white"
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        {/* Hero英雄区域 */}
        <section id="hero" className="relative">
          <Hero />
        </section>

        {/* Features功能展示区域 */}
        <section id="features" className="relative">
          <Features />
        </section>

        {/* About关于我们区域 */}
        <section id="about" className="relative">
          <About />
        </section>

        {/* Advantages优势展示区域 */}
        <section id="advantages" className="relative">
          <Advantages />
        </section>

        {/* Contact联系支持区域 */}
        {/* <section id="contact" className="relative">
          <Contact />
        </section> */}
      </motion.div>
    </MainLayout>
  );
}
