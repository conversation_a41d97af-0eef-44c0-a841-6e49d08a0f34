// MySQLAi.de - 导航配置文件
// 管理网站导航相关的配置和工具函数

import { NavigationItem } from './types';

// 工具导航配置
export const TOOLS_NAVIGATION: NavigationItem[] = [
  {
    name: 'ER图生成工具',
    href: '/tools/er-diagram',
    icon: 'GitBranch',
  },
  {
    name: 'MySQL安装工具',
    href: '/tools/mysql-installer',
    icon: 'Download',
  },
];

// 主导航菜单配置
export const MAIN_NAVIGATION: NavigationItem[] = [
  {
    name: '首页',
    href: '/',
    icon: 'Home',
  },
  {
    name: '知识库',
    href: '/knowledge',
    icon: 'BookOpen',
  },
  {
    name: '项目管理',
    href: '/projects',
    icon: 'FolderOpen',
  },
  {
    name: '报告展示',
    href: '/reports',
    icon: 'BarChart3',
  },
  {
    name: '关于我们',
    href: '/about',
    icon: 'Users',
  },
  {
    name: '工具',
    href: '/tools',
    icon: 'Wrench',
    children: TOOLS_NAVIGATION,
  },
  {
    name: '联系我们',
    href: '/contact',
    icon: 'Mail',
  },
];

// 页脚导航配置
export const FOOTER_NAVIGATION = {
  产品服务: [
    { name: 'MySQL优化', href: '/services/optimization' },
    { name: '性能调优', href: '/services/tuning' },
    { name: '架构设计', href: '/services/architecture' },
    { name: '数据迁移', href: '/services/migration' },
  ],
  解决方案: [
    { name: '企业级方案', href: '/solutions/enterprise' },
    { name: '云数据库', href: '/solutions/cloud' },
    { name: '高可用架构', href: '/solutions/ha' },
    { name: '灾备方案', href: '/solutions/disaster-recovery' },
  ],
  学习资源: [
    { name: '技术博客', href: '/blog' },
    { name: '视频教程', href: '/tutorials' },
    { name: 'API文档', href: '/docs' },
    { name: '最佳实践', href: '/best-practices' },
  ],
  关于我们: [
    { name: '公司介绍', href: '/about' },
    { name: '团队成员', href: '/team' },
    { name: '招聘信息', href: '/careers' },
    { name: '联系我们', href: '/contact' },
  ],
};

// 社交媒体链接
export const SOCIAL_LINKS: NavigationItem[] = [
  {
    name: 'GitHub',
    href: 'https://github.com/mysqlai',
    icon: 'Github',
    isExternal: true,
  },
  {
    name: '微信公众号',
    href: '#wechat',
    icon: 'MessageCircle',
  },
  {
    name: 'QQ技术群',
    href: '#qq-group',
    icon: 'Users',
  },
  {
    name: '技术博客',
    href: '/blog',
    icon: 'BookOpen',
  },
];

// 法律链接
export const LEGAL_LINKS: NavigationItem[] = [
  { name: '服务条款', href: '/terms' },
  { name: '隐私政策', href: '/privacy' },
  { name: '免责声明', href: '/disclaimer' },
];

// 导航工具函数

/**
 * 检查当前路径是否为活跃导航项
 * @param href 导航链接
 * @param currentPath 当前路径
 * @returns 是否为活跃状态
 */
export function isActiveNavItem(href: string, currentPath: string): boolean {
  if (href === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(href);
}

/**
 * 获取导航项的完整URL
 * @param href 相对或绝对链接
 * @param baseUrl 基础URL
 * @returns 完整URL
 */
export function getFullUrl(href: string, baseUrl: string = ''): string {
  if (href.startsWith('http') || href.startsWith('//')) {
    return href;
  }
  return `${baseUrl}${href}`;
}

/**
 * 检查链接是否为外部链接
 * @param href 链接地址
 * @returns 是否为外部链接
 */
export function isExternalLink(href: string): boolean {
  return href.startsWith('http') || href.startsWith('//');
}

/**
 * 获取导航项的aria-label
 * @param item 导航项
 * @returns aria-label文本
 */
export function getNavItemAriaLabel(item: NavigationItem): string {
  if (item.isExternal) {
    return `${item.name} (在新窗口中打开)`;
  }
  return `前往${item.name}页面`;
}

/**
 * 根据路径获取面包屑导航
 * @param pathname 当前路径
 * @returns 面包屑数组
 */
export function getBreadcrumbs(pathname: string): NavigationItem[] {
  const breadcrumbs: NavigationItem[] = [
    { name: '首页', href: '/' }
  ];

  if (pathname === '/') {
    return breadcrumbs;
  }

  // 页面的中文名称映射
  const pageNames: Record<string, string> = {
    'terms': '服务条款',
    'privacy': '隐私政策',
    'disclaimer': '免责声明',
    'cookies': 'Cookie政策',
    'contact': '联系我们',
    'about': '关于我们'
  };

  // 查找匹配的主导航项
  const mainNavItem = MAIN_NAVIGATION.find(item =>
    pathname.startsWith(item.href) && item.href !== '/'
  );

  if (mainNavItem) {
    breadcrumbs.push(mainNavItem);
    // 如果找到了主导航项，直接返回，不再处理子页面逻辑
    return breadcrumbs;
  }

  // 处理子页面路径
  const pathSegments = pathname.split('/').filter(Boolean);

  // 对于单级页面，只有在主导航中没有找到时才添加
  if (pathSegments.length === 1) {
    const segment = pathSegments[0];
    const name = pageNames[segment];

    if (!mainNavItem) {
      // 只有在主导航中没有找到时，才添加到面包屑
      if (name) {
        // 使用页面名称映射
        breadcrumbs.push({ name, href: pathname });
      } else {
        // 生成fallback名称
        const fallbackName = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        breadcrumbs.push({ name: fallbackName, href: pathname });
      }
    }
  } else if (pathSegments.length > 1) {
    // 处理多级路径
    for (let i = 1; i < pathSegments.length; i++) {
      const segment = pathSegments[i];
      const href = '/' + pathSegments.slice(0, i + 1).join('/');

      const name = pageNames[segment] || segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      breadcrumbs.push({ name, href });
    }
  }

  return breadcrumbs;
}

/**
 * 获取页面标题
 * @param pathname 当前路径
 * @param siteName 网站名称
 * @returns 页面标题
 */
export function getPageTitle(pathname: string, siteName: string = 'MySQLAi.de'): string {
  if (pathname === '/') {
    return `${siteName} - MySQL智能分析专家`;
  }

  const navItem = MAIN_NAVIGATION.find(item => 
    pathname.startsWith(item.href) && item.href !== '/'
  );

  if (navItem) {
    return `${navItem.name} - ${siteName}`;
  }

  // 从路径生成标题
  const pathSegments = pathname.split('/').filter(Boolean);
  const lastSegment = pathSegments[pathSegments.length - 1];
  const title = lastSegment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return `${title} - ${siteName}`;
}

/**
 * 获取相关导航项（用于推荐链接）
 * @param currentHref 当前页面链接
 * @param maxItems 最大返回数量
 * @returns 相关导航项数组
 */
export function getRelatedNavItems(
  currentHref: string, 
  maxItems: number = 3
): NavigationItem[] {
  return MAIN_NAVIGATION
    .filter(item => item.href !== currentHref && item.href !== '/')
    .slice(0, maxItems);
}

// 导航动画配置
export const NAV_ANIMATIONS = {
  // 移动端菜单动画
  mobileMenu: {
    initial: { x: '100%' },
    animate: { x: 0 },
    exit: { x: '100%' },
    transition: { type: 'spring', damping: 25, stiffness: 200 }
  },
  
  // 背景遮罩动画
  overlay: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 }
  },
  
  // 菜单项动画
  menuItem: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: (index: number) => ({ delay: index * 0.1 })
  },
  
  // Header背景变化动画
  headerBackground: {
    transition: { duration: 0.3 }
  }
};

// 导航样式类名
export const NAV_STYLES = {
  // 桌面端导航链接
  desktopLink: [
    'relative px-3 py-2 text-sm font-medium transition-all duration-200',
    'text-mysql-text hover:text-mysql-primary',
    'before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5',
    'before:bg-mysql-primary before:transition-all before:duration-300',
    'hover:before:w-full'
  ].join(' '),
  
  // 移动端菜单链接
  mobileLink: [
    'flex items-center px-4 py-3 text-mysql-text',
    'hover:text-mysql-primary hover:bg-mysql-primary-light',
    'rounded-lg transition-all duration-200'
  ].join(' '),
  
  // 主要操作按钮
  primaryButton: [
    'inline-flex items-center px-4 py-2 bg-mysql-primary text-white',
    'text-sm font-medium rounded-lg hover:bg-mysql-primary-dark',
    'transition-colors duration-200'
  ].join(' '),
  
  // 汉堡菜单按钮
  hamburgerButton: [
    'lg:hidden p-2 rounded-lg text-mysql-text',
    'hover:text-mysql-primary hover:bg-mysql-primary-light',
    'transition-all duration-200'
  ].join(' ')
};
