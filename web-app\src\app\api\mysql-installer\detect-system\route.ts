// MySQLAi.de - MySQL安装器系统检测 API
// 提供服务端系统检测辅助功能

import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { SupportedOS, SystemArchitecture, SystemInfo } from '@/app/tools/mysql-installer/types/mysql-installer';

/**
 * 解析User-Agent获取操作系统信息
 */
function parseUserAgent(userAgent: string): {
  os: SupportedOS;
  osVersion: string;
  architecture: SystemArchitecture;
  browser: string;
} {
  const ua = userAgent.toLowerCase();
  
  // 检测操作系统
  let os: SupportedOS;
  let osVersion = 'Unknown';
  
  if (ua.includes('windows')) {
    os = SupportedOS.WINDOWS;
    if (ua.includes('windows nt 10.0')) osVersion = 'Windows 10/11';
    else if (ua.includes('windows nt 6.3')) osVersion = 'Windows 8.1';
    else if (ua.includes('windows nt 6.2')) osVersion = 'Windows 8';
    else if (ua.includes('windows nt 6.1')) osVersion = 'Windows 7';
    else osVersion = 'Windows';
  } else if (ua.includes('mac os x') || ua.includes('macos')) {
    os = SupportedOS.MACOS;
    const macMatch = ua.match(/mac os x (\d+[._]\d+[._]?\d*)/);
    if (macMatch) {
      const version = macMatch[1].replace(/_/g, '.');
      osVersion = `macOS ${version}`;
    } else {
      osVersion = 'macOS';
    }
  } else if (ua.includes('linux')) {
    os = SupportedOS.LINUX;
    if (ua.includes('ubuntu')) osVersion = 'Ubuntu Linux';
    else if (ua.includes('debian')) osVersion = 'Debian Linux';
    else if (ua.includes('centos')) osVersion = 'CentOS Linux';
    else if (ua.includes('fedora')) osVersion = 'Fedora Linux';
    else if (ua.includes('red hat')) osVersion = 'Red Hat Linux';
    else osVersion = 'Linux';
  } else {
    os = SupportedOS.LINUX; // 默认为Linux
    osVersion = 'Unknown OS';
  }
  
  // 检测架构
  let architecture: SystemArchitecture;
  if (ua.includes('x86_64') || ua.includes('amd64') || ua.includes('win64') || ua.includes('wow64')) {
    architecture = SystemArchitecture.X64;
  } else if (ua.includes('arm64') || ua.includes('aarch64')) {
    architecture = SystemArchitecture.ARM64;
  } else if (ua.includes('arm')) {
    architecture = SystemArchitecture.ARM64; // 现代ARM通常是64位
  } else {
    architecture = SystemArchitecture.X64; // 默认为x64
  }
  
  // 检测浏览器
  let browser = 'Unknown';
  if (ua.includes('chrome') && !ua.includes('edg')) {
    browser = 'Chrome';
  } else if (ua.includes('firefox')) {
    browser = 'Firefox';
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    browser = 'Safari';
  } else if (ua.includes('edg')) {
    browser = 'Edge';
  } else if (ua.includes('opera') || ua.includes('opr')) {
    browser = 'Opera';
  }
  
  return { os, osVersion, architecture, browser };
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (remoteAddr) {
    return remoteAddr;
  }
  
  return 'unknown';
}

/**
 * 基于IP获取地理位置信息（简化版）
 */
async function getGeolocation(_ip: string): Promise<{
  country: string;
  region: string;
  timezone: string;
}> {
  // 在实际应用中，这里可以调用地理位置API
  // 为了演示，我们返回默认值
  try {
    // 可以集成如 ipapi.co, ipinfo.io 等服务
    // const response = await fetch(`https://ipapi.co/${ip}/json/`);
    // const data = await response.json();

    // 暂时返回默认值
    return {
      country: 'CN',
      region: 'Asia',
      timezone: 'Asia/Shanghai'
    };
  } catch {
    return {
      country: 'CN',
      region: 'Asia',
      timezone: 'Asia/Shanghai'
    };
  }
}

/**
 * 检查系统兼容性
 */
function checkSystemCompatibility(os: SupportedOS, architecture: SystemArchitecture): {
  isSupported: boolean;
  warnings: string[];
  recommendations: string[];
} {
  const warnings: string[] = [];
  const recommendations: string[] = [];
  let isSupported = true;
  
  // 检查操作系统支持
  switch (os) {
    case SupportedOS.WINDOWS:
      recommendations.push('建议使用 Windows 10 或更高版本');
      recommendations.push('确保已安装 Visual C++ Redistributable');
      break;
      
    case SupportedOS.MACOS:
      recommendations.push('建议使用 macOS 10.15 或更高版本');
      recommendations.push('可能需要安装 Xcode Command Line Tools');
      break;
      
    case SupportedOS.LINUX:
      recommendations.push('建议使用 Ubuntu 18.04+ 或 CentOS 7+');
      recommendations.push('确保已安装必要的开发工具包');
      break;
      
    default:
      isSupported = false;
      warnings.push('不支持的操作系统');
  }
  
  // 检查架构支持
  if (architecture === SystemArchitecture.X86) {
    warnings.push('32位系统支持有限，建议使用64位系统');
  }
  
  return { isSupported, warnings, recommendations };
}

// GET /api/mysql-installer/detect-system - 检测系统信息
export async function GET(request: NextRequest) {
  try {
    const headersList = headers();
    const userAgent = headersList.get('user-agent') || '';
    const clientIP = getClientIP(request);
    
    // 解析用户代理
    const { os, osVersion, architecture, browser } = parseUserAgent(userAgent);
    
    // 获取地理位置
    const geolocation = await getGeolocation(clientIP);
    
    // 检查兼容性
    const compatibility = checkSystemCompatibility(os, architecture);
    
    // 构建系统信息
    const systemInfo: SystemInfo = {
      os,
      osVersion,
      architecture,
      browser,
      isSupported: compatibility.isSupported,
      detectedAt: new Date().toISOString()
    };
    
    return NextResponse.json({
      success: true,
      data: {
        systemInfo,
        geolocation,
        compatibility,
        clientIP: clientIP === 'unknown' ? undefined : clientIP,
        userAgent
      }
    });
    
  } catch (error) {
    console.error('系统检测API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '系统检测失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// POST /api/mysql-installer/detect-system - 提交客户端检测结果
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证客户端提交的系统信息
    if (!body.systemInfo) {
      return NextResponse.json(
        { success: false, error: '缺少系统信息' },
        { status: 400 }
      );
    }
    
    const { systemInfo } = body;
    
    // 服务端验证和补充信息
    const headersList = headers();
    const userAgent = headersList.get('user-agent') || '';
    const serverDetection = parseUserAgent(userAgent);
    
    // 比较客户端和服务端检测结果
    const discrepancies: string[] = [];
    
    if (systemInfo.os !== serverDetection.os) {
      discrepancies.push(`操作系统不匹配: 客户端=${systemInfo.os}, 服务端=${serverDetection.os}`);
    }
    
    if (systemInfo.architecture !== serverDetection.architecture) {
      discrepancies.push(`架构不匹配: 客户端=${systemInfo.architecture}, 服务端=${serverDetection.architecture}`);
    }
    
    // 返回验证结果
    return NextResponse.json({
      success: true,
      data: {
        clientDetection: systemInfo,
        serverDetection,
        discrepancies,
        recommendation: discrepancies.length > 0 
          ? '建议使用服务端检测结果' 
          : '客户端检测结果可信'
      }
    });
    
  } catch (error) {
    console.error('系统检测验证API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '系统检测验证失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
