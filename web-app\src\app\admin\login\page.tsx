'use client';

// MySQLAi.de - 管理员登录页面
// 复用现有UI组件和样式系统

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Database, Lock, User, Eye, EyeOff, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { AdminAuth } from '@/lib/admin-auth';

export default function AdminLoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [mounted, setMounted] = useState(false);

  // 确保组件已挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 检查是否已登录（简化检查，避免API调用）
  useEffect(() => {
    if (mounted) {
      const token = AdminAuth.getToken();
      if (token) {
        // 有令牌就直接跳转，让管理后台去验证
        router.replace('/admin');
      }
    }
  }, [router, mounted]);

  // 处理表单输入
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // 清除错误信息
    if (error) setError('');
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username || !formData.password) {
      setError('请输入用户名和密码');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await AdminAuth.login(formData.username, formData.password);
      
      if (result.success) {
        // 登录成功，跳转到管理后台
        router.replace('/admin');
      } else {
        setError(result.error || '登录失败');
      }
    } catch (error) {
      console.error('登录错误:', error);
      setError('登录过程中发生错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 如果还没有挂载，显示加载状态
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-mysql-primary-light to-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-mysql-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-mysql-text">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-mysql-primary-light to-white flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
        {/* 登录卡片 */}
        <div className="bg-white rounded-2xl shadow-2xl border border-mysql-border overflow-hidden">
          {/* 头部 */}
          <div className="bg-gradient-to-r from-mysql-primary to-mysql-accent p-6 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mx-auto mb-4">
              <Database className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">
              MySQLAi.de 管理后台
            </h1>
            <p className="text-white/80 text-sm">
              请使用管理员账号登录
            </p>
          </div>

          {/* 表单区域 */}
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* 用户名输入 */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-mysql-text mb-2">
                  用户名
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-mysql-text-light" />
                  </div>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    value={formData.username}
                    onChange={handleInputChange}
                    placeholder="请输入管理员用户名"
                    className={cn(
                      'w-full pl-10 pr-4 py-3 text-base',
                      'bg-white border-2 border-mysql-border rounded-lg',
                      'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                      'placeholder-mysql-text-light text-mysql-text',
                      'transition-all duration-200'
                    )}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* 密码输入 */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-mysql-text mb-2">
                  密码
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-mysql-text-light" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="请输入管理员密码"
                    className={cn(
                      'w-full pl-10 pr-12 py-3 text-base',
                      'bg-white border-2 border-mysql-border rounded-lg',
                      'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                      'placeholder-mysql-text-light text-mysql-text',
                      'transition-all duration-200'
                    )}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-mysql-text-light hover:text-mysql-primary transition-colors" />
                    ) : (
                      <Eye className="h-5 w-5 text-mysql-text-light hover:text-mysql-primary transition-colors" />
                    )}
                  </button>
                </div>
              </div>

              {/* 错误信息 */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-3 bg-red-50 border border-red-200 rounded-lg"
                >
                  <p className="text-sm text-red-600">{error}</p>
                </motion.div>
              )}

              {/* 登录按钮 */}
              <Button
                type="submit"
                variant="primary"
                size="lg"
                className="w-full"
                loading={isLoading}
                disabled={isLoading || !formData.username || !formData.password}
                icon={isLoading ? <Loader2 className="w-5 h-5 animate-spin" /> : undefined}
              >
                {isLoading ? '登录中...' : '登录管理后台'}
              </Button>
            </form>

            {/* 提示信息 */}
            <div className="mt-6 text-center">
              <p className="text-xs text-mysql-text-light">
                仅限授权管理员访问 • 请妥善保管登录凭据
              </p>
            </div>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-mysql-text-light">
            © 2024 MySQLAi.de - MySQL智能分析专家
          </p>
        </div>
      </motion.div>
    </div>
  );
}
