'use client';

// MySQLAi.de - AnimatedText动画文字组件
// 支持打字机效果、淡入动画等多种文字动画

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimatedTextProps {
  text: string;
  className?: string;
  animation?: 'typewriter' | 'fadeIn' | 'slideUp' | 'slideDown';
  delay?: number;
  duration?: number;
  speed?: number; // 打字机速度（毫秒）
  cursor?: boolean; // 是否显示光标
  onComplete?: () => void;
}

export default function AnimatedText({
  text,
  className,
  animation = 'typewriter',
  delay = 0,
  duration = 0.8,
  speed = 100,
  cursor = false,
  onComplete
}: AnimatedTextProps) {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [showCursor, setShowCursor] = useState(cursor);

  // 打字机效果
  useEffect(() => {
    if (animation !== 'typewriter') return;

    const timer = setTimeout(() => {
      if (currentIndex < text.length) {
        setDisplayText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      } else if (!isComplete) {
        setIsComplete(true);
        onComplete?.();
        
        // 如果有光标，在完成后闪烁一段时间再隐藏
        if (cursor) {
          setTimeout(() => setShowCursor(false), 2000);
        }
      }
    }, delay + currentIndex * speed);

    return () => clearTimeout(timer);
  }, [currentIndex, text, animation, delay, speed, cursor, isComplete, onComplete]);

  // 光标闪烁效果
  const cursorVariants = {
    visible: { opacity: 1 },
    hidden: { opacity: 0 }
  };

  // 根据动画类型渲染不同的动画效果
  const renderAnimatedText = () => {
    switch (animation) {
      case 'typewriter':
        return (
          <span className={cn('inline-block', className)}>
            {displayText}
            {showCursor && (
              <motion.span
                variants={cursorVariants}
                animate="visible"
                initial="hidden"
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  repeatType: 'reverse'
                }}
                className="inline-block ml-1 w-0.5 h-[1em] bg-current"
              />
            )}
          </span>
        );

      case 'fadeIn':
        return (
          <motion.span
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration, delay }}
            className={cn('inline-block', className)}
            onAnimationComplete={onComplete}
          >
            {text}
          </motion.span>
        );

      case 'slideUp':
        return (
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration, delay, ease: 'easeOut' }}
            className={cn('inline-block', className)}
            onAnimationComplete={onComplete}
          >
            {text}
          </motion.span>
        );

      case 'slideDown':
        return (
          <motion.span
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration, delay, ease: 'easeOut' }}
            className={cn('inline-block', className)}
            onAnimationComplete={onComplete}
          >
            {text}
          </motion.span>
        );

      default:
        return <span className={className}>{text}</span>;
    }
  };

  return renderAnimatedText();
}

// 字符级别的动画文字组件
interface AnimatedTextByCharProps {
  text: string;
  className?: string;
  charClassName?: string;
  delay?: number;
  charDelay?: number;
  duration?: number;
  animation?: 'fadeIn' | 'slideUp' | 'slideDown' | 'scale';
  onComplete?: () => void;
}

export function AnimatedTextByChar({
  text,
  className,
  charClassName,
  delay = 0,
  charDelay = 0.05,
  duration = 0.3,
  animation = 'fadeIn',
  onComplete
}: AnimatedTextByCharProps) {
  const chars = text.split('');

  const getCharVariants = () => {
    switch (animation) {
      case 'fadeIn':
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 }
        };
      case 'slideUp':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0 }
        };
      case 'slideDown':
        return {
          hidden: { opacity: 0, y: -20 },
          visible: { opacity: 1, y: 0 }
        };
      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.5 },
          visible: { opacity: 1, scale: 1 }
        };
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 }
        };
    }
  };

  const charVariants = getCharVariants();

  return (
    <motion.span
      className={className}
      initial="hidden"
      animate="visible"
      onAnimationComplete={onComplete}
    >
      {chars.map((char, index) => (
        <motion.span
          key={index}
          variants={charVariants}
          transition={{
            duration,
            delay: delay + index * charDelay,
            ease: 'easeOut'
          }}
          className={cn('inline-block', charClassName)}
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </motion.span>
  );
}

// 单词级别的动画文字组件
interface AnimatedTextByWordProps {
  text: string;
  className?: string;
  wordClassName?: string;
  delay?: number;
  wordDelay?: number;
  duration?: number;
  animation?: 'fadeIn' | 'slideUp' | 'slideDown' | 'scale';
  onComplete?: () => void;
}

export function AnimatedTextByWord({
  text,
  className,
  wordClassName,
  delay = 0,
  wordDelay = 0.1,
  duration = 0.5,
  animation = 'slideUp',
  onComplete
}: AnimatedTextByWordProps) {
  const words = text.split(' ');

  const getWordVariants = () => {
    switch (animation) {
      case 'fadeIn':
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 }
        };
      case 'slideUp':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0 }
        };
      case 'slideDown':
        return {
          hidden: { opacity: 0, y: -20 },
          visible: { opacity: 1, y: 0 }
        };
      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: { opacity: 1, scale: 1 }
        };
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 }
        };
    }
  };

  const wordVariants = getWordVariants();

  return (
    <motion.span
      className={className}
      initial="hidden"
      animate="visible"
      onAnimationComplete={onComplete}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={wordVariants}
          transition={{
            duration,
            delay: delay + index * wordDelay,
            ease: 'easeOut'
          }}
          className={cn('inline-block mr-1', wordClassName)}
        >
          {word}
        </motion.span>
      ))}
    </motion.span>
  );
}
