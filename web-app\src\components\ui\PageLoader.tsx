'use client';

// MySQLAi.de - 页面加载动画组件
// 专业的页面加载指示器，提供流畅的用户体验

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Database, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PageLoaderProps {
  isLoading?: boolean;
  message?: string;
  className?: string;
}

export default function PageLoader({
  isLoading = true,
  message = '加载中...',
  className,
}: PageLoaderProps) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!isLoading) return;

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) return prev;
        return prev + Math.random() * 10;
      });
    }, 200);

    return () => clearInterval(interval);
  }, [isLoading]);

  useEffect(() => {
    if (!isLoading) {
      setProgress(100);
    }
  }, [isLoading]);

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className={cn(
            'fixed inset-0 bg-white z-50 flex flex-col items-center justify-center',
            className
          )}
        >
          {/* 主要加载动画 */}
          <div className="flex flex-col items-center space-y-8">
            {/* Logo动画 */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="relative"
            >
              <div className="flex items-center justify-center w-20 h-20 bg-mysql-primary rounded-2xl shadow-lg">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Database className="w-10 h-10 text-white" />
                </motion.div>
              </div>
              
              {/* 脉冲效果 */}
              <motion.div
                animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 bg-mysql-primary rounded-2xl"
              />
            </motion.div>

            {/* 品牌名称 */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              className="text-center"
            >
              <h1 className="text-2xl font-bold text-mysql-text mb-2">
                MySQLAi.de
              </h1>
              <p className="text-mysql-text-light">
                MySQL智能分析专家
              </p>
            </motion.div>

            {/* 加载进度条 */}
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 200, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              className="relative"
            >
              <div className="w-48 h-2 bg-mysql-primary-light rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: '0%' }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="h-full bg-gradient-to-r from-mysql-primary to-mysql-accent rounded-full"
                />
              </div>
              <div className="text-center mt-3 text-sm text-mysql-text-light">
                {Math.round(progress)}%
              </div>
            </motion.div>

            {/* 加载消息 */}
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
              className="flex items-center space-x-2 text-mysql-text-light"
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">{message}</span>
            </motion.div>
          </div>

          {/* 背景装饰 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {/* 浮动圆点 */}
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ 
                  x: Math.random() * window.innerWidth,
                  y: Math.random() * window.innerHeight,
                  opacity: 0 
                }}
                animate={{ 
                  x: Math.random() * window.innerWidth,
                  y: Math.random() * window.innerHeight,
                  opacity: [0, 0.3, 0] 
                }}
                transition={{ 
                  duration: 4 + Math.random() * 2,
                  repeat: Infinity,
                  delay: i * 0.5,
                  ease: "easeInOut" 
                }}
                className="absolute w-2 h-2 bg-mysql-primary rounded-full"
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// 简化版加载器
interface SimpleLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

export function SimpleLoader({
  size = 'md',
  color = 'mysql-primary',
  className,
}: SimpleLoaderProps) {
  const sizeConfig = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        className={cn(
          'border-2 border-transparent rounded-full',
          `border-t-${color}`,
          sizeConfig[size]
        )}
      />
    </div>
  );
}

// 骨架屏加载器
interface SkeletonLoaderProps {
  lines?: number;
  className?: string;
}

export function SkeletonLoader({ lines = 3, className }: SkeletonLoaderProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {[...Array(lines)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0.3 }}
          animate={{ opacity: [0.3, 0.7, 0.3] }}
          transition={{ 
            duration: 1.5, 
            repeat: Infinity, 
            delay: i * 0.1,
            ease: "easeInOut" 
          }}
          className={cn(
            'h-4 bg-mysql-primary-light rounded',
            i === 0 && 'w-3/4',
            i === 1 && 'w-full',
            i === 2 && 'w-2/3'
          )}
        />
      ))}
    </div>
  );
}

// 内容加载器
interface ContentLoaderProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

export function ContentLoader({
  isLoading,
  children,
  fallback,
  className,
}: ContentLoaderProps) {
  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {fallback || <SkeletonLoader />}
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// 页面过渡加载器
export function PageTransitionLoader() {
  return (
    <motion.div
      initial={{ scaleX: 0 }}
      animate={{ scaleX: 1 }}
      exit={{ scaleX: 0 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-mysql-primary to-mysql-accent z-50 origin-left"
    />
  );
}
