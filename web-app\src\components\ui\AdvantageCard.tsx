'use client';

// MySQLAi.de - AdvantageCard优势卡片组件
// 可复用的优势展示卡片组件

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdvantageCardComponentProps {
  title: string;
  description: string;
  details?: string;
  icon: LucideIcon;
  emoji?: string;
  stats?: Record<string, string>;
  versions?: string[];
  color?: string;
  index?: number;
  layout?: 'left' | 'right' | 'center';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
}

export default function AdvantageCard({
  title,
  description,
  details,
  icon: IconComponent,
  emoji,
  stats,
  versions,
  color = 'mysql-primary',
  index = 0,
  layout = 'left',
  size = 'md',
  onClick,
  className,
}: AdvantageCardComponentProps) {
  const isEven = index % 2 === 0;
  const isReversed = layout === 'right' || (!isEven && layout !== 'center');

  // 尺寸配置
  const sizeConfig = {
    sm: {
      container: 'p-6 gap-6',
      icon: 'w-16 h-16',
      iconSize: 'w-8 h-8',
      emoji: 'text-lg w-8 h-8',
      title: 'text-lg',
      description: 'text-base',
      details: 'text-sm',
    },
    md: {
      container: 'p-8 gap-8',
      icon: 'w-24 h-24',
      iconSize: 'w-12 h-12',
      emoji: 'text-2xl w-10 h-10',
      title: 'text-2xl',
      description: 'text-lg',
      details: 'text-sm',
    },
    lg: {
      container: 'p-10 gap-10',
      icon: 'w-32 h-32',
      iconSize: 'w-16 h-16',
      emoji: 'text-3xl w-12 h-12',
      title: 'text-3xl',
      description: 'text-xl',
      details: 'text-base',
    },
  };

  const config = sizeConfig[size];

  return (
    <motion.div
      initial={{ opacity: 0, x: isReversed ? 50 : -50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ 
        duration: 0.8, 
        delay: index * 0.1,
        ease: "easeOut" 
      }}
      viewport={{ once: true }}
      className="group"
    >
      <div
        className={cn(
          'flex flex-col lg:flex-row items-center bg-white rounded-2xl shadow-lg border border-mysql-border',
          'hover:shadow-2xl hover:scale-102 transition-all duration-300 ease-out',
          isReversed ? 'lg:flex-row-reverse' : 'lg:flex-row',
          config.container,
          onClick && 'cursor-pointer',
          className
        )}
        onClick={onClick}
      >
        {/* 图标和emoji区域 */}
        <div className="flex-shrink-0">
          <div className="relative">
            {/* 背景装饰圆圈 */}
            <div className={cn(
              'absolute inset-0 bg-gradient-to-br rounded-full opacity-20 scale-150',
              `from-${color} to-${color}-dark`
            )} />
            
            {/* 主图标容器 */}
            <div className={cn(
              'relative flex items-center justify-center rounded-full',
              'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
              `from-${color} to-${color}-dark`,
              config.icon
            )}>
              <IconComponent className={cn('text-white', config.iconSize)} />
            </div>
            
            {/* Emoji装饰 */}
            {emoji && (
              <div className={cn(
                'absolute -top-2 -right-2 bg-white rounded-full flex items-center justify-center shadow-md',
                config.emoji
              )}>
                {emoji}
              </div>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className={cn(
          'flex-1 text-center lg:text-left',
          isReversed && 'lg:text-right'
        )}>
          <h3 className={cn(
            'font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300',
            config.title
          )}>
            {title}
          </h3>
          <p className={cn(
            'text-mysql-text-light mb-4 leading-relaxed',
            config.description
          )}>
            {description}
          </p>
          {details && (
            <p className={cn(
              'text-mysql-text-light/80 italic',
              config.details
            )}>
              {details}
            </p>
          )}

          {/* 统计数据展示 */}
          {stats && (
            <div className={cn(
              'flex gap-6 mt-4',
              layout === 'center' ? 'justify-center' : 
              isReversed ? 'lg:justify-end justify-center' : 'justify-start'
            )}>
              {Object.entries(stats).map(([key, value], statIndex) => (
                <div key={statIndex} className="text-center">
                  <div className="text-xl font-bold text-mysql-primary">
                    {value}
                  </div>
                  <div className="text-xs text-mysql-text-light">
                    {key === 'customers' ? '企业客户' :
                     key === 'regions' ? '服务地区' :
                     key === 'users' ? '用户信赖' : key}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 版本支持展示 */}
          {versions && (
            <div className={cn(
              'flex gap-2 mt-4 flex-wrap',
              layout === 'center' ? 'justify-center' :
              isReversed ? 'lg:justify-end justify-center' : 'justify-start'
            )}>
              {versions.map((version, vIndex) => (
                <span
                  key={vIndex}
                  className="px-3 py-1 bg-mysql-primary-light text-mysql-primary text-xs font-medium rounded-full"
                >
                  MySQL {version}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}

// 简化版优势卡片
interface SimpleAdvantageCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  emoji?: string;
  className?: string;
  color?: string;
  onClick?: () => void;
}

export function SimpleAdvantageCard({
  title,
  description,
  icon: IconComponent,
  emoji,
  className,
  color = 'mysql-primary',
  onClick
}: SimpleAdvantageCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -4 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        'bg-white rounded-xl p-6 shadow-md border border-mysql-border',
        'hover:shadow-lg transition-all duration-300 ease-out',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        <div className="relative flex-shrink-0">
          <div className={cn(
            'flex items-center justify-center w-12 h-12 rounded-lg',
            `bg-${color} text-white`
          )}>
            <IconComponent className="w-6 h-6" />
          </div>
          {emoji && (
            <div className="absolute -top-1 -right-1 text-sm bg-white rounded-full w-6 h-6 flex items-center justify-center shadow-sm">
              {emoji}
            </div>
          )}
        </div>
        <div className="flex-1">
          <h4 className="text-lg font-semibold text-mysql-text mb-2">
            {title}
          </h4>
          <p className="text-mysql-text-light text-sm leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </motion.div>
  );
}

// 紧凑版优势卡片
interface CompactAdvantageCardProps {
  title: string;
  icon: LucideIcon;
  emoji?: string;
  className?: string;
  color?: string;
  onClick?: () => void;
}

export function CompactAdvantageCard({
  title,
  icon: IconComponent,
  emoji,
  className,
  color = 'mysql-primary',
  onClick
}: CompactAdvantageCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={cn(
        'bg-white rounded-lg p-4 shadow-sm border border-mysql-border',
        'hover:shadow-md transition-all duration-200 ease-out',
        'flex items-center space-x-3',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <div className="relative flex-shrink-0">
        <div className={cn(
          'flex items-center justify-center w-10 h-10 rounded-lg',
          `bg-${color} text-white`
        )}>
          <IconComponent className="w-5 h-5" />
        </div>
        {emoji && (
          <div className="absolute -top-1 -right-1 text-xs bg-white rounded-full w-5 h-5 flex items-center justify-center shadow-sm">
            {emoji}
          </div>
        )}
      </div>
      <span className="text-mysql-text font-medium">
        {title}
      </span>
    </motion.div>
  );
}
