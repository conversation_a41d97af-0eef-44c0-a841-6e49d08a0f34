'use client';

// MySQLAi.de - 管理系统主布局组件
// 提供侧边栏导航和主内容区域的布局结构

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Database, LogOut, User } from 'lucide-react';
// import { cn } from '@/lib/utils';
import { AdminAuth } from '@/lib/admin-auth';
import { useRouter } from 'next/navigation';
import AdminSidebar from './AdminSidebar';
import Button from '@/components/ui/Button';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // 获取管理员信息
  const adminUser = AdminAuth.getUser();

  // 切换侧边栏
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // 关闭侧边栏
  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  // 处理登出
  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await AdminAuth.logout();
      router.replace('/admin/login');
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 桌面端侧边栏 */}
      <aside className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-80 border-r border-mysql-border bg-white h-full">
          <AdminSidebar onNavigate={closeSidebar} />
        </div>
      </aside>

      {/* 移动端侧边栏遮罩和侧边栏 */}
      <AnimatePresence>
        {isSidebarOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm lg:hidden z-40"
              onClick={closeSidebar}
            />

            {/* 移动端侧边栏 */}
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl lg:hidden z-50"
            >
              <AdminSidebar onNavigate={closeSidebar} />
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col min-w-0 h-full">
        {/* 顶部栏 */}
        <header className="bg-white border-b border-mysql-border px-4 lg:px-6 py-4">
          <div className="flex items-center justify-between">
            {/* 左侧：移动端菜单按钮 + 标题 */}
            <div className="flex items-center space-x-4">
              {/* 移动端汉堡菜单按钮 */}
              <button
                type="button"
                onClick={toggleSidebar}
                className="lg:hidden p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200"
                aria-label="切换菜单"
              >
                {isSidebarOpen ? (
                  <X className="w-6 h-6" />
                ) : (
                  <Menu className="w-6 h-6" />
                )}
              </button>

              {/* 页面标题区域 */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-mysql-primary rounded-lg lg:hidden">
                  <Database className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-mysql-text">
                    MySQLAi.de 管理后台
                  </h1>
                  <p className="text-sm text-mysql-text-light hidden sm:block">
                    知识库内容管理系统
                  </p>
                </div>
              </div>
            </div>

            {/* 右侧：用户信息和操作 */}
            <div className="flex items-center space-x-4">
              {/* 用户信息 */}
              <div className="hidden sm:flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-mysql-primary rounded-full">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-mysql-text">
                    管理员
                  </p>
                  <p className="text-xs text-mysql-text-light">
                    {adminUser?.username}
                  </p>
                </div>
              </div>

              {/* 登出按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                loading={isLoggingOut}
                icon={<LogOut className="w-4 h-4" />}
                className="text-mysql-text border-mysql-border hover:border-mysql-primary"
              >
                <span className="hidden sm:inline">登出</span>
              </Button>
            </div>
          </div>
        </header>

        {/* 主内容区域 */}
        <div className="flex-1 overflow-y-auto bg-gray-50">
          <div className="p-4 lg:p-6">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}
