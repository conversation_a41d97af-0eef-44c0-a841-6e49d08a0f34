'use client';

// MySQLAi.de - SearchBox搜索组件
// 集成fuse.js实现知识库内容的模糊搜索功能，支持实时搜索建议和搜索结果高亮

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Clock, ArrowRight, Loader2 } from 'lucide-react';
import Fuse, { FuseResult, FuseResultMatch } from 'fuse.js';
import { cn, debounce } from '@/lib/utils';
import { SearchBoxProps, KnowledgeItem } from '@/lib/types';
import { getKnowledgeItems, searchKnowledgeItems } from '@/lib/knowledge';

// Fuse.js搜索配置
const fuseOptions = {
  keys: [
    { name: 'title', weight: 0.4 },
    { name: 'description', weight: 0.3 },
    { name: 'content', weight: 0.2 },
    { name: 'tags', weight: 0.1 }
  ],
  threshold: 0.3,
  includeMatches: true,
  includeScore: true,
  minMatchCharLength: 2,
  ignoreLocation: true,
};

// 搜索结果接口
interface SearchResult {
  item: KnowledgeItem;
  matches?: readonly FuseResultMatch[];
  score?: number;
}

// 搜索历史记录接口
interface SearchHistory {
  query: string;
  timestamp: number;
}

const SearchBox = React.forwardRef<HTMLDivElement, SearchBoxProps>(({
  placeholder = '搜索MySQL知识点...',
  onSearch,
  onResultSelect,
  className,
  ...props
}, ref) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [fuse, setFuse] = useState<Fuse<KnowledgeItem> | null>(null);

  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 初始化Fuse.js实例
  useEffect(() => {
    const initializeFuse = async () => {
      const knowledgeItems = await getKnowledgeItems();
      const fuseInstance = new Fuse(knowledgeItems, fuseOptions);
      setFuse(fuseInstance);
    };
    initializeFuse();
  }, []);

  // 加载搜索历史
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedHistory = localStorage.getItem('mysql-search-history');
      if (savedHistory) {
        try {
          const history = JSON.parse(savedHistory);
          setSearchHistory(history.slice(0, 5)); // 最多保存5条历史记录
        } catch (error) {
          console.error('加载搜索历史失败:', error);
        }
      }
    }
  }, []);

  // 保存搜索历史
  const saveSearchHistory = useCallback((searchQuery: string) => {
    if (!searchQuery.trim() || typeof window === 'undefined') return;

    const newHistory = [
      { query: searchQuery, timestamp: Date.now() },
      ...searchHistory.filter(item => item.query !== searchQuery)
    ].slice(0, 5);

    setSearchHistory(newHistory);
    localStorage.setItem('mysql-search-history', JSON.stringify(newHistory));
  }, [searchHistory]);

  // 执行搜索
  const performSearch = useCallback((searchQuery: string) => {
    if (!fuse || !searchQuery.trim()) {
      setSearchResults([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    
    try {
      const results = fuse.search(searchQuery, { limit: 8 });
      const formattedResults: SearchResult[] = results.map(result => ({
        item: result.item,
        matches: result.matches,
        score: result.score
      }));
      
      setSearchResults(formattedResults);
      setSelectedIndex(-1);
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [fuse]);

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      performSearch(searchQuery);
    }, 300),
    [performSearch]
  );

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(true);
    
    if (value.trim()) {
      debouncedSearch(value);
    } else {
      setSearchResults([]);
      setIsLoading(false);
    }
  };

  // 处理搜索提交
  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query;
    if (!finalQuery.trim()) return;

    saveSearchHistory(finalQuery);
    setIsOpen(false);
    onSearch?.(finalQuery);
  };

  // 处理结果选择
  const handleResultSelect = (item: KnowledgeItem) => {
    saveSearchHistory(query);
    setQuery('');
    setIsOpen(false);
    onResultSelect?.(item);
  };

  // 处理历史记录选择
  const handleHistorySelect = (historyQuery: string) => {
    setQuery(historyQuery);
    performSearch(historyQuery);
    inputRef.current?.focus();
  };

  // 键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const totalItems = searchResults.length + searchHistory.length;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev + 1) % totalItems);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev - 1 + totalItems) % totalItems);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (selectedIndex < searchResults.length) {
            handleResultSelect(searchResults[selectedIndex].item);
          } else {
            const historyIndex = selectedIndex - searchResults.length;
            handleHistorySelect(searchHistory[historyIndex].query);
          }
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // 清空搜索
  const clearSearch = () => {
    setQuery('');
    setSearchResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 高亮匹配文本
  const highlightMatches = (text: string, matches?: readonly FuseResultMatch[]) => {
    if (!matches || matches.length === 0) return text;

    let highlightedText = text;
    const sortedMatches = matches
      .filter(match => match.value === text)
      .flatMap(match => match.indices)
      .sort((a, b) => b[0] - a[0]); // 从后往前替换，避免索引偏移

    sortedMatches.forEach(([start, end]) => {
      const before = highlightedText.slice(0, start);
      const match = highlightedText.slice(start, end + 1);
      const after = highlightedText.slice(end + 1);
      highlightedText = `${before}<mark class="bg-yellow-200 text-mysql-text">${match}</mark>${after}`;
    });

    return highlightedText;
  };

  return (
    <div ref={containerRef} className={cn('relative w-full max-w-2xl', className)} {...props}>
      {/* 搜索输入框 */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-mysql-text-light" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className={cn(
            'w-full pl-10 pr-10 py-3 text-base',
            'bg-white border-2 border-mysql-border rounded-lg',
            'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
            'placeholder-mysql-text-light text-mysql-text',
            'transition-all duration-200'
          )}
        />

        {/* 清空按钮 */}
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-mysql-text-light hover:text-mysql-primary transition-colors"
            aria-label="清空搜索"
            title="清空搜索"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* 搜索结果下拉列表 */}
      <AnimatePresence>
        {isOpen && (query.trim() || searchHistory.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-mysql-border rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto"
          >
            {/* 加载状态 */}
            {isLoading && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-5 w-5 animate-spin text-mysql-primary mr-2" />
                <span className="text-mysql-text-light">搜索中...</span>
              </div>
            )}

            {/* 搜索结果 */}
            {!isLoading && searchResults.length > 0 && (
              <div>
                <div className="px-4 py-2 text-xs font-medium text-mysql-text-light bg-mysql-primary-light border-b border-mysql-border">
                  搜索结果 ({searchResults.length})
                </div>
                {searchResults.map((result, index) => (
                  <motion.button
                    key={result.item.id}
                    onClick={() => handleResultSelect(result.item)}
                    className={cn(
                      'w-full px-4 py-3 text-left hover:bg-mysql-primary-light transition-colors',
                      'border-b border-mysql-border last:border-b-0',
                      selectedIndex === index && 'bg-mysql-primary-light'
                    )}
                    whileHover={{ x: 4 }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div 
                          className="font-medium text-mysql-text truncate"
                          dangerouslySetInnerHTML={{ 
                            __html: highlightMatches(result.item.title, result.matches?.filter(m => m.key === 'title'))
                          }}
                        />
                        <div 
                          className="text-sm text-mysql-text-light mt-1 line-clamp-2"
                          dangerouslySetInnerHTML={{ 
                            __html: highlightMatches(result.item.description || '', result.matches?.filter(m => m.key === 'description'))
                          }}
                        />
                        <div className="flex items-center mt-2 space-x-2">
                          <span className={cn(
                            'px-2 py-1 text-xs rounded',
                            result.item.difficulty === 'beginner' && 'bg-green-100 text-green-700',
                            result.item.difficulty === 'intermediate' && 'bg-yellow-100 text-yellow-700',
                            result.item.difficulty === 'advanced' && 'bg-red-100 text-red-700'
                          )}>
                            {result.item.difficulty === 'beginner' && '初级'}
                            {result.item.difficulty === 'intermediate' && '中级'}
                            {result.item.difficulty === 'advanced' && '高级'}
                          </span>
                          {result.item.tags?.slice(0, 2).map(tag => (
                            <span key={tag} className="px-2 py-1 text-xs bg-mysql-primary-light text-mysql-primary rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 text-mysql-text-light ml-2 flex-shrink-0" />
                    </div>
                  </motion.button>
                ))}
              </div>
            )}

            {/* 搜索历史 */}
            {!isLoading && !query.trim() && searchHistory.length > 0 && (
              <div>
                <div className="px-4 py-2 text-xs font-medium text-mysql-text-light bg-mysql-primary-light border-b border-mysql-border">
                  搜索历史
                </div>
                {searchHistory.map((history, index) => (
                  <button
                    type="button"
                    key={`${history.query}-${history.timestamp}`}
                    onClick={() => handleHistorySelect(history.query)}
                    className={cn(
                      'w-full px-4 py-3 text-left hover:bg-mysql-primary-light transition-colors',
                      'border-b border-mysql-border last:border-b-0',
                      selectedIndex === searchResults.length + index && 'bg-mysql-primary-light'
                    )}
                  >
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-mysql-text-light mr-3" />
                      <span className="text-mysql-text">{history.query}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* 无结果提示 */}
            {!isLoading && query.trim() && searchResults.length === 0 && (
              <div className="px-4 py-8 text-center">
                <Search className="h-8 w-8 text-mysql-text-light mx-auto mb-2" />
                <p className="text-mysql-text-light">未找到相关内容</p>
                <p className="text-sm text-mysql-text-light mt-1">
                  尝试使用不同的关键词搜索
                </p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

SearchBox.displayName = 'SearchBox';

export default SearchBox;
