'use client';

// MySQLAi.de - 知识库模块专用布局
// 为知识库模块提供统一的布局结构和面包屑导航

import React from 'react';
import KnowledgeLayout from '@/components/knowledge/KnowledgeLayout';

interface KnowledgeLayoutProps {
  children: React.ReactNode;
}

export default function KnowledgeRootLayout({ children }: KnowledgeLayoutProps) {
  return (
    <KnowledgeLayout fullWidth={true}>
      {children}
    </KnowledgeLayout>
  );
}
