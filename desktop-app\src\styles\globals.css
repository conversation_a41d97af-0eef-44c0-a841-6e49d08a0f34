@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #2d3748;

  /* MySQL Theme Colors */
  --mysql-primary: #00758f;
  --mysql-primary-dark: #003545;
  --mysql-primary-light: #e6f3f7;
  --mysql-accent: #0066cc;
  --mysql-text: #2d3748;
  --mysql-text-light: #718096;
  --mysql-border: #e2e8f0;
  --mysql-success: #38a169;
  --mysql-warning: #d69e2e;
  --mysql-error: #e53e3e;
  --mysql-info: #3182ce;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: "Inter", "Avenir", "Helvetica", "Arial", sans-serif;
  --font-mono: "Consolas", "Monaco", "Courier New", monospace;

  /* MySQL Custom Colors */
  --color-mysql-primary: var(--mysql-primary);
  --color-mysql-primary-dark: var(--mysql-primary-dark);
  --color-mysql-primary-light: var(--mysql-primary-light);
  --color-mysql-accent: var(--mysql-accent);
  --color-mysql-text: var(--mysql-text);
  --color-mysql-text-light: var(--mysql-text-light);
  --color-mysql-border: var(--mysql-border);
  --color-mysql-success: var(--mysql-success);
  --color-mysql-warning: var(--mysql-warning);
  --color-mysql-error: var(--mysql-error);
  --color-mysql-info: var(--mysql-info);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a202c;
    --foreground: #f7fafc;
    --mysql-text: #f7fafc;
    --mysql-text-light: #a0aec0;
    --mysql-border: #2d3748;
  }
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-sans);
  background-color: var(--background);
  color: var(--foreground);
}

body {
  color: rgb(var(--foreground));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--mysql-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--mysql-primary-dark);
}

/* 选择文本样式 */
::selection {
  background-color: var(--mysql-primary-light);
  color: var(--mysql-primary-dark);
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--mysql-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 按钮基础样式 */
button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input:disabled,
textarea:disabled,
select:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 图片基础样式 */
img {
  max-width: 100%;
  height: auto;
}

/* 表格基础样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 列表基础样式 */
ul,
ol {
  list-style: none;
}

/* 标题基础样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--mysql-text);
}

/* 段落基础样式 */
p {
  line-height: 1.6;
  color: var(--mysql-text);
}

/* 链接基础样式 */
a {
  color: var(--mysql-primary);
  transition: color 0.2s ease;
}

a:hover {
  color: var(--mysql-primary-dark);
}

/* 代码基础样式 */
code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--mysql-primary-light);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  color: var(--mysql-primary-dark);
}

pre {
  font-family: var(--font-mono);
  background-color: var(--mysql-primary-light);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  line-height: 1.5;
}

pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  color: inherit;
}

/* 分隔线样式 */
hr {
  border: none;
  height: 1px;
  background-color: var(--mysql-border);
  margin: 1rem 0;
}

/* 引用样式 */
blockquote {
  border-left: 4px solid var(--mysql-primary);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--mysql-text-light);
}

/* 工具提示样式 */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--mysql-text);
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  opacity: 1;
  transition: opacity 0.2s ease;
}

[data-tooltip]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(100%);
  border: 4px solid transparent;
  border-top-color: var(--mysql-text);
  z-index: 1000;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* 响应式工具类 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* 桌面应用特定样式 */
.desktop-app {
  user-select: none;
  -webkit-user-select: none;
  -webkit-app-region: no-drag;
}

.desktop-app .draggable {
  -webkit-app-region: drag;
}

.desktop-app .no-drag {
  -webkit-app-region: no-drag;
}

/* 自定义滚动条（桌面应用） */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--mysql-border);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--mysql-text-light);
}

/* 打印样式 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .no-print {
    display: none !important;
  }
}