/**
 * 设置页面组件
 * 应用配置和偏好设置
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Save, 
  RotateCcw, 
  FolderOpen,
  Bell,
  Download,
  FileText,
  Palette,
  // Globe
} from 'lucide-react';
import { useAppSettings, useTheme, useLanguage, useAppActions } from '../store/appStore';
import { Button } from './ui';
import { cn } from '../lib/utils';

interface SettingsPanelProps {
  className?: string;
}

export default function SettingsPanel({ className }: SettingsPanelProps) {
  const settings = useAppSettings();
  const theme = useTheme();
  const language = useLanguage();
  const { updateSettings, setTheme, setLanguage } = useAppActions();
  
  const [localSettings, setLocalSettings] = useState(settings);
  const [hasChanges, setHasChanges] = useState(false);

  // 更新本地设置
  const updateLocalSetting = <K extends keyof typeof settings>(
    key: K, 
    value: typeof settings[K]
  ) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  // 保存设置
  const handleSave = () => {
    updateSettings(localSettings);
    setHasChanges(false);
  };

  // 重置设置
  const handleReset = () => {
    setLocalSettings(settings);
    setHasChanges(false);
  };

  // 选择下载目录
  const handleSelectDownloadPath = async () => {
    // 这里可以调用Tauri API来选择文件夹
    // const selected = await open({ directory: true });
    // if (selected) {
    //   updateLocalSetting('downloadPath', selected);
    // }
  };

  return (
    <div className={cn('p-6 space-y-6', className)}>
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-mysql-text">应用设置</h2>
          <p className="text-mysql-text-light mt-1">配置应用偏好和系统选项</p>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges}
            icon={<RotateCcw className="w-4 h-4" />}
          >
            重置
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={!hasChanges}
            icon={<Save className="w-4 h-4" />}
          >
            保存设置
          </Button>
        </div>
      </div>

      {/* 设置分组 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 外观设置 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl p-6 shadow-sm border border-mysql-border"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Palette className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-mysql-text">外观设置</h3>
              <p className="text-sm text-mysql-text-light">主题和界面配置</p>
            </div>
          </div>

          <div className="space-y-4">
            {/* 主题选择 */}
            <div>
              <label className="block text-sm font-medium text-mysql-text mb-2">
                主题模式
              </label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: 'light', label: '浅色' },
                  { value: 'dark', label: '深色' },
                  { value: 'auto', label: '自动' }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setTheme(option.value as any)}
                    className={cn(
                      'px-3 py-2 text-sm rounded-lg border transition-colors',
                      theme === option.value
                        ? 'bg-mysql-primary text-white border-mysql-primary'
                        : 'bg-white text-mysql-text border-mysql-border hover:bg-mysql-primary-light'
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 语言选择 */}
            <div>
              <label className="block text-sm font-medium text-mysql-text mb-2">
                界面语言
              </label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value as any)}
                className="w-full px-3 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary"
              >
                <option value="zh-CN">简体中文</option>
                <option value="en-US">English</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* 下载设置 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl p-6 shadow-sm border border-mysql-border"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Download className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-mysql-text">下载设置</h3>
              <p className="text-sm text-mysql-text-light">下载路径和并发配置</p>
            </div>
          </div>

          <div className="space-y-4">
            {/* 下载路径 */}
            <div>
              <label className="block text-sm font-medium text-mysql-text mb-2">
                下载目录
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={localSettings.downloadPath}
                  onChange={(e) => updateLocalSetting('downloadPath', e.target.value)}
                  className="flex-1 px-3 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary"
                  placeholder="选择下载目录"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectDownloadPath}
                  icon={<FolderOpen className="w-4 h-4" />}
                >
                  浏览
                </Button>
              </div>
            </div>

            {/* 最大并发下载数 */}
            <div>
              <label className="block text-sm font-medium text-mysql-text mb-2">
                最大并发下载数
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={localSettings.maxConcurrentDownloads}
                onChange={(e) => updateLocalSetting('maxConcurrentDownloads', parseInt(e.target.value) || 1)}
                className="w-full px-3 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary"
              />
            </div>

            {/* 自动更新 */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-mysql-text">自动更新</span>
                <p className="text-xs text-mysql-text-light">自动检查并下载应用更新</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localSettings.autoUpdate}
                  onChange={(e) => updateLocalSetting('autoUpdate', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mysql-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mysql-primary"></div>
              </label>
            </div>
          </div>
        </motion.div>

        {/* 通知设置 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl p-6 shadow-sm border border-mysql-border"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Bell className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-mysql-text">通知设置</h3>
              <p className="text-sm text-mysql-text-light">消息提醒和通知配置</p>
            </div>
          </div>

          <div className="space-y-4">
            {/* 启用通知 */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-mysql-text">启用通知</span>
                <p className="text-xs text-mysql-text-light">显示系统通知消息</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localSettings.enableNotifications}
                  onChange={(e) => updateLocalSetting('enableNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mysql-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mysql-primary"></div>
              </label>
            </div>
          </div>
        </motion.div>

        {/* 日志设置 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl p-6 shadow-sm border border-mysql-border"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-mysql-text">日志设置</h3>
              <p className="text-sm text-mysql-text-light">日志记录和调试配置</p>
            </div>
          </div>

          <div className="space-y-4">
            {/* 启用日志 */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-mysql-text">启用日志</span>
                <p className="text-xs text-mysql-text-light">记录应用运行日志</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localSettings.enableLogging}
                  onChange={(e) => updateLocalSetting('enableLogging', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mysql-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mysql-primary"></div>
              </label>
            </div>

            {/* 日志级别 */}
            <div>
              <label className="block text-sm font-medium text-mysql-text mb-2">
                日志级别
              </label>
              <select
                value={localSettings.logLevel}
                onChange={(e) => updateLocalSetting('logLevel', e.target.value as any)}
                disabled={!localSettings.enableLogging}
                className="w-full px-3 py-2 border border-mysql-border rounded-lg focus:outline-none focus:ring-2 focus:ring-mysql-primary disabled:opacity-50"
              >
                <option value="debug">调试 (Debug)</option>
                <option value="info">信息 (Info)</option>
                <option value="warn">警告 (Warning)</option>
                <option value="error">错误 (Error)</option>
              </select>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 更改提示 */}
      {hasChanges && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-yellow-50 border border-yellow-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <Bell className="w-5 h-5 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">
              您有未保存的更改，请点击"保存设置"按钮保存配置。
            </span>
          </div>
        </motion.div>
      )}
    </div>
  );
}