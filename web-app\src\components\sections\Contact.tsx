'use client';

// MySQLAi.de - 联系支持组件
// 版本：v3.0 - 2025-06-28 - 统一专业特性风格

import React from 'react';
import { motion } from 'framer-motion';
import { Phone, Mail, MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ContactProps {
  className?: string;
}

// 联系方式配置 - 参考专业特性风格
const contactMethods = [
  {
    id: 'phone',
    title: '电话支持',
    contact: '+86 ************',
    icon: Phone,
    available: '7×24小时',
    href: 'tel:+8640088899999',
    description: '专家团队5分钟快速响应',
    color: 'mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
    details: [
      '5分钟快速响应',
      '专业技术团队',
      '全年无休服务',
      '紧急故障处理',
    ],
  },
  {
    id: 'email',
    title: '邮件支持',
    contact: '<EMAIL>',
    icon: Mail,
    available: '24小时内回复',
    href: 'mailto:<EMAIL>',
    description: '专业技术咨询与解决方案',
    color: 'mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
    details: [
      '详细技术咨询',
      '定制解决方案',
      '24小时内回复',
      '专业建议指导',
    ],
  },
  {
    id: 'chat',
    title: '在线客服',
    contact: '点击开始对话',
    icon: MessageCircle,
    available: '工作日 9:00-18:00',
    href: '#',
    description: '实时沟通快速响应技术需求',
    color: 'mysql-success',
    gradient: 'from-mysql-success to-green-600',
    details: [
      '实时在线沟通',
      '快速问题解答',
      '工作日即时响应',
      '技术需求分析',
    ],
  },
];

function Contact({ className }: ContactProps) {
  return (
    <section
      className={cn(
        'py-20 px-4 bg-gradient-to-b from-mysql-primary-light/30 to-white',
        className
      )}
    >
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 - 参考专业特性风格 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-mysql-text mb-6">
            联系我们
          </h2>
          <p className="text-lg sm:text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed">
            专业的MySQL技术支持团队，7×24小时为您提供最优质的服务和技术支持
          </p>
        </motion.div>

        {/* 联系方式网格 - 参考专业特性卡片风格 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {contactMethods.map((method, index) => {
            const IconComponent = method.icon;

            return (
              <motion.div
                key={method.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.15,
                  ease: "easeOut"
                }}
                viewport={{ once: true }}
                className="group"
              >
                <a
                  href={method.href}
                  className="relative bg-white rounded-2xl p-8 shadow-lg border border-mysql-border hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out overflow-hidden block"
                >
                  {/* 渐变背景装饰 - 参考专业特性 */}
                  <div className={cn(
                    'absolute top-0 left-0 right-0 h-1 bg-gradient-to-r',
                    method.gradient
                  )} />

                  {/* 图标区域 - 参考专业特性大图标 */}
                  <div className="flex items-center justify-center mb-6">
                    <div className={cn(
                      'flex items-center justify-center w-20 h-20 rounded-2xl',
                      'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
                      method.gradient
                    )}>
                      <IconComponent className="w-10 h-10 text-white" />
                    </div>
                  </div>

                  {/* 标题和描述 - 参考专业特性 */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300">
                      {method.title}
                    </h3>
                    <div className="text-mysql-primary font-medium mb-2">
                      {method.contact}
                    </div>
                    <div className="text-sm text-mysql-text-light mb-3">
                      {method.available}
                    </div>
                    <p className="text-mysql-text-light leading-relaxed text-sm">
                      {method.description}
                    </p>
                  </div>

                  {/* 详细特性列表 - 参考专业特性 */}
                  <div className="space-y-2">
                    {method.details.map((detail, detailIndex) => (
                      <motion.div
                        key={detailIndex}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{
                          duration: 0.3,
                          delay: (index * 0.15) + (detailIndex * 0.1) + 0.3
                        }}
                        viewport={{ once: true }}
                        className="flex items-center text-sm text-mysql-text"
                      >
                        <div className={cn(
                          'w-2 h-2 rounded-full mr-3 flex-shrink-0',
                          `bg-gradient-to-r ${method.gradient}`
                        )} />
                        <span>{detail}</span>
                      </motion.div>
                    ))}
                  </div>

                  {/* 悬停时的光效 - 参考专业特性 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
                </a>
              </motion.div>
            );
          })}
        </div>

        {/* 底部统计数据 - 参考专业特性 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">5分钟</div>
              <div className="text-mysql-text-light">响应时间</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">7×24</div>
              <div className="text-mysql-text-light">小时支持</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">99.9%</div>
              <div className="text-mysql-text-light">服务可用性</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-mysql-primary mb-2">15年+</div>
              <div className="text-mysql-text-light">专业经验</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

export default Contact;