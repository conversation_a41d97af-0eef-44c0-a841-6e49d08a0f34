/**
 * ER图生成工具的TypeScript类型定义
 * 基于原始Java模型和Vue组件的数据结构设计
 */

// ===== 核心数据模型 =====

/**
 * 数据库列信息
 */
export interface ColumnInfo {
  /** 列名 */
  name: string;
  /** 数据类型 (VARCHAR, INT, DATETIME等) */
  type: string;
  /** 是否可为空 */
  nullable: boolean;
  /** 默认值 */
  defaultValue?: string;
  /** 列注释 */
  comment?: string;
  /** 是否有UNIQUE约束 */
  isUnique?: boolean;
}

/**
 * 外键关系信息
 */
export interface ForeignKeyInfo {
  /** 当前表的列名 */
  columnName: string;
  /** 引用的表名 */
  referenceTable: string;
  /** 引用的列名 */
  referenceColumn: string;
}

/**
 * 数据库表信息
 */
export interface TableInfo {
  /** 表名 */
  tableName: string;
  /** 表的所有列 */
  columns: ColumnInfo[];
  /** 主键列名数组 */
  primaryKeys: string[];
  /** 外键关系数组 */
  foreignKeys: ForeignKeyInfo[];
  /** 表注释 */
  comment?: string;
}

/**
 * 数据库关系类型
 * 用于标识实体间的关系基数
 */
export type RelationshipType = '1:1' | '1:N' | 'N:M';

// ===== GoJS图形相关类型 =====

/**
 * GoJS节点数据类型
 */
export interface DiagramNodeData {
  /** 节点类型 */
  category: 'Entity' | 'Attribute' | 'Relationship';
  /** 节点唯一标识 */
  key: string;
  /** 显示名称 */
  name: string;
  /** 是否为主键（仅属性节点使用） */
  isPrimaryKey?: boolean;
}

/**
 * GoJS连接线数据类型
 */
export interface DiagramLinkData {
  /** 起始节点key */
  from: string;
  /** 目标节点key */
  to: string;
  /** 连接线上的文本标签 */
  text?: string;
}

/**
 * 图形布局配置
 */
export interface DiagramLayoutConfig {
  /** 布局类型 */
  layoutType: 'ForceDirected' | 'Layered' | 'Circular';
  /** 节点间距 */
  nodeSpacing: number;
  /** 层级间距 */
  layerSpacing: number;
  /** 是否启用动画 */
  animationEnabled: boolean;
}

/**
 * 图形样式配置
 */
export interface DiagramStyleConfig {
  /** 实体节点颜色 */
  entityColor: string;
  /** 属性节点颜色 */
  attributeColor: string;
  /** 主键属性颜色 */
  primaryKeyColor: string;
  /** 关系节点颜色 */
  relationshipColor: string;
  /** 连接线颜色 */
  linkColor: string;
  /** 字体大小 */
  fontSize: number;
  /** 字体系列 */
  fontFamily: string;
}

// ===== 导出功能相关类型 =====

/**
 * 导出图片格式
 */
export type ExportFormat = 'PNG' | 'JPEG';

/**
 * 导出背景类型
 */
export type ExportBackground = 'transparent' | 'white';

/**
 * 导出质量等级
 */
export type ExportQuality = 'high' | 'medium' | 'low';

/**
 * 导出选项配置
 */
export interface ExportOptions {
  /** 导出格式 */
  format: ExportFormat;
  /** 背景设置 */
  background: ExportBackground;
  /** 图片质量 */
  quality: ExportQuality;
  /** 自定义文件名 */
  filename?: string;
  /** 图片缩放比例 */
  scale?: number;
}

// ===== 组件Props接口 =====

/**
 * SQL编辑器组件Props
 */
export interface SqlEditorProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  /** SQL文本内容 */
  value: string;
  /** 文本变化回调 */
  onChange: (value: string) => void;
  /** 解析SQL回调 */
  onParse: () => void;
  /** 是否正在解析 */
  isLoading?: boolean;
  /** 解析错误信息 */
  error?: string | null;
  /** 是否只读 */
  readOnly?: boolean;
  /** 是否显示头部 */
  showHeader?: boolean;
}

/**
 * ER图显示组件Props
 */
export interface DiagramViewerProps {
  /** 表数据 */
  tableData: TableInfo[];
  /** 布局配置 */
  layoutConfig?: DiagramLayoutConfig;
  /** 样式配置 */
  styleConfig?: DiagramStyleConfig;
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 错误信息 */
  error?: string | null;
  /** 图形更新回调 */
  onDiagramUpdate?: (diagram: unknown) => void;
  /** CSS类名 */
  className?: string;
}

/**
 * 导出对话框组件Props
 */
export interface ExportDialogProps {
  /** 是否显示对话框 */
  isOpen: boolean;
  /** 关闭对话框回调 */
  onClose: () => void;
  /** 导出回调 */
  onExport: (options: ExportOptions) => void;
  /** 是否正在导出 */
  isExporting?: boolean;
  /** CSS类名 */
  className?: string;
}

// ===== Hook返回类型 =====

/**
 * SQL解析Hook返回类型
 */
export interface UseSqlParserReturn {
  /** 解析SQL函数 */
  parseSql: (sql: string) => Promise<TableInfo[]>;
  /** 是否正在解析 */
  isLoading: boolean;
  /** 解析错误 */
  error: string | null;
  /** 清除错误 */
  clearError: () => void;
}

/**
 * 图形管理Hook返回类型
 */
export interface UseDiagramReturn {
  /** GoJS图形实例引用 */
  diagramRef: React.RefObject<HTMLDivElement>;
  /** 图形实例 */
  diagram: unknown | null;
  /** 更新图形数据 */
  updateDiagram: (tableData: TableInfo[]) => void;
  /** 适应画布 */
  zoomToFit: () => void;
  /** 重置布局 */
  resetLayout: () => void;
  /** 是否已初始化 */
  isInitialized: boolean;
}

/**
 * 导出功能Hook返回类型
 */
export interface UseExportReturn {
  /** 导出图形函数 */
  exportDiagram: (diagram: unknown, options: ExportOptions) => Promise<void>;
  /** 是否正在导出 */
  isExporting: boolean;
  /** 导出错误 */
  error: string | null;
}

// ===== 工具函数类型 =====

/**
 * SQL解析结果
 */
export interface SqlParseResult {
  /** 解析成功的表数据 */
  tables: TableInfo[];
  /** 解析警告信息 */
  warnings: string[];
  /** 解析统计信息 */
  stats: {
    tableCount: number;
    columnCount: number;
    foreignKeyCount: number;
  };
}

/**
 * 解析错误信息
 */
export interface ParseError {
  /** 错误消息 */
  message: string;
  /** 错误位置（行号） */
  line?: number;
  /** 错误位置（列号） */
  column?: number;
  /** 错误类型 */
  type: 'syntax' | 'semantic' | 'unknown';
}
