/**
 * 反调试检测模块
 * 实现多种反调试和反分析技术
 */

use std::time::{Duration, Instant};
use std::sync::atomic::{AtomicBool, Ordering};

/// 反调试检查结果
#[derive(Debug, Clone, PartialEq)]
pub enum AntiDebugResult {
    Safe,
    DebuggerDetected,
    Suspicious,
}

/// 全局调试检测标志
static DEBUGGER_DETECTED: AtomicBool = AtomicBool::new(false);

/// 检查调试器存在
pub fn check_debugger_presence() -> AntiDebugResult {
    // 如果之前已经检测到调试器，直接返回
    if DEBUGGER_DETECTED.load(Ordering::Relaxed) {
        return AntiDebugResult::DebuggerDetected;
    }

    let mut suspicious_count = 0;

    // 检查1：Windows IsDebuggerPresent API
    #[cfg(target_os = "windows")]
    {
        if check_windows_debugger() {
            DEBUGGER_DETECTED.store(true, Ordering::Relaxed);
            return AntiDebugResult::DebuggerDetected;
        }
    }

    // 检查2：Linux/macOS ptrace检测
    #[cfg(any(target_os = "linux", target_os = "macos"))]
    {
        if check_unix_debugger() {
            DEBUGGER_DETECTED.store(true, Ordering::Relaxed);
            return AntiDebugResult::DebuggerDetected;
        }
    }

    // 检查3：进程名称检查
    if check_suspicious_processes() {
        suspicious_count += 1;
    }

    // 检查4：环境变量检查
    if check_debug_environment() {
        suspicious_count += 1;
    }

    // 检查5：内存模式检查
    if check_memory_patterns() {
        suspicious_count += 1;
    }

    if suspicious_count >= 2 {
        AntiDebugResult::Suspicious
    } else if suspicious_count >= 1 {
        AntiDebugResult::Suspicious
    } else {
        AntiDebugResult::Safe
    }
}

/// Windows调试器检测
#[cfg(target_os = "windows")]
fn check_windows_debugger() -> bool {
    use std::ffi::c_void;
    
    extern "system" {
        fn IsDebuggerPresent() -> i32;
        fn CheckRemoteDebuggerPresent(hProcess: *mut c_void, pbDebuggerPresent: *mut i32) -> i32;
        fn GetCurrentProcess() -> *mut c_void;
    }

    unsafe {
        // 检查本地调试器
        if IsDebuggerPresent() != 0 {
            return true;
        }

        // 检查远程调试器
        let mut debugger_present = 0;
        let current_process = GetCurrentProcess();
        if CheckRemoteDebuggerPresent(current_process, &mut debugger_present) != 0 {
            if debugger_present != 0 {
                return true;
            }
        }
    }

    false
}

/// Unix系统调试器检测
#[cfg(any(target_os = "linux", target_os = "macos"))]
fn check_unix_debugger() -> bool {
    use std::fs;
    
    // Linux: 检查/proc/self/status中的TracerPid
    #[cfg(target_os = "linux")]
    {
        if let Ok(status) = fs::read_to_string("/proc/self/status") {
            for line in status.lines() {
                if line.starts_with("TracerPid:") {
                    if let Some(pid_str) = line.split_whitespace().nth(1) {
                        if let Ok(pid) = pid_str.parse::<i32>() {
                            if pid != 0 {
                                return true;
                            }
                        }
                    }
                }
            }
        }
    }

    // macOS: 检查sysctl
    #[cfg(target_os = "macos")]
    {
        use std::process::Command;
        
        if let Ok(output) = Command::new("sysctl")
            .arg("kern.proc.pid.$$")
            .output() 
        {
            let output_str = String::from_utf8_lossy(&output.stdout);
            if output_str.contains("traced") {
                return true;
            }
        }
    }

    false
}

/// 检查可疑进程
fn check_suspicious_processes() -> bool {
    use std::process::Command;

    let suspicious_processes = [
        "gdb", "lldb", "strace", "ltrace", "ida", "ida64", 
        "x64dbg", "x32dbg", "ollydbg", "windbg", "cheat engine",
        "process hacker", "process monitor", "wireshark", "fiddler"
    ];

    #[cfg(target_os = "windows")]
    {
        if let Ok(output) = Command::new("tasklist").output() {
            let output_str = String::from_utf8_lossy(&output.stdout).to_lowercase();
            for process in &suspicious_processes {
                if output_str.contains(process) {
                    return true;
                }
            }
        }
    }

    #[cfg(any(target_os = "linux", target_os = "macos"))]
    {
        if let Ok(output) = Command::new("ps").arg("aux").output() {
            let output_str = String::from_utf8_lossy(&output.stdout).to_lowercase();
            for process in &suspicious_processes {
                if output_str.contains(process) {
                    return true;
                }
            }
        }
    }

    false
}

/// 检查调试环境变量
fn check_debug_environment() -> bool {
    let debug_vars = [
        "DEBUG", "_DEBUG", "RUST_BACKTRACE", "RUST_LOG",
        "LLDB_DEBUGSERVER_PATH", "GDB", "DEBUGGING"
    ];

    for var in &debug_vars {
        if let Ok(value) = std::env::var(var) {
            if !value.is_empty() && value != "0" && value.to_lowercase() != "false" {
                return true;
            }
        }
    }

    false
}

/// 检查内存模式（简化版本）
fn check_memory_patterns() -> bool {
    // 检查堆栈模式，调试器通常会改变内存布局
    let stack_var = 0u64;
    let stack_addr = &stack_var as *const u64 as usize;
    
    // 检查栈地址是否在预期范围内
    // 这是一个简化的检查，实际实现会更复杂
    #[cfg(target_pointer_width = "64")]
    {
        // 64位系统的典型栈地址范围
        if stack_addr < 0x7f0000000000 || stack_addr > 0x7fffffffffff {
            return true;
        }
    }

    false
}

/// 执行时间检查（异步版本）
pub async fn check_execution_timing() -> AntiDebugResult {
    let iterations = 1000;
    let start = Instant::now();
    
    // 执行一些简单的计算
    let mut sum = 0u64;
    for i in 0..iterations {
        sum = sum.wrapping_add(i * 2);
        
        // 添加一些随机延迟以混淆时间检查
        if i % 100 == 0 {
            tokio::time::sleep(Duration::from_nanos(1)).await;
        }
    }
    
    let elapsed = start.elapsed();
    
    // 如果执行时间异常长，可能存在调试器
    let expected_max = Duration::from_millis(10);
    if elapsed > expected_max {
        AntiDebugResult::Suspicious
    } else {
        AntiDebugResult::Safe
    }
}

/// 检查虚拟环境
pub fn check_virtual_environment() -> AntiDebugResult {
    let mut vm_indicators = 0;

    // 检查虚拟机特征
    if check_vm_artifacts() {
        vm_indicators += 1;
    }

    // 检查沙箱环境
    if check_sandbox_environment() {
        vm_indicators += 1;
    }

    // 检查容器环境
    if check_container_environment() {
        vm_indicators += 1;
    }

    if vm_indicators >= 2 {
        AntiDebugResult::Suspicious
    } else if vm_indicators >= 1 {
        AntiDebugResult::Suspicious
    } else {
        AntiDebugResult::Safe
    }
}

/// 检查虚拟机特征
fn check_vm_artifacts() -> bool {
    // 检查常见的虚拟机标识
    let vm_indicators = [
        "vmware", "virtualbox", "qemu", "kvm", "xen", "hyper-v",
        "parallels", "bochs", "sandboxie"
    ];

    // 检查系统信息
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        
        if let Ok(output) = Command::new("systeminfo").output() {
            let output_str = String::from_utf8_lossy(&output.stdout).to_lowercase();
            for indicator in &vm_indicators {
                if output_str.contains(indicator) {
                    return true;
                }
            }
        }
    }

    #[cfg(any(target_os = "linux", target_os = "macos"))]
    {
        use std::fs;
        
        // 检查DMI信息
        if let Ok(dmi) = fs::read_to_string("/sys/class/dmi/id/sys_vendor") {
            let dmi_lower = dmi.to_lowercase();
            for indicator in &vm_indicators {
                if dmi_lower.contains(indicator) {
                    return true;
                }
            }
        }
    }

    false
}

/// 检查沙箱环境
fn check_sandbox_environment() -> bool {
    // 检查文件系统特征
    let sandbox_paths = [
        "/tmp/sandbox", "/var/tmp/sandbox", "C:\\Sandbox",
        "/Applications/Sandboxie", "C:\\Program Files\\Sandboxie"
    ];

    for path in &sandbox_paths {
        if std::path::Path::new(path).exists() {
            return true;
        }
    }

    false
}

/// 检查容器环境
fn check_container_environment() -> bool {
    #[cfg(target_os = "linux")]
    {
        use std::fs;
        
        // 检查cgroup信息
        if let Ok(cgroup) = fs::read_to_string("/proc/1/cgroup") {
            if cgroup.contains("docker") || cgroup.contains("lxc") || cgroup.contains("containerd") {
                return true;
            }
        }

        // 检查容器特征文件
        if std::path::Path::new("/.dockerenv").exists() {
            return true;
        }
    }

    false
}

/// 反调试陷阱函数
pub fn anti_debug_trap() {
    // 这个函数可以在关键位置调用，如果检测到调试器就执行混淆操作
    if check_debugger_presence() != AntiDebugResult::Safe {
        // 执行一些混淆操作
        std::process::exit(1);
    }
}