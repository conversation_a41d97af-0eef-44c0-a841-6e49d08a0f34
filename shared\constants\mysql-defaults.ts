/**
 * MySQL一键安装工具的默认配置和常量定义
 * 包含各操作系统的默认安装路径、配置参数和模板
 * 
 * 从Web版本移植，用于桌面应用和Web版本的配置统一
 */

import {
  SupportedOS,
  MySQLVersion,
  DefaultTemplate,
  QuickInstallConfig
} from '../types/mysql-installer';

// ===== 默认安装路径配置 =====

/**
 * 各系统默认安装路径
 */
export const DEFAULT_INSTALL_PATHS: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'C:\\MySQL\\8.0',
  [SupportedOS.MACOS]: '/usr/local/mysql',
  [SupportedOS.LINUX]: '/opt/mysql',
  [SupportedOS.UNKNOWN]: '/opt/mysql'
};

/**
 * 各系统默认数据目录路径
 */
export const DEFAULT_DATA_PATHS: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'C:\\MySQL\\8.0\\data',
  [SupportedOS.MACOS]: '/usr/local/mysql/data',
  [SupportedOS.LINUX]: '/var/lib/mysql',
  [SupportedOS.UNKNOWN]: '/var/lib/mysql'
};

/**
 * 各系统默认配置文件名
 */
export const CONFIG_FILE_NAMES: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'my.ini',
  [SupportedOS.MACOS]: 'my.cnf',
  [SupportedOS.LINUX]: 'my.cnf',
  [SupportedOS.UNKNOWN]: 'my.cnf'
};

/**
 * 各系统默认服务名称
 */
export const SERVICE_NAMES: Record<SupportedOS, string> = {
  [SupportedOS.WINDOWS]: 'MySQL80',
  [SupportedOS.MACOS]: 'com.oracle.oss.mysql.mysqld',
  [SupportedOS.LINUX]: 'mysqld',
  [SupportedOS.UNKNOWN]: 'mysqld'
};

// ===== 默认MySQL配置参数 =====

/**
 * 默认MySQL配置
 */
export const DEFAULT_MYSQL_CONFIG = {
  /** 默认端口 */
  PORT: 3306,
  /** 默认root密码 */
  ROOT_PASSWORD: '123456',
  /** 默认字符集 */
  CHARSET: 'utf8mb4',
  /** 默认排序规则 */
  COLLATION: 'utf8mb4_0900_ai_ci',
  /** 默认存储引擎 */
  STORAGE_ENGINE: 'InnoDB',
  /** 默认最大连接数 */
  MAX_CONNECTIONS: 200,
  /** 默认InnoDB缓冲池大小 */
  INNODB_BUFFER_POOL_SIZE: '256M',
  /** 默认查询缓存大小 */
  QUERY_CACHE_SIZE: '64M',
  /** 默认慢查询时间阈值（秒） */
  LONG_QUERY_TIME: 2
} as const;

/**
 * 支持的MySQL版本列表
 */
export const SUPPORTED_MYSQL_VERSIONS = [
  MySQLVersion.V8_0_36,
  MySQLVersion.V8_0_28
] as const;

/**
 * 默认MySQL版本
 */
export const DEFAULT_MYSQL_VERSION = MySQLVersion.V8_0_36;

// ===== 系统模板配置 =====

/**
 * 各系统的默认安装模板
 */
export const DEFAULT_TEMPLATES: Record<SupportedOS, DefaultTemplate> = {
  [SupportedOS.WINDOWS]: {
    os: SupportedOS.WINDOWS,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.WINDOWS],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.WINDOWS],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.WINDOWS],
    serviceName: SERVICE_NAMES[SupportedOS.WINDOWS]
  },
  [SupportedOS.MACOS]: {
    os: SupportedOS.MACOS,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.MACOS],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.MACOS],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.MACOS],
    serviceName: SERVICE_NAMES[SupportedOS.MACOS]
  },
  [SupportedOS.LINUX]: {
    os: SupportedOS.LINUX,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.LINUX],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.LINUX],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.LINUX],
    serviceName: SERVICE_NAMES[SupportedOS.LINUX]
  },
  [SupportedOS.UNKNOWN]: {
    os: SupportedOS.UNKNOWN,
    defaultInstallPath: DEFAULT_INSTALL_PATHS[SupportedOS.UNKNOWN],
    defaultDataPath: DEFAULT_DATA_PATHS[SupportedOS.UNKNOWN],
    defaultPort: DEFAULT_MYSQL_CONFIG.PORT,
    defaultCharset: DEFAULT_MYSQL_CONFIG.CHARSET,
    defaultCollation: DEFAULT_MYSQL_CONFIG.COLLATION,
    configFileName: CONFIG_FILE_NAMES[SupportedOS.UNKNOWN],
    serviceName: SERVICE_NAMES[SupportedOS.UNKNOWN]
  }
};

// ===== 一键安装默认配置生成器 =====

/**
 * 生成指定操作系统的默认一键安装配置
 */
export function createDefaultQuickInstallConfig(os: SupportedOS): QuickInstallConfig {
  const template = DEFAULT_TEMPLATES[os];
  
  return {
    version: DEFAULT_MYSQL_VERSION,
    targetOS: os,
    installPath: template.defaultInstallPath,
    dataPath: template.defaultDataPath,
    port: template.defaultPort,
    rootPassword: DEFAULT_MYSQL_CONFIG.ROOT_PASSWORD,
    charset: template.defaultCharset,
    collation: template.defaultCollation,
    enableBinLog: false, // 默认不启用二进制日志
    createService: os === SupportedOS.WINDOWS // 仅Windows默认创建服务
  };
}