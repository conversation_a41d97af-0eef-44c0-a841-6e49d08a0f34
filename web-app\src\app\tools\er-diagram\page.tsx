'use client';

/**
 * MySQLAi.de - ER图生成工具页面
 * 智能数据库关系图生成工具，可视化数据库结构，支持SQL DDL解析和多格式导出
 * 更新：2025-06-28
 */

import React, { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { GitBranch, ArrowLeft, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { SqlEditor, ExportDialog } from './components';
import { parseSql } from './lib/sql-parser';
import { exportDiagram } from './lib/export-utils';
import type { TableInfo, ExportOptions } from './types/er-diagram';
import { SkeletonLoader } from '@/components/ui/PageLoader';

// 懒加载DiagramViewer组件 - 利用GoJS代码分割配置
const DiagramViewer = dynamic(
  () => import('./components/DiagramViewer'),
  {
    loading: () => (
      <div className="flex flex-col items-center justify-center h-full space-y-4 bg-white">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-mysql-primary" />
          <span className="text-lg text-mysql-text">正在加载图形引擎...</span>
        </div>
        <div className="w-full max-w-md">
          <SkeletonLoader lines={4} />
        </div>
      </div>
    ),
    ssr: false // 禁用服务端渲染，确保客户端懒加载
  }
);

/**
 * ER图生成工具主页面组件
 */
export default function ERDiagramPage() {
  // 客户端渲染保护状态
  const [isClient, setIsClient] = useState(false);

  // 状态管理
  const [sqlText, setSqlText] = useState(`-- 示例SQL DDL语句
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE user_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    avatar_url VARCHAR(255),
    bio TEXT,
    phone VARCHAR(20),
    address TEXT,
    birth_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    stock_quantity INT DEFAULT 0,
    category_id INT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    shipping_address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);`);
  const [tableData, setTableData] = useState<TableInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 引用
  const diagramViewerRef = useRef<HTMLDivElement>(null);

  // 客户端渲染保护 - 确保布局在客户端正确初始化
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 防抖函数 - 优化SQL解析频率
  const debounce = useCallback((func: () => void, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(func, delay);
    };
  }, []);

  // 自动滚动到ER图区域 - 仅在移动端使用
  const scrollToERDiagram = useCallback(() => {
    // 只在小屏幕设备上自动滚动，避免桌面端布局跳跃
    if (window.innerWidth < 768 && diagramViewerRef.current) {
      diagramViewerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  }, []);

  // 防抖的SQL解析处理 - 减少解析频率，提升性能
  const debouncedParseSql = useMemo(() => {
    return debounce(async () => {
      if (!sqlText.trim()) {
        setError('请输入SQL DDL语句');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('开始解析SQL:', sqlText);
        const result = await parseSql(sqlText);

        if (result && result.tables && result.tables.length > 0) {
          setTableData(result.tables);
          setError(null);
          console.log('SQL解析成功:', result);

          // 显示警告信息（如果有）
          if (result.warnings && result.warnings.length > 0) {
            console.warn('解析警告:', result.warnings);
          }

          // 延迟滚动到ER图区域，确保ER图已完全渲染
          setTimeout(scrollToERDiagram, 300);
        } else {
          setError('未能识别有效的建表语句，请检查SQL语法');
        }
      } catch (err) {
        console.error('SQL解析失败:', err);
        setError(err instanceof Error ? err.message : '解析失败，请检查SQL语法');
      } finally {
        setIsLoading(false);
      }
    }, 500); // 500ms防抖延迟
  }, [sqlText, debounce, scrollToERDiagram]);

  // SQL解析处理
  const handleParseSql = useCallback(async () => {
    if (!sqlText.trim()) {
      setError('请输入SQL DDL语句');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('开始解析SQL:', sqlText);
      const result = await parseSql(sqlText);

      if (result && result.tables && result.tables.length > 0) {
        setTableData(result.tables);
        setError(null);
        console.log('SQL解析成功:', result);

        // 显示警告信息（如果有）
        if (result.warnings && result.warnings.length > 0) {
          console.warn('解析警告:', result.warnings);
        }

        // 延迟滚动到ER图区域，确保ER图已完全渲染
        setTimeout(scrollToERDiagram, 300);
      } else {
        setError('未能识别有效的建表语句，请检查SQL语法');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '解析失败，请检查SQL语句格式';
      setError(errorMessage);
      console.error('SQL解析失败:', err);
    } finally {
      setIsLoading(false);
    }
  }, [sqlText]);

  // 存储图形实例的引用 - 使用unknown类型避免GoJS依赖
  const diagramInstanceRef = useRef<unknown | null>(null);

  // 缓存图表统计信息
  const chartStats = useMemo(() => {
    if (tableData.length === 0) return null;

    return {
      tableCount: tableData.length,
      columnCount: tableData.reduce((sum, table) => sum + table.columns.length, 0),
      foreignKeyCount: tableData.reduce((sum, table) => sum + (table.foreignKeys?.length || 0), 0)
    };
  }, [tableData]);

  // 导出处理
  const handleExport = useCallback((diagram: unknown) => {
    // 存储图形实例引用
    diagramInstanceRef.current = diagram;
    setIsExportDialogOpen(true);
  }, []);

  // 导出确认处理
  const handleExportConfirm = useCallback(async (options: ExportOptions) => {
    if (!diagramInstanceRef.current) {
      setError('图形实例不可用，请先生成ER图');
      return;
    }

    setIsExporting(true);

    try {
      await exportDiagram(diagramInstanceRef.current as any, options);
      setIsExportDialogOpen(false);
      setError(null);

      // 添加成功反馈 - 临时显示成功消息
      const successMessage = `ER图已成功导出为 ${options.format.toUpperCase()} 格式`;
      console.log(successMessage);

      // 可以在这里添加toast通知或其他成功反馈
      // 暂时使用console.log，后续可以集成toast组件

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '导出失败';
      setError(errorMessage);
      console.error('导出失败:', err);
    } finally {
      setIsExporting(false);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-mysql-primary-light via-white to-blue-50">
        {/* 面包屑导航和返回按钮 - 容器化设计 */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-mysql-border/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between min-h-[3rem]">
              {/* 左侧：面包屑导航 */}
              <Breadcrumb pathname="/tools/er-diagram" />

              {/* 右侧：返回按钮和状态指示器 */}
              <div className="flex items-center space-x-4">
                <Link
                  href="/tools"
                  className="inline-flex items-center text-mysql-text-light hover:text-mysql-primary transition-colors duration-200"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  返回工具集
                </Link>

                {/* 状态指示器 - 增强用户体验 */}
                {isLoading && (
                  <div className="flex items-center space-x-2 text-mysql-primary bg-mysql-primary/10 px-4 py-2 rounded-full shadow-lg">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm font-medium">正在解析SQL并生成ER图...</span>
                  </div>
                )}
                {error && (
                  <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-2 rounded-full shadow-lg border border-red-200">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">生成失败</span>
                  </div>
                )}
                {tableData.length > 0 && !isLoading && !error && (
                  <div className="flex items-center space-x-2 text-green-600 bg-green-50 px-4 py-2 rounded-full shadow-lg border border-green-200">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">✨ 成功生成 {tableData.length} 个表的ER图</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

      {/* 主要内容区域 - A/B区域左右分栏布局 */}
      <div className="flex-1 overflow-hidden">
        {!isClient ? (
          /* 加载状态 - 避免布局闪烁 */
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center space-y-3 text-mysql-text text-center">
              <Loader2 className="w-8 h-8 md:w-6 md:h-6 animate-spin text-mysql-primary" />
              <span className="text-base md:text-lg">正在加载ER图工具...</span>
              <span className="text-sm text-mysql-text-light">请稍候，正在初始化界面...</span>
            </div>
          </div>
        ) : (
          /* A/B区域左右分栏布局 - 经典IDE风格 */
          <div
            className="flex h-full er-diagram-main-content"
            suppressHydrationWarning={true}
          >
          {/* A区域：SQL编辑器 - 左侧小框（约30%宽度） */}
          <div className="w-[30%] flex flex-col bg-white border-r border-mysql-border/20">
            {/* SQL编辑器标题栏 */}
            <div className="flex items-center justify-between px-4 py-3 border-b border-mysql-border/30 bg-gradient-to-r from-mysql-primary-light/50 to-white">
              <h2 className="text-base md:text-lg font-semibold text-mysql-text flex items-center">
                <GitBranch className="w-4 h-4 md:w-5 md:h-5 mr-2 md:mr-3 text-mysql-primary" />
                <span className="hidden sm:inline">SQL DDL 编辑器</span>
                <span className="sm:hidden">SQL编辑器</span>
              </h2>
              <div className="flex items-center space-x-2 md:space-x-4">
                <div className="hidden md:block text-xs text-mysql-text-light bg-white px-2 py-1 rounded-md border">
                  Ctrl+Enter 生成ER图
                </div>
                <div className="md:hidden text-xs text-mysql-text-light bg-white px-2 py-1 rounded-md border">
                  快捷键
                </div>
                {!isLoading && !error && sqlText.trim() && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="就绪"></div>
                    <span className="text-xs text-green-600 font-medium">就绪</span>
                  </div>
                )}
                <button
                  type="button"
                  onClick={handleParseSql}
                  disabled={!sqlText.trim() || isLoading}
                  className={cn(
                    'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium',
                    'bg-mysql-primary text-white shadow-lg',
                    'hover:bg-mysql-primary-dark hover:shadow-xl hover:scale-105',
                    'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                    'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-none',
                    'transition-all duration-200 transform disabled:hover:scale-100'
                  )}
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <GitBranch className="w-4 h-4" />
                  )}
                  <span>生成ER图</span>
                </button>
              </div>
            </div>

            {/* 编辑器内容区域 */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <SqlEditor
                value={sqlText}
                onChange={setSqlText}
                onParse={handleParseSql}
                isLoading={isLoading}
                error={error}
                readOnly={false}
                showHeader={false}
                className="h-full border-none rounded-none"
              />
            </div>
          </div>

          {/* B区域：ER图预览 - 右侧大框（约70%宽度） */}
          <div className="w-[70%] flex flex-col bg-white">
            {/* ER图标题栏 */}
            <div className="flex items-center justify-between px-4 py-3 border-b border-mysql-border/30 bg-gradient-to-r from-white to-mysql-primary-light/50">
              <h2 className="text-lg font-semibold text-mysql-text flex items-center">
                <svg className="w-5 h-5 mr-3 text-mysql-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                ER图预览
              </h2>

              {/* 图表统计信息 - 美化版 */}
              {chartStats && (
                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2 bg-white px-3 py-1 rounded-lg border shadow-md">
                    <div className="w-3 h-3 bg-gradient-to-r from-mysql-primary to-mysql-primary-dark rounded-full"></div>
                    <span className="text-sm font-medium text-mysql-text">表: {chartStats.tableCount}</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white px-3 py-1 rounded-lg border shadow-md">
                    <div className="w-3 h-3 bg-gradient-to-r from-mysql-accent to-orange-500 rounded-full"></div>
                    <span className="text-sm font-medium text-mysql-text">字段: {chartStats.columnCount}</span>
                  </div>
                  <div className="flex items-center space-x-2 bg-white px-3 py-1 rounded-lg border shadow-md">
                    <div className="w-3 h-3 bg-gradient-to-r from-mysql-success to-green-600 rounded-full"></div>
                    <span className="text-sm font-medium text-mysql-text">关系: {chartStats.foreignKeyCount}</span>
                  </div>
                </div>
              )}
            </div>

            {/* ER图内容区域 */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <DiagramViewer
                ref={diagramViewerRef}
                tableData={tableData}
                isLoading={isLoading}
                error={error}
                onDiagramUpdate={handleExport}
              />
            </div>
          </div>
          </div>
        )}
      </div>

      {/* 导出对话框 */}
      <ExportDialog
        isOpen={isExportDialogOpen}
        onClose={() => setIsExportDialogOpen(false)}
        onExport={handleExportConfirm}
        isExporting={isExporting}
      />
    </div>
  );
}
