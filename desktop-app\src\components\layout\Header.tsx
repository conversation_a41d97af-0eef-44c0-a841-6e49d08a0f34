/**
 * 页面头部组件
 * 显示当前页面的标题和操作按钮
 */

// import React from 'react';
import { motion } from 'framer-motion';
import { 
  Key, 
  Download, 
  Database, 
  Monitor, 
  Settings,
  RefreshCw,
  HelpCircle,
  Sun,
  Moon
} from 'lucide-react';
import { AppPage, useTheme, useAppActions } from '../../store/appStore';
import { Button } from '../ui';
import { cn } from '../../lib/utils';

interface HeaderProps {
  currentPage: AppPage;
}

const pageConfig = {
  activation: {
    title: '激活验证',
    subtitle: '验证产品许可证以解锁完整功能',
    icon: Key,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
  },
  download: {
    title: 'MySQL下载',
    subtitle: '下载最新版本的MySQL数据库',
    icon: Download,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  installer: {
    title: 'MySQL安装',
    subtitle: '安装和配置MySQL数据库服务',
    icon: Database,
    color: 'text-mysql-primary',
    bgColor: 'bg-mysql-primary-light',
  },
  system: {
    title: '系统信息',
    subtitle: '查看系统配置和硬件信息',
    icon: Monitor,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  settings: {
    title: '应用设置',
    subtitle: '配置应用偏好和系统选项',
    icon: Settings,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50',
  },
};

export default function Header({ currentPage }: HeaderProps) {
  const theme = useTheme();
  const { setTheme } = useAppActions();
  
  const config = pageConfig[currentPage];
  const IconComponent = config.icon;

  const handleRefresh = () => {
    // 刷新当前页面数据
    window.location.reload();
  };

  const handleHelp = () => {
    // 打开帮助文档
    window.open('https://mysqlai.de/docs', '_blank');
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <header className="bg-white border-b border-mysql-border px-6 py-4">
      <div className="flex items-center justify-between">
        {/* 左侧：页面信息 */}
        <div className="flex items-center space-x-4">
          {/* 页面图标 */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200 }}
            className={cn(
              'flex items-center justify-center w-12 h-12 rounded-xl',
              config.bgColor
            )}
          >
            <IconComponent className={cn('w-6 h-6', config.color)} />
          </motion.div>

          {/* 页面标题和描述 */}
          <div>
            <motion.h1
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="text-2xl font-bold text-mysql-text"
            >
              {config.title}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="text-mysql-text-light mt-1"
            >
              {config.subtitle}
            </motion.p>
          </div>
        </div>

        {/* 右侧：操作按钮 */}
        <div className="flex items-center space-x-2">
          {/* 主题切换按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            icon={theme === 'light' ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
            // title={theme === 'light' ? '切换到深色模式' : '切换到浅色模式'}
          />

          {/* 刷新按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            icon={<RefreshCw className="w-4 h-4" />}
            // title="刷新页面"
          />

          {/* 帮助按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleHelp}
            icon={<HelpCircle className="w-4 h-4" />}
            // title="查看帮助文档"
          />
        </div>
      </div>

      {/* 页面导航面包屑（可选） */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="mt-4 flex items-center space-x-2 text-sm text-mysql-text-light"
      >
        <span>MySQLAi.de</span>
        <span>/</span>
        <span>桌面应用</span>
        <span>/</span>
        <span className="text-mysql-primary font-medium">{config.title}</span>
      </motion.div>
    </header>
  );
}