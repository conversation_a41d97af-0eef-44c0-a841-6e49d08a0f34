/**
 * 类型定义文件
 * 从Web版本移植的通用类型定义
 */

import { ReactNode } from 'react';

// ===== 基础类型 =====

/**
 * 基础组件Props
 */
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

/**
 * 尺寸类型
 */
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * 颜色变体类型
 */
export type ColorVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';

/**
 * 按钮变体类型
 */
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';

// ===== 按钮组件类型 =====

/**
 * 按钮组件Props
 */
export interface ButtonProps extends BaseComponentProps {
  variant?: ButtonVariant;
  size?: Size;
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  type?: 'button' | 'submit' | 'reset';
}

// ===== 卡片组件类型 =====

/**
 * 功能卡片Props
 */
export interface FeatureCardProps extends BaseComponentProps {
  title: string;
  description: string;
  icon: ReactNode;
  href?: string;
  features?: string[];
  color?: string;
  gradient?: string;
  expandable?: boolean;
  onClick?: () => void;
}

/**
 * 简单功能卡片Props
 */
export interface SimpleFeatureCardProps extends BaseComponentProps {
  title: string;
  description: string;
  icon: ReactNode;
  color?: string;
  onClick?: () => void;
}

/**
 * 紧凑功能卡片Props
 */
export interface CompactFeatureCardProps extends BaseComponentProps {
  title: string;
  icon: ReactNode;
  color?: string;
  onClick?: () => void;
}

// ===== 图片组件类型 =====

/**
 * 优化图片Props
 */
export interface OptimizedImageProps extends BaseComponentProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean;
  quality?: number;
  placeholder?: 'empty' | 'blur';
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  objectPosition?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  showPlaceholder?: boolean;
  placeholderClassName?: string;
  errorClassName?: string;
}

/**
 * 头像图片Props
 */
export interface AvatarImageProps extends BaseComponentProps {
  src: string;
  alt: string;
  size?: Size;
  fallbackSrc?: string;
}

/**
 * 卡片图片Props
 */
export interface CardImageProps extends BaseComponentProps {
  src: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'tall';
  overlay?: ReactNode;
  hoverEffect?: boolean;
}

// ===== 状态组件类型 =====

/**
 * 加载状态Props
 */
export interface LoadingStateProps extends BaseComponentProps {
  message?: string;
  size?: Size;
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
  itemCount?: number;
}

/**
 * 错误状态Props
 */
export interface ErrorStateProps extends BaseComponentProps {
  title?: string;
  message: string;
  actionText?: string;
  onAction?: () => void;
  showIcon?: boolean;
}

/**
 * 空状态Props
 */
export interface EmptyStateProps extends BaseComponentProps {
  title?: string;
  message: string;
  actionText?: string;
  onAction?: () => void;
  icon?: ReactNode;
}

// ===== 表单组件类型 =====

/**
 * 表单字段类型
 */
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'number' | 'email' | 'password' | 'file' | 'switch' | 'radio' | 'checkbox';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: { label: string; value: string | number }[];
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: any) => string | null;
  };
  description?: string;
  rows?: number;
  accept?: string;
  multiple?: boolean;
}

/**
 * 表单Props
 */
export interface FormProps extends BaseComponentProps {
  fields: FormField[];
  values: Record<string, any>;
  errors: Record<string, string>;
  loading?: boolean;
  onChange: (name: string, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
}

// ===== 进度组件类型 =====

/**
 * 进度条Props
 */
export interface ProgressBarProps extends BaseComponentProps {
  value: number;
  max?: number;
  size?: Size;
  variant?: ColorVariant;
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
}

/**
 * 步骤进度Props
 */
export interface StepProgressProps extends BaseComponentProps {
  steps: Array<{
    id: string;
    title: string;
    description?: string;
    completed?: boolean;
    current?: boolean;
    error?: boolean;
  }>;
  currentStep?: string;
  orientation?: 'horizontal' | 'vertical';
}

// ===== 通知组件类型 =====

/**
 * 通知类型
 */
export type NotificationType = 'success' | 'warning' | 'error' | 'info';

/**
 * 通知Props
 */
export interface NotificationProps {
  id: string;
  type: NotificationType;
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: ButtonVariant;
  }>;
  onClose?: () => void;
}

// ===== 模态框组件类型 =====

/**
 * 模态框Props
 */
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: Size;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  footer?: ReactNode;
}

/**
 * 确认对话框Props
 */
export interface ConfirmDialogProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: ColorVariant;
  loading?: boolean;
}

// ===== 数据表格类型 =====

/**
 * 表格列定义
 */
export interface TableColumn {
  key: string;
  title: string;
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any) => ReactNode;
  responsive?: 'always' | 'desktop' | 'tablet' | 'mobile';
}

/**
 * 数据表格Props
 */
export interface DataTableProps extends BaseComponentProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: boolean;
  pageSize?: number;
  searchable?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  selectable?: boolean;
  responsive?: boolean;
  emptyText?: string;
  rowKey?: string;
  onRowClick?: (record: any) => void;
  onSelectionChange?: (selectedRows: any[]) => void;
}

// ===== 主题类型 =====

/**
 * 主题色彩类型
 */
export interface ThemeColors {
  primary: string;
  primaryDark: string;
  primaryLight: string;
  accent: string;
  text: string;
  textLight: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

/**
 * 主题配置类型
 */
export interface ThemeConfig {
  colors: ThemeColors;
  fonts: {
    primary: string;
    mono: string;
    sizes: Record<string, string>;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
  shadows: Record<string, string>;
  animations: {
    duration: Record<string, string>;
    easing: Record<string, string>;
  };
}

// ===== 应用状态类型 =====

/**
 * 应用状态
 */
export interface AppState {
  theme: 'light' | 'dark';
  language: string;
  user?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  settings: Record<string, any>;
}

/**
 * API响应类型
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

/**
 * 分页数据类型
 */
export interface PaginatedData<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// ===== 事件类型 =====

/**
 * 键盘事件类型
 */
export type KeyboardEventHandler = (event: React.KeyboardEvent) => void;

/**
 * 鼠标事件类型
 */
export type MouseEventHandler = (event: React.MouseEvent) => void;

/**
 * 表单事件类型
 */
export type FormEventHandler = (event: React.FormEvent) => void;

/**
 * 变更事件类型
 */
export type ChangeEventHandler = (event: React.ChangeEvent) => void;

// ===== 工具类型 =====

/**
 * 可选属性类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 必需属性类型
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度必需类型
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * 值类型提取
 */
export type ValueOf<T> = T[keyof T];

/**
 * 函数参数类型提取
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never;

/**
 * 函数返回类型提取
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any;

/**
 * Promise解包类型
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T;

// ===== 桌面应用特定类型 =====

/**
 * 窗口状态类型
 */
export interface WindowState {
  isMaximized: boolean;
  isMinimized: boolean;
  isFullscreen: boolean;
  width: number;
  height: number;
  x: number;
  y: number;
}

/**
 * 系统信息类型
 */
export interface SystemInfo {
  platform: string;
  arch: string;
  version: string;
  totalMemory: number;
  freeMemory: number;
  cpuCount: number;
  hostname: string;
  username: string;
}

/**
 * 文件信息类型
 */
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  type: string;
  lastModified: number;
  isDirectory: boolean;
}

/**
 * 下载进度类型
 */
export interface DownloadProgress {
  url: string;
  filename: string;
  totalBytes: number;
  downloadedBytes: number;
  percentage: number;
  speed: number;
  timeRemaining: number;
  status: 'pending' | 'downloading' | 'completed' | 'error' | 'cancelled';
}

/**
 * 安装进度类型
 */
export interface InstallationProgress {
  currentStep: string;
  progressPercentage: number;
  status: 'pending' | 'inProgress' | 'completed' | 'failed' | 'cancelled';
  message: string;
  estimatedTimeRemaining?: number;
}

// ===== 导出所有类型 =====

export type {
  // 重新导出常用类型
  ReactNode,
};

// 默认导出
export default {
  // 可以在这里添加默认导出的内容
};