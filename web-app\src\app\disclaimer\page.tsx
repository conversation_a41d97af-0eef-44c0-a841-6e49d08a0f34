// MySQLAi.de - 免责声明页面
// 展示平台的免责条款和责任限制说明

import { Metadata } from 'next';
import { getLegalContent } from '@/lib/legal';
import { PAGE_METADATA } from '@/lib/constants';
import { generatePageMetadata } from '@/app/metadata';
import LegalPageLayout from '@/components/layout/LegalPageLayout';

// 生成页面元数据
export function generateMetadata(): Metadata {
  const pageData = PAGE_METADATA.disclaimer;
  return generatePageMetadata(
    pageData.title,
    pageData.description,
    '/disclaimer'
  );
}

export default function DisclaimerPage() {
  // 获取免责声明内容
  const disclaimerContent = getLegalContent('disclaimer');

  return (
    <LegalPageLayout
      type="disclaimer"
      title={disclaimerContent.title}
      lastUpdated={disclaimerContent.lastUpdated}
      pathname="/disclaimer"
    >
      {/* 渲染免责声明内容 */}
      {disclaimerContent.sections.map((section) => (
        <div key={section.id} className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {section.title}
          </h2>
          <p className="text-gray-700 leading-relaxed mb-4">
            {section.content}
          </p>
          
          {/* 渲染子章节 */}
          {section.subsections && section.subsections.length > 0 && (
            <div className="ml-4 space-y-4">
              {section.subsections.map((subsection) => (
                <div key={subsection.id}>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">
                    {subsection.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {subsection.content}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}

      {/* 技术服务特殊免责说明 */}
      <div className="mt-12 p-6 bg-amber-50 border border-amber-200 rounded-lg">
        <h3 className="text-lg font-semibold text-amber-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          技术服务免责说明
        </h3>
        <div className="text-amber-800 space-y-2">
          <p>• <strong>MySQL分析结果</strong>：基于算法和数据模型，仅供参考，不保证100%准确性</p>
          <p>• <strong>数据安全</strong>：虽采取安全措施，但无法保证绝对安全，请做好数据备份</p>
          <p>• <strong>服务可用性</strong>：可能因维护、升级等原因暂时中断，我们将尽快恢复</p>
          <p>• <strong>第三方服务</strong>：对于集成的第三方服务问题，我们不承担直接责任</p>
        </div>
      </div>

      {/* 责任限制说明 */}
      <div className="mt-8 p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          责任限制范围
        </h3>
        <div className="text-red-800 space-y-2">
          <p>• <strong>直接损失</strong>：不承担因使用本服务导致的直接经济损失</p>
          <p>• <strong>间接损失</strong>：不承担利润损失、业务中断等间接损失</p>
          <p>• <strong>数据丢失</strong>：用户应自行备份重要数据，我们不承担数据丢失责任</p>
          <p>• <strong>决策后果</strong>：基于分析结果的业务决策，责任由用户自行承担</p>
        </div>
      </div>

      {/* 平衡保护说明 */}
      <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="text-lg font-semibold text-green-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          我们的承诺
        </h3>
        <div className="text-green-800 space-y-2">
          <p>• <strong>尽力服务</strong>：我们将尽最大努力提供稳定、可靠的服务</p>
          <p>• <strong>持续改进</strong>：不断优化算法和服务质量，提升用户体验</p>
          <p>• <strong>及时响应</strong>：对于服务问题，我们将及时响应和处理</p>
          <p>• <strong>透明沟通</strong>：保持与用户的透明沟通，及时告知重要变更</p>
        </div>
      </div>

      {/* 争议解决 */}
      <div className="mt-8 p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          争议解决方式
        </h3>
        <div className="text-gray-700 space-y-2">
          <p><strong>协商解决：</strong> 如发生争议，双方应首先通过友好协商解决</p>
          <p><strong>调解机制：</strong> 协商不成的，可申请第三方调解机构调解</p>
          <p><strong>法律途径：</strong> 调解无效的，提交本平台所在地人民法院管辖</p>
          <p><strong>适用法律：</strong> 本免责声明适用中华人民共和国法律</p>
        </div>
      </div>
    </LegalPageLayout>
  );
}
