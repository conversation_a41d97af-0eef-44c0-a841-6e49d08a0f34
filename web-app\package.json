{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "dev:clean": "next dev --turbopack --experimental-https", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:types": "supabase gen types typescript --local > src/lib/database.types.ts"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "@types/prismjs": "^1.26.5", "@uiw/react-md-editor": "^4.0.4", "clsx": "^2.1.1", "dotenv": "^17.0.0", "framer-motion": "^12.19.2", "fuse.js": "^7.1.0", "gojs": "^3.0.14", "lucide-react": "^0.525.0", "next": "^15.3.4", "node-sql-parser": "^5.3.3", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.53.2", "react-markdown": "^9.1.0", "recharts": "^2.15.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}