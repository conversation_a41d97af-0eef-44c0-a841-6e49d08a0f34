// MySQLAi.de - 知识库统计 API
// 提供知识库的统计数据和分析

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET /api/knowledge/stats - 获取知识库统计数据
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // 天数
    const includeSearchStats = searchParams.get('includeSearchStats') === 'true';

    // 基础统计
    const [
      categoriesResult,
      articlesResult,
      codeExamplesResult,
      relationsResult
    ] = await Promise.all([
      // 分类统计
      supabase
        .from('knowledge_categories')
        .select('*', { count: 'exact', head: true }),
      
      // 文章统计
      supabase
        .from('knowledge_articles')
        .select('*', { count: 'exact', head: true }),
      
      // 代码示例统计
      supabase
        .from('code_examples')
        .select('*', { count: 'exact', head: true }),
      
      // 文章关联统计
      supabase
        .from('article_relations')
        .select('*', { count: 'exact', head: true })
    ]);

    // 按分类统计文章数量
    const { data: categoryStats, error: categoryStatsError } = await supabase
      .from('knowledge_categories')
      .select(`
        id,
        name,
        knowledge_articles(count)
      `);

    if (categoryStatsError) {
      console.error('获取分类统计失败:', categoryStatsError);
    }

    // 按难度统计文章数量
    const { data: difficultyStats, error: difficultyStatsError } = await supabase
      .from('knowledge_articles')
      .select('difficulty')
      .then(({ data, error }) => {
        if (error) return { data: null, error };
        
        const stats = data?.reduce((acc, article) => {
          acc[article.difficulty] = (acc[article.difficulty] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        return { data: stats, error: null };
      });

    if (difficultyStatsError) {
      console.error('获取难度统计失败:', difficultyStatsError);
    }

    // 按语言统计代码示例
    const { data: languageStats, error: languageStatsError } = await supabase
      .from('code_examples')
      .select('language')
      .then(({ data, error }) => {
        if (error) return { data: null, error };
        
        const stats = data?.reduce((acc, example) => {
          acc[example.language] = (acc[example.language] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        return { data: stats, error: null };
      });

    if (languageStatsError) {
      console.error('获取语言统计失败:', languageStatsError);
    }

    // 最近更新的文章
    const { data: recentArticles, error: recentError } = await supabase
      .from('knowledge_articles')
      .select('id, title, last_updated, knowledge_categories(name)')
      .order('last_updated', { ascending: false })
      .limit(5);

    if (recentError) {
      console.error('获取最近文章失败:', recentError);
    }

    let searchStats = null;
    if (includeSearchStats) {
      // 搜索统计
      const periodDate = new Date();
      periodDate.setDate(periodDate.getDate() - parseInt(period));

      const [
        totalSearchesResult,
        popularQueriesResult,
        searchTrendsResult
      ] = await Promise.all([
        // 总搜索次数
        supabase
          .from('search_history')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', periodDate.toISOString()),
        
        // 热门搜索词
        supabase
          .from('search_history')
          .select('query, count(*)')
          .gte('created_at', periodDate.toISOString())
          .order('count', { ascending: false })
          .limit(10),
        
        // 搜索趋势（按天）
        supabase
          .from('search_history')
          .select('created_at')
          .gte('created_at', periodDate.toISOString())
          .order('created_at', { ascending: true })
      ]);

      // 处理搜索趋势数据
      const searchTrends = searchTrendsResult.data?.reduce((acc, search) => {
        const date = search.created_at.split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      searchStats = {
        totalSearches: totalSearchesResult.count || 0,
        popularQueries: popularQueriesResult.data || [],
        searchTrends: searchTrends || {}
      };
    }

    const stats = {
      overview: {
        totalCategories: categoriesResult.count || 0,
        totalArticles: articlesResult.count || 0,
        totalCodeExamples: codeExamplesResult.count || 0,
        totalRelations: relationsResult.count || 0
      },
      categoryStats: categoryStats?.map(cat => ({
        id: cat.id,
        name: cat.name,
        articleCount: cat.knowledge_articles?.[0]?.count || 0
      })) || [],
      difficultyStats: difficultyStats || {},
      languageStats: languageStats || {},
      recentArticles: recentArticles || [],
      searchStats
    };

    return NextResponse.json({
      success: true,
      data: stats,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/stats/export - 导出统计数据
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { format = 'json', includeDetails = false } = body;

    // 获取完整统计数据
    const statsResponse = await GET(request);
    const statsData = await statsResponse.json();

    if (!statsData.success) {
      return statsResponse;
    }

    if (format === 'csv') {
      // 转换为 CSV 格式
      const csvData = convertToCSV(statsData.data, includeDetails);
      
      return new NextResponse(csvData, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="knowledge-stats.csv"'
        }
      });
    }

    // 默认返回 JSON
    return NextResponse.json({
      success: true,
      data: statsData.data,
      format,
      exportedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// 辅助函数：转换为 CSV 格式
function convertToCSV(data: Record<string, unknown>): string {
  const lines = [];
  
  // 基础统计
  lines.push('统计项目,数量');
  lines.push(`总分类数,${data.overview.totalCategories}`);
  lines.push(`总文章数,${data.overview.totalArticles}`);
  lines.push(`总代码示例数,${data.overview.totalCodeExamples}`);
  lines.push(`总关联关系数,${data.overview.totalRelations}`);
  lines.push('');
  
  // 分类统计
  lines.push('分类名称,文章数量');
  (data as Record<string, unknown>).categoryStats?.forEach((cat: {name: string; articleCount: number}) => {
    lines.push(`${cat.name},${cat.articleCount}`);
  });
  lines.push('');
  
  // 难度统计
  lines.push('难度等级,文章数量');
  Object.entries(data.difficultyStats).forEach(([difficulty, count]) => {
    lines.push(`${difficulty},${count}`);
  });
  
  return lines.join('\n');
}
