'use client';

// MySQLAi.de - DropdownMenu下拉菜单组件
// 支持键盘导航、无障碍访问和平滑动画的可复用下拉菜单

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DropdownMenuProps, NavigationItem } from '@/lib/types';

const DropdownMenu = React.forwardRef<HTMLDivElement, DropdownMenuProps>(({
  trigger,
  items,
  align = 'left',
  onItemClick,
  isOpen: controlledIsOpen,
  onOpenChange,
  className,
  ...props
}, ref) => {
  // 路由导航
  const router = useRouter();

  // 状态管理
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  
  // 使用受控或非受控状态
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;
  const setIsOpen = onOpenChange || setInternalIsOpen;
  
  // Refs
  const triggerRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLAnchorElement | null)[]>([]);
  
  // 切换菜单状态
  const toggleMenu = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setFocusedIndex(-1);
    }
  };
  
  // 关闭菜单
  const closeMenu = () => {
    setIsOpen(false);
    setFocusedIndex(-1);
    triggerRef.current?.focus();
  };
  
  // 处理菜单项点击
  const handleItemClick = (item: NavigationItem, event?: React.MouseEvent) => {
    event?.preventDefault();
    onItemClick?.(item);

    // 导航到目标页面
    if (item.isExternal) {
      window.open(item.href, '_blank', 'noopener,noreferrer');
    } else {
      router.push(item.href);
    }

    closeMenu();
  };
  
  // 键盘导航处理
  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        closeMenu();
        break;
        
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else {
          const nextIndex = focusedIndex < items.length - 1 ? focusedIndex + 1 : 0;
          setFocusedIndex(nextIndex);
          itemRefs.current[nextIndex]?.focus();
        }
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          const prevIndex = focusedIndex > 0 ? focusedIndex - 1 : items.length - 1;
          setFocusedIndex(prevIndex);
          itemRefs.current[prevIndex]?.focus();
        }
        break;
        
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else if (focusedIndex >= 0) {
          const item = items[focusedIndex];
          handleItemClick(item);
        }
        break;
        
      case 'Tab':
        if (isOpen) {
          closeMenu();
        }
        break;
    }
  };
  
  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        triggerRef.current &&
        menuRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        !menuRef.current.contains(event.target as Node)
      ) {
        closeMenu();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);
  
  // 对齐样式
  const alignmentStyles = {
    left: 'left-0',
    right: 'right-0',
    center: 'left-1/2 transform -translate-x-1/2'
  };
  
  // 动画配置
  const menuVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95,
      y: -10,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -10,
    }
  };
  
  return (
    <div 
      ref={ref}
      className={cn('relative inline-block', className)}
      {...props}
    >
      {/* 触发器 */}
      <button
        ref={triggerRef}
        type="button"
        onClick={toggleMenu}
        onKeyDown={handleKeyDown}
        className={cn(
          'inline-flex items-center space-x-1 px-3 py-2 text-sm font-medium',
          'text-mysql-text hover:text-mysql-primary transition-colors duration-200',
          'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30 rounded-lg',
          isOpen && 'text-mysql-primary'
        )}
        aria-expanded={isOpen ? 'true' : 'false'}
        aria-haspopup="true"
        aria-label="打开菜单"
      >
        {trigger}
        <ChevronDown 
          className={cn(
            'w-4 h-4 transition-transform duration-200',
            isOpen && 'rotate-180'
          )} 
        />
      </button>
      
      {/* 下拉菜单 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={menuRef}
            variants={menuVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.2, ease: 'easeOut' }}
            className={cn(
              'absolute top-full mt-2 w-56 bg-white rounded-lg shadow-xl border border-mysql-border z-50',
              alignmentStyles[align]
            )}
          >
            <div className="py-2" role="menu" aria-orientation="vertical">
              {items.map((item, index) => (
                <a
                  key={item.href}
                  ref={(el) => {
                    itemRefs.current[index] = el;
                  }}
                  href={item.href}
                  onClick={(e) => handleItemClick(item, e)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleItemClick(item);
                    }
                  }}
                  onFocus={() => setFocusedIndex(index)}
                  className={cn(
                    'flex items-center px-4 py-3 text-sm text-mysql-text',
                    'hover:bg-mysql-primary-light hover:text-mysql-primary',
                    'focus:bg-mysql-primary-light focus:text-mysql-primary focus:outline-none',
                    'transition-colors duration-200 cursor-pointer',
                    focusedIndex === index && 'bg-mysql-primary-light text-mysql-primary'
                  )}
                  role="menuitem"
                  tabIndex={-1}
                >
                  <span className="font-medium">{item.name}</span>
                </a>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

DropdownMenu.displayName = 'DropdownMenu';

export default DropdownMenu;
