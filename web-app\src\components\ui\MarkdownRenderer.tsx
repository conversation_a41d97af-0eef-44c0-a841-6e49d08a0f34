'use client';

// MySQLAi.de - Markdown渲染器组件
// 用于渲染Markdown内容的专用组件

import React from 'react';
import { cn } from '@/lib/utils';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export default function MarkdownRenderer({ content, className }: MarkdownRendererProps) {
  // 处理内联Markdown格式
  const renderInlineMarkdown = (text: string): React.ReactNode => {
    // 处理**bold**格式
    let result = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    // 处理*italic*格式
    result = result.replace(/\*(.*?)\*/g, '<em>$1</em>');
    // 处理`code`格式
    result = result.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>');

    return <span dangerouslySetInnerHTML={{ __html: result }} />;
  };

  // 渲染代码块
  const renderCodeBlock = (code: string, language: string = '') => {
    return (
      <div className="my-6 bg-gray-900 rounded-lg overflow-hidden">
        {language && (
          <div className="px-4 py-2 bg-gray-800 text-gray-300 text-sm font-mono">
            {language}
          </div>
        )}
        <pre className="p-4 overflow-x-auto">
          <code className="text-green-400 font-mono text-sm whitespace-pre">
            {code}
          </code>
        </pre>
      </div>
    );
  };

  // 渲染表格
  const renderTable = (rows: string[]) => {
    if (rows.length === 0) return null;

    const headers = rows[0].split('|').map(h => h.trim()).filter(h => h);
    const dataRows = rows.slice(2).map(row => 
      row.split('|').map(cell => cell.trim()).filter(cell => cell)
    );

    return (
      <div className="my-6 overflow-x-auto">
        <table className="min-w-full border border-mysql-border rounded-lg">
          <thead className="bg-mysql-bg-light">
            <tr>
              {headers.map((header, index) => (
                <th key={index} className="px-4 py-3 text-left text-sm font-semibold text-mysql-text border-b border-mysql-border">
                  {renderInlineMarkdown(header)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {dataRows.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-mysql-bg-light">
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="px-4 py-3 text-sm text-mysql-text border-b border-mysql-border">
                    {renderInlineMarkdown(cell)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染Markdown内容
  const renderMarkdownContent = (content: string) => {
    const lines = content.split('\n');
    const elements: React.ReactNode[] = [];
    let currentCodeBlock = '';
    let currentCodeLanguage = '';
    let inCodeBlock = false;
    let currentList: string[] = [];
    let inList = false;
    let currentTable: string[] = [];
    let inTable = false;

    lines.forEach((line, index) => {
      // 代码块处理
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          // 结束代码块
          elements.push(
            <div key={`code-${index}`}>
              {renderCodeBlock(currentCodeBlock, currentCodeLanguage)}
            </div>
          );
          currentCodeBlock = '';
          currentCodeLanguage = '';
          inCodeBlock = false;
        } else {
          // 开始代码块
          currentCodeLanguage = line.substring(3).trim();
          inCodeBlock = true;
        }
        return;
      }

      if (inCodeBlock) {
        currentCodeBlock += line + '\n';
        return;
      }

      // 表格处理
      if (line.includes('|') && line.trim() !== '') {
        if (!inTable) {
          inTable = true;
          currentTable = [];
        }
        currentTable.push(line);
        return;
      } else if (inTable) {
        // 结束表格
        elements.push(
          <div key={`table-${index}`}>
            {renderTable(currentTable)}
          </div>
        );
        currentTable = [];
        inTable = false;
      }

      // 列表处理
      if (line.match(/^[\s]*[-*+]\s+/) || line.match(/^[\s]*\d+\.\s+/)) {
        if (!inList) {
          inList = true;
          currentList = [];
        }
        currentList.push(line);
        return;
      } else if (inList && line.trim() === '') {
        // 空行，继续列表
        return;
      } else if (inList) {
        // 结束列表
        const isOrdered = currentList[0]?.match(/^[\s]*\d+\.\s+/);
        const ListTag = isOrdered ? 'ol' : 'ul';
        elements.push(
          <ListTag key={`list-${index}`} className={cn(
            'my-4 space-y-2',
            isOrdered ? 'list-decimal list-inside' : 'list-disc list-inside'
          )}>
            {currentList.map((item, i) => {
              const content = item.replace(/^[\s]*[-*+\d.]\s+/, '');
              return (
                <li key={i} className="text-mysql-text leading-relaxed">
                  {renderInlineMarkdown(content)}
                </li>
              );
            })}
          </ListTag>
        );
        currentList = [];
        inList = false;
      }

      // 标题处理
      if (line.startsWith('#')) {
        const level = line.match(/^#+/)?.[0].length || 1;
        const text = line.replace(/^#+\s*/, '');
        const HeadingTag = `h${Math.min(level, 6)}` as keyof JSX.IntrinsicElements;
        
        const headingClasses = {
          1: 'text-3xl font-bold text-mysql-text mb-6 mt-8',
          2: 'text-2xl font-semibold text-mysql-text mb-4 mt-6',
          3: 'text-xl font-semibold text-mysql-text mb-3 mt-5',
          4: 'text-lg font-medium text-mysql-text mb-2 mt-4',
          5: 'text-base font-medium text-mysql-text mb-2 mt-3',
          6: 'text-sm font-medium text-mysql-text mb-2 mt-2'
        };

        elements.push(
          <HeadingTag key={`heading-${index}`} className={headingClasses[level as keyof typeof headingClasses]}>
            {renderInlineMarkdown(text)}
          </HeadingTag>
        );
        return;
      }

      // 引用处理
      if (line.startsWith('>')) {
        const text = line.replace(/^>\s*/, '');
        elements.push(
          <blockquote key={`quote-${index}`} className="border-l-4 border-mysql-primary pl-4 py-2 my-4 bg-mysql-bg-light italic text-mysql-text-light">
            {renderInlineMarkdown(text)}
          </blockquote>
        );
        return;
      }

      // 分隔线处理
      if (line.match(/^[-*_]{3,}$/)) {
        elements.push(
          <hr key={`hr-${index}`} className="my-8 border-mysql-border" />
        );
        return;
      }

      // 普通段落处理
      if (line.trim() !== '') {
        elements.push(
          <p key={`p-${index}`} className="text-mysql-text leading-relaxed mb-4">
            {renderInlineMarkdown(line)}
          </p>
        );
      }
    });

    // 处理未结束的块
    if (inCodeBlock) {
      elements.push(
        <div key="final-code">
          {renderCodeBlock(currentCodeBlock, currentCodeLanguage)}
        </div>
      );
    }

    if (inList) {
      const isOrdered = currentList[0]?.match(/^[\s]*\d+\.\s+/);
      const ListTag = isOrdered ? 'ol' : 'ul';
      elements.push(
        <ListTag key="final-list" className={cn(
          'my-4 space-y-2',
          isOrdered ? 'list-decimal list-inside' : 'list-disc list-inside'
        )}>
          {currentList.map((item, i) => {
            const content = item.replace(/^[\s]*[-*+\d.]\s+/, '');
            return (
              <li key={i} className="text-mysql-text leading-relaxed">
                {renderInlineMarkdown(content)}
              </li>
            );
          })}
        </ListTag>
      );
    }

    if (inTable) {
      elements.push(
        <div key="final-table">
          {renderTable(currentTable)}
        </div>
      );
    }

    return elements;
  };

  return (
    <div className={cn('prose prose-mysql max-w-none', className)}>
      {renderMarkdownContent(content)}
    </div>
  );
}
