// MySQLAi.de - 主网站布局组件
// 为主网站页面提供Header和Footer

import React from 'react';
import Header from './Header';
import Footer from './Footer';

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <>
      <Header />
      <main className="pt-16 lg:pt-20 relative">
        {children}
      </main>
      <Footer />
    </>
  );
}
