'use client';

// MySQLAi.de - 统计图表组件
// 使用recharts创建各种统计图表

import React from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { cn } from '@/lib/utils';

// 图表数据类型
interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

interface StatsChartProps {
  title: string;
  type: 'line' | 'area' | 'bar' | 'pie';
  data: ChartData[];
  loading?: boolean;
  className?: string;
  height?: number;
  colors?: string[];
}

// 默认颜色配置
const DEFAULT_COLORS = [
  '#3B82F6', // blue
  '#10B981', // green
  '#F59E0B', // yellow
  '#EF4444', // red
  '#8B5CF6', // purple
  '#06B6D4', // cyan
  '#EC4899', // pink
  '#6B7280'  // gray
];

// MySQL主题颜色
const MYSQL_COLORS = [
  '#E97627', // mysql-primary
  '#F4A261', // mysql-accent
  '#2A9D8F', // mysql-secondary
  '#264653', // mysql-dark
  '#E76F51', // mysql-warning
  '#F77F00', // mysql-info
  '#FCBF49', // mysql-success
  '#D62828'  // mysql-danger
];

export default function StatsChart({ 
  title, 
  type, 
  data, 
  loading = false, 
  className,
  height = 300,
  colors = MYSQL_COLORS
}: StatsChartProps) {

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-mysql-border rounded-lg shadow-lg">
          <p className="text-sm font-medium text-mysql-text mb-1">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 渲染图表
  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              strokeWidth={2}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: colors[0], strokeWidth: 2 }}
            />
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              fill={colors[0]}
              fillOpacity={0.3}
              strokeWidth={2}
            />
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: '#6B7280' }}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="value" 
              fill={colors[0]}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        );

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend />
          </PieChart>
        );

      default:
        return <div>不支持的图表类型</div>;
    }
  };

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={cn(
          'bg-white rounded-lg shadow-md border border-mysql-border p-6',
          className
        )}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div 
          className="bg-gray-100 rounded animate-pulse"
          style={{ height: height }}
        ></div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn(
        'bg-white rounded-lg shadow-md border border-mysql-border p-6',
        className
      )}
    >
      {/* 图表标题 */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-mysql-text">
          {title}
        </h3>
        <div className="text-sm text-mysql-text-light">
          共 {data.length} 项数据
        </div>
      </div>

      {/* 图表内容 */}
      {data.length > 0 ? (
        <ResponsiveContainer width="100%" height={height}>
          {renderChart()}
        </ResponsiveContainer>
      ) : (
        <div 
          className="flex items-center justify-center bg-gray-50 rounded-lg"
          style={{ height: height }}
        >
          <div className="text-center">
            <div className="text-4xl text-gray-300 mb-2">📊</div>
            <p className="text-mysql-text-light">暂无数据</p>
          </div>
        </div>
      )}
    </motion.div>
  );
}
