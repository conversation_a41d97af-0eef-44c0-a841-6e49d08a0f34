'use client';

/**
 * 导出对话框组件 - ER图生成工具
 * 支持多种图片格式选择、质量设置和背景选项，提供用户友好的导出体验
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Download, 
  Image, 
  Settings,
  Check,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ExportDialogProps, ExportOptions } from '../types/er-diagram';

/**
 * 导出对话框组件 - 使用React.memo优化性能
 */
const ExportDialog = React.memo(React.forwardRef<HTMLDivElement, ExportDialogProps>(({
  isOpen,
  onClose,
  onExport,
  isExporting = false,
  className,
  ...props
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'PNG',
    background: 'transparent',
    quality: 'high',
    filename: 'er-diagram'
  });
  
  const [errors, setErrors] = useState<Partial<Record<keyof ExportOptions, string>>>({});
  const dialogRef = useRef<HTMLDivElement>(null);
  const filenameInputRef = useRef<HTMLInputElement>(null);

  // 重置表单
  const resetForm = () => {
    setExportOptions({
      format: 'PNG',
      background: 'transparent',
      quality: 'high',
      filename: 'er-diagram'
    });
    setErrors({});
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof ExportOptions, string>> = {};
    
    if (!exportOptions.filename?.trim()) {
      newErrors.filename = '文件名不能为空';
    } else if (!/^[a-zA-Z0-9_-]+$/.test(exportOptions.filename.trim())) {
      newErrors.filename = '文件名只能包含字母、数字、下划线和连字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = () => {
    if (!validateForm()) return;
    
    const finalOptions = {
      ...exportOptions,
      filename: exportOptions.filename?.trim() || 'er-diagram'
    };
    
    onExport(finalOptions);
  };

  // 处理关闭
  const handleClose = () => {
    if (!isExporting) {
      resetForm();
      onClose();
    }
  };

  // 处理ESC键关闭
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !isExporting) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // 聚焦到文件名输入框
      setTimeout(() => {
        filenameInputRef.current?.focus();
      }, 100);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, isExporting]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        !isExporting &&
        dialogRef.current &&
        !dialogRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, isExporting]);

  // 阻止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // 对话框动画配置
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const dialogVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95,
      y: -20
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -20
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.2 }}
        >
          {/* 遮罩层 */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
          
          {/* 对话框 */}
          <motion.div
            ref={dialogRef}
            className={cn(
              'relative w-full max-w-md bg-white rounded-lg shadow-xl border border-mysql-border',
              'max-h-[90vh] overflow-hidden',
              className
            )}
            variants={dialogVariants}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            {...props}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-mysql-border">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-mysql-primary-light rounded-full flex items-center justify-center">
                  <Download className="w-4 h-4 text-mysql-primary" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-mysql-text">导出ER图</h2>
                  <p className="text-sm text-mysql-text-light">选择导出格式和选项</p>
                </div>
              </div>
              
              <button
                type="button"
                onClick={handleClose}
                disabled={isExporting}
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center',
                  'hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                  'transition-colors duration-200',
                  isExporting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                )}
                aria-label="关闭对话框"
              >
                <X className="w-4 h-4 text-mysql-text-light" />
              </button>
            </div>

            {/* 表单内容 */}
            <div className="p-6 space-y-6">
              {/* 文件名 */}
              <div className="space-y-2">
                <label htmlFor="filename" className="block text-sm font-medium text-mysql-text">
                  文件名
                </label>
                <div className="relative">
                  <input
                    ref={filenameInputRef}
                    id="filename"
                    type="text"
                    value={exportOptions.filename}
                    onChange={(e) => {
                      setExportOptions(prev => ({ ...prev, filename: e.target.value }));
                      if (errors.filename) {
                        setErrors(prev => ({ ...prev, filename: undefined }));
                      }
                    }}
                    disabled={isExporting}
                    className={cn(
                      'w-full px-3 py-2 border rounded-lg text-sm',
                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30 focus:border-mysql-primary',
                      'transition-colors duration-200',
                      errors.filename
                        ? 'border-red-300 bg-red-50'
                        : 'border-mysql-border bg-white',
                      isExporting && 'opacity-50 cursor-not-allowed'
                    )}
                    placeholder="输入文件名"
                  />
                  {errors.filename && (
                    <p className="mt-1 text-xs text-red-600">{errors.filename}</p>
                  )}
                </div>
              </div>

              {/* 图片格式 */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-mysql-text">
                  <Image className="w-4 h-4 inline mr-2" />
                  图片格式
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { value: 'PNG', label: 'PNG', desc: '支持透明背景' },
                    { value: 'JPEG', label: 'JPEG', desc: '文件更小' }
                  ].map((format) => (
                    <button
                      key={format.value}
                      type="button"
                      onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as 'PNG' | 'JPEG' }))}
                      disabled={isExporting}
                      className={cn(
                        'p-3 border rounded-lg text-left transition-all duration-200',
                        'hover:border-mysql-primary hover:bg-mysql-primary-light',
                        'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                        exportOptions.format === format.value
                          ? 'border-mysql-primary bg-mysql-primary-light text-mysql-primary'
                          : 'border-mysql-border bg-white text-mysql-text',
                        isExporting && 'opacity-50 cursor-not-allowed'
                      )}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{format.label}</span>
                        {exportOptions.format === format.value && (
                          <Check className="w-4 h-4 text-mysql-primary" />
                        )}
                      </div>
                      <p className="text-xs text-mysql-text-light mt-1">{format.desc}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* 背景选项 */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-mysql-text">
                  <Settings className="w-4 h-4 inline mr-2" />
                  背景选项
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { value: 'transparent', label: '透明背景', desc: '适合PNG格式' },
                    { value: 'white', label: '白色背景', desc: '适合所有格式' }
                  ].map((bg) => (
                    <button
                      key={bg.value}
                      type="button"
                      onClick={() => setExportOptions(prev => ({ ...prev, background: bg.value as 'transparent' | 'white' }))}
                      disabled={isExporting || (bg.value === 'transparent' && exportOptions.format === 'JPEG')}
                      className={cn(
                        'p-3 border rounded-lg text-left transition-all duration-200',
                        'hover:border-mysql-primary hover:bg-mysql-primary-light',
                        'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                        exportOptions.background === bg.value
                          ? 'border-mysql-primary bg-mysql-primary-light text-mysql-primary'
                          : 'border-mysql-border bg-white text-mysql-text',
                        (isExporting || (bg.value === 'transparent' && exportOptions.format === 'JPEG')) && 'opacity-50 cursor-not-allowed'
                      )}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{bg.label}</span>
                        {exportOptions.background === bg.value && (
                          <Check className="w-4 h-4 text-mysql-primary" />
                        )}
                      </div>
                      <p className="text-xs text-mysql-text-light mt-1">{bg.desc}</p>
                    </button>
                  ))}
                </div>
                {exportOptions.format === 'JPEG' && (
                  <p className="text-xs text-amber-600 bg-amber-50 p-2 rounded">
                    注意：JPEG格式不支持透明背景，将自动使用白色背景
                  </p>
                )}
              </div>

              {/* 图片质量 */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-mysql-text">
                  图片质量
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {[
                    { value: 'high', label: '高质量', desc: '最佳效果' },
                    { value: 'medium', label: '中等质量', desc: '平衡大小' },
                    { value: 'low', label: '低质量', desc: '文件最小' }
                  ].map((quality) => (
                    <button
                      key={quality.value}
                      type="button"
                      onClick={() => setExportOptions(prev => ({ ...prev, quality: quality.value as 'high' | 'medium' | 'low' }))}
                      disabled={isExporting}
                      className={cn(
                        'p-2 border rounded-lg text-center transition-all duration-200',
                        'hover:border-mysql-primary hover:bg-mysql-primary-light',
                        'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                        exportOptions.quality === quality.value
                          ? 'border-mysql-primary bg-mysql-primary-light text-mysql-primary'
                          : 'border-mysql-border bg-white text-mysql-text',
                        isExporting && 'opacity-50 cursor-not-allowed'
                      )}
                    >
                      <div className="font-medium text-sm">{quality.label}</div>
                      <div className="text-xs text-mysql-text-light mt-1">{quality.desc}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-mysql-border bg-gray-50">
              <button
                type="button"
                onClick={handleClose}
                disabled={isExporting}
                className={cn(
                  'px-4 py-2 text-sm font-medium rounded-lg border border-mysql-border',
                  'bg-white text-mysql-text hover:bg-gray-50',
                  'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                  'transition-colors duration-200',
                  isExporting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                )}
              >
                取消
              </button>

              <button
                type="button"
                onClick={handleSubmit}
                disabled={isExporting || !exportOptions.filename?.trim()}
                className={cn(
                  'px-4 py-2 text-sm font-medium rounded-lg',
                  'bg-mysql-primary text-white border border-mysql-primary',
                  'hover:bg-mysql-primary-dark hover:border-mysql-primary-dark',
                  'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                  'transition-colors duration-200',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'flex items-center space-x-2'
                )}
              >
                {isExporting ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>导出中...</span>
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4" />
                    <span>导出</span>
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}));

ExportDialog.displayName = 'ExportDialog';

export default ExportDialog;
