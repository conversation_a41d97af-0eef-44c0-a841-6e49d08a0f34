use tauri::command;
use crate::core::{
    system::{get_system_info as get_sys_info, SystemDetector, SystemPerformanceInfo},
    fingerprint::{generate_machine_fingerprint, get_machine_features_summary, get_machine_features, MachineFeatures},
    types::SystemInfo,
};
use std::collections::HashMap;

#[command]
pub fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[command]
pub fn get_system_info() -> Result<SystemInfo, String> {
    match std::panic::catch_unwind(|| get_sys_info()) {
        Ok(info) => Ok(info),
        Err(_) => Err("Failed to get system information".to_string()),
    }
}

#[command]
pub fn test_connection() -> Result<String, String> {
    Ok("Connection test successful!".to_string())
}

#[command]
pub fn generate_fingerprint() -> Result<String, String> {
    match std::panic::catch_unwind(|| generate_machine_fingerprint()) {
        Ok(fingerprint) => Ok(fingerprint),
        Err(_) => Err("Failed to generate machine fingerprint".to_string()),
    }
}

#[command]
pub fn get_machine_features() -> Result<MachineFeatures, String> {
    match std::panic::catch_unwind(|| get_machine_features()) {
        Ok(features) => Ok(features),
        Err(_) => Err("Failed to get machine features".to_string()),
    }
}

#[command]
pub fn get_features_summary() -> Result<HashMap<String, String>, String> {
    match std::panic::catch_unwind(|| get_machine_features_summary()) {
        Ok(summary) => Ok(summary),
        Err(_) => Err("Failed to get features summary".to_string()),
    }
}

#[command]
pub fn get_system_performance() -> Result<SystemPerformanceInfo, String> {
    match std::panic::catch_unwind(|| {
        let detector = SystemDetector::new();
        detector.get_system_performance_info()
    }) {
        Ok(info) => Ok(info),
        Err(_) => Err("Failed to get system performance information".to_string()),
    }
}

// 激活系统相关命令
use crate::core::activation::{
    initialize_activation_system, is_activated, activate_license as activate_license_fn,
    validate_license_key as validate_license_key_fn, get_activation_status as get_activation_status_fn,
    deactivate_license as deactivate_license_fn, ActivationStatus
};
use crate::core::database::get_default_db_path;

#[command]
pub async fn init_activation_system() -> Result<String, String> {
    let db_path = get_default_db_path();
    match initialize_activation_system(&db_path).await {
        Ok(_) => Ok("Activation system initialized successfully".to_string()),
        Err(e) => Err(format!("Failed to initialize activation system: {}", e)),
    }
}

#[command]
pub async fn check_activation_status() -> Result<ActivationStatus, String> {
    match get_activation_status_fn().await {
        Ok(status) => Ok(status),
        Err(e) => Err(format!("Failed to get activation status: {}", e)),
    }
}

#[command]
pub async fn validate_license(license_key: String) -> Result<bool, String> {
    match validate_license_key_fn(&license_key).await {
        Ok(is_valid) => Ok(is_valid),
        Err(e) => Err(format!("Failed to validate license: {}", e)),
    }
}

#[command]
pub async fn activate_license_key(license_key: String) -> Result<bool, String> {
    match activate_license_fn(&license_key).await {
        Ok(success) => Ok(success),
        Err(e) => Err(format!("Failed to activate license: {}", e)),
    }
}

#[command]
pub async fn deactivate_license_key(license_key: String) -> Result<bool, String> {
    match deactivate_license_fn(&license_key).await {
        Ok(success) => Ok(success),
        Err(e) => Err(format!("Failed to deactivate license: {}", e)),
    }
}

#[command]
pub async fn check_is_activated() -> Result<bool, String> {
    match is_activated().await {
        Ok(activated) => Ok(activated),
        Err(e) => Err(format!("Failed to check activation: {}", e)),
    }
}

// 下载管理相关命令
use crate::core::download::{
    detect_user_location as detect_location_fn, get_mysql_package as get_package_fn,
    get_supported_mysql_versions, LocationInfo, MySQLPackage, DownloadProgress
};
use std::path::PathBuf;

#[command]
pub async fn detect_user_location() -> Result<LocationInfo, String> {
    match detect_location_fn().await {
        Ok(location) => Ok(location),
        Err(e) => Err(format!("Failed to detect user location: {}", e)),
    }
}

#[command]
pub fn get_mysql_package(version: String, os: String, architecture: String) -> Result<Option<MySQLPackage>, String> {
    match get_package_fn(&version, &os, &architecture) {
        Some(package) => Ok(Some(package)),
        None => Ok(None),
    }
}

#[command]
pub fn get_supported_versions() -> Result<Vec<(String, String, String)>, String> {
    Ok(get_supported_mysql_versions())
}

#[command]
pub async fn download_mysql_package(
    version: String,
    os: String,
    architecture: String,
    download_dir: String,
) -> Result<String, String> {
    use crate::core::download::download_mysql;
    use std::path::Path;

    let download_path = Path::new(&download_dir);

    // 创建一个简单的进度回调（实际应用中可能需要更复杂的处理）
    let progress_callback = |progress: DownloadProgress| {
        log::info!(
            "下载进度: {:.1}% ({} / {} bytes) - {:.1} KB/s",
            progress.percentage,
            progress.downloaded,
            progress.total,
            progress.speed
        );
    };

    match download_mysql(&version, &os, &architecture, download_path, progress_callback).await {
        Ok(file_path) => Ok(file_path.to_string_lossy().to_string()),
        Err(e) => Err(format!("Failed to download MySQL package: {}", e)),
    }
}

// MySQL安装器相关命令
use crate::core::installer::{
    detect_mysql_components as detect_components_fn, uninstall_old_mysql as uninstall_fn,
    install_mysql as install_fn, verify_mysql_installation as verify_fn,
    set_mysql_config as set_config_fn, get_mysql_config as get_config_fn,
    MySQLComponent, MySQLConfig, InstallationProgress
};
use std::path::Path;

#[command]
pub fn detect_mysql_components() -> Result<Vec<MySQLComponent>, String> {
    match detect_components_fn() {
        Ok(components) => Ok(components),
        Err(e) => Err(format!("Failed to detect MySQL components: {}", e)),
    }
}

#[command]
pub async fn uninstall_old_mysql(components: Vec<MySQLComponent>) -> Result<(), String> {
    match uninstall_fn(&components).await {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("Failed to uninstall old MySQL: {}", e)),
    }
}

#[command]
pub async fn install_mysql(zip_file_path: String) -> Result<(), String> {
    let path = Path::new(&zip_file_path);
    match install_fn(path).await {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("Failed to install MySQL: {}", e)),
    }
}

#[command]
pub async fn verify_mysql_installation() -> Result<bool, String> {
    match verify_fn().await {
        Ok(result) => Ok(result),
        Err(e) => Err(format!("Failed to verify MySQL installation: {}", e)),
    }
}

#[command]
pub fn set_mysql_config(config: MySQLConfig) -> Result<(), String> {
    set_config_fn(config);
    Ok(())
}

#[command]
pub fn get_mysql_config() -> Result<MySQLConfig, String> {
    Ok(get_config_fn())
}