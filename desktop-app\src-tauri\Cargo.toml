[package]
name = "mysqlai-desktop"
version = "1.0.0"
description = "MySQLAi Desktop MySQL Installer"
authors = ["MySQLAi.de"]
license = "MIT"
repository = "https://github.com/mysqlai/mysqlai-de"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.1", features = [] }

[dependencies]
tauri = { version = "1.6.1", features = [ "window-start-dragging", "window-unminimize", "window-set-title", "dialog-ask", "window-set-size", "window-set-position", "dialog-save", "window-minimize", "window-maximize", "shell-open", "dialog-open", "fs-read-file", "window-close", "fs-read-dir", "fs-exists", "fs-copy-file", "window-show", "fs-create-dir", "fs-write-file", "path-all", "window-hide", "fs-rename-file", "dialog-confirm", "window-unmaximize", "fs-remove-file", "fs-remove-dir", "http-request", "dialog-message"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }
sha2 = "0.10"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
sysinfo = "0.30"
mac_address = "1.1"
aes-gcm = "0.10"
base64 = "0.21"
rand = "0.8"
pbkdf2 = "0.12"
hmac = "0.12"
futures-util = "0.3"
indicatif = "0.17"
env_logger = "0.10"
log = "0.4"
zip = "0.6"

[target.'cfg(windows)'.dependencies]
winreg = "0.52"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[profile.release]
panic = "abort"
codegen-units = 1
lto = "fat"
opt-level = "s"
strip = "symbols"
debug = false
debug-assertions = false
overflow-checks = false
incremental = false
rpath = false

# 额外的安全编译标志
[profile.release.build-override]
opt-level = "s"
codegen-units = 1

# 开发配置（保持调试能力）
[profile.dev]
panic = "unwind"
opt-level = 0
debug = true
debug-assertions = true
overflow-checks = true
lto = false
incremental = true
codegen-units = 256
