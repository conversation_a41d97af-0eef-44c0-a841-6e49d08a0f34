// MySQLAi.de - Supabase 客户端配置
// 提供类型安全的 Supabase 客户端实例

import { createClient } from '@supabase/supabase-js';
import type { Database } from './database.types';

// 环境变量验证 - 构建时使用占位符
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://example.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4YW1wbGUiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY0MjQ0NjQwMCwiZXhwIjoxOTU4MDIyNDAwfQ.example';

// 仅在客户端检查环境变量
if (typeof window !== 'undefined' && (supabaseUrl.includes('example') || supabaseAnonKey.includes('example'))) {
  console.warn('请配置正确的 Supabase 环境变量');
}

// 创建 Supabase 客户端实例
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// 服务端客户端（用于服务端操作）
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY || supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// 认证相关工具函数
export const auth = {
  // 获取当前用户
  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  // 登录
  signIn: async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({ email, password });
  },

  // 注册
  signUp: async (email: string, password: string, metadata?: Record<string, any>) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
  },

  // 登出
  signOut: async () => {
    return await supabase.auth.signOut();
  },

  // 重置密码
  resetPassword: async (email: string) => {
    return await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    });
  }
};

// 数据库操作工具函数
export const db = {
  // 知识库文章
  articles: {
    getAll: () => supabase.from('knowledge_articles').select('*'),
    getById: (id: string) => supabase.from('knowledge_articles').select('*').eq('id', id).single(),
    getByCategory: (categoryId: string) => supabase.from('knowledge_articles').select('*').eq('category_id', categoryId),
    search: (query: string) => supabase.from('knowledge_articles').select('*').textSearch('title,content', query)
  },

  // ER图项目
  erProjects: {
    getAll: () => supabase.from('er_projects').select('*'),
    getById: (id: string) => supabase.from('er_projects').select('*').eq('id', id).single(),
    getByUser: (userId: string) => supabase.from('er_projects').select('*').eq('user_id', userId),
    create: (project: any) => supabase.from('er_projects').insert(project),
    update: (id: string, updates: any) => supabase.from('er_projects').update(updates).eq('id', id),
    delete: (id: string) => supabase.from('er_projects').delete().eq('id', id)
  },

  // 用户收藏
  favorites: {
    getByUser: (userId: string) => supabase.from('user_favorites').select('*, knowledge_articles(*)').eq('user_id', userId),
    add: (userId: string, articleId: string) => supabase.from('user_favorites').insert({ user_id: userId, article_id: articleId }),
    remove: (userId: string, articleId: string) => supabase.from('user_favorites').delete().eq('user_id', userId).eq('article_id', articleId)
  }
};

export default supabase;
