'use client';

/**
 * SQL编辑器组件 - ER图生成工具
 * 基于现有CodeBlock组件设计，提供SQL代码编辑、语法高亮、实时解析反馈功能
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Play,
  AlertCircle,
  CheckCircle,
  Loader2,
  Code,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { SqlEditorProps } from '../types/er-diagram';



/**
 * SQL编辑器组件 - 使用React.memo优化性能
 */
const SqlEditor = React.memo(React.forwardRef<HTMLDivElement, SqlEditorProps>(({
  value,
  onChange,
  onParse,
  isLoading = false,
  error = null,
  readOnly = false,
  showHeader = true,
  className,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  // 移除预览功能 - 简化编辑器界面
  // const [showPreview, setShowPreview] = useState(false);
  const [scrollTop, setScrollTop] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);



  // 移除自动解析，改为手动解析
  // useEffect(() => {
  //   if (value.trim()) {
  //     debouncedParse();
  //   }
  // }, [value, debouncedParse]);

  // 处理文本变化 - 使用useCallback优化性能
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!readOnly) {
      onChange?.(e.target.value);
    }
  }, [readOnly, onChange]);

  // 处理滚动同步 - 确保行号与文本区域同步滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    setScrollTop(target.scrollTop);
  }, []);

  // ResizeObserver监听容器尺寸变化
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      // 容器尺寸变化时，可以在这里处理相关逻辑
      // 目前主要用于确保布局响应性
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 处理键盘快捷键 - 使用useCallback优化性能
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      if (!readOnly && value.trim()) {
        onParse?.();
      }
    }
  }, [readOnly, value, onParse]);





  // 获取状态图标和颜色
  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="w-4 h-4 animate-spin text-mysql-primary" />;
    }
    if (error) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    if (value.trim()) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <Code className="w-4 h-4 text-mysql-text-light" />;
  };

  // 获取状态文本
  const getStatusText = () => {
    if (isLoading) return '解析中...';
    if (error) return error;
    if (value.trim()) return '语法正确';
    return 'SQL编辑器';
  };

  // 容器样式 - 适应全高度布局
  const containerStyles = cn(
    'relative group bg-white overflow-hidden h-full flex flex-col',
    'transition-all duration-300',
    isFocused ? 'ring-2 ring-mysql-primary/20' : '',
    error ? 'border-red-300' : '',
    // 如果传入了className且包含border相关类，则保留，否则移除默认边框
    className?.includes('border') ? className : cn('border-none', className)
  );

  // 头部样式 - 只在独立使用时显示
  const headerStyles = cn(
    'flex items-center justify-between px-4 py-3',
    'bg-mysql-primary-light border-b border-mysql-border'
  );

  // 编辑器容器样式 - 使用calc()进行精确高度计算
  const editorContainerStyles = cn(
    'relative bg-gray-50',
    // 使用calc()计算精确高度，避免flex布局的高度计算问题
    showHeader ? 'h-[calc(100%-4rem)]' : 'h-full'
  );

  return (
    <div
      ref={ref}
      className={containerStyles}
      {...props}
    >
      <div ref={containerRef} className="h-full">
      {/* 头部 - 根据showHeader属性决定是否显示 */}
      {showHeader && (
        <div className={headerStyles}>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm font-medium text-mysql-text">
              {getStatusText()}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            {!readOnly && (
              <button
                type="button"
                onClick={onParse}
                disabled={!value.trim() || isLoading}
                className={cn(
                  'flex items-center space-x-1 px-3 py-1 rounded text-xs',
                  'bg-mysql-primary text-white border border-mysql-primary',
                  'hover:bg-mysql-primary-dark hover:border-mysql-primary-dark',
                  'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'transition-all duration-200'
                )}
              >
                {isLoading ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <Play className="w-3 h-3" />
                )}
                <span>生成ER图 (Ctrl+Enter)</span>
              </button>
            )}

            <span className="px-2 py-1 text-xs bg-white border border-mysql-border rounded text-mysql-text-light">
              SQL
            </span>
          </div>
        </div>
      )}

      {/* 编辑器内容 */}
      <div className={editorContainerStyles}>
        {/* 移除编辑器工具栏 - 简化界面 */}

        {/* 直接显示编辑模式 - 移除预览功能 */}
        <div className="h-full bg-white">
            <div className="flex h-full">
              {/* 行号区域 - 固定宽度，同步滚动 */}
              <div className="flex-shrink-0 w-12 bg-gray-50 border-r border-mysql-border relative">
                <div className="absolute inset-0 overflow-hidden">
                  <div
                    className="py-4 px-2 text-mysql-text-light text-sm font-mono select-none text-right"
                    style={{
                      transform: `translateY(-${scrollTop}px)`,
                      lineHeight: '1.5rem'
                    }}
                  >
                    {value.split('\n').map((_, index) => (
                      <div
                        key={index + 1}
                        className="h-6 flex items-center justify-end"
                      >
                        {index + 1}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 文本输入区域 - 使用calc()精确计算宽度 */}
              <textarea
                ref={textareaRef}
                value={value}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                onScroll={handleScroll}
                readOnly={readOnly}
                placeholder="请输入SQL DDL语句，例如：CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(50))..."
                className={cn(
                  'w-[calc(100%-3rem)] h-full p-4 font-mono text-sm leading-6',
                  'bg-white text-mysql-text resize-none',
                  'border-none outline-none whitespace-pre-wrap',
                  'placeholder:text-mysql-text-light',
                  readOnly ? 'cursor-default' : 'cursor-text'
                )}
                style={{ lineHeight: '1.5rem' }}
              />
            </div>
          </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="px-4 py-2 bg-red-50 border-t border-red-200">
          <div className="flex items-center space-x-2 text-sm text-red-600">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* 快捷键提示 */}
      {!readOnly && (
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="flex items-center space-x-1 px-2 py-1 text-xs bg-mysql-primary text-white rounded">
            <Zap className="w-3 h-3" />
            <span>Ctrl+Enter 生成ER图</span>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}));

SqlEditor.displayName = 'SqlEditor';

export default SqlEditor;
