'use client';

// MySQLAi.de - 管理系统侧边栏导航组件
// 提供管理功能的导航菜单

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Database,
  LayoutDashboard,
  FileText,
  FolderOpen,
  Code,
  BarChart3,
  Settings,
  HelpCircle,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdminSidebarProps {
  onNavigate?: () => void;
}

// 导航项目类型定义
interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  children?: NavigationItem[];
}

// 管理导航菜单配置
const ADMIN_NAVIGATION: NavigationItem[] = [
  {
    name: '仪表板',
    href: '/admin',
    icon: LayoutDashboard,
    description: '系统概览和统计'
  },
  {
    name: '知识库管理',
    href: '/admin/knowledge',
    icon: Database,
    description: '统一管理知识库内容',
    children: [
      {
        name: '文章管理',
        href: '/admin/knowledge/articles',
        icon: FileText,
        description: '知识库文章的增删改查'
      },
      {
        name: '分类管理',
        href: '/admin/knowledge/categories',
        icon: FolderOpen,
        description: '知识库分类的管理'
      },
      {
        name: '代码示例',
        href: '/admin/knowledge/code-examples',
        icon: Code,
        description: '代码片段的管理'
      }
    ]
  },
  {
    name: '统计分析',
    href: '/admin/stats',
    icon: BarChart3,
    description: '数据统计和分析'
  }
];

// 底部菜单
const BOTTOM_NAVIGATION: NavigationItem[] = [
  {
    name: '系统设置',
    href: '/admin/settings',
    icon: Settings,
    description: '系统配置和设置'
  },
  {
    name: '帮助文档',
    href: '/admin/help',
    icon: HelpCircle,
    description: '使用帮助和文档'
  }
];

export default function AdminSidebar({ onNavigate }: AdminSidebarProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>(() => {
    // 初始化时展开包含当前路径的父级菜单
    const expanded: string[] = [];
    ADMIN_NAVIGATION.forEach(item => {
      if (item.children && item.children.some(child => pathname.startsWith(child.href))) {
        expanded.push(item.name);
      }
    });
    return expanded;
  });

  // 检查是否为当前路径
  const isActive = (href: string) => {
    if (href === '/admin') {
      return pathname === '/admin';
    }
    return pathname.startsWith(href);
  };

  // 检查是否为父级菜单激活状态
  const isParentActive = (item: NavigationItem) => {
    if (isActive(item.href)) return true;
    if (item.children) {
      return item.children.some(child => isActive(child.href));
    }
    return false;
  };

  // 切换展开/收起状态
  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  // 渲染子导航项
  const renderChildNavItem = (child: NavigationItem, parentIndex: number, childIndex: number) => {
    const active = isActive(child.href);
    const IconComponent = child.icon;

    return (
      <motion.div
        key={child.name}
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -10 }}
        transition={{ delay: childIndex * 0.05 }}
        className="ml-6"
      >
        <Link
          href={child.href}
          onClick={onNavigate}
          className={cn(
            'flex items-center px-3 py-2 mx-3 rounded-lg transition-all duration-200 group',
            active
              ? 'bg-mysql-primary text-white shadow-md'
              : 'text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light/50'
          )}
        >
          <div className={cn(
            'flex items-center justify-center w-4 h-4 mr-3',
            active ? 'text-white' : 'text-mysql-text-light group-hover:text-mysql-primary'
          )}>
            <IconComponent className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className={cn(
              'font-medium text-sm',
              active ? 'text-white' : 'text-mysql-text group-hover:text-mysql-primary'
            )}>
              {child.name}
            </div>
            <div className={cn(
              'text-xs mt-0.5',
              active ? 'text-white/80' : 'text-mysql-text-light'
            )}>
              {child.description}
            </div>
          </div>
        </Link>
      </motion.div>
    );
  };

  // 渲染主导航项
  const renderNavItem = (item: NavigationItem, index: number) => {
    const active = isParentActive(item);
    const expanded = expandedItems.includes(item.name);
    const hasChildren = item.children && item.children.length > 0;
    const IconComponent = item.icon;

    return (
      <motion.div
        key={item.name}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        {/* 主导航项 */}
        <div
          className={cn(
            'flex items-center px-4 py-3 mx-3 rounded-lg transition-all duration-200 group cursor-pointer',
            active
              ? 'bg-mysql-primary text-white shadow-lg'
              : 'text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light'
          )}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.name);
            } else {
              onNavigate?.();
            }
          }}
        >
          <div className={cn(
            'flex items-center justify-center w-5 h-5 mr-3',
            active ? 'text-white' : 'text-mysql-text-light group-hover:text-mysql-primary'
          )}>
            <IconComponent className="w-5 h-5" />
          </div>
          <div className="flex-1">
            {hasChildren ? (
              <div>
                <div className={cn(
                  'font-medium text-sm',
                  active ? 'text-white' : 'text-mysql-text group-hover:text-mysql-primary'
                )}>
                  {item.name}
                </div>
                <div className={cn(
                  'text-xs mt-0.5',
                  active ? 'text-white/80' : 'text-mysql-text-light'
                )}>
                  {item.description}
                </div>
              </div>
            ) : (
              <Link href={item.href} onClick={onNavigate} className="block">
                <div className={cn(
                  'font-medium text-sm',
                  active ? 'text-white' : 'text-mysql-text group-hover:text-mysql-primary'
                )}>
                  {item.name}
                </div>
                <div className={cn(
                  'text-xs mt-0.5',
                  active ? 'text-white/80' : 'text-mysql-text-light'
                )}>
                  {item.description}
                </div>
              </Link>
            )}
          </div>
          {hasChildren && (
            <div className={cn(
              'flex items-center justify-center w-4 h-4 ml-2 transition-transform duration-200',
              expanded ? 'rotate-90' : '',
              active ? 'text-white' : 'text-mysql-text-light group-hover:text-mysql-primary'
            )}>
              <ChevronRight className="w-4 h-4" />
            </div>
          )}
        </div>

        {/* 子导航项 */}
        {hasChildren && (
          <AnimatePresence>
            {expanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="py-2 space-y-1">
                  {item.children!.map((child, childIndex) =>
                    renderChildNavItem(child, index, childIndex)
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </motion.div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* 侧边栏头部 */}
      <div className="flex items-center px-6 py-6 border-b border-mysql-border">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-mysql-primary rounded-lg">
            <Database className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-mysql-text">
              MySQLAi.de
            </h2>
            <p className="text-xs text-mysql-text-light">
              管理后台
            </p>
          </div>
        </div>
      </div>

      {/* 主导航菜单 */}
      <nav className="flex-1 px-3 py-6">
        <div className="space-y-2">
          {ADMIN_NAVIGATION.map((item, index) => renderNavItem(item, index))}
        </div>
      </nav>

      {/* 底部导航菜单 */}
      <div className="border-t border-mysql-border px-3 py-4">
        <div className="space-y-2">
          {BOTTOM_NAVIGATION.map((item, index) => renderNavItem(item, ADMIN_NAVIGATION.length + index))}
        </div>
      </div>

      {/* 侧边栏底部信息 */}
      <div className="px-6 py-4 border-t border-mysql-border bg-mysql-primary-light/30">
        <div className="text-center">
          <p className="text-xs text-mysql-text-light">
            © 2024 MySQLAi.de
          </p>
          <p className="text-xs text-mysql-text-light mt-1">
            v1.0.0
          </p>
        </div>
      </div>
    </div>
  );
}
