'use client';

// MySQLAi.de - 搜索历史组件
// 显示和管理用户的搜索历史记录

import React, { useState, useEffect } from 'react';
import { Clock, X, Trash2, Search } from 'lucide-react';
import { SearchHistoryItem } from '@/lib/types';
import { hybridSearchHistory } from '@/lib/api/search-history';

interface SearchHistoryProps {
  onSelectQuery?: (query: string) => void;
  onClose?: () => void;
  maxItems?: number;
  className?: string;
}

export default function SearchHistory({
  onSelectQuery,
  onClose,
  maxItems = 10,
  className = ''
}: SearchHistoryProps) {
  const [historyItems, setHistoryItems] = useState<SearchHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载搜索历史
  const loadHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      const items = await hybridSearchHistory.getHistory(maxItems);
      setHistoryItems(items);
    } catch (err) {
      console.error('加载搜索历史失败:', err);
      setError('加载搜索历史失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除单个搜索记录
  const handleDeleteItem = async (id: string) => {
    try {
      await hybridSearchHistory.deleteRecord(id);
      setHistoryItems(prev => prev.filter(item => item.id !== id));
    } catch (err) {
      console.error('删除搜索记录失败:', err);
    }
  };

  // 清除所有搜索历史
  const handleClearAll = async () => {
    if (!window.confirm('确定要清除所有搜索历史吗？')) {
      return;
    }

    try {
      await hybridSearchHistory.clearAll();
      setHistoryItems([]);
    } catch (err) {
      console.error('清除搜索历史失败:', err);
    }
  };

  // 选择搜索查询
  const handleSelectQuery = (query: string) => {
    onSelectQuery?.(query);
    onClose?.();
  };

  // 格式化时间显示
  const formatTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  };

  // 组件挂载时加载历史
  useEffect(() => {
    loadHistory();
  }, [maxItems]);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-mysql-border p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-mysql-text flex items-center gap-2">
            <Clock className="w-4 h-4" />
            搜索历史
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-mysql-text-light hover:text-mysql-text transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
        <div className="space-y-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-mysql-bg rounded w-3/4 mb-1"></div>
              <div className="h-3 bg-mysql-bg rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-mysql-border p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-mysql-text flex items-center gap-2">
            <Clock className="w-4 h-4" />
            搜索历史
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-mysql-text-light hover:text-mysql-text transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
        <div className="text-sm text-mysql-text-light text-center py-4">
          {error}
        </div>
      </div>
    );
  }

  if (historyItems.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-lg border border-mysql-border p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-mysql-text flex items-center gap-2">
            <Clock className="w-4 h-4" />
            搜索历史
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-mysql-text-light hover:text-mysql-text transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
        <div className="text-sm text-mysql-text-light text-center py-4">
          暂无搜索历史
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-mysql-border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-mysql-text flex items-center gap-2">
          <Clock className="w-4 h-4" />
          搜索历史
        </h3>
        <div className="flex items-center gap-2">
          {historyItems.length > 0 && (
            <button
              onClick={handleClearAll}
              className="text-mysql-text-light hover:text-mysql-danger transition-colors"
              title="清除所有历史"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          )}
          {onClose && (
            <button
              onClick={onClose}
              className="text-mysql-text-light hover:text-mysql-text transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      <div className="space-y-1 max-h-64 overflow-y-auto">
        {historyItems.map((item) => (
          <div
            key={item.id}
            className="group flex items-center justify-between p-2 rounded hover:bg-mysql-bg transition-colors cursor-pointer"
            onClick={() => handleSelectQuery(item.query)}
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 text-sm text-mysql-text">
                <Search className="w-3 h-3 text-mysql-text-light flex-shrink-0" />
                <span className="truncate">{item.query}</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-mysql-text-light mt-1">
                <span>{formatTime(item.created_at)}</span>
                {item.results_count > 0 && (
                  <>
                    <span>•</span>
                    <span>{item.results_count} 个结果</span>
                  </>
                )}
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteItem(item.id);
              }}
              className="opacity-0 group-hover:opacity-100 text-mysql-text-light hover:text-mysql-danger transition-all p-1"
              title="删除此记录"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>

      {historyItems.length >= maxItems && (
        <div className="text-xs text-mysql-text-light text-center mt-3 pt-3 border-t border-mysql-border">
          显示最近 {maxItems} 条搜索记录
        </div>
      )}
    </div>
  );
}
