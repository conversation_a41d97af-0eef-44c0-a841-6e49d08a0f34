'use client';

// MySQLAi.de - Breadcrumb面包屑导航组件
// 基于现有的getBreadcrumbs工具函数实现面包屑导航UI

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getBreadcrumbs } from '@/lib/navigation';
import { BreadcrumbProps } from '@/lib/types';

const Breadcrumb: React.FC<BreadcrumbProps> = React.memo(({
  pathname,
  maxItems = 5,
  className,
  ...props
}) => {
  // 获取面包屑数据
  const breadcrumbs = React.useMemo(() => getBreadcrumbs(pathname), [pathname]);

  // 如果只有首页，不显示面包屑
  if (breadcrumbs.length <= 1) {
    return null;
  }

  // 处理超长路径的截断
  const displayBreadcrumbs = breadcrumbs.length > maxItems
    ? [
        breadcrumbs[0], // 首页
        { name: '...', href: '#', isEllipsis: true },
        ...breadcrumbs.slice(-2) // 最后两项
      ]
    : breadcrumbs;

  return (
    <motion.nav
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      aria-label="面包屑导航"
      className={cn(
        'flex items-center space-x-1 text-sm',
        'py-2 px-1',
        className
      )}
      {...props}
    >
      <ol className="flex items-center space-x-1 md:space-x-2">
        {displayBreadcrumbs.map((item, index) => {
          const isLast = index === displayBreadcrumbs.length - 1;
          const isEllipsis = 'isEllipsis' in item && item.isEllipsis;
          const isHome = item.href === '/';

          return (
            <li key={`${item.href}-${index}`} className="flex items-center">
              {/* 分隔符 */}
              {index > 0 && (
                <ChevronRight 
                  className="w-3 h-3 md:w-4 md:h-4 mx-1 md:mx-2 text-gray-400 flex-shrink-0" 
                  aria-hidden="true"
                />
              )}

              {/* 面包屑项 */}
              {isEllipsis ? (
                <span className="text-gray-400 px-1">...</span>
              ) : isLast ? (
                // 当前页面（不可点击）
                <span
                  className={cn(
                    'font-medium text-mysql-primary',
                    'px-2 py-1 rounded-md',
                    'bg-mysql-primary/5',
                    'flex items-center space-x-1'
                  )}
                  aria-current="page"
                >
                  {isHome && <Home className="w-3 h-3 md:w-4 md:h-4" />}
                  <span className={cn(
                    'truncate max-w-[80px] md:max-w-[120px]',
                    isHome && 'hidden md:inline'
                  )}>
                    {item.name}
                  </span>
                </span>
              ) : (
                // 可点击的面包屑项
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      'text-gray-600 hover:text-mysql-primary',
                      'px-2 py-1 rounded-md',
                      'hover:bg-mysql-primary/5',
                      'transition-all duration-200',
                      'flex items-center space-x-1',
                      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30'
                    )}
                  >
                    {isHome && <Home className="w-3 h-3 md:w-4 md:h-4" />}
                    <span className={cn(
                      'truncate max-w-[80px] md:max-w-[120px]',
                      isHome && 'hidden md:inline'
                    )}>
                      {item.name}
                    </span>
                  </Link>
                </motion.div>
              )}
            </li>
          );
        })}
      </ol>
    </motion.nav>
  );
});

Breadcrumb.displayName = 'Breadcrumb';

export default Breadcrumb;
