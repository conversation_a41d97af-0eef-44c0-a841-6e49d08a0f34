// MySQLAi.de - 知识库文章 API
// 提供知识库文章的 CRUD 操作和搜索功能

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type KnowledgeArticleInsert = Database['public']['Tables']['knowledge_articles']['Insert'];

// GET /api/knowledge/articles - 获取文章列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 查询参数
    const categoryId = searchParams.get('category');
    const search = searchParams.get('search');
    const tags = searchParams.get('tags');
    const difficulty = searchParams.get('difficulty');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const includeCodeExamples = searchParams.get('includeCodeExamples') === 'true';
    const includeRelated = searchParams.get('includeRelated') === 'true';

    // 构建查询
    let query = supabase
      .from('knowledge_articles')
      .select(`
        *,
        knowledge_categories!inner(id, name, icon, color)
      `);

    // 分类筛选
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    // 难度筛选
    if (difficulty) {
      query = query.eq('difficulty', difficulty);
    }

    // 标签筛选
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query = query.overlaps('tags', tagArray);
    }

    // 搜索功能
    if (search) {
      // 使用全文搜索
      query = query.textSearch('title,content', search);
      
      // 记录搜索历史
      const userAgent = request.headers.get('user-agent');
      const forwarded = request.headers.get('x-forwarded-for');
      const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip');

      // 异步记录搜索历史，不影响主查询
      supabase
        .from('search_history')
        .insert({
          query: search,
          ip_address: ip,
          user_agent: userAgent
        })
        .then(({ error }) => {
          if (error) console.error('记录搜索历史失败:', error);
        });
    }

    // 排序和分页
    const offset = (page - 1) * limit;
    query = query
      .order('order_index', { ascending: true })
      .range(offset, offset + limit - 1);

    const { data: articles, error, count } = await query;

    if (error) {
      console.error('获取文章失败:', error);
      return NextResponse.json(
        { success: false, error: '获取文章失败', details: error.message },
        { status: 500 }
      );
    }

    // 如果需要包含代码示例
    if (includeCodeExamples && articles) {
      for (const article of articles) {
        const { data: codeExamples } = await supabase
          .from('code_examples')
          .select('*')
          .eq('article_id', article.id)
          .order('order_index');
        
        article.codeExamples = codeExamples || [];
      }
    }

    // 如果需要包含相关文章
    if (includeRelated && articles) {
      for (const article of articles) {
        const { data: relations } = await supabase
          .from('article_relations')
          .select(`
            target_article_id,
            knowledge_articles!article_relations_target_article_id_fkey(
              id, title, description, difficulty, tags
            )
          `)
          .eq('source_article_id', article.id);
        
        article.relatedArticles = relations?.map(r => r.knowledge_articles).filter(Boolean) || [];
      }
    }

    // 更新搜索历史的结果数量
    if (search && articles) {
      supabase
        .from('search_history')
        .update({ results_count: articles.length })
        .eq('query', search)
        .order('created_at', { ascending: false })
        .limit(1)
        .then(({ error }) => {
          if (error) console.error('更新搜索结果数量失败:', error);
        });
    }

    return NextResponse.json({
      success: true,
      data: articles,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/articles - 创建新文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    if (!body.id || !body.title || !body.content) {
      return NextResponse.json(
        { success: false, error: '缺少必需字段: id, title, content' },
        { status: 400 }
      );
    }

    const articleData: KnowledgeArticleInsert = {
      id: body.id,
      title: body.title,
      description: body.description || null,
      content: body.content,
      category_id: body.category_id || null,
      tags: body.tags || null,
      difficulty: body.difficulty || 'beginner',
      order_index: body.order_index || 0,
      last_updated: new Date().toISOString().split('T')[0]
    };

    const { data, error } = await supabase
      .from('knowledge_articles')
      .insert(articleData)
      .select()
      .single();

    if (error) {
      console.error('创建文章失败:', error);
      
      if (error.code === '23505') {
        return NextResponse.json(
          { success: false, error: '文章ID已存在' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { success: false, error: '创建文章失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: '文章创建成功'
    }, { status: 201 });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
