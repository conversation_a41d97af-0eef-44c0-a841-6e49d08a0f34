// MySQLAi.de - Cookie政策页面
// 详细说明网站Cookie的使用情况、类型、目的和用户控制选项

import { Metadata } from 'next';
import { getLegalContent } from '@/lib/legal';
import { PAGE_METADATA } from '@/lib/constants';
import { generatePageMetadata } from '@/app/metadata';
import LegalPageLayout from '@/components/layout/LegalPageLayout';

// 生成页面元数据
export function generateMetadata(): Metadata {
  const pageData = PAGE_METADATA.cookies;
  return generatePageMetadata(
    pageData.title,
    pageData.description,
    '/cookies'
  );
}

export default function CookiesPage() {
  // 获取Cookie政策内容
  const cookiesContent = getLegalContent('cookies');

  return (
    <LegalPageLayout
      type="cookies"
      title={cookiesContent.title}
      lastUpdated={cookiesContent.lastUpdated}
      pathname="/cookies"
    >
      {/* 渲染Cookie政策内容 */}
      {cookiesContent.sections.map((section) => (
        <div key={section.id} className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {section.title}
          </h2>
          <p className="text-gray-700 leading-relaxed mb-4">
            {section.content}
          </p>
          
          {/* 渲染子章节 */}
          {section.subsections && section.subsections.length > 0 && (
            <div className="ml-4 space-y-4">
              {section.subsections.map((subsection) => (
                <div key={subsection.id}>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">
                    {subsection.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {subsection.content}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}

      {/* Cookie类型详细说明 */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 必要Cookie */}
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-lg font-semibold text-red-900 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            必要Cookie
          </h3>
          <div className="text-red-800 space-y-2 text-sm">
            <p><strong>用途：</strong>网站基本功能运行</p>
            <p><strong>示例：</strong>用户登录状态、安全验证</p>
            <p><strong>控制：</strong>无法禁用，网站运行必需</p>
            <p><strong>有效期：</strong>会话期间或30天</p>
          </div>
        </div>

        {/* 功能Cookie */}
        <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
            功能Cookie
          </h3>
          <div className="text-blue-800 space-y-2 text-sm">
            <p><strong>用途：</strong>记住用户偏好设置</p>
            <p><strong>示例：</strong>语言选择、主题设置</p>
            <p><strong>控制：</strong>可在设置中禁用</p>
            <p><strong>有效期：</strong>1年</p>
          </div>
        </div>

        {/* 分析Cookie */}
        <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-900 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
              <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
            </svg>
            分析Cookie
          </h3>
          <div className="text-green-800 space-y-2 text-sm">
            <p><strong>用途：</strong>了解网站使用情况</p>
            <p><strong>示例：</strong>页面访问统计、用户行为</p>
            <p><strong>控制：</strong>可在设置中禁用</p>
            <p><strong>有效期：</strong>2年</p>
          </div>
        </div>

        {/* 营销Cookie */}
        <div className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
          <h3 className="text-lg font-semibold text-purple-900 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
            </svg>
            营销Cookie
          </h3>
          <div className="text-purple-800 space-y-2 text-sm">
            <p><strong>用途：</strong>提供个性化内容</p>
            <p><strong>示例：</strong>广告推荐、内容定制</p>
            <p><strong>控制：</strong>可在设置中禁用</p>
            <p><strong>有效期：</strong>1年</p>
          </div>
        </div>
      </div>

      {/* Cookie管理指南 */}
      <div className="mt-12 p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
          如何管理Cookie
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-800 mb-2">浏览器设置</h4>
            <ul className="text-gray-700 text-sm space-y-1">
              <li>• Chrome：设置 → 隐私和安全 → Cookie</li>
              <li>• Firefox：选项 → 隐私与安全 → Cookie</li>
              <li>• Safari：偏好设置 → 隐私 → Cookie</li>
              <li>• Edge：设置 → Cookie和网站权限</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-800 mb-2">平台设置</h4>
            <ul className="text-gray-700 text-sm space-y-1">
              <li>• 登录后在账户设置中管理</li>
              <li>• 可选择性禁用非必要Cookie</li>
              <li>• 随时修改Cookie偏好</li>
              <li>• 清除已存储的Cookie数据</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 第三方Cookie说明 */}
      <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          第三方Cookie
        </h3>
        <div className="text-yellow-800 space-y-2">
          <p>我们的网站可能包含以下第三方服务的Cookie：</p>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li><strong>Google Analytics</strong>：网站访问统计分析</li>
            <li><strong>百度统计</strong>：中文用户行为分析</li>
            <li><strong>社交媒体插件</strong>：分享和登录功能</li>
            <li><strong>CDN服务</strong>：内容分发和加速</li>
          </ul>
          <p className="text-sm mt-3">
            这些第三方Cookie受其各自隐私政策约束，我们建议您查阅相关服务提供商的隐私政策。
          </p>
        </div>
      </div>
    </LegalPageLayout>
  );
}
