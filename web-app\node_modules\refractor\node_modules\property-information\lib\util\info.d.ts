export class Info {
    /**
     * @constructor
     * @param {string} property
     * @param {string} attribute
     */
    constructor(property: string, attribute: string);
    /** @type {string} */
    property: string;
    /** @type {string} */
    attribute: string;
    /** @type {string|null} */
    space: string | null;
    boolean: boolean;
    booleanish: boolean;
    overloadedBoolean: boolean;
    number: boolean;
    commaSeparated: boolean;
    spaceSeparated: boolean;
    commaOrSpaceSeparated: boolean;
    mustUseProperty: boolean;
    defined: boolean;
}
