'use client';

// MySQLAi.de - 优化图片组件
// 支持懒加载、占位符、错误处理的高性能图片组件

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { ImageIcon, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  objectPosition?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  showPlaceholder?: boolean;
  placeholderClassName?: string;
  errorClassName?: string;
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  loading = 'lazy',
  onLoad,
  onError,
  fallbackSrc,
  showPlaceholder = true,
  placeholderClassName,
  errorClassName,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  // 交叉观察器用于懒加载
  useEffect(() => {
    if (!imgRef.current || priority) {
      setIsInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    observer.observe(imgRef.current);

    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  // 生成默认的模糊占位符
  const generateBlurDataURL = (w: number = 10, h: number = 10) => {
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#E6F3F7'; // mysql-primary-light
      ctx.fillRect(0, 0, w, h);
    }
    return canvas.toDataURL();
  };

  const defaultBlurDataURL = blurDataURL || generateBlurDataURL();

  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden',
        fill ? 'w-full h-full' : '',
        className
      )}
      {...(!fill && {
        style: {
          '--img-width': typeof width === 'number' ? `${width}px` : width,
          '--img-height': typeof height === 'number' ? `${height}px` : height,
          width: 'var(--img-width)',
          height: 'var(--img-height)'
        } as React.CSSProperties
      })}
    >
      <AnimatePresence>
        {/* 加载占位符 */}
        {isLoading && showPlaceholder && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className={cn(
              'absolute inset-0 flex items-center justify-center bg-mysql-primary-light',
              placeholderClassName
            )}
          >
            <div className="flex flex-col items-center space-y-2">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <ImageIcon className="w-8 h-8 text-mysql-primary opacity-50" />
              </motion.div>
              <span className="text-xs text-mysql-text-light">加载中...</span>
            </div>
          </motion.div>
        )}

        {/* 错误状态 */}
        {hasError && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className={cn(
              'absolute inset-0 flex items-center justify-center bg-mysql-border',
              errorClassName
            )}
          >
            <div className="flex flex-col items-center space-y-2 text-mysql-text-light">
              <AlertCircle className="w-8 h-8" />
              <span className="text-xs">图片加载失败</span>
              {fallbackSrc && (
                <button
                  type="button"
                  onClick={() => {
                    setHasError(false);
                    setIsLoading(true);
                  }}
                  className="text-xs text-mysql-primary hover:underline"
                >
                  重试
                </button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 主图片 */}
      {isInView && !hasError && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoading ? 0 : 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="relative w-full h-full"
        >
          <Image
            src={hasError && fallbackSrc ? fallbackSrc : src}
            alt={alt}
            width={fill ? undefined : width}
            height={fill ? undefined : height}
            fill={fill}
            priority={priority}
            quality={quality}
            placeholder={placeholder}
            blurDataURL={placeholder === 'blur' ? defaultBlurDataURL : undefined}
            sizes={sizes}
            loading={loading}
            style={{
              objectFit,
              objectPosition,
            }}
            onLoad={handleLoad}
            onError={handleError}
            className="transition-opacity duration-300"
          />
        </motion.div>
      )}
    </div>
  );
}

// 头像图片组件
interface AvatarImageProps {
  src: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackInitials?: string;
}

export function AvatarImage({
  src,
  alt,
  size = 'md',
  className,
  fallbackInitials,
}: AvatarImageProps) {
  const [hasError, setHasError] = useState(false);

  const sizeConfig = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-12 h-12 text-sm',
    lg: 'w-16 h-16 text-base',
    xl: 'w-24 h-24 text-lg',
  };

  if (hasError && fallbackInitials) {
    return (
      <div
        className={cn(
          'flex items-center justify-center bg-mysql-primary text-white font-semibold rounded-full',
          sizeConfig[size],
          className
        )}
      >
        {fallbackInitials}
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 64 : 96}
      height={size === 'sm' ? 32 : size === 'md' ? 48 : size === 'lg' ? 64 : 96}
      className={cn('rounded-full', sizeConfig[size], className)}
      onError={() => setHasError(true)}
      objectFit="cover"
    />
  );
}

// 卡片图片组件
interface CardImageProps {
  src: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'tall';
  className?: string;
  overlay?: React.ReactNode;
  hoverEffect?: boolean;
}

export function CardImage({
  src,
  alt,
  aspectRatio = 'video',
  className,
  overlay,
  hoverEffect = true,
}: CardImageProps) {
  const aspectConfig = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]',
    tall: 'aspect-[3/4]',
  };

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-lg group',
        aspectConfig[aspectRatio],
        className
      )}
    >
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        className={cn(
          'transition-transform duration-300',
          hoverEffect && 'group-hover:scale-105'
        )}
      />
      
      {overlay && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="absolute bottom-4 left-4 right-4">
            {overlay}
          </div>
        </div>
      )}
    </div>
  );
}

// 背景图片组件
interface BackgroundImageProps {
  src: string;
  alt: string;
  children?: React.ReactNode;
  className?: string;
  overlay?: boolean;
  overlayOpacity?: number;
  parallax?: boolean;
}

export function BackgroundImage({
  src,
  alt,
  children,
  className,
  overlay = false,
  overlayOpacity = 0.5,
  parallax = false,
}: BackgroundImageProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        priority
        className={cn(
          'absolute inset-0',
          parallax && 'transform-gpu will-change-transform'
        )}
      />
      
      {overlay && (
        <div
          className="absolute inset-0 bg-black"
          style={{ opacity: overlayOpacity }}
        />
      )}
      
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  );
}
