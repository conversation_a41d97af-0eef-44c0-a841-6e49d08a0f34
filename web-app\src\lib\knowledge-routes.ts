// MySQLAi.de - 知识库路由配置
// 统一管理知识库模块的路由结构和权限配置

// 知识库路由定义
export const KNOWLEDGE_ROUTES = {
  // 主入口
  ROOT: '/admin/knowledge',
  
  // 文章管理
  ARTICLES: {
    ROOT: '/admin/knowledge/articles',
    NEW: '/admin/knowledge/articles/new',
    EDIT: (id: string) => `/admin/knowledge/articles/${id}`,
    PREVIEW: (id: string) => `/admin/knowledge/articles/${id}/preview`, // 管理员预览路由
    VIEW: (id: string) => `/knowledge/${id}` // 前端查看路由（保留用于公共访问）
  },
  
  // 分类管理
  CATEGORIES: {
    ROOT: '/admin/knowledge/categories',
    EDIT: (id: string) => `/admin/knowledge/categories/${id}`,
    VIEW: (id: string) => `/knowledge/category/${id}` // 前端查看路由
  },
  
  // 代码示例管理
  CODE_EXAMPLES: {
    ROOT: '/admin/knowledge/code-examples',
    NEW: '/admin/knowledge/code-examples/new',
    EDIT: (id: string) => `/admin/knowledge/code-examples/${id}`,
    VIEW: (id: string) => `/knowledge/code/${id}` // 前端查看路由
  }
} as const;

// 旧路由到新路由的映射（用于重定向）
export const LEGACY_ROUTE_MAPPING = {
  '/admin/articles': KNOWLEDGE_ROUTES.ARTICLES.ROOT,
  '/admin/articles/new': KNOWLEDGE_ROUTES.ARTICLES.NEW,
  '/admin/articles/[id]': (id: string) => KNOWLEDGE_ROUTES.ARTICLES.EDIT(id),
  '/admin/categories': KNOWLEDGE_ROUTES.CATEGORIES.ROOT,
  '/admin/code-examples': KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT,
  '/admin/code-examples/new': KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW,
  '/admin/code-examples/[id]': (id: string) => KNOWLEDGE_ROUTES.CODE_EXAMPLES.EDIT(id)
} as const;

// 面包屑导航配置
export interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: string;
}

export const KNOWLEDGE_BREADCRUMBS: Record<string, BreadcrumbItem[]> = {
  [KNOWLEDGE_ROUTES.ROOT]: [
    { label: '管理后台', href: '/admin' },
    { label: '知识库管理', href: KNOWLEDGE_ROUTES.ROOT }
  ],
  
  [KNOWLEDGE_ROUTES.ARTICLES.ROOT]: [
    { label: '管理后台', href: '/admin' },
    { label: '知识库管理', href: KNOWLEDGE_ROUTES.ROOT },
    { label: '文章管理', href: KNOWLEDGE_ROUTES.ARTICLES.ROOT }
  ],
  
  [KNOWLEDGE_ROUTES.ARTICLES.NEW]: [
    { label: '管理后台', href: '/admin' },
    { label: '知识库管理', href: KNOWLEDGE_ROUTES.ROOT },
    { label: '文章管理', href: KNOWLEDGE_ROUTES.ARTICLES.ROOT },
    { label: '创建文章', href: KNOWLEDGE_ROUTES.ARTICLES.NEW }
  ],
  
  [KNOWLEDGE_ROUTES.CATEGORIES.ROOT]: [
    { label: '管理后台', href: '/admin' },
    { label: '知识库管理', href: KNOWLEDGE_ROUTES.ROOT },
    { label: '分类管理', href: KNOWLEDGE_ROUTES.CATEGORIES.ROOT }
  ],
  
  [KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT]: [
    { label: '管理后台', href: '/admin' },
    { label: '知识库管理', href: KNOWLEDGE_ROUTES.ROOT },
    { label: '代码示例', href: KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT }
  ],
  
  [KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW]: [
    { label: '管理后台', href: '/admin' },
    { label: '知识库管理', href: KNOWLEDGE_ROUTES.ROOT },
    { label: '代码示例', href: KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT },
    { label: '创建示例', href: KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW }
  ]
};

// 动态生成面包屑导航
export function generateBreadcrumbs(pathname: string, params?: Record<string, string>): BreadcrumbItem[] {
  // 处理动态路由
  if (pathname.includes('/articles/') && params?.id) {
    if (params.id === 'new') {
      return KNOWLEDGE_BREADCRUMBS[KNOWLEDGE_ROUTES.ARTICLES.NEW];
    } else {
      return [
        ...KNOWLEDGE_BREADCRUMBS[KNOWLEDGE_ROUTES.ARTICLES.ROOT],
        { label: '编辑文章', href: KNOWLEDGE_ROUTES.ARTICLES.EDIT(params.id) }
      ];
    }
  }
  
  if (pathname.includes('/code-examples/') && params?.id) {
    if (params.id === 'new') {
      return KNOWLEDGE_BREADCRUMBS[KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW];
    } else {
      return [
        ...KNOWLEDGE_BREADCRUMBS[KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT],
        { label: '编辑示例', href: KNOWLEDGE_ROUTES.CODE_EXAMPLES.EDIT(params.id) }
      ];
    }
  }
  
  // 返回静态路由的面包屑
  return KNOWLEDGE_BREADCRUMBS[pathname] || [
    { label: '管理后台', href: '/admin' }
  ];
}

// 路由权限配置
export const KNOWLEDGE_PERMISSIONS = {
  // 知识库模块基础权限
  KNOWLEDGE_ACCESS: 'knowledge:access',
  
  // 文章权限
  ARTICLES_VIEW: 'articles:view',
  ARTICLES_CREATE: 'articles:create',
  ARTICLES_EDIT: 'articles:edit',
  ARTICLES_DELETE: 'articles:delete',
  
  // 分类权限
  CATEGORIES_VIEW: 'categories:view',
  CATEGORIES_CREATE: 'categories:create',
  CATEGORIES_EDIT: 'categories:edit',
  CATEGORIES_DELETE: 'categories:delete',
  
  // 代码示例权限
  CODE_EXAMPLES_VIEW: 'code_examples:view',
  CODE_EXAMPLES_CREATE: 'code_examples:create',
  CODE_EXAMPLES_EDIT: 'code_examples:edit',
  CODE_EXAMPLES_DELETE: 'code_examples:delete'
} as const;

// 路由权限映射
export const ROUTE_PERMISSIONS: Record<string, string[]> = {
  [KNOWLEDGE_ROUTES.ROOT]: [KNOWLEDGE_PERMISSIONS.KNOWLEDGE_ACCESS],
  
  [KNOWLEDGE_ROUTES.ARTICLES.ROOT]: [
    KNOWLEDGE_PERMISSIONS.KNOWLEDGE_ACCESS,
    KNOWLEDGE_PERMISSIONS.ARTICLES_VIEW
  ],
  
  [KNOWLEDGE_ROUTES.ARTICLES.NEW]: [
    KNOWLEDGE_PERMISSIONS.KNOWLEDGE_ACCESS,
    KNOWLEDGE_PERMISSIONS.ARTICLES_CREATE
  ],
  
  [KNOWLEDGE_ROUTES.CATEGORIES.ROOT]: [
    KNOWLEDGE_PERMISSIONS.KNOWLEDGE_ACCESS,
    KNOWLEDGE_PERMISSIONS.CATEGORIES_VIEW
  ],
  
  [KNOWLEDGE_ROUTES.CODE_EXAMPLES.ROOT]: [
    KNOWLEDGE_PERMISSIONS.KNOWLEDGE_ACCESS,
    KNOWLEDGE_PERMISSIONS.CODE_EXAMPLES_VIEW
  ],
  
  [KNOWLEDGE_ROUTES.CODE_EXAMPLES.NEW]: [
    KNOWLEDGE_PERMISSIONS.KNOWLEDGE_ACCESS,
    KNOWLEDGE_PERMISSIONS.CODE_EXAMPLES_CREATE
  ]
};

// 检查路由权限
export function checkRoutePermission(pathname: string, userPermissions: string[]): boolean {
  const requiredPermissions = ROUTE_PERMISSIONS[pathname];
  
  if (!requiredPermissions) {
    // 如果没有定义权限要求，默认允许访问
    return true;
  }
  
  // 检查用户是否拥有所有必需的权限
  return requiredPermissions.every(permission => 
    userPermissions.includes(permission)
  );
}

// 获取重定向路由
export function getRedirectRoute(legacyPath: string, params?: Record<string, string>): string | null {
  // 处理动态路由
  if (legacyPath.includes('[id]') && params?.id) {
    const routeFunction = LEGACY_ROUTE_MAPPING[legacyPath as keyof typeof LEGACY_ROUTE_MAPPING];
    if (typeof routeFunction === 'function') {
      return routeFunction(params.id);
    }
  }
  
  // 处理静态路由
  const newRoute = LEGACY_ROUTE_MAPPING[legacyPath as keyof typeof LEGACY_ROUTE_MAPPING];
  if (typeof newRoute === 'string') {
    return newRoute;
  }
  
  return null;
}

// 验证路由参数
export function validateRouteParams(pathname: string, params: Record<string, string>): boolean {
  // ID参数验证
  if (params.id) {
    // 检查ID格式（可以是UUID或自定义格式）
    const isValidId = /^[a-zA-Z0-9_-]+$/.test(params.id);
    if (!isValidId) {
      return false;
    }
  }
  
  // 可以添加更多参数验证逻辑
  
  return true;
}

// 导出类型
export type KnowledgeRoute = typeof KNOWLEDGE_ROUTES;
export type KnowledgePermission = typeof KNOWLEDGE_PERMISSIONS;
export type LegacyRouteMapping = typeof LEGACY_ROUTE_MAPPING;
