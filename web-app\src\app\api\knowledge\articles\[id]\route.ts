// MySQLAi.de - 单个知识库文章 API
// 提供单个文章的详细操作

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type KnowledgeArticleUpdate = Database['public']['Tables']['knowledge_articles']['Update'];

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/knowledge/articles/[id] - 获取单个文章
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const includeCodeExamples = searchParams.get('includeCodeExamples') !== 'false'; // 默认包含
    const includeRelated = searchParams.get('includeRelated') !== 'false'; // 默认包含

    // 获取文章基本信息
    const { data: article, error } = await supabase
      .from('knowledge_articles')
      .select(`
        *,
        knowledge_categories(id, name, icon, color)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: '文章不存在' },
          { status: 404 }
        );
      }

      console.error('获取文章失败:', error);
      return NextResponse.json(
        { success: false, error: '获取文章失败', details: error.message },
        { status: 500 }
      );
    }

    const result: Record<string, unknown> = { ...article };

    // 获取代码示例
    if (includeCodeExamples) {
      const { data: codeExamples, error: codeError } = await supabase
        .from('code_examples')
        .select('*')
        .eq('article_id', id)
        .order('order_index', { ascending: true });

      if (codeError) {
        console.error('获取代码示例失败:', codeError);
      } else {
        result.codeExamples = codeExamples || [];
      }
    }

    // 获取相关文章
    if (includeRelated) {
      const { data: relations, error: relatedError } = await supabase
        .from('article_relations')
        .select(`
          target_article_id,
          knowledge_articles!article_relations_target_article_id_fkey(
            id, title, description, difficulty, tags, last_updated
          )
        `)
        .eq('source_article_id', id);

      if (relatedError) {
        console.error('获取相关文章失败:', relatedError);
      } else {
        result.relatedArticles = relations?.map(r => r.knowledge_articles).filter(Boolean) || [];
      }
    }

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PUT /api/knowledge/articles/[id] - 更新文章
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const body = await request.json();

    // 构建更新数据
    const updateData: KnowledgeArticleUpdate = {};
    
    if (body.title !== undefined) updateData.title = body.title;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.content !== undefined) updateData.content = body.content;
    if (body.category_id !== undefined) updateData.category_id = body.category_id;
    if (body.tags !== undefined) updateData.tags = body.tags;
    if (body.difficulty !== undefined) updateData.difficulty = body.difficulty;
    if (body.order_index !== undefined) updateData.order_index = body.order_index;
    
    // 自动更新最后修改日期
    updateData.last_updated = new Date().toISOString().split('T')[0];

    // 检查是否有数据需要更新
    if (Object.keys(updateData).length <= 1) { // 只有 last_updated
      return NextResponse.json(
        { success: false, error: '没有提供需要更新的数据' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('knowledge_articles')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: '文章不存在' },
          { status: 404 }
        );
      }

      console.error('更新文章失败:', error);
      return NextResponse.json(
        { success: false, error: '更新文章失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: '文章更新成功'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// DELETE /api/knowledge/articles/[id] - 删除文章
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;

    // 删除相关的代码示例（由于外键约束，会自动删除）
    // 删除相关的文章关联（由于外键约束，会自动删除）
    
    const { error } = await supabase
      .from('knowledge_articles')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('删除文章失败:', error);
      return NextResponse.json(
        { success: false, error: '删除文章失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '文章删除成功'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
