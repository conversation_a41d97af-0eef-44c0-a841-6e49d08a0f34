/**
 * 多系统MySQL配置生成器
 * 移植Python配置模板逻辑，支持Windows/macOS/Linux的配置文件生成
 */

import {
  SupportedOS,
  MySQLVersion,
  QuickInstallConfig,
  AdvancedConfig
} from '../types/mysql-installer';
import {
  CONFIG_FILE_NAMES,
  SERVICE_NAMES,
  createDefaultQuickInstallConfig
} from './defaults';

/**
 * MySQL版本特定配置
 * 移植自Python的version_specific配置
 */
const VERSION_SPECIFIC_CONFIG = {
  [MySQLVersion.V8_0_28]: {
    max_connections: 300,
    innodb_buffer_pool_size: '512M',
    innodb_log_file_size: '128M',
    innodb_log_buffer_size: '32M',
    collation_server: 'utf8mb4_0900_ai_ci',
    innodb_buffer_pool_instances: 4,
    innodb_read_io_threads: 8,
    innodb_write_io_threads: 8,
    innodb_thread_concurrency: 16,
    table_open_cache: 1000,
    thread_cache_size: 32,
    sort_buffer_size: '4M',
    read_buffer_size: '2M',
    read_rnd_buffer_size: '2M',
    authentication_policy: 'mysql_native_password,*,*',
    binlog_expire_logs_seconds: 604800,
    sql_mode: 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
  },
  [MySQLVersion.V8_0_36]: {
    max_connections: 300,
    innodb_buffer_pool_size: '512M',
    innodb_log_file_size: '128M',
    innodb_log_buffer_size: '32M',
    collation_server: 'utf8mb4_0900_ai_ci',
    innodb_buffer_pool_instances: 4,
    innodb_read_io_threads: 8,
    innodb_write_io_threads: 8,
    innodb_thread_concurrency: 16,
    table_open_cache: 1000,
    thread_cache_size: 32,
    sort_buffer_size: '4M',
    read_buffer_size: '2M',
    read_rnd_buffer_size: '2M',
    authentication_policy: 'mysql_native_password,*,*',
    binlog_expire_logs_seconds: 604800,
    sql_mode: 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
  }
} as const;

/**
 * 基础配置模板（所有版本通用）
 * 移植自Python的base_config
 */
const BASE_CONFIG = {
  character_set_server: 'utf8mb4',
  default_storage_engine: 'INNODB',
  default_authentication_plugin: 'mysql_native_password',
  max_connect_errors: 10,
  innodb_file_per_table: 1,
  innodb_flush_log_at_trx_commit: 1,
  performance_schema: 'ON',
  wait_timeout: 28800,
  interactive_timeout: 28800,
  max_allowed_packet: '64M'
} as const;

/**
 * 配置文件模板
 * 移植自Python的config_template，使用模板字面量
 */
function getConfigTemplate(os: SupportedOS): string {
  const isWindows = os === SupportedOS.WINDOWS;
  const pathSeparator = isWindows ? '\\\\' : '/';
  
  return `[mysqld]
# 设置MySQL安装目录
basedir={{INSTALL_PATH}}
# 设置MySQL数据目录
datadir={{DATA_DIR}}
# 设置服务器端口
port={{PORT}}
# 设置服务器字符集
character-set-server={{CHARACTER_SET_SERVER}}
{{COLLATION_SERVER}}
# 设置默认存储引擎
default-storage-engine={{DEFAULT_STORAGE_ENGINE}}
# 设置最大连接数
max_connections={{MAX_CONNECTIONS}}
# 设置临时文件目录
tmpdir={{INSTALL_PATH}}${pathSeparator}tmp
# 设置错误日志文件
log-error={{DATA_DIR}}${pathSeparator}mysql_error.log
# 设置InnoDB缓冲池大小
innodb_buffer_pool_size={{INNODB_BUFFER_POOL_SIZE}}
# 设置InnoDB日志文件大小
innodb_log_file_size={{INNODB_LOG_FILE_SIZE}}
# 设置InnoDB日志缓冲区大小
innodb_log_buffer_size={{INNODB_LOG_BUFFER_SIZE}}
# 设置InnoDB文件每次同步
innodb_flush_log_at_trx_commit={{INNODB_FLUSH_LOG_AT_TRX_COMMIT}}
# 设置InnoDB文件每表一个文件
innodb_file_per_table={{INNODB_FILE_PER_TABLE}}
{{VERSION_SPECIFIC_CONFIG}}
# 设置性能模式
performance_schema={{PERFORMANCE_SCHEMA}}
# 设置表打开缓存
table_open_cache={{TABLE_OPEN_CACHE}}
# 设置线程缓存大小
thread_cache_size={{THREAD_CACHE_SIZE}}
{{ADDITIONAL_CONFIG}}
# 设置排序缓冲区大小
sort_buffer_size={{SORT_BUFFER_SIZE}}
# 设置读取缓冲区大小
read_buffer_size={{READ_BUFFER_SIZE}}
# 设置读取随机缓冲区大小
read_rnd_buffer_size={{READ_RND_BUFFER_SIZE}}
# 设置连接超时
wait_timeout={{WAIT_TIMEOUT}}
# 设置交互式超时
interactive_timeout={{INTERACTIVE_TIMEOUT}}
# 设置最大允许的包大小
max_allowed_packet={{MAX_ALLOWED_PACKET}}
# 设置默认认证插件
default_authentication_plugin={{DEFAULT_AUTHENTICATION_PLUGIN}}
# 设置安全文件权限
secure_file_priv={{INSTALL_PATH}}${pathSeparator}uploads
# 设置最大连接错误
max_connect_errors={{MAX_CONNECT_ERRORS}}
{{MYSQL8_SPECIFIC}}

[mysql]
# 设置mysql客户端默认字符集
default-character-set={{CLIENT_CHARACTER_SET}}

[client]
# 设置mysql客户端连接服务端时默认使用的端口
port={{PORT}}
default-character-set={{CLIENT_CHARACTER_SET}}

[mysqldump]
# 设置mysqldump字符集
default-character-set={{CLIENT_CHARACTER_SET}}
# 设置最大允许的包大小
max_allowed_packet={{MAX_ALLOWED_PACKET}}`;
}

/**
 * 获取MySQL主版本号
 * 移植自Python的get_mysql_version_major方法
 */
function getMySQLVersionMajor(version: MySQLVersion): string {
  const parts = version.split('.');
  if (parts.length >= 2) {
    return `${parts[0]}.${parts[1]}`;
  }
  return version;
}

/**
 * 获取版本特定的替换参数
 * 移植自Python的_get_version_specific_replacements方法
 */
function getVersionSpecificReplacements(
  version: MySQLVersion, 
  config: Record<string, unknown>
): Record<string, string> {
  const replacements: Record<string, string> = {};
  const majorVersion = getMySQLVersionMajor(version);

  // 处理排序规则
  if ('collation_server' in config) {
    replacements['COLLATION_SERVER'] = `collation-server=${config.collation_server}`;
  } else {
    replacements['COLLATION_SERVER'] = '';
  }

  // 处理版本特定配置
  const versionSpecificLines: string[] = [];

  if (majorVersion === '8.0') {
    // MySQL 8.0特定配置
    if ('innodb_buffer_pool_instances' in config) {
      versionSpecificLines.push(`innodb_buffer_pool_instances=${config.innodb_buffer_pool_instances}`);
    }
    if ('innodb_read_io_threads' in config) {
      versionSpecificLines.push(`innodb_read_io_threads=${config.innodb_read_io_threads}`);
      versionSpecificLines.push(`innodb_write_io_threads=${config.innodb_write_io_threads}`);
    }
    if ('innodb_thread_concurrency' in config) {
      versionSpecificLines.push(`innodb_thread_concurrency=${config.innodb_thread_concurrency}`);
    }
  }

  replacements['VERSION_SPECIFIC_CONFIG'] = versionSpecificLines.join('\n');

  // 处理额外配置
  replacements['ADDITIONAL_CONFIG'] = '';

  // 处理MySQL 8.0特定配置
  const mysql8Lines: string[] = [];
  if (majorVersion === '8.0') {
    if ('authentication_policy' in config) {
      mysql8Lines.push('# 设置认证策略 (MySQL 8.0.27+推荐)');
      mysql8Lines.push(`authentication_policy=${config.authentication_policy}`);
    }
    if ('binlog_expire_logs_seconds' in config) {
      mysql8Lines.push('# 启用二进制日志');
      mysql8Lines.push('log-bin=mysql-bin');
      mysql8Lines.push('server-id=1');
      mysql8Lines.push('binlog_format=ROW');
      mysql8Lines.push(`binlog_expire_logs_seconds=${config.binlog_expire_logs_seconds}`);
    }
    if ('sql_mode' in config) {
      mysql8Lines.push('# 设置SQL模式');
      mysql8Lines.push(`sql_mode=${config.sql_mode}`);
    }
  }

  replacements['MYSQL8_SPECIFIC'] = mysql8Lines.join('\n');

  return replacements;
}

/**
 * 规范化路径格式
 * 根据操作系统调整路径分隔符
 */
function normalizePath(path: string, os: SupportedOS): string {
  if (os === SupportedOS.WINDOWS) {
    return path.replace(/\//g, '\\\\');
  } else {
    return path.replace(/\\/g, '/');
  }
}

/**
 * 生成系统特定的默认配置
 * 移植自Python的generate_config方法
 */
export function generateDefaultConfig(os: SupportedOS): QuickInstallConfig {
  return createDefaultQuickInstallConfig(os);
}

/**
 * 生成一键安装配置
 * 支持自定义选项覆盖默认配置
 */
export function generateQuickInstallConfig(
  os: SupportedOS, 
  customOptions?: Partial<QuickInstallConfig>
): QuickInstallConfig {
  const defaultConfig = generateDefaultConfig(os);
  
  return {
    ...defaultConfig,
    ...customOptions
  };
}

/**
 * 生成MySQL配置文件内容
 * 移植自Python的generate_config方法核心逻辑
 */
export function generateMySQLConfig(
  config: QuickInstallConfig | AdvancedConfig
): string {
  try {
    const version = config.version;

    // 获取版本特定配置
    const versionConfig = VERSION_SPECIFIC_CONFIG[version] || VERSION_SPECIFIC_CONFIG[MySQLVersion.V8_0_36];

    // 合并基础配置和版本特定配置
    const mergedConfig = { ...BASE_CONFIG, ...versionConfig };

    // 准备替换参数
    const replacements: Record<string, string> = {
      'INSTALL_PATH': normalizePath(config.installPath, config.targetOS),
      'DATA_DIR': normalizePath(config.dataPath, config.targetOS),
      'PORT': config.port.toString(),
      'CHARACTER_SET_SERVER': config.charset,
      'DEFAULT_STORAGE_ENGINE': mergedConfig.default_storage_engine,
      'MAX_CONNECTIONS': mergedConfig.max_connections.toString(),
      'INNODB_BUFFER_POOL_SIZE': mergedConfig.innodb_buffer_pool_size,
      'INNODB_LOG_FILE_SIZE': mergedConfig.innodb_log_file_size,
      'INNODB_LOG_BUFFER_SIZE': mergedConfig.innodb_log_buffer_size,
      'INNODB_FLUSH_LOG_AT_TRX_COMMIT': mergedConfig.innodb_flush_log_at_trx_commit.toString(),
      'INNODB_FILE_PER_TABLE': mergedConfig.innodb_file_per_table.toString(),
      'PERFORMANCE_SCHEMA': mergedConfig.performance_schema,
      'TABLE_OPEN_CACHE': mergedConfig.table_open_cache.toString(),
      'THREAD_CACHE_SIZE': mergedConfig.thread_cache_size.toString(),
      'SORT_BUFFER_SIZE': mergedConfig.sort_buffer_size,
      'READ_BUFFER_SIZE': mergedConfig.read_buffer_size,
      'READ_RND_BUFFER_SIZE': mergedConfig.read_rnd_buffer_size,
      'WAIT_TIMEOUT': mergedConfig.wait_timeout.toString(),
      'INTERACTIVE_TIMEOUT': mergedConfig.interactive_timeout.toString(),
      'MAX_ALLOWED_PACKET': mergedConfig.max_allowed_packet,
      'DEFAULT_AUTHENTICATION_PLUGIN': mergedConfig.default_authentication_plugin,
      'MAX_CONNECT_ERRORS': mergedConfig.max_connect_errors.toString(),
      'CLIENT_CHARACTER_SET': 'utf8mb4'
    };

    // 处理版本特定的配置
    const versionSpecificReplacements = getVersionSpecificReplacements(version, mergedConfig);
    Object.assign(replacements, versionSpecificReplacements);

    // 获取配置模板并替换占位符
    let configContent = getConfigTemplate(config.targetOS);
    
    for (const [key, value] of Object.entries(replacements)) {
      const placeholder = `{{${key}}}`;
      configContent = configContent.replace(new RegExp(placeholder, 'g'), value);
    }

    return configContent;

  } catch (error) {
    console.error('生成MySQL配置文件失败:', error);
    throw new Error(`配置文件生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 验证配置参数
 * 检查配置的有效性和合理性
 */
export function validateConfigParameters(config: QuickInstallConfig | AdvancedConfig): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 端口验证
  if (config.port < 1024 || config.port > 65535) {
    errors.push('端口号必须在1024-65535范围内');
  }
  if (config.port === 3306) {
    warnings.push('使用默认端口3306可能存在安全风险');
  }

  // 路径验证
  if (!config.installPath || config.installPath.trim().length === 0) {
    errors.push('安装路径不能为空');
  }
  if (!config.dataPath || config.dataPath.trim().length === 0) {
    errors.push('数据目录路径不能为空');
  }

  // 密码验证
  if (!config.rootPassword || config.rootPassword.length < 6) {
    errors.push('root密码长度至少6位');
  }
  if (config.rootPassword === 'mysql123') {
    warnings.push('建议修改默认密码以提高安全性');
  }

  // 高级配置验证
  if ('maxConnections' in config) {
    const advancedConfig = config as AdvancedConfig;
    if (advancedConfig.maxConnections < 10 || advancedConfig.maxConnections > 10000) {
      errors.push('最大连接数应在10-10000范围内');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 获取配置文件名
 * 根据操作系统返回对应的配置文件名
 */
export function getConfigFileName(os: SupportedOS): string {
  return CONFIG_FILE_NAMES[os];
}

/**
 * 获取服务名称
 * 根据操作系统返回对应的MySQL服务名称
 */
export function getServiceName(os: SupportedOS): string {
  return SERVICE_NAMES[os];
}

/**
 * 生成配置文件下载内容
 * 返回可供下载的配置文件内容和元数据
 */
export function generateConfigFileForDownload(config: QuickInstallConfig | AdvancedConfig): {
  content: string;
  filename: string;
  mimeType: string;
} {
  const content = generateMySQLConfig(config);
  const filename = getConfigFileName(config.targetOS);
  const mimeType = 'text/plain';

  return {
    content,
    filename,
    mimeType
  };
}