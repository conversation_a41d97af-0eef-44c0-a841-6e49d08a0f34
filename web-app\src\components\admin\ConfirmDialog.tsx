'use client';

// MySQLAi.de - 确认对话框组件
// 用于批量操作的安全确认机制

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger' | 'info';
  loading?: boolean;
  itemCount?: number;
}

export default function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = '确认',
  cancelText = '取消',
  type = 'warning',
  loading = false,
  itemCount
}: ConfirmDialogProps) {

  // 类型样式配置
  const typeStyles = {
    warning: {
      iconColor: 'text-yellow-600',
      iconBg: 'bg-yellow-100',
      borderColor: 'border-yellow-200',
      confirmButton: 'primary'
    },
    danger: {
      iconColor: 'text-red-600',
      iconBg: 'bg-red-100',
      borderColor: 'border-red-200',
      confirmButton: 'danger'
    },
    info: {
      iconColor: 'text-blue-600',
      iconBg: 'bg-blue-100',
      borderColor: 'border-blue-200',
      confirmButton: 'primary'
    }
  };

  const currentStyle = typeStyles[type];

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && !loading) {
      onConfirm();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            onClick={onClose}
          >
            {/* 对话框 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className={cn(
                'bg-white rounded-xl shadow-2xl border-2 max-w-md w-full',
                currentStyle.borderColor
              )}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={handleKeyDown}
              tabIndex={-1}
            >
              {/* 对话框头部 */}
              <div className="flex items-center space-x-4 p-6 pb-4">
                {/* 图标 */}
                <div className={cn(
                  'flex items-center justify-center w-12 h-12 rounded-full',
                  currentStyle.iconBg
                )}>
                  <AlertTriangle className={cn('w-6 h-6', currentStyle.iconColor)} />
                </div>

                {/* 标题和关闭按钮 */}
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-mysql-text">
                    {title}
                  </h3>
                </div>

                <button
                  onClick={onClose}
                  disabled={loading}
                  aria-label="关闭对话框"
                  className="text-mysql-text-light hover:text-mysql-text transition-colors p-1"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* 对话框内容 */}
              <div className="px-6 pb-6">
                <p className="text-mysql-text-light mb-4">
                  {message}
                </p>

                {/* 项目数量提示 */}
                {itemCount && itemCount > 0 && (
                  <div className={cn(
                    'p-3 rounded-lg border mb-4',
                    currentStyle.borderColor,
                    currentStyle.iconBg
                  )}>
                    <p className="text-sm font-medium text-mysql-text">
                      将影响 <span className="font-bold">{itemCount}</span> 个项目
                    </p>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex items-center justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={onClose}
                    disabled={loading}
                  >
                    {cancelText}
                  </Button>
                  
                  <Button
                    variant={type === 'danger' ? 'outline' : 'primary'}
                    onClick={onConfirm}
                    loading={loading}
                    disabled={loading}
                    icon={loading ? undefined : <Check className="w-4 h-4" />}
                    className={type === 'danger' ? 'text-red-600 border-red-300 hover:bg-red-50' : ''}
                  >
                    {loading ? '处理中...' : confirmText}
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
