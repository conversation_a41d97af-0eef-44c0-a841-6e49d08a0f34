/**
 * 激活UI显示逻辑测试
 * 验证激活码输入框的条件渲染是否正确
 */

import { useAppStore } from '../store/appStore';

export interface UITestResult {
  testName: string;
  passed: boolean;
  description: string;
  actualResult: string;
  expectedResult: string;
}

/**
 * 测试激活码输入框的显示逻辑
 */
export function testActivationInputVisibility(): UITestResult[] {
  const results: UITestResult[] = [];
  const store = useAppStore.getState();

  // 测试1: 未激活状态 - 应该显示输入框
  store.updateActivation({ status: 'not_activated' });
  const shouldShowForNotActivated = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  
  results.push({
    testName: '未激活状态显示测试',
    passed: shouldShowForNotActivated === true,
    description: '当激活状态为未激活时，应该显示激活码输入框',
    actualResult: `shouldShowActivationButton: ${shouldShowForNotActivated}`,
    expectedResult: 'shouldShowActivationButton: true'
  });

  // 测试2: 已激活状态 - 应该隐藏输入框
  store.updateActivation({ 
    status: 'activated',
    licenseKey: 'TEST-KEY-123',
    machineId: 'test-machine'
  });
  const shouldShowForActivated = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  
  results.push({
    testName: '已激活状态隐藏测试',
    passed: shouldShowForActivated === false,
    description: '当激活状态为已激活时，应该隐藏激活码输入框',
    actualResult: `shouldShowActivationButton: ${shouldShowForActivated}`,
    expectedResult: 'shouldShowActivationButton: false'
  });

  // 测试3: 过期状态 - 应该显示输入框
  store.updateActivation({ status: 'expired' });
  const shouldShowForExpired = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  
  results.push({
    testName: '过期状态显示测试',
    passed: shouldShowForExpired === true,
    description: '当激活状态为过期时，应该显示激活码输入框',
    actualResult: `shouldShowActivationButton: ${shouldShowForExpired}`,
    expectedResult: 'shouldShowActivationButton: true'
  });

  // 测试4: 检查状态 - 应该隐藏输入框
  store.updateActivation({ status: 'checking' });
  const shouldShowForChecking = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  
  results.push({
    testName: '检查状态隐藏测试',
    passed: shouldShowForChecking === false,
    description: '当激活状态为检查中时，应该隐藏激活码输入框',
    actualResult: `shouldShowActivationButton: ${shouldShowForChecking}`,
    expectedResult: 'shouldShowActivationButton: false'
  });

  // 清理测试状态
  store.resetToDefaults();

  return results;
}

/**
 * 运行所有UI测试
 */
export function runAllUITests(): void {
  console.log('🧪 开始运行激活UI显示逻辑测试...');
  
  const results = testActivationInputVisibility();
  
  let passedCount = 0;
  let failedCount = 0;
  
  results.forEach((result, index) => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`\n测试 ${index + 1}: ${result.testName}`);
    console.log(`状态: ${status}`);
    console.log(`描述: ${result.description}`);
    console.log(`期望结果: ${result.expectedResult}`);
    console.log(`实际结果: ${result.actualResult}`);
    
    if (result.passed) {
      passedCount++;
    } else {
      failedCount++;
    }
  });
  
  console.log(`\n📊 测试总结:`);
  console.log(`✅ 通过: ${passedCount}`);
  console.log(`❌ 失败: ${failedCount}`);
  console.log(`📈 通过率: ${((passedCount / results.length) * 100).toFixed(1)}%`);
  
  if (failedCount === 0) {
    console.log('🎉 所有测试通过！激活码输入框的条件渲染逻辑正确！');
  } else {
    console.log('⚠️ 有测试失败，请检查激活码输入框的显示逻辑！');
  }
}

/**
 * 模拟用户激活流程测试
 */
export function simulateActivationFlow(): void {
  console.log('🎭 开始模拟用户激活流程...');
  
  const store = useAppStore.getState();
  
  // 步骤1: 初始状态 - 未激活
  console.log('\n步骤1: 应用启动，初始状态为未激活');
  store.updateActivation({ status: 'not_activated' });
  const step1Show = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  console.log(`激活码输入框显示: ${step1Show ? '是' : '否'} ✅`);
  
  // 步骤2: 用户输入激活码，开始验证
  console.log('\n步骤2: 用户输入激活码，开始验证');
  store.updateActivation({ status: 'checking' });
  const step2Show = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  console.log(`激活码输入框显示: ${step2Show ? '是' : '否'} ✅`);
  
  // 步骤3: 激活成功
  console.log('\n步骤3: 激活成功');
  store.updateActivation({ 
    status: 'activated',
    licenseKey: 'DEMO-KEY-123',
    machineId: 'user-machine'
  });
  const step3Show = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  console.log(`激活码输入框显示: ${step3Show ? '是' : '否'} ✅`);
  
  // 步骤4: 模拟许可证过期
  console.log('\n步骤4: 许可证过期');
  store.updateActivation({ status: 'expired' });
  const step4Show = store.activation.status === 'not_activated' || store.activation.status === 'expired';
  console.log(`激活码输入框显示: ${step4Show ? '是' : '否'} ✅`);
  
  // 清理
  store.resetToDefaults();
  
  console.log('\n🎉 激活流程模拟完成！UI显示逻辑符合预期！');
}

// 导出测试函数供外部调用
export default {
  testActivationInputVisibility,
  runAllUITests,
  simulateActivationFlow
};
