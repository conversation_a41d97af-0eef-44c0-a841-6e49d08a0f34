'use client';

// MySQLAi.de - FeatureCard功能卡片组件
// 可复用的功能展示卡片组件

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, ArrowRight, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FeatureCardProps } from '@/lib/types';

interface FeatureCardComponentProps extends Omit<FeatureCardProps, 'icon'> {
  icon: LucideIcon;
  features?: string[];
  color?: string;
  gradient?: string;
  index?: number;
  expandable?: boolean;
  onClick?: () => void;
}

export default function FeatureCard({
  title,
  description,
  icon: IconComponent,
  features = [],
  color = 'mysql-primary',
  gradient = 'from-mysql-primary to-mysql-primary-dark',
  index = 0,
  expandable = true,
  onClick,
  className,
  href,
}: FeatureCardComponentProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleClick = () => {
    if (expandable) {
      setIsExpanded(!isExpanded);
    }
    onClick?.();
  };

  const cardContent = (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.2,
        ease: "easeOut" 
      }}
      viewport={{ once: true }}
      className="group h-full"
    >
      <div
        className={cn(
          'relative bg-white rounded-2xl shadow-lg border border-mysql-border h-full',
          'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out',
          expandable && 'cursor-pointer',
          'overflow-hidden',
          isExpanded && 'ring-2 ring-mysql-primary/50',
          className
        )}
        onClick={handleClick}
      >
        {/* 渐变背景装饰 */}
        <div className={cn(
          'absolute top-0 left-0 right-0 h-2 bg-gradient-to-r',
          gradient
        )} />

        <div className="p-8 h-full flex flex-col">
          {/* 图标和标题 */}
          <div className="flex items-center mb-6">
            <div className={cn(
              'flex items-center justify-center w-16 h-16 rounded-xl',
              'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
              gradient
            )}>
              <IconComponent className="w-8 h-8 text-white" />
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300">
                {title}
              </h3>
            </div>
          </div>

          {/* 描述 */}
          <p className="text-mysql-text-light mb-6 leading-relaxed flex-grow">
            {description}
          </p>

          {/* 功能列表 */}
          {features.length > 0 && (
            <motion.div
              initial={false}
              animate={{ 
                height: isExpanded ? 'auto' : expandable ? '120px' : 'auto',
                opacity: isExpanded ? 1 : 0.8
              }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className={cn(
                expandable && !isExpanded && 'overflow-hidden',
                'mb-6'
              )}
            >
              <ul className="space-y-3">
                {features.map((item, itemIndex) => (
                  <motion.li
                    key={itemIndex}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ 
                      duration: 0.3, 
                      delay: isExpanded ? itemIndex * 0.1 : 0 
                    }}
                    className="flex items-center text-mysql-text"
                  >
                    <CheckCircle className="w-5 h-5 text-mysql-success mr-3 flex-shrink-0" />
                    <span className="text-sm">{item}</span>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          )}

          {/* 展开/收起按钮或链接 */}
          {(expandable || href) && (
            <motion.div
              className="mt-auto pt-6 border-t border-mysql-border"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between text-mysql-primary group-hover:text-mysql-primary-dark transition-colors duration-300">
                <span className="text-sm font-medium">
                  {href ? '了解更多' : (isExpanded ? '收起详情' : '查看详情')}
                </span>
                <motion.div
                  animate={{ 
                    rotate: expandable && isExpanded ? 90 : 0 
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
              </div>
            </motion.div>
          )}

          {/* 悬停时的光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
        </div>
      </div>
    </motion.div>
  );

  // 如果有链接，包装在Link中
  if (href) {
    return (
      <a href={href} className="block h-full">
        {cardContent}
      </a>
    );
  }

  return cardContent;
}

// 简化版功能卡片
interface SimpleFeatureCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  className?: string;
  color?: string;
  onClick?: () => void;
}

export function SimpleFeatureCard({
  title,
  description,
  icon: IconComponent,
  className,
  color = 'mysql-primary',
  onClick
}: SimpleFeatureCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -4 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        'bg-white rounded-xl p-6 shadow-md border border-mysql-border',
        'hover:shadow-lg transition-all duration-300 ease-out cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        <div className={cn(
          'flex items-center justify-center w-12 h-12 rounded-lg',
          `bg-${color} text-white flex-shrink-0`
        )}>
          <IconComponent className="w-6 h-6" />
        </div>
        <div className="flex-1">
          <h4 className="text-lg font-semibold text-mysql-text mb-2">
            {title}
          </h4>
          <p className="text-mysql-text-light text-sm leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </motion.div>
  );
}

// 紧凑版功能卡片
interface CompactFeatureCardProps {
  title: string;
  icon: LucideIcon;
  className?: string;
  color?: string;
  onClick?: () => void;
}

export function CompactFeatureCard({
  title,
  icon: IconComponent,
  className,
  color = 'mysql-primary',
  onClick
}: CompactFeatureCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={cn(
        'bg-white rounded-lg p-4 shadow-sm border border-mysql-border',
        'hover:shadow-md transition-all duration-200 ease-out cursor-pointer',
        'flex items-center space-x-3',
        className
      )}
      onClick={onClick}
    >
      <div className={cn(
        'flex items-center justify-center w-10 h-10 rounded-lg',
        `bg-${color} text-white flex-shrink-0`
      )}>
        <IconComponent className="w-5 h-5" />
      </div>
      <span className="text-mysql-text font-medium">
        {title}
      </span>
    </motion.div>
  );
}
