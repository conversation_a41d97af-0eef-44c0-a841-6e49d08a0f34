'use client';

// MySQLAi.de - 分类表单组件
// 用于创建和编辑知识库分类

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Save, X, FolderOpen } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import type { Database } from '@/lib/database.types';

type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];

interface CategoryFormData {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  order_index: number;
}

interface CategoryFormProps {
  category?: KnowledgeCategory | null;
  onSave: (data: CategoryFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

// 预设图标选项
const ICON_OPTIONS = [
  { value: 'database', label: '数据库', icon: '🗄️' },
  { value: 'table', label: '表结构', icon: '📊' },
  { value: 'query', label: '查询', icon: '🔍' },
  { value: 'function', label: '函数', icon: '⚙️' },
  { value: 'optimization', label: '优化', icon: '⚡' },
  { value: 'security', label: '安全', icon: '🔒' },
  { value: 'backup', label: '备份', icon: '💾' },
  { value: 'monitor', label: '监控', icon: '📈' },
  { value: 'tutorial', label: '教程', icon: '📚' },
  { value: 'tips', label: '技巧', icon: '💡' }
] as const;

// 预设颜色选项
const COLOR_OPTIONS = [
  { value: '#3B82F6', label: '蓝色', bg: 'bg-blue-500' },
  { value: '#10B981', label: '绿色', bg: 'bg-green-500' },
  { value: '#F59E0B', label: '橙色', bg: 'bg-yellow-500' },
  { value: '#EF4444', label: '红色', bg: 'bg-red-500' },
  { value: '#8B5CF6', label: '紫色', bg: 'bg-purple-500' },
  { value: '#06B6D4', label: '青色', bg: 'bg-cyan-500' },
  { value: '#EC4899', label: '粉色', bg: 'bg-pink-500' },
  { value: '#6B7280', label: '灰色', bg: 'bg-gray-500' }
] as const;

export default function CategoryForm({ category, onSave, onCancel, loading = false }: CategoryFormProps) {
  const [selectedIcon, setSelectedIcon] = useState(category?.icon || 'database');
  const [selectedColor, setSelectedColor] = useState(category?.color || '#3B82F6');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<CategoryFormData>({
    defaultValues: {
      id: category?.id || '',
      name: category?.name || '',
      description: category?.description || '',
      icon: category?.icon || 'database',
      color: category?.color || '#3B82F6',
      order_index: category?.order_index || 0
    }
  });

  // 处理表单提交
  const onSubmit = async (data: CategoryFormData) => {
    try {
      await onSave({
        ...data,
        icon: selectedIcon,
        color: selectedColor
      });
    } catch (error) {
      console.error('保存分类失败:', error);
    }
  };

  // 处理图标选择
  const handleIconSelect = (icon: string) => {
    setSelectedIcon(icon);
    setValue('icon', icon, { shouldDirty: true });
  };

  // 处理颜色选择
  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setValue('color', color, { shouldDirty: true });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-mysql-border max-w-6xl w-full mx-auto">
      {/* 表单头部 */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-mysql-border">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-mysql-primary-light rounded-lg">
            <FolderOpen className="w-5 h-5 text-mysql-primary" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-mysql-text">
              {category ? '编辑分类' : '创建新分类'}
            </h2>
            <p className="text-sm text-mysql-text-light">
              {category ? '修改现有分类信息' : '添加新的知识库分类'}
            </p>
          </div>
        </div>

        {/* 预览 */}
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <p className="text-sm text-mysql-text-light">预览效果</p>
            <div className="flex items-center space-x-2 mt-1">
              <div
                className="w-6 h-6 rounded flex items-center justify-center text-white text-sm"
                style={{ backgroundColor: selectedColor }}
              >
                {ICON_OPTIONS.find(opt => opt.value === selectedIcon)?.icon || '📁'}
              </div>
              <span className="text-sm font-medium text-mysql-text">
                {watch('name') || '分类名称'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 表单内容 */}
      <form onSubmit={handleSubmit(onSubmit)} className="px-6 py-4 space-y-4">
        {/* 基本信息区域 */}
        <div className="bg-gray-50 rounded-xl p-4 space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-mysql-primary rounded-full"></div>
            <h3 className="text-base font-semibold text-mysql-text">基本信息</h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 分类ID */}
            <div>
              <label htmlFor="id" className="block text-sm font-medium text-mysql-text mb-1">
                分类ID *
              </label>
              <input
                id="id"
                type="text"
                {...register('id', {
                  required: '分类ID不能为空',
                  pattern: {
                    value: /^[a-zA-Z0-9_-]+$/,
                    message: '分类ID只能包含字母、数字、下划线和连字符'
                  }
                })}
                placeholder="例如: mysql-basics"
                className={cn(
                  'w-full px-3 py-2 text-sm',
                  'bg-white border-2 border-gray-200 rounded-lg',
                  'focus:outline-none focus:border-mysql-primary focus:ring-2 focus:ring-mysql-primary/10',
                  'placeholder-gray-400 text-mysql-text',
                  'transition-all duration-200 shadow-sm',
                  'hover:border-gray-300',
                  errors.id && 'border-red-300 focus:border-red-500 focus:ring-red-500/10'
                )}
                disabled={loading || !!category}
              />
              {errors.id && (
                <p className="mt-1 text-xs text-red-600">{errors.id.message}</p>
              )}
            </div>

            {/* 分类名称 */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-mysql-text mb-1">
                分类名称 *
              </label>
              <input
                id="name"
                type="text"
                {...register('name', { required: '分类名称不能为空' })}
                placeholder="请输入分类名称"
                className={cn(
                  'w-full px-3 py-2 text-sm',
                  'bg-white border-2 border-gray-200 rounded-lg',
                  'focus:outline-none focus:border-mysql-primary focus:ring-2 focus:ring-mysql-primary/10',
                  'placeholder-gray-400 text-mysql-text',
                  'transition-all duration-200 shadow-sm',
                  'hover:border-gray-300',
                  errors.name && 'border-red-300 focus:border-red-500 focus:ring-red-500/10'
                )}
                disabled={loading}
              />
              {errors.name && (
                <p className="mt-1 text-xs text-red-600">{errors.name.message}</p>
              )}
            </div>
          </div>

          {/* 分类描述 */}
          <div className="lg:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-mysql-text mb-1">
              分类描述
            </label>
            <textarea
              id="description"
              {...register('description')}
              placeholder="请输入分类的详细描述..."
              rows={2}
              className={cn(
                'w-full px-3 py-2 text-sm',
                'bg-white border-2 border-gray-200 rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-2 focus:ring-mysql-primary/10',
                'placeholder-gray-400 text-mysql-text',
                'transition-all duration-200 resize-none shadow-sm',
                'hover:border-gray-300'
              )}
              disabled={loading}
            />
          </div>
        </div>

        {/* 图标选择区域 */}
        <div className="bg-gray-50 rounded-xl p-4 space-y-3">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-mysql-primary rounded-full"></div>
            <h3 className="text-base font-semibold text-mysql-text">选择图标</h3>
            <div className="text-xs text-gray-500">选择一个代表性的图标</div>
          </div>

          <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 xl:grid-cols-15 gap-3">
            {ICON_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleIconSelect(option.value)}
                className={cn(
                  'flex flex-col items-center p-2 rounded-lg border-2 transition-all duration-200 min-h-[60px] shadow-sm',
                  'hover:shadow-md hover:scale-105 transform',
                  selectedIcon === option.value
                    ? 'border-mysql-primary bg-mysql-primary text-white shadow-lg scale-105'
                    : 'border-gray-200 bg-white hover:border-mysql-primary/50 hover:bg-mysql-primary/5'
                )}
                disabled={loading}
              >
                <span className="text-lg mb-1">{option.icon}</span>
                <span className={cn(
                  "text-xs text-center leading-tight font-medium",
                  selectedIcon === option.value ? "text-white" : "text-gray-600"
                )}>
                  {option.label}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* 颜色选择区域 */}
        <div className="bg-gray-50 rounded-xl p-4 space-y-3">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-mysql-primary rounded-full"></div>
            <h3 className="text-base font-semibold text-mysql-text">选择颜色</h3>
            <div className="text-xs text-gray-500">选择分类的主题颜色</div>
          </div>

          <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3">
            {COLOR_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleColorSelect(option.value)}
                className={cn(
                  'flex flex-col items-center p-2 rounded-lg border-2 transition-all duration-200 min-h-[55px] shadow-sm',
                  'hover:shadow-md hover:scale-105 transform',
                  selectedColor === option.value
                    ? 'border-mysql-primary bg-mysql-primary-light shadow-lg scale-105'
                    : 'border-gray-200 bg-white hover:border-mysql-primary/50'
                )}
                disabled={loading}
              >
                <div className={cn('w-6 h-6 rounded-full mb-1 shadow-sm', option.bg)}></div>
                <span className="text-xs text-gray-600 font-medium text-center">{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 排序索引区域 */}
        <div className="bg-gray-50 rounded-xl p-4 space-y-3">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-mysql-primary rounded-full"></div>
            <h3 className="text-base font-semibold text-mysql-text">排序设置</h3>
            <div className="text-xs text-gray-500">设置分类的显示顺序</div>
          </div>

          <div className="max-w-xs">
            <label htmlFor="order_index" className="block text-sm font-medium text-mysql-text mb-1">
              排序索引
            </label>
            <input
              id="order_index"
              type="number"
              {...register('order_index', { valueAsNumber: true })}
              placeholder="0"
              min="0"
              className={cn(
                'w-full px-3 py-2 text-sm',
                'bg-white border-2 border-gray-200 rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-2 focus:ring-mysql-primary/10',
                'placeholder-gray-400 text-mysql-text',
                'transition-all duration-200 shadow-sm',
                'hover:border-gray-300'
              )}
              disabled={loading}
            />
            <p className="mt-1 text-xs text-gray-500">
              数字越小排序越靠前，建议使用10的倍数便于后续调整
            </p>
          </div>
        </div>

        {/* 表单操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t border-mysql-border">
          <div className="text-xs text-mysql-text-light">
            {isDirty && '* 表单有未保存的更改'}
          </div>

          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              icon={<X className="w-4 h-4" />}
              className="px-4 py-2 text-sm"
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
              icon={<Save className="w-4 h-4" />}
              className="px-6 py-2 text-sm"
            >
              {loading ? '保存中...' : (category ? '更新分类' : '创建分类')}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
