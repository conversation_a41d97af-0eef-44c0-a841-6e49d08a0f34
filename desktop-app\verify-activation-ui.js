/**
 * 验证激活UI修改的简单脚本
 * 检查ConfigPanel.tsx中的条件渲染逻辑
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function verifyActivationUIChanges() {
  console.log('🔍 验证激活UI修改...\n');
  
  const configPanelPath = path.join(__dirname, 'src', 'components', 'panels', 'ConfigPanel.tsx');
  
  if (!fs.existsSync(configPanelPath)) {
    console.log('❌ ConfigPanel.tsx 文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(configPanelPath, 'utf8');
  
  // 检查点1: 激活码输入区域是否有条件渲染
  const hasConditionalInput = content.includes('shouldShowActivationButton && (') && 
                              content.includes('激活码输入 - 条件渲染');
  
  // 检查点2: shouldShowActivationButton 逻辑是否正确
  const hasCorrectLogic = content.includes("activation.status === 'not_activated' || activation.status === 'expired'");
  
  // 检查点3: 激活码输入框是否在条件渲染内
  const inputInCondition = content.includes('shouldShowActivationButton && (') &&
                          content.includes('placeholder="请输入激活码"');
  
  // 检查点4: 激活按钮是否在条件渲染内
  const buttonInCondition = content.includes('激活许可证') &&
                           content.includes('shouldShowActivationButton && (');
  
  console.log('📋 验证结果:');
  console.log(`✅ 激活码输入区域条件渲染: ${hasConditionalInput ? '是' : '否'}`);
  console.log(`✅ shouldShowActivationButton 逻辑正确: ${hasCorrectLogic ? '是' : '否'}`);
  console.log(`✅ 激活码输入框在条件内: ${inputInCondition ? '是' : '否'}`);
  console.log(`✅ 激活按钮在条件内: ${buttonInCondition ? '是' : '否'}`);
  
  const allPassed = hasConditionalInput && hasCorrectLogic && inputInCondition && buttonInCondition;
  
  console.log(`\n📊 总体结果: ${allPassed ? '✅ 所有检查通过' : '❌ 有检查失败'}`);
  
  if (allPassed) {
    console.log('\n🎉 激活UI修改验证成功！');
    console.log('📝 修改摘要:');
    console.log('   - 激活码输入框现在只在未激活或过期时显示');
    console.log('   - 激活成功后，激活码输入框完全隐藏');
    console.log('   - 条件渲染逻辑: shouldShowActivationButton');
    console.log('   - 显示条件: not_activated || expired');
  } else {
    console.log('\n⚠️ 激活UI修改可能有问题，请检查代码！');
  }
  
  return allPassed;
}

// 运行验证
verifyActivationUIChanges();
