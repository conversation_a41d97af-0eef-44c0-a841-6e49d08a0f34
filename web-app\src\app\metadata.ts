// MySQLAi.de - SEO元数据配置
// 专业的SEO优化配置，包含Open Graph、Twitter Cards等

import { Metadata } from 'next';
import { SITE_CONFIG } from '@/lib/constants';

// 基础SEO配置
export const baseMetadata: Metadata = {
  title: {
    default: SITE_CONFIG.title,
    template: `%s - ${SITE_CONFIG.name}`,
  },
  description: SITE_CONFIG.description,
  keywords: SITE_CONFIG.keywords,
  authors: [{ name: SITE_CONFIG.author }],
  creator: SITE_CONFIG.author,
  publisher: SITE_CONFIG.name,
  
  // 基础元数据
  metadataBase: new URL(SITE_CONFIG.url),
  alternates: {
    canonical: '/',
    languages: {
      'zh-CN': '/zh-CN',
      'en-US': '/en-US',
    },
  },
  
  // Open Graph配置
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: SITE_CONFIG.url,
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    siteName: SITE_CONFIG.name,
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: `${SITE_CONFIG.name} - ${SITE_CONFIG.title}`,
      },
      {
        url: '/og-image-square.png',
        width: 1200,
        height: 1200,
        alt: `${SITE_CONFIG.name} Logo`,
      },
    ],
  },
  
  // Twitter Cards配置
  twitter: {
    card: 'summary_large_image',
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    creator: '@mysqlai',
    site: '@mysqlai',
    images: ['/twitter-image.png'],
  },
  
  // 应用程序配置
  applicationName: SITE_CONFIG.name,
  appleWebApp: {
    capable: true,
    title: SITE_CONFIG.name,
    statusBarStyle: 'default',
  },
  
  // 格式检测
  formatDetection: {
    telephone: false,
    date: false,
    address: false,
    email: false,
    url: false,
  },
  
  // 图标配置
  icons: {
    icon: '/favicon.ico',
  },
  
  // 清单文件
  manifest: '/site.webmanifest',
  
  // 其他元数据
  other: {
    'msapplication-TileColor': '#00758F',
    'msapplication-config': '/browserconfig.xml',
    'theme-color': '#00758F',
  },
};

// 首页专用元数据
export const homeMetadata: Metadata = {
  ...baseMetadata,
  title: `${SITE_CONFIG.title} - 专业的MySQL智能分析平台`,
  description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。7×24小时专业技术支持。',
  keywords: [
    ...SITE_CONFIG.keywords,
    'MySQL优化',
    '数据库性能',
    '智能分析',
    'AI驱动',
    '项目管理',
    '报告展示',
    '技术支持',
    '企业级',
    '专业服务',
  ],
  openGraph: {
    ...baseMetadata.openGraph,
    title: `${SITE_CONFIG.title} - 专业的MySQL智能分析平台`,
    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',
    url: SITE_CONFIG.url,
  },
  twitter: {
    ...baseMetadata.twitter,
    title: `${SITE_CONFIG.title} - 专业的MySQL智能分析平台`,
    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',
  },
};

// 结构化数据配置
export const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: SITE_CONFIG.name,
  alternateName: SITE_CONFIG.title,
  url: SITE_CONFIG.url,
  logo: `${SITE_CONFIG.url}/logo.png`,
  description: SITE_CONFIG.description,
  foundingDate: '2020',
  founder: {
    '@type': 'Person',
    name: 'MySQLAi Team',
  },
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+86-************',
    contactType: 'customer service',
    availableLanguage: ['Chinese', 'English'],
    areaServed: 'CN',
    hoursAvailable: {
      '@type': 'OpeningHoursSpecification',
      dayOfWeek: [
        'Monday',
        'Tuesday', 
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
      ],
      opens: '00:00',
      closes: '23:59',
    },
  },
  address: {
    '@type': 'PostalAddress',
    addressLocality: '北京市',
    addressRegion: '朝阳区',
    addressCountry: 'CN',
    streetAddress: '科技园区',
  },
  sameAs: [
    'https://github.com/mysqlai',
    'https://twitter.com/mysqlai',
    'https://linkedin.com/company/mysqlai',
  ],
  offers: {
    '@type': 'Offer',
    category: 'Database Services',
    description: 'MySQL数据库优化和管理服务',
    areaServed: 'CN',
  },
  knowsAbout: [
    'MySQL',
    'Database Optimization',
    'Performance Tuning',
    'Project Management',
    'AI Analysis',
    'Technical Support',
  ],
  serviceType: [
    'MySQL知识库',
    '项目管理',
    '报告展示',
    'AI智能分析',
    '技术支持',
  ],
};

// 网站配置
export const siteConfig = {
  name: SITE_CONFIG.name,
  title: SITE_CONFIG.title,
  description: SITE_CONFIG.description,
  url: SITE_CONFIG.url,
  ogImage: `${SITE_CONFIG.url}/og-image.png`,
  links: {
    twitter: 'https://twitter.com/mysqlai',
    github: 'https://github.com/mysqlai',
    linkedin: 'https://linkedin.com/company/mysqlai',
  },
  creator: SITE_CONFIG.author,
};

// 生成页面特定的元数据
export function generatePageMetadata(
  title: string,
  description: string,
  path: string = '',
  image?: string
): Metadata {
  const url = `${SITE_CONFIG.url}${path}`;
  const ogImage = image || '/og-image.png';

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      title,
      description,
      images: [ogImage],
    },
    alternates: {
      canonical: url,
    },
  };
}

// 生成JSON-LD结构化数据
export function generateJsonLd(data: Record<string, unknown>) {
  return {
    __html: JSON.stringify(data),
  };
}
