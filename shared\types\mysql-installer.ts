/**
 * MySQL一键安装工具的TypeScript类型定义
 * 支持多系统（Windows/macOS/Linux）的一键安装和可选的高级配置
 * 
 * 这是从Web版本移植的类型定义，用于桌面应用和Web版本的类型统一
 */

// ===== 核心枚举类型 =====

/**
 * 支持的操作系统
 */
export enum SupportedOS {
  WINDOWS = 'windows',
  MACOS = 'macos',
  LINUX = 'linux',
  UNKNOWN = 'unknown'
}

/**
 * 系统架构
 */
export enum SystemArchitecture {
  X64 = 'x64',
  ARM64 = 'arm64',
  X86 = 'x86',
  UNKNOWN = 'unknown'
}

/**
 * 安装模式
 */
export enum InstallationMode {
  QUICK = 'quick',        // 一键安装
  ADVANCED = 'advanced'   // 高级配置
}

/**
 * 安装状态
 */
export enum InstallationStatus {
  DETECTING = 'detecting',           // 检测系统中
  READY = 'ready',                   // 准备安装
  GENERATING_CONFIG = 'generating',  // 生成配置中
  DOWNLOADING = 'downloading',       // 下载中
  INSTALLING = 'installing',         // 安装中
  COMPLETED = 'completed',           // 安装完成
  ERROR = 'error'                    // 安装错误
}

/**
 * MySQL版本信息
 */
export enum MySQLVersion {
  V8_0_28 = '8.0.28',
  V8_0_36 = '8.0.36'
}

// ===== 核心接口定义 =====

/**
 * 系统信息接口
 */
export interface SystemInfo {
  /** 操作系统 */
  os: SupportedOS;
  /** 系统架构 */
  architecture: SystemArchitecture;
  /** 操作系统版本 */
  osVersion?: string;
  /** 浏览器信息 */
  browser?: string;
  /** 是否支持MySQL安装 */
  isSupported: boolean;
  /** 系统要求检查结果 */
  requirements: SystemRequirement[];
}

/**
 * 系统要求检查
 */
export interface SystemRequirement {
  /** 要求名称 */
  name: string;
  /** 是否满足要求 */
  satisfied: boolean;
  /** 描述信息 */
  description: string;
  /** 建议操作 */
  suggestion?: string;
}

/**
 * 一键安装配置接口
 */
export interface QuickInstallConfig {
  /** MySQL版本 */
  version: MySQLVersion;
  /** 目标操作系统 */
  targetOS: SupportedOS;
  /** 安装路径 */
  installPath: string;
  /** 数据目录路径 */
  dataPath: string;
  /** 端口号 */
  port: number;
  /** root密码 */
  rootPassword: string;
  /** 字符集 */
  charset: string;
  /** 排序规则 */
  collation: string;
  /** 是否启用二进制日志 */
  enableBinLog: boolean;
  /** 是否创建Windows服务 */
  createService: boolean;
}

/**
 * 默认安装模板接口
 */
export interface DefaultTemplate {
  /** 操作系统 */
  os: SupportedOS;
  /** 默认安装路径 */
  defaultInstallPath: string;
  /** 默认数据路径 */
  defaultDataPath: string;
  /** 默认端口 */
  defaultPort: number;
  /** 默认字符集 */
  defaultCharset: string;
  /** 默认排序规则 */
  defaultCollation: string;
  /** 配置文件名 */
  configFileName: string;
  /** 服务名称 */
  serviceName?: string;
}

/**
 * 下载源信息
 */
export interface DownloadSource {
  /** 源名称 */
  name: string;
  /** 下载URL */
  url: string;
  /** 地区 */
  region: string;
  /** 优先级 */
  priority: number;
  /** 下载速度（KB/s） */
  speed?: number;
  /** 是否可用 */
  available: boolean;
}

/**
 * MySQL安装包信息
 */
export interface MySQLPackage {
  /** 版本 */
  version: MySQLVersion;
  /** 操作系统 */
  os: SupportedOS;
  /** 系统架构 */
  architecture: SystemArchitecture;
  /** 文件名 */
  filename: string;
  /** 文件大小（字节） */
  fileSize: number;
  /** 下载源列表 */
  downloadSources: DownloadSource[];
  /** 校验和 */
  checksum?: string;
  /** 发布日期 */
  releaseDate: string;
}

/**
 * 安装结果接口
 */
export interface InstallationResult {
  /** 安装状态 */
  status: InstallationStatus;
  /** 安装配置 */
  config: QuickInstallConfig;
  /** 生成的配置文件内容 */
  configFileContent?: string;
  /** 安装指导步骤 */
  installationSteps: InstallationStep[];
  /** 下载链接 */
  downloadUrl?: string;
  /** 错误信息 */
  error?: string;
  /** 安装时间 */
  timestamp: string;
}

/**
 * 安装步骤
 */
export interface InstallationStep {
  /** 步骤ID */
  id: string;
  /** 步骤标题 */
  title: string;
  /** 步骤描述 */
  description: string;
  /** 执行命令 */
  command?: string;
  /** 是否必需 */
  required: boolean;
  /** 是否已完成 */
  completed: boolean;
  /** 预计耗时（分钟） */
  estimatedTime?: number;
}

/**
 * 高级配置选项
 */
export interface AdvancedConfig extends QuickInstallConfig {
  /** InnoDB缓冲池大小 */
  innodbBufferPoolSize: string;
  /** 最大连接数 */
  maxConnections: number;
  /** 查询缓存大小 */
  queryCacheSize: string;
  /** 慢查询日志 */
  slowQueryLog: boolean;
  /** 慢查询时间阈值 */
  longQueryTime: number;
  /** 自定义配置项 */
  customConfig: Record<string, string>;
}

// ===== 桌面应用特有类型 =====

/**
 * 激活状态接口（桌面应用专用）
 */
export interface ActivationStatus {
  /** 是否已激活 */
  isActivated: boolean;
  /** 许可证密钥 */
  licenseKey?: string;
  /** 过期时间 */
  expiresAt?: string;
  /** 机器ID */
  machineId: string;
  /** 试用模式 */
  isTrialMode?: boolean;
  /** 剩余试用次数 */
  remainingTrials?: number;
}

/**
 * 下载进度接口（桌面应用专用）
 */
export interface DownloadProgress {
  /** 总字节数 */
  totalBytes: number;
  /** 已下载字节数 */
  downloadedBytes: number;
  /** 下载百分比 */
  percentage: number;
  /** 下载速度（字节/秒） */
  speedBps: number;
  /** 剩余时间（秒） */
  remainingTime?: number;
  /** 当前下载源 */
  currentSource?: string;
}

/**
 * 安装进度接口（桌面应用专用）
 */
export interface InstallationProgress {
  /** 当前步骤 */
  currentStep: string;
  /** 步骤索引 */
  stepIndex: number;
  /** 总步骤数 */
  totalSteps: number;
  /** 当前步骤进度百分比 */
  stepProgress: number;
  /** 整体进度百分比 */
  overallProgress: number;
  /** 状态消息 */
  message: string;
  /** 是否可以取消 */
  cancellable: boolean;
}

/**
 * 错误详情接口（桌面应用专用）
 */
export interface ErrorDetails {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细描述 */
  details?: string;
  /** 建议解决方案 */
  suggestions?: string[];
  /** 错误发生时间 */
  timestamp: string;
  /** 错误堆栈（调试用） */
  stack?: string;
}