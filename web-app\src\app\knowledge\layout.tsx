'use client';

// MySQLAi.de - 知识库共享布局
// 实现左右分割布局：左侧导航，右侧内容区域

import React from 'react';
import { NavigationProvider } from '@/contexts/NavigationContext';
import KnowledgeSidebarWrapper from '@/components/knowledge/KnowledgeSidebarWrapper';

interface KnowledgeLayoutProps {
  children: React.ReactNode;
}

export default function KnowledgeLayout({ children }: KnowledgeLayoutProps) {
  return (
    <NavigationProvider>
      <div className="flex h-screen bg-white">
        {/* 左侧导航区域 - 固定不滚动 */}
        <aside className="hidden lg:flex lg:flex-shrink-0">
          <div className="flex flex-col w-80 border-r border-mysql-border bg-white h-full">
            <KnowledgeSidebarWrapper />
          </div>
        </aside>

        {/* 右侧内容区域 - 可滚动 */}
        <main className="flex-1 flex flex-col min-w-0 h-full">
          <div className="flex-1 relative focus:outline-none overflow-y-auto">
            {children}
          </div>
        </main>

        {/* 移动端侧边栏遮罩 */}
        <div className="lg:hidden">
          {/* 移动端导航将在KnowledgeSidebar组件中实现 */}
        </div>
      </div>
    </NavigationProvider>
  );
}
