// MySQLAi.de - MySQL知识库首页
// 展示知识库概览、分类导航、搜索功能和热门推荐

import React from 'react';
import { PAGE_METADATA } from '@/lib/constants';
import KnowledgePageClient from '@/components/knowledge/KnowledgePageClient';

// 生成页面元数据
export async function generateMetadata() {
  const metadata = PAGE_METADATA.knowledge;
  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords?.join(', '),
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
    },
  };
}

export default function KnowledgePage() {
  return <KnowledgePageClient />;
}
