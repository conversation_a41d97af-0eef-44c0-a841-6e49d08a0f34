'use client';

// MySQLAi.de - Features功能展示组件
// 展示三个核心功能模块：MySQL知识库、项目管理、报告展示

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Database, FolderOpen, BarChart3, ArrowRight, CheckCircle, GitBranch, Download } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FeaturesProps } from '@/lib/types';

interface FeaturesComponentProps {
  className?: string;
}

// 功能数据配置
const FEATURES_DATA = [
  {
    id: 'mysql-knowledge',
    title: 'MySQL知识库',
    description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',
    icon: Database,
    features: [
      '数据库性能优化',
      '查询语句调优',
      '索引设计最佳实践',
      '架构设计指南',
    ],
    color: 'mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
  },
  {
    id: 'project-management',
    title: '项目管理',
    description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',
    icon: FolderOpen,
    features: [
      '任务分配与跟踪',
      '项目进度管理',
      '团队协作工具',
      '时间管理优化',
    ],
    color: 'mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
  },
  {
    id: 'report-display',
    title: '报告展示',
    description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',
    icon: BarChart3,
    features: [
      '多媒体报告支持',
      '数据可视化图表',
      '实时数据展示',
      '自定义报告模板',
    ],
    color: 'mysql-success',
    gradient: 'from-mysql-success to-green-600',
  },
  {
    id: 'er-diagram-tool',
    title: 'ER图生成工具',
    description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式。',
    icon: GitBranch,
    features: [
      '自动表关系识别',
      '可视化图形编辑',
      '多格式导出支持',
      '团队协作共享',
    ],
    color: 'purple-600',
    gradient: 'from-purple-500 to-purple-700',
  },
  {
    id: 'mysql-installer-tool',
    title: 'MySQL安装工具',
    description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置。',
    icon: Download,
    features: [
      '一键安装配置',
      '多版本管理',
      '环境自动检测',
      '安全配置优化',
    ],
    color: 'orange-600',
    gradient: 'from-orange-500 to-orange-700',
  },
];

export default function Features({ className }: FeaturesComponentProps) {
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  const toggleCard = (cardId: string) => {
    setExpandedCard(expandedCard === cardId ? null : cardId);
  };

  return (
    <section
      className={cn(
        'py-20 px-4 bg-gradient-to-b from-white to-mysql-primary-light/30',
        className
      )}
    >
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-mysql-text mb-6">
            核心功能特性
          </h2>
          <p className="text-lg sm:text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed">
            专业的MySQL解决方案，涵盖知识分享、项目管理和报告展示的完整生态系统
          </p>
        </motion.div>

        {/* 功能卡片网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
          {FEATURES_DATA.map((feature, index) => {
            const IconComponent = feature.icon;
            const isExpanded = expandedCard === feature.id;

            return (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.2,
                  ease: "easeOut" 
                }}
                viewport={{ once: true }}
                className="group"
              >
                <div
                  className={cn(
                    'relative bg-white rounded-2xl shadow-lg border border-mysql-border',
                    'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out',
                    'cursor-pointer overflow-hidden h-[480px] flex flex-col',
                    isExpanded && 'ring-2 ring-mysql-primary/50'
                  )}
                  onClick={() => toggleCard(feature.id)}
                >
                  {/* 渐变背景装饰 */}
                  <div className={cn(
                    'absolute top-0 left-0 right-0 h-2 bg-gradient-to-r',
                    feature.gradient
                  )} />

                  <div className="p-8 flex flex-col h-full">
                    {/* 图标和标题 */}
                    <div className="flex items-center mb-6">
                      <div className={cn(
                        'flex items-center justify-center w-16 h-16 rounded-xl',
                        'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
                        feature.gradient
                      )}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-xl font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300">
                          {feature.title}
                        </h3>
                      </div>
                    </div>

                    {/* 描述 */}
                    <p className="text-mysql-text-light mb-6 leading-relaxed text-sm">
                      {feature.description}
                    </p>

                    {/* 功能列表 */}
                    <div className="flex-1 flex flex-col">
                      <motion.div
                        initial={false}
                        animate={{
                          height: isExpanded ? 'auto' : '120px',
                          opacity: isExpanded ? 1 : 0.8
                        }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden flex-1"
                      >
                        <ul className="space-y-2">
                          {feature.features.map((item, itemIndex) => (
                            <li key={itemIndex} className="flex items-center text-mysql-text">
                              <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{
                                  duration: 0.3,
                                  delay: isExpanded ? itemIndex * 0.1 : 0
                                }}
                                className="flex items-center w-full"
                              >
                                <CheckCircle className="w-4 h-4 text-mysql-success mr-3 flex-shrink-0" />
                                <span className="text-sm">{item}</span>
                              </motion.div>
                            </li>
                          ))}
                        </ul>
                      </motion.div>
                    </div>

                    {/* 展开/收起按钮 */}
                    <motion.div
                      className="mt-auto pt-6 border-t border-mysql-border"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center justify-between text-mysql-primary group-hover:text-mysql-primary-dark transition-colors duration-300">
                        <span className="text-sm font-medium">
                          {isExpanded ? '收起详情' : '查看详情'}
                        </span>
                        <motion.div
                          animate={{ rotate: isExpanded ? 90 : 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <ArrowRight className="w-5 h-5" />
                        </motion.div>
                      </div>
                    </motion.div>
                  </div>

                  {/* 悬停时的光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* 底部CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-mysql-text-light mb-6">
            想要了解更多功能特性？
          </p>
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className={cn(
              'inline-flex items-center px-8 py-4 bg-mysql-primary text-white',
              'text-lg font-semibold rounded-xl shadow-lg',
              'hover:bg-mysql-primary-dark hover:shadow-xl',
              'transition-all duration-300 ease-out',
              'focus:outline-none focus:ring-4 focus:ring-mysql-primary/30'
            )}
          >
            <span>探索全部功能</span>
            <ArrowRight className="ml-2 w-5 h-5" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
