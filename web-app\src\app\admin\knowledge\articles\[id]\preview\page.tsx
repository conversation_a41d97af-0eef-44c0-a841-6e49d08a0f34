'use client';

// MySQLAi.de - 管理员文章预览页面
// 专门为管理员提供的文章预览功能，包含管理员专用操作

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Edit, 
  Eye, 
  EyeOff, 
  Calendar, 
  Clock, 
  User, 
  Tag,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import { articlesApi } from '@/lib/api/knowledge';
import { KNOWLEDGE_ROUTES } from '@/lib/knowledge-routes';
import Button from '@/components/ui/Button';
import { LoadingState } from '@/components/ui/StateComponents';
import MarkdownRenderer from '@/components/ui/MarkdownRenderer';
import type { Database } from '@/lib/database.types';

type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];

export default function AdminArticlePreviewPage() {
  const router = useRouter();
  const params = useParams();
  const articleId = params.id as string;

  const [article, setArticle] = useState<KnowledgeArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取文章数据
  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await articlesApi.getById(articleId, {
          includeCodeExamples: true,
          includeRelated: true
        });

        if (response.success && response.data) {
          setArticle(response.data);
        } else {
          setError(response.error || '文章不存在');
        }
      } catch (error) {
        console.error('获取文章失败:', error);
        setError('获取文章失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    if (articleId) {
      fetchArticle();
    }
  }, [articleId]);

  // 处理编辑文章
  const handleEditArticle = () => {
    router.push(KNOWLEDGE_ROUTES.ARTICLES.EDIT(articleId));
  };

  // 处理查看公共页面
  const handleViewPublic = () => {
    if (article) {
      // 构建正确的公共访问URL
      const publicUrl = `/knowledge/${article.category_id}/${article.slug || article.id}`;
      window.open(publicUrl, '_blank');
    }
  };

  // 处理返回列表
  const handleBackToList = () => {
    router.push(KNOWLEDGE_ROUTES.ARTICLES.ROOT);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingState message="加载文章中..." />
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={handleBackToList}
            icon={<ArrowLeft className="w-5 h-5" />}
          >
            返回列表
          </Button>
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-6"
        >
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-6 h-6 text-red-600" />
            <div>
              <h3 className="text-lg font-semibold text-red-800">加载失败</h3>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={handleBackToList}
            icon={<ArrowLeft className="w-5 h-5" />}
            className="text-mysql-text-light hover:text-mysql-primary"
          >
            返回列表
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold text-mysql-text">
              文章预览
            </h1>
            <p className="text-mysql-text-light">
              管理员预览模式 - {article.title}
            </p>
          </div>
        </div>

        {/* 管理员操作 */}
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={handleViewPublic}
            icon={<ExternalLink className="w-4 h-4" />}
          >
            查看公共页面
          </Button>
          <Button
            variant="primary"
            onClick={handleEditArticle}
            icon={<Edit className="w-4 h-4" />}
          >
            编辑文章
          </Button>
        </div>
      </motion.div>

      {/* 文章状态信息 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-mysql-bg-light border border-mysql-border rounded-lg p-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              article.status === 'published' ? 'bg-green-500' : 
              article.status === 'draft' ? 'bg-yellow-500' : 'bg-gray-500'
            }`} />
            <span className="text-sm font-medium">
              {article.status === 'published' ? '已发布' : 
               article.status === 'draft' ? '草稿' : '未知状态'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-mysql-text-light">
            <Calendar className="w-4 h-4" />
            <span className="text-sm">
              创建于 {new Date(article.created_at).toLocaleDateString()}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-mysql-text-light">
            <Clock className="w-4 h-4" />
            <span className="text-sm">
              更新于 {new Date(article.updated_at).toLocaleDateString()}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-mysql-text-light">
            <Eye className="w-4 h-4" />
            <span className="text-sm">
              阅读时间 {article.reading_time || 0} 分钟
            </span>
          </div>
        </div>
      </motion.div>

      {/* 文章内容预览 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white border border-mysql-border rounded-lg overflow-hidden"
      >
        {/* 文章头部 */}
        <div className="p-6 border-b border-mysql-border">
          <div className="space-y-4">
            <h1 className="text-4xl font-bold text-mysql-text">
              {article.title}
            </h1>
            
            {article.description && (
              <p className="text-lg text-mysql-text-light">
                {article.description}
              </p>
            )}
            
            {/* 标签 */}
            {article.tags && article.tags.length > 0 && (
              <div className="flex items-center space-x-2">
                <Tag className="w-4 h-4 text-mysql-text-light" />
                <div className="flex flex-wrap gap-2">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-mysql-primary-light text-mysql-primary text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 文章正文 */}
        <div className="p-6">
          <MarkdownRenderer content={article.content || ''} />
        </div>
      </motion.div>
    </div>
  );
}
