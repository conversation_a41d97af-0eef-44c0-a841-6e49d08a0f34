'use client';

// MySQLAi.de - ContactButton联系按钮组件
// 可复用的联系按钮组件

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, ArrowRight, CheckCircle, Phone, Mail, MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ContactButtonProps {
  title: string;
  description: string;
  contact: string;
  available?: string;
  icon: LucideIcon;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  className?: string;
  onClick?: () => void;
  onSuccess?: () => void;
}

export default function ContactButton({
  title,
  description,
  contact,
  available,
  icon: IconComponent,
  color = 'mysql-primary',
  size = 'md',
  variant = 'default',
  className,
  onClick,
  onSuccess,
}: ContactButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  // 尺寸配置
  const sizeConfig = {
    sm: {
      container: 'p-6',
      icon: 'w-12 h-12',
      iconSize: 'w-6 h-6',
      title: 'text-lg',
      description: 'text-sm',
      contact: 'text-base',
      button: 'px-4 py-2 text-sm',
    },
    md: {
      container: 'p-8',
      icon: 'w-16 h-16',
      iconSize: 'w-8 h-8',
      title: 'text-xl',
      description: 'text-base',
      contact: 'text-lg',
      button: 'px-6 py-3 text-base',
    },
    lg: {
      container: 'p-10',
      icon: 'w-20 h-20',
      iconSize: 'w-10 h-10',
      title: 'text-2xl',
      description: 'text-lg',
      contact: 'text-xl',
      button: 'px-8 py-4 text-lg',
    },
  };

  const config = sizeConfig[size];

  // 变体配置
  const variantConfig = {
    default: {
      container: 'bg-white border-mysql-border shadow-lg hover:shadow-2xl',
      button: 'bg-mysql-primary text-white hover:bg-mysql-primary-dark',
    },
    outline: {
      container: 'bg-transparent border-mysql-primary border-2 hover:bg-mysql-primary/5',
      button: 'border border-mysql-primary text-mysql-primary hover:bg-mysql-primary hover:text-white',
    },
    ghost: {
      container: 'bg-mysql-primary/5 border-transparent hover:bg-mysql-primary/10',
      button: 'text-mysql-primary hover:bg-mysql-primary/10',
    },
  };

  const variantStyles = variantConfig[variant];

  const handleClick = async () => {
    setIsLoading(true);
    
    // 执行点击回调
    onClick?.();
    
    // 模拟联系处理
    setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
      onSuccess?.();
      
      // 3秒后重置状态
      setTimeout(() => {
        setIsSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      viewport={{ once: true }}
      className="group h-full"
    >
      <div
        className={cn(
          'rounded-2xl border transition-all duration-300 ease-out h-full flex flex-col',
          'hover:scale-105 cursor-pointer',
          variantStyles.container,
          config.container,
          className
        )}
        onClick={handleClick}
      >
        {/* 图标区域 */}
        <div className="flex items-center justify-center mb-6">
          <div className={cn(
            'flex items-center justify-center rounded-2xl',
            'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
            `from-${color} to-${color}-dark`,
            config.icon
          )}>
            <IconComponent className={cn('text-white', config.iconSize)} />
          </div>
        </div>

        {/* 内容区域 */}
        <div className="text-center flex-grow flex flex-col">
          <h3 className={cn(
            'font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300',
            config.title
          )}>
            {title}
          </h3>
          <p className={cn(
            'text-mysql-text-light mb-4 leading-relaxed flex-grow',
            config.description
          )}>
            {description}
          </p>
          
          {/* 联系信息 */}
          <div className="mb-6">
            <div className={cn(
              'font-semibold text-mysql-primary mb-2',
              config.contact
            )}>
              {contact}
            </div>
            {available && (
              <div className="text-sm text-mysql-text-light">
                {available}
              </div>
            )}
          </div>

          {/* 联系按钮 */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={isLoading || isSuccess}
            className={cn(
              'inline-flex items-center justify-center rounded-lg font-medium',
              'transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              variantStyles.button,
              config.button
            )}
          >
            {isSuccess ? (
              <>
                <CheckCircle className="mr-2 w-4 h-4" />
                <span>已发送</span>
              </>
            ) : isLoading ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2 w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                />
                <span>发送中...</span>
              </>
            ) : (
              <>
                <span>立即联系</span>
                <ArrowRight className="ml-2 w-4 h-4" />
              </>
            )}
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
}

// 快速联系按钮组件
interface QuickContactProps {
  type: 'phone' | 'email' | 'chat';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

export function QuickContact({
  type,
  className,
  size = 'md',
  onClick
}: QuickContactProps) {
  const contactConfig = {
    phone: {
      icon: Phone,
      title: '电话咨询',
      contact: '+86 ************',
      color: 'mysql-primary',
    },
    email: {
      icon: Mail,
      title: '邮件支持',
      contact: '<EMAIL>',
      color: 'mysql-accent',
    },
    chat: {
      icon: MessageCircle,
      title: '在线客服',
      contact: '点击开始对话',
      color: 'mysql-success',
    },
  };

  const config = contactConfig[type];

  return (
    <motion.button
      whileHover={{ scale: 1.05, y: -2 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={cn(
        'flex items-center space-x-3 p-4 bg-white rounded-lg shadow-md border border-mysql-border',
        'hover:shadow-lg transition-all duration-300 ease-out',
        className
      )}
    >
      <div className={cn(
        'flex items-center justify-center w-10 h-10 rounded-lg',
        `bg-${config.color} text-white`
      )}>
        <config.icon className="w-5 h-5" />
      </div>
      <div className="text-left">
        <div className="font-medium text-mysql-text">
          {config.title}
        </div>
        <div className="text-sm text-mysql-text-light">
          {config.contact}
        </div>
      </div>
    </motion.button>
  );
}

// 浮动联系按钮
interface FloatingContactProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  onClick?: () => void;
}

export function FloatingContact({
  className,
  position = 'bottom-right',
  onClick
}: FloatingContactProps) {
  const positionConfig = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6',
  };

  return (
    <motion.button
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={onClick}
      className={cn(
        'fixed z-50 w-14 h-14 bg-mysql-primary text-white rounded-full shadow-lg',
        'hover:bg-mysql-primary-dark transition-colors duration-300',
        'focus:outline-none focus:ring-4 focus:ring-mysql-primary/30',
        positionConfig[position],
        className
      )}
    >
      <MessageCircle className="w-6 h-6 mx-auto" />
    </motion.button>
  );
}
