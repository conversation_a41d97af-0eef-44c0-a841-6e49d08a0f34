'use client';

// MySQLAi.de - 分类列表组件
// 显示分类列表，支持拖拽排序和操作

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Edit,
  Trash2,
  GripVertical,
  FileText,
  Calendar,
  MoreVertical,
  Eye,
  CheckSquare,
  Square
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import type { Database } from '@/lib/database.types';

type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'] & {
  article_count?: number; // API返回的字段名
  articleCount?: number;  // 兼容旧的字段名
};

interface CategoryListProps {
  categories: KnowledgeCategory[];
  onEdit: (category: KnowledgeCategory) => void;
  onDelete: (categoryId: string) => void;
  onReorder?: (categories: KnowledgeCategory[]) => void;
  loading?: boolean;
  deleteLoading?: string | null;
  selectedCategories?: string[];
  onSelectCategory?: (categoryId: string) => void;
}

// 图标映射
const ICON_MAP: Record<string, string> = {
  database: '🗄️',
  table: '📊',
  query: '🔍',
  function: '⚙️',
  optimization: '⚡',
  security: '🔒',
  backup: '💾',
  monitor: '📈',
  tutorial: '📚',
  tips: '💡'
};

export default function CategoryList({
  categories,
  onEdit,
  onDelete,
  onReorder,
  loading = false,
  deleteLoading = null,
  selectedCategories = [],
  onSelectCategory
}: CategoryListProps) {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [dragOverItem, setDragOverItem] = useState<string | null>(null);

  // 处理拖拽开始
  const handleDragStart = (e: React.DragEvent, categoryId: string) => {
    setDraggedItem(categoryId);
    e.dataTransfer.effectAllowed = 'move';
  };

  // 处理拖拽结束
  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverItem(null);
  };

  // 处理拖拽悬停
  const handleDragOver = (e: React.DragEvent, categoryId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverItem(categoryId);
  };

  // 处理拖拽离开
  const handleDragLeave = () => {
    setDragOverItem(null);
  };

  // 处理拖拽放置
  const handleDrop = (e: React.DragEvent, targetCategoryId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetCategoryId || !onReorder) {
      return;
    }

    const draggedIndex = categories.findIndex(cat => cat.id === draggedItem);
    const targetIndex = categories.findIndex(cat => cat.id === targetCategoryId);

    if (draggedIndex === -1 || targetIndex === -1) {
      return;
    }

    // 重新排序
    const newCategories = [...categories];
    const [draggedCategory] = newCategories.splice(draggedIndex, 1);
    newCategories.splice(targetIndex, 0, draggedCategory);

    // 更新排序索引
    const updatedCategories = newCategories.map((cat, index) => ({
      ...cat,
      order_index: index
    }));

    onReorder(updatedCategories);
    setDraggedItem(null);
    setDragOverItem(null);
  };

  // 渲染分类卡片
  const renderCategoryCard = (category: KnowledgeCategory) => {
    const isDragging = draggedItem === category.id;
    const isDragOver = dragOverItem === category.id;
    const isSelected = selectedCategories.includes(category.id);

    return (
      <motion.div
        key={category.id}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        draggable={!loading && onReorder ? "true" : "false"}
        onDragStart={(e: any) => handleDragStart(e as React.DragEvent, category.id)}
        onDragEnd={handleDragEnd}
        onDragOver={(e: any) => handleDragOver(e as React.DragEvent, category.id)}
        onDragLeave={handleDragLeave}
        onDrop={(e: any) => handleDrop(e as React.DragEvent, category.id)}
        className={cn(
          'bg-white rounded-lg shadow-md border transition-all duration-300',
          'hover:shadow-lg hover:border-mysql-primary/30',
          isDragging && 'opacity-50 scale-95',
          isDragOver && 'border-mysql-primary border-2 bg-mysql-primary-light/20',
          isSelected && 'border-mysql-primary shadow-lg ring-2 ring-mysql-primary/20',
          loading && 'opacity-50 pointer-events-none',
          !isSelected && !isDragOver && 'border-mysql-border'
        )}
      >
        <div className="p-6">
          {/* 分类头部信息 */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3 flex-1">
              {/* 多选复选框 */}
              {onSelectCategory && (
                <button
                  type="button"
                  onClick={() => onSelectCategory(category.id)}
                  className="text-mysql-text-light hover:text-mysql-primary transition-colors"
                >
                  {isSelected ? (
                    <CheckSquare className="w-5 h-5 text-mysql-primary" />
                  ) : (
                    <Square className="w-5 h-5" />
                  )}
                </button>
              )}
              {/* 拖拽手柄 */}
              {onReorder && (
                <div className="cursor-grab active:cursor-grabbing text-mysql-text-light hover:text-mysql-primary transition-colors">
                  <GripVertical className="w-5 h-5" />
                </div>
              )}

              {/* 分类图标和信息 */}
              <div
                className={cn(
                  'w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg',
                  !category.color && 'bg-blue-500'
                )}
                style={category.color ? { backgroundColor: category.color } : undefined}
              >
                {ICON_MAP[category.icon || 'database'] || '📁'}
              </div>
              
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-mysql-text mb-1">
                  {category.name}
                </h3>
                {category.description && (
                  <p className="text-sm text-mysql-text-light line-clamp-2">
                    {category.description}
                  </p>
                )}
              </div>
            </div>

            {/* 操作菜单 */}
            <div className="flex items-center space-x-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(category)}
                icon={<Edit className="w-4 h-4" />}
                className="text-mysql-text-light hover:text-mysql-primary"
                disabled={loading}
              >
                编辑
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(category.id)}
                loading={deleteLoading === category.id}
                icon={<Trash2 className="w-4 h-4" />}
                className="text-mysql-text-light hover:text-red-600"
                disabled={loading}
              >
                删除
              </Button>
            </div>
          </div>

          {/* 分类统计信息 */}
          <div className="flex items-center justify-between text-sm text-mysql-text-light">
            <div className="flex items-center space-x-4">
              {/* 文章数量 */}
              <div className="flex items-center space-x-1">
                <FileText className="w-4 h-4" />
                <span>{category.article_count || category.articleCount || 0} 篇文章</span>
              </div>
              
              {/* 创建时间 */}
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>{category.created_at}</span>
              </div>
            </div>

            {/* 排序索引 */}
            <div className="text-xs bg-mysql-primary-light text-mysql-primary px-2 py-1 rounded">
              排序: {category.order_index}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  if (loading && categories.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-mysql-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-mysql-text-light">加载分类列表...</p>
        </div>
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-mysql-primary-light rounded-full flex items-center justify-center mx-auto mb-4">
          <FileText className="w-8 h-8 text-mysql-primary" />
        </div>
        <h3 className="text-lg font-medium text-mysql-text mb-2">
          还没有分类
        </h3>
        <p className="text-mysql-text-light">
          开始创建您的第一个知识库分类吧
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 拖拽提示 */}
      {onReorder && (
        <div className="bg-mysql-primary-light/30 border border-mysql-primary/20 rounded-lg p-3">
          <p className="text-sm text-mysql-primary flex items-center">
            <GripVertical className="w-4 h-4 mr-2" />
            拖拽分类卡片可以调整排序顺序
          </p>
        </div>
      )}

      {/* 分类列表 */}
      <AnimatePresence>
        {categories.map(category => renderCategoryCard(category))}
      </AnimatePresence>
    </div>
  );
}
