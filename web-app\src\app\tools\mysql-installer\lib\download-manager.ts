/**
 * 多系统下载管理器
 * 移植Python下载源逻辑，支持智能下载源选择和地理位置优化
 */

import { 
  SupportedOS, 
  SystemArchitecture, 
  MySQLVersion, 
  DownloadSource, 
  MySQLPackage 
} from '../types/mysql-installer';
// 导入默认配置（如果需要的话可以在这里添加）
// import { OFFICIAL_DOWNLOAD_SOURCES, CHINA_MIRROR_SOURCES } from './defaults';

/**
 * MySQL版本信息
 * 移植自Python的MYSQL_VERSIONS，支持多系统和多架构
 */
const MYSQL_PACKAGES: Record<string, MySQLPackage> = {
  // Windows x64
  [`${MySQLVersion.V8_0_36}-${SupportedOS.WINDOWS}-${SystemArchitecture.X64}`]: {
    version: MySQLVersion.V8_0_36,
    os: SupportedOS.WINDOWS,
    architecture: SystemArchitecture.X64,
    filename: 'mysql-8.0.36-winx64.zip',
    fileSize: 398458880, // 约380MB
    downloadSources: [
      {
        name: 'MySQL官方档案',
        url: 'https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-winx64.zip',
        region: 'global',
        priority: 1,
        available: true
      },
      {
        name: '阿里云镜像',
        url: 'https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.36-winx64.zip',
        region: 'china',
        priority: 2,
        available: true
      },
      {
        name: '华为云镜像',
        url: 'https://mirrors.huaweicloud.com/mysql/Downloads/MySQL-8.0/mysql-8.0.36-winx64.zip',
        region: 'china',
        priority: 3,
        available: true
      },
      {
        name: '北外镜像',
        url: 'https://mirrors.bfsu.edu.cn/mysql/downloads/MySQL-8.0/mysql-8.0.36-winx64.zip',
        region: 'china',
        priority: 4,
        available: true
      },
      {
        name: 'GWDG镜像',
        url: 'https://ftp.gwdg.de/pub/misc/mysql/Downloads/MySQL-8.0/mysql-8.0.36-winx64.zip',
        region: 'europe',
        priority: 5,
        available: true
      },
      {
        name: 'KAIST镜像',
        url: 'https://ftp.kaist.ac.kr/mysql/Downloads/MySQL-8.0/mysql-8.0.36-winx64.zip',
        region: 'asia',
        priority: 6,
        available: true
      }
    ],
    checksum: 'sha256:...',
    releaseDate: '2024-01-16'
  },

  // Windows x86
  [`${MySQLVersion.V8_0_36}-${SupportedOS.WINDOWS}-${SystemArchitecture.X86}`]: {
    version: MySQLVersion.V8_0_36,
    os: SupportedOS.WINDOWS,
    architecture: SystemArchitecture.X86,
    filename: 'mysql-8.0.36-win32.zip',
    fileSize: 378458880, // 约361MB
    downloadSources: [
      {
        name: 'MySQL官方档案',
        url: 'https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-win32.zip',
        region: 'global',
        priority: 1,
        available: true
      },
      {
        name: '阿里云镜像',
        url: 'https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.36-win32.zip',
        region: 'china',
        priority: 2,
        available: true
      }
    ],
    checksum: 'sha256:...',
    releaseDate: '2024-01-16'
  },

  // macOS x64
  [`${MySQLVersion.V8_0_36}-${SupportedOS.MACOS}-${SystemArchitecture.X64}`]: {
    version: MySQLVersion.V8_0_36,
    os: SupportedOS.MACOS,
    architecture: SystemArchitecture.X64,
    filename: 'mysql-8.0.36-macos13-x86_64.dmg',
    fileSize: 445458880, // 约425MB
    downloadSources: [
      {
        name: 'MySQL官方档案',
        url: 'https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-macos13-x86_64.dmg',
        region: 'global',
        priority: 1,
        available: true
      },
      {
        name: '阿里云镜像',
        url: 'https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.36-macos13-x86_64.dmg',
        region: 'china',
        priority: 2,
        available: true
      }
    ],
    checksum: 'sha256:...',
    releaseDate: '2024-01-16'
  },

  // macOS ARM64 (Apple Silicon)
  [`${MySQLVersion.V8_0_36}-${SupportedOS.MACOS}-${SystemArchitecture.ARM64}`]: {
    version: MySQLVersion.V8_0_36,
    os: SupportedOS.MACOS,
    architecture: SystemArchitecture.ARM64,
    filename: 'mysql-8.0.36-macos13-arm64.dmg',
    fileSize: 425458880, // 约406MB
    downloadSources: [
      {
        name: 'MySQL官方档案',
        url: 'https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-macos13-arm64.dmg',
        region: 'global',
        priority: 1,
        available: true
      },
      {
        name: '阿里云镜像',
        url: 'https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.36-macos13-arm64.dmg',
        region: 'china',
        priority: 2,
        available: true
      }
    ],
    checksum: 'sha256:...',
    releaseDate: '2024-01-16'
  },

  // Linux x64
  [`${MySQLVersion.V8_0_36}-${SupportedOS.LINUX}-${SystemArchitecture.X64}`]: {
    version: MySQLVersion.V8_0_36,
    os: SupportedOS.LINUX,
    architecture: SystemArchitecture.X64,
    filename: 'mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz',
    fileSize: 685458880, // 约654MB
    downloadSources: [
      {
        name: 'MySQL官方档案',
        url: 'https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz',
        region: 'global',
        priority: 1,
        available: true
      },
      {
        name: '阿里云镜像',
        url: 'https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz',
        region: 'china',
        priority: 2,
        available: true
      },
      {
        name: '华为云镜像',
        url: 'https://mirrors.huaweicloud.com/mysql/Downloads/MySQL-8.0/mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz',
        region: 'china',
        priority: 3,
        available: true
      }
    ],
    checksum: 'sha256:...',
    releaseDate: '2024-01-16'
  },

  // MySQL 8.0.28版本 - Windows x64
  [`${MySQLVersion.V8_0_28}-${SupportedOS.WINDOWS}-${SystemArchitecture.X64}`]: {
    version: MySQLVersion.V8_0_28,
    os: SupportedOS.WINDOWS,
    architecture: SystemArchitecture.X64,
    filename: 'mysql-8.0.28-winx64.zip',
    fileSize: 378458880, // 约361MB
    downloadSources: [
      {
        name: 'MySQL官方档案',
        url: 'https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.28-winx64.zip',
        region: 'global',
        priority: 1,
        available: true
      },
      {
        name: '阿里云镜像',
        url: 'https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.28-winx64.zip',
        region: 'china',
        priority: 2,
        available: true
      }
    ],
    checksum: 'sha256:...',
    releaseDate: '2022-01-18'
  }
};

/**
 * 地理位置检测缓存
 */
interface LocationCache {
  country?: string;
  region?: string;
  timestamp: number;
}

let locationCache: LocationCache = { timestamp: 0 };
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时

/**
 * 检测用户地理位置
 * 用于优化下载源选择
 */
async function detectUserLocation(): Promise<{ country: string; region: string }> {
  // 检查缓存
  const now = Date.now();
  if (locationCache.country && locationCache.region && 
      (now - locationCache.timestamp) < CACHE_DURATION) {
    return {
      country: locationCache.country,
      region: locationCache.region
    };
  }

  try {
    // 使用免费的IP地理位置API
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch('https://ipapi.co/json/', {
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    
    if (response.ok) {
      const data = await response.json();
      const country = data.country_code?.toLowerCase() || 'unknown';
      const region = getRegionFromCountry(country);
      
      // 更新缓存
      locationCache = {
        country,
        region,
        timestamp: now
      };
      
      return { country, region };
    }
  } catch (error) {
    console.warn('地理位置检测失败，使用默认设置:', error);
  }

  // 备用检测方法：基于时区
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const region = getRegionFromTimezone(timezone);
    return { country: 'unknown', region };
  } catch (error) {
    console.warn('时区检测失败:', error);
  }

  // 默认返回全球
  return { country: 'unknown', region: 'global' };
}

/**
 * 根据国家代码获取地区
 */
function getRegionFromCountry(countryCode: string): string {
  const chinaRegion = ['cn', 'hk', 'mo', 'tw'];
  const asiaRegion = ['jp', 'kr', 'sg', 'th', 'my', 'id', 'ph', 'vn', 'in'];
  const europeRegion = ['de', 'fr', 'gb', 'it', 'es', 'nl', 'se', 'no', 'dk', 'fi'];
  const americaRegion = ['us', 'ca', 'mx', 'br', 'ar'];

  if (chinaRegion.includes(countryCode)) {
    return 'china';
  } else if (asiaRegion.includes(countryCode)) {
    return 'asia';
  } else if (europeRegion.includes(countryCode)) {
    return 'europe';
  } else if (americaRegion.includes(countryCode)) {
    return 'america';
  }

  return 'global';
}

/**
 * 根据时区获取地区
 */
function getRegionFromTimezone(timezone: string): string {
  if (timezone.includes('Asia/Shanghai') || 
      timezone.includes('Asia/Hong_Kong') ||
      timezone.includes('Asia/Taipei')) {
    return 'china';
  } else if (timezone.includes('Asia/')) {
    return 'asia';
  } else if (timezone.includes('Europe/')) {
    return 'europe';
  } else if (timezone.includes('America/')) {
    return 'america';
  }

  return 'global';
}

/**
 * 测试下载源速度
 * 通过HEAD请求测试响应时间
 */
async function testDownloadSpeed(source: DownloadSource): Promise<number> {
  try {
    const startTime = Date.now();

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    const response = await fetch(source.url, {
      method: 'HEAD',
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    if (response.ok) {
      // 计算速度分数（响应时间越短分数越高）
      const speed = Math.max(0, 1000 - responseTime);
      return speed;
    }
    
    return 0;
  } catch (error) {
    console.warn(`测试下载源速度失败: ${source.name}`, error);
    return 0;
  }
}

/**
 * 获取MySQL安装包信息
 */
export function getMySQLPackage(
  version: MySQLVersion, 
  os: SupportedOS, 
  architecture: SystemArchitecture
): MySQLPackage | null {
  const key = `${version}-${os}-${architecture}`;
  return MYSQL_PACKAGES[key] || null;
}

/**
 * 获取最佳下载URL
 * 基于地理位置和下载源测试结果选择最优下载源
 */
export async function getOptimalDownloadUrl(
  version: MySQLVersion,
  os: SupportedOS, 
  architecture: SystemArchitecture
): Promise<{
  package: MySQLPackage;
  recommendedSource: DownloadSource;
  alternativeSources: DownloadSource[];
} | null> {
  const packageInfo = getMySQLPackage(version, os, architecture);
  
  if (!packageInfo) {
    return null;
  }

  try {
    // 检测用户地理位置
    const { region } = await detectUserLocation();
    
    // 根据地理位置对下载源进行排序
    const sortedSources = [...packageInfo.downloadSources].sort((a, b) => {
      // 地区匹配优先
      const aRegionMatch = a.region === region ? 1000 : 0;
      const bRegionMatch = b.region === region ? 1000 : 0;
      
      // 中国用户优先使用国内镜像
      const aChinaPriority = (region === 'china' && a.region === 'china') ? 500 : 0;
      const bChinaPriority = (region === 'china' && b.region === 'china') ? 500 : 0;
      
      // 原始优先级
      const aPriority = 100 - a.priority;
      const bPriority = 100 - b.priority;
      
      const aScore = aRegionMatch + aChinaPriority + aPriority;
      const bScore = bRegionMatch + bChinaPriority + bPriority;
      
      return bScore - aScore;
    });

    // 测试前3个下载源的速度
    const testSources = sortedSources.slice(0, 3);
    const speedTests = await Promise.allSettled(
      testSources.map(async (source) => {
        const speed = await testDownloadSpeed(source);
        return { ...source, speed };
      })
    );

    // 获取成功的测试结果
    const testedSources = speedTests
      .filter((result): result is PromiseFulfilledResult<DownloadSource & { speed: number }> => 
        result.status === 'fulfilled')
      .map(result => result.value)
      .sort((a, b) => b.speed - a.speed);

    // 选择最佳下载源
    const recommendedSource = testedSources.length > 0 
      ? testedSources[0] 
      : sortedSources[0];

    // 备选下载源
    const alternativeSources = sortedSources
      .filter(source => source.url !== recommendedSource.url)
      .slice(0, 3);

    return {
      package: packageInfo,
      recommendedSource,
      alternativeSources
    };

  } catch (error) {
    console.error('获取最佳下载URL失败:', error);
    
    // 发生错误时返回默认的第一个下载源
    return {
      package: packageInfo,
      recommendedSource: packageInfo.downloadSources[0],
      alternativeSources: packageInfo.downloadSources.slice(1, 4)
    };
  }
}

/**
 * 获取所有支持的MySQL版本和平台组合
 */
export function getSupportedPackages(): Array<{
  version: MySQLVersion;
  os: SupportedOS;
  architecture: SystemArchitecture;
  package: MySQLPackage;
}> {
  return Object.entries(MYSQL_PACKAGES).map(([key, packageInfo]) => {
    const [version, os, architecture] = key.split('-') as [MySQLVersion, SupportedOS, SystemArchitecture];
    return {
      version,
      os,
      architecture,
      package: packageInfo
    };
  });
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * 验证下载URL的可用性
 */
export async function validateDownloadUrl(url: string): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.warn(`下载URL验证失败: ${url}`, error);
    return false;
  }
}

/**
 * 获取下载进度估算
 */
export function estimateDownloadTime(fileSize: number, speedKbps: number = 1000): {
  estimatedMinutes: number;
  estimatedSeconds: number;
  formattedTime: string;
} {
  const totalSeconds = Math.ceil(fileSize / 1024 / speedKbps);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  let formattedTime = '';
  if (minutes > 0) {
    formattedTime = `${minutes}分${seconds}秒`;
  } else {
    formattedTime = `${seconds}秒`;
  }

  return {
    estimatedMinutes: minutes,
    estimatedSeconds: seconds,
    formattedTime
  };
}

/**
 * 获取所有下载源
 */
export function getAllDownloadSources(
  version: MySQLVersion,
  os: SupportedOS,
  architecture: SystemArchitecture
): DownloadSource[] {
  const packageInfo = getMySQLPackage(version, os, architecture);
  if (!packageInfo) {
    return [];
  }

  // 返回包中配置的所有下载源
  return packageInfo.downloadSources;
}

/**
 * 测试下载源速度
 */
export async function testDownloadSourceSpeed(
  url: string,
  timeout: number = 10000
): Promise<{
  isAvailable: boolean;
  responseTime: number;
  error?: string;
}> {
  const startTime = Date.now();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    return {
      isAvailable: response.ok,
      responseTime,
      error: response.ok ? undefined : `HTTP ${response.status}`
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      isAvailable: false,
      responseTime,
      error: error instanceof Error ? error.message : '网络错误'
    };
  }
}