// MySQLAi.de - 管理员认证工具函数
// 提供管理员认证状态管理和API调用

// 管理员用户类型
export interface AdminUser {
  username: string;
  role: 'admin';
}

// 认证响应类型
export interface AuthResponse {
  success: boolean;
  data?: {
    token?: string;
    user?: AdminUser;
  };
  error?: string;
  message?: string;
}

// 存储键名
const ADMIN_TOKEN_KEY = 'mysql_admin_token';
const ADMIN_USER_KEY = 'mysql_admin_user';

// 管理员认证工具类
export class AdminAuth {
  // 获取存储的认证令牌（优先从cookie读取，然后从sessionStorage）
  static getToken(): string | null {
    if (typeof window === 'undefined') return null;

    // 首先尝试从cookie读取
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'mysql_admin_token') {
        return decodeURIComponent(value);
      }
    }

    // 如果cookie中没有，从sessionStorage读取
    return sessionStorage.getItem(ADMIN_TOKEN_KEY);
  }

  // 获取存储的用户信息
  static getUser(): AdminUser | null {
    if (typeof window === 'undefined') return null;
    const userStr = sessionStorage.getItem(ADMIN_USER_KEY);
    if (!userStr) return null;
    
    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  }

  // 保存认证信息（同时设置cookie和sessionStorage）
  static saveAuth(token: string, user: AdminUser): void {
    if (typeof window === 'undefined') return;

    // 保存到sessionStorage
    sessionStorage.setItem(ADMIN_TOKEN_KEY, token);
    sessionStorage.setItem(ADMIN_USER_KEY, JSON.stringify(user));

    // 设置cookie（24小时过期）
    const expires = new Date();
    expires.setTime(expires.getTime() + (24 * 60 * 60 * 1000)); // 24小时
    document.cookie = `mysql_admin_token=${encodeURIComponent(token)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
  }

  // 清除认证信息（同时清除cookie和sessionStorage）
  static clearAuth(): void {
    if (typeof window === 'undefined') return;

    // 清除sessionStorage
    sessionStorage.removeItem(ADMIN_TOKEN_KEY);
    sessionStorage.removeItem(ADMIN_USER_KEY);

    // 清除cookie
    document.cookie = 'mysql_admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
  }

  // 检查是否已认证
  static isAuthenticated(): boolean {
    return !!this.getToken() && !!this.getUser();
  }

  // 管理员登录
  static async login(username: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data: AuthResponse = await response.json();

      if (data.success && data.data?.token && data.data?.user) {
        this.saveAuth(data.data.token, data.data.user);
      }

      return data;
    } catch (error) {
      console.error('登录请求失败:', error);
      return {
        success: false,
        error: '网络错误，请稍后重试'
      };
    }
  }

  // 验证认证状态
  static async verifyAuth(): Promise<boolean> {
    const token = this.getToken();
    if (!token) return false;

    try {
      const response = await fetch('/api/admin/auth', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data: AuthResponse = await response.json();

      if (!data.success) {
        this.clearAuth();
        return false;
      }

      return true;
    } catch (error) {
      console.error('认证验证失败:', error);
      this.clearAuth();
      return false;
    }
  }

  // 管理员登出
  static async logout(): Promise<void> {
    const token = this.getToken();
    
    // 清除本地认证信息
    this.clearAuth();

    // 通知服务端登出（可选）
    if (token) {
      try {
        await fetch('/api/admin/auth', {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      } catch (error) {
        console.error('登出请求失败:', error);
        // 即使服务端请求失败，本地认证信息已清除
      }
    }
  }

  // 获取认证头
  static getAuthHeaders(): Record<string, string> {
    const token = this.getToken();
    if (!token) return {};
    
    return {
      'Authorization': `Bearer ${token}`,
    };
  }
}

// 导出便捷函数
export const {
  getToken,
  getUser,
  saveAuth,
  clearAuth,
  isAuthenticated,
  login,
  verifyAuth,
  logout,
  getAuthHeaders
} = AdminAuth;
