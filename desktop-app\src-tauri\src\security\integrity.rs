/**
 * 完整性检查模块
 * 验证二进制文件、配置文件和关键数据的完整性
 */

use sha2::{Sha256, Digest};
use std::fs;
use std::path::Path;
use std::collections::HashMap;

/// 完整性检查结果
#[derive(Debug, Clone, PartialEq)]
pub enum IntegrityResult {
    Valid,
    Invalid,
    Unknown,
}

/// 预计算的文件哈希值（在发布时生成）
/// 这些值应该在构建时自动生成并嵌入到二进制文件中
static EXPECTED_HASHES: &[(&str, &str)] = &[
    // 这些哈希值应该在构建时自动生成
    // ("config.json", "expected_hash_here"),
    // ("critical_file.dll", "expected_hash_here"),
];

/// 计算文件的SHA256哈希值
pub fn calculate_file_hash<P: AsRef<Path>>(path: P) -> Result<String, Box<dyn std::error::Error>> {
    let contents = fs::read(path)?;
    let mut hasher = Sha256::new();
    hasher.update(&contents);
    let result = hasher.finalize();
    Ok(format!("{:x}", result))
}

/// 验证二进制文件完整性
pub async fn verify_binary_integrity() -> IntegrityResult {
    // 获取当前可执行文件路径
    let exe_path = match std::env::current_exe() {
        Ok(path) => path,
        Err(_) => return IntegrityResult::Unknown,
    };

    // 计算当前二进制文件的哈希值
    let current_hash = match calculate_file_hash(&exe_path) {
        Ok(hash) => hash,
        Err(_) => return IntegrityResult::Unknown,
    };

    // 在实际应用中，这里应该与预存的哈希值进行比较
    // 由于我们无法在编译时知道最终的哈希值，这里实现一个简化版本
    
    // 检查文件大小是否合理（简单的完整性检查）
    if let Ok(metadata) = fs::metadata(&exe_path) {
        let file_size = metadata.len();
        
        // 如果文件太小，可能被篡改
        if file_size < 1024 * 1024 {  // 小于1MB
            return IntegrityResult::Invalid;
        }
        
        // 如果文件异常大，也可能有问题
        if file_size > 100 * 1024 * 1024 {  // 大于100MB
            return IntegrityResult::Invalid;
        }
    }

    // 检查文件的数字签名（Windows）
    #[cfg(target_os = "windows")]
    {
        if !verify_digital_signature(&exe_path) {
            return IntegrityResult::Invalid;
        }
    }

    // 检查二进制文件的结构完整性
    if !verify_binary_structure(&exe_path) {
        return IntegrityResult::Invalid;
    }

    IntegrityResult::Valid
}

/// 验证配置文件完整性
pub fn verify_config_integrity() -> IntegrityResult {
    let config_files = [
        "tauri.conf.json",
        "package.json",
    ];

    for config_file in &config_files {
        if let Ok(hash) = calculate_file_hash(config_file) {
            // 检查是否有预期的哈希值
            if let Some(expected) = get_expected_hash(config_file) {
                if hash != expected {
                    log::warn!("配置文件 {} 完整性检查失败", config_file);
                    return IntegrityResult::Invalid;
                }
            }
        }
    }

    IntegrityResult::Valid
}

/// 获取预期的文件哈希值
fn get_expected_hash(filename: &str) -> Option<String> {
    for (file, hash) in EXPECTED_HASHES {
        if *file == filename {
            return Some(hash.to_string());
        }
    }
    None
}

/// 验证数字签名（Windows）
#[cfg(target_os = "windows")]
fn verify_digital_signature<P: AsRef<Path>>(path: P) -> bool {
    use std::ffi::OsStr;
    use std::os::windows::ffi::OsStrExt;
    use std::ptr;

    let path_wide: Vec<u16> = path.as_ref()
        .as_os_str()
        .encode_wide()
        .chain(std::iter::once(0))
        .collect();

    // 这里应该调用Windows API来验证数字签名
    // 由于复杂性，这里返回true作为占位符
    // 实际实现需要使用WinVerifyTrust API
    true
}

/// 验证二进制文件结构
fn verify_binary_structure<P: AsRef<Path>>(path: P) -> bool {
    let contents = match fs::read(path) {
        Ok(data) => data,
        Err(_) => return false,
    };

    // 检查PE/ELF文件头
    #[cfg(target_os = "windows")]
    {
        // 检查PE文件头
        if contents.len() < 64 {
            return false;
        }
        
        // 检查DOS头
        if &contents[0..2] != b"MZ" {
            return false;
        }
        
        // 获取PE头偏移
        let pe_offset = u32::from_le_bytes([
            contents[60], contents[61], contents[62], contents[63]
        ]) as usize;
        
        if pe_offset + 4 > contents.len() {
            return false;
        }
        
        // 检查PE签名
        if &contents[pe_offset..pe_offset + 4] != b"PE\0\0" {
            return false;
        }
    }

    #[cfg(target_os = "linux")]
    {
        // 检查ELF文件头
        if contents.len() < 16 {
            return false;
        }
        
        // 检查ELF魔数
        if &contents[0..4] != b"\x7fELF" {
            return false;
        }
    }

    #[cfg(target_os = "macos")]
    {
        // 检查Mach-O文件头
        if contents.len() < 8 {
            return false;
        }
        
        // 检查Mach-O魔数
        let magic = u32::from_le_bytes([
            contents[0], contents[1], contents[2], contents[3]
        ]);
        
        if magic != 0xfeedface && magic != 0xfeedfacf {
            return false;
        }
    }

    true
}

/// 验证关键内存区域完整性
pub fn verify_memory_integrity() -> IntegrityResult {
    // 检查关键函数的完整性
    let critical_functions = [
        verify_binary_integrity as *const (),
        calculate_file_hash as *const (),
    ];

    for func_ptr in &critical_functions {
        if !verify_function_integrity(*func_ptr) {
            return IntegrityResult::Invalid;
        }
    }

    IntegrityResult::Valid
}

/// 验证函数完整性（简化版本）
fn verify_function_integrity(func_ptr: *const ()) -> bool {
    // 这是一个简化的实现
    // 实际应用中可能需要更复杂的检查
    !func_ptr.is_null()
}

/// 创建完整性检查报告
pub struct IntegrityReport {
    pub binary_integrity: IntegrityResult,
    pub config_integrity: IntegrityResult,
    pub memory_integrity: IntegrityResult,
    pub overall_status: IntegrityResult,
    pub details: Vec<String>,
}

/// 生成完整性检查报告
pub async fn generate_integrity_report() -> IntegrityReport {
    let mut details = Vec::new();
    
    let binary_integrity = verify_binary_integrity().await;
    match binary_integrity {
        IntegrityResult::Valid => details.push("二进制文件完整性：通过".to_string()),
        IntegrityResult::Invalid => details.push("二进制文件完整性：失败".to_string()),
        IntegrityResult::Unknown => details.push("二进制文件完整性：无法验证".to_string()),
    }

    let config_integrity = verify_config_integrity();
    match config_integrity {
        IntegrityResult::Valid => details.push("配置文件完整性：通过".to_string()),
        IntegrityResult::Invalid => details.push("配置文件完整性：失败".to_string()),
        IntegrityResult::Unknown => details.push("配置文件完整性：无法验证".to_string()),
    }

    let memory_integrity = verify_memory_integrity();
    match memory_integrity {
        IntegrityResult::Valid => details.push("内存完整性：通过".to_string()),
        IntegrityResult::Invalid => details.push("内存完整性：失败".to_string()),
        IntegrityResult::Unknown => details.push("内存完整性：无法验证".to_string()),
    }

    // 确定总体状态
    let overall_status = if binary_integrity == IntegrityResult::Invalid 
        || config_integrity == IntegrityResult::Invalid 
        || memory_integrity == IntegrityResult::Invalid {
        IntegrityResult::Invalid
    } else if binary_integrity == IntegrityResult::Valid 
        && config_integrity == IntegrityResult::Valid 
        && memory_integrity == IntegrityResult::Valid {
        IntegrityResult::Valid
    } else {
        IntegrityResult::Unknown
    };

    IntegrityReport {
        binary_integrity,
        config_integrity,
        memory_integrity,
        overall_status,
        details,
    }
}

/// 实时完整性监控
pub struct IntegrityMonitor {
    baseline_hashes: HashMap<String, String>,
    monitoring_active: bool,
}

impl IntegrityMonitor {
    pub fn new() -> Self {
        Self {
            baseline_hashes: HashMap::new(),
            monitoring_active: false,
        }
    }

    /// 建立基线哈希值
    pub fn establish_baseline(&mut self, files: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        for file in files {
            let hash = calculate_file_hash(file)?;
            self.baseline_hashes.insert(file.to_string(), hash);
        }
        Ok(())
    }

    /// 检查文件是否被修改
    pub fn check_modifications(&self) -> Vec<String> {
        let mut modified_files = Vec::new();
        
        for (file, baseline_hash) in &self.baseline_hashes {
            if let Ok(current_hash) = calculate_file_hash(file) {
                if current_hash != *baseline_hash {
                    modified_files.push(file.clone());
                }
            }
        }
        
        modified_files
    }

    /// 启动监控
    pub fn start_monitoring(&mut self) {
        self.monitoring_active = true;
    }

    /// 停止监控
    pub fn stop_monitoring(&mut self) {
        self.monitoring_active = false;
    }
}