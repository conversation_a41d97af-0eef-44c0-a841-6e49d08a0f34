'use client';

// MySQLAi.de - 批量操作组件
// 通用的批量操作工具栏

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Trash2,
  X,
  CheckSquare,
  Square
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

interface BatchAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  variant?: 'primary' | 'outline' | 'ghost';
  className?: string;
  onClick: () => void;
  disabled?: boolean;
}

interface BatchOperationsProps {
  selectedItems?: string[];
  selectedCount?: number;
  totalItems?: number;
  onSelectAll?: () => void;
  onDeselectAll?: () => void;
  onDelete?: () => void;
  actions?: BatchAction[];
  loading?: boolean;
  className?: string;
}

export default function BatchOperations({
  selectedItems = [],
  selectedCount: propSelectedCount,
  totalItems = 0,
  onSelectAll,
  onDeselectAll,
  onDelete,
  actions = [],
  loading = false,
  className
}: BatchOperationsProps) {

  const selectedCount = propSelectedCount ?? selectedItems.length;
  const isAllSelected = selectedCount === totalItems && totalItems > 0;
  const isPartialSelected = selectedCount > 0 && selectedCount < totalItems;

  // 处理全选/取消全选
  const handleToggleSelectAll = () => {
    if (isAllSelected && onDeselectAll) {
      onDeselectAll();
    } else if (onSelectAll) {
      onSelectAll();
    }
  };

  // 默认操作：如果没有传入actions但有onDelete，创建默认删除操作
  const finalActions = actions.length > 0 ? actions : (onDelete ? [{
    id: 'delete',
    label: '删除',
    icon: <Trash2 className="w-4 h-4" />,
    variant: 'outline' as const,
    onClick: onDelete,
    disabled: false,
    className: 'text-red-600 hover:text-red-700 border-red-300 hover:border-red-400'
  }] : []);

  // 如果没有选中项目，不显示批量操作栏
  if (selectedCount === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
        className={cn(
          'bg-mysql-primary-light border border-mysql-primary/20 rounded-lg p-4 mb-6',
          className
        )}
      >
        <div className="flex items-center justify-between">
          {/* 左侧：选择状态和全选控制 */}
          <div className="flex items-center space-x-4">
            {/* 全选复选框 */}
            <button
              type="button"
              onClick={handleToggleSelectAll}
              disabled={loading || totalItems === 0}
              className="flex items-center space-x-2 text-mysql-primary hover:text-mysql-primary-dark transition-colors"
            >
              {isAllSelected ? (
                <CheckSquare className="w-5 h-5" />
              ) : isPartialSelected ? (
                <div className="w-5 h-5 border-2 border-mysql-primary rounded flex items-center justify-center">
                  <div className="w-2 h-2 bg-mysql-primary rounded"></div>
                </div>
              ) : (
                <Square className="w-5 h-5" />
              )}
              <span className="text-sm font-medium">
                {isAllSelected ? '取消全选' : '全选'}
              </span>
            </button>

            {/* 选择状态 */}
            <div className="text-sm text-mysql-primary">
              已选择 <span className="font-bold">{selectedCount}</span> / {totalItems} 项
            </div>
          </div>

          {/* 右侧：批量操作按钮 */}
          <div className="flex items-center space-x-2">
            {/* 取消选择按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onDeselectAll}
              disabled={loading}
              icon={<X className="w-4 h-4" />}
              className="text-mysql-text-light hover:text-mysql-text"
            >
              取消选择
            </Button>

            {/* 分隔线 */}
            <div className="w-px h-6 bg-mysql-border"></div>

            {/* 批量操作按钮 */}
            {finalActions.map((action) => (
              <Button
                key={action.id}
                variant={action.variant || 'outline'}
                size="sm"
                onClick={action.onClick}
                disabled={loading || action.disabled}
                icon={action.icon}
                className={cn(
                  'transition-all duration-200',
                  action.className
                )}
              >
                {action.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 加载状态指示器 */}
        {loading && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-3 pt-3 border-t border-mysql-primary/20"
          >
            <div className="flex items-center space-x-2 text-sm text-mysql-primary">
              <div className="w-4 h-4 border-2 border-mysql-primary border-t-transparent rounded-full animate-spin"></div>
              <span>正在处理批量操作...</span>
            </div>
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}
