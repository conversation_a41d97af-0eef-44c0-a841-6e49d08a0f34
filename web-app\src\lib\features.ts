// MySQLAi.de - 功能特性配置文件
// 管理网站功能展示相关的数据和配置

import { Database, FolderOpen, BarChart3, Zap, Shield, Users, Clock, FileText, Settings, TrendingUp } from 'lucide-react';

// 主要功能特性数据
export const MAIN_FEATURES = [
  {
    id: 'mysql-knowledge',
    title: 'MySQL知识库',
    description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',
    icon: 'Database',
    features: [
      '数据库性能优化',
      '查询语句调优',
      '索引设计最佳实践',
      '架构设计指南',
      'SQL语句优化技巧',
      '数据库安全配置',
    ],
    color: 'mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
    href: '/knowledge',
  },
  {
    id: 'project-management',
    title: '项目管理',
    description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',
    icon: 'FolderOpen',
    features: [
      '任务分配与跟踪',
      '项目进度管理',
      '团队协作工具',
      '时间管理优化',
      '里程碑管理',
      '资源分配优化',
    ],
    color: 'mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
    href: '/projects',
  },
  {
    id: 'report-display',
    title: '报告展示',
    description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',
    icon: 'BarChart3',
    features: [
      '多媒体报告支持',
      '数据可视化图表',
      '实时数据展示',
      '自定义报告模板',
      '导出多种格式',
      '分享协作功能',
    ],
    color: 'mysql-success',
    gradient: 'from-mysql-success to-green-600',
    href: '/reports',
  },
] as const;

// 专业特性数据
export const PROFESSIONAL_FEATURES = [
  {
    id: 'ai-analysis',
    title: '智能分析',
    description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',
    icon: 'Zap',
    color: 'mysql-primary',
  },
  {
    id: 'expert-consulting',
    title: '专业咨询',
    description: '资深数据库专家团队，提供一对一的专业咨询服务。',
    icon: 'Users',
    color: 'mysql-accent',
  },
  {
    id: 'efficient-management',
    title: '高效管理',
    description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',
    icon: 'TrendingUp',
    color: 'mysql-success',
  },
  {
    id: 'transparent-reporting',
    title: '透明报告',
    description: '详细的项目报告和数据分析，确保项目进展透明可控。',
    icon: 'FileText',
    color: 'mysql-warning',
  },
  {
    id: '24-7-support',
    title: '7x24支持',
    description: '全天候技术支持服务，确保您的数据库系统稳定运行。',
    icon: 'Clock',
    color: 'mysql-error',
  },
] as const;

// 技术特性数据
export const TECHNICAL_FEATURES = [
  {
    id: 'performance-optimization',
    title: '性能优化',
    description: '深度分析数据库性能瓶颈，提供针对性的优化方案。',
    icon: 'Zap',
    details: [
      '查询性能分析',
      '索引优化建议',
      '配置参数调优',
      '硬件资源优化',
    ],
  },
  {
    id: 'security-enhancement',
    title: '安全加固',
    description: '全面的数据库安全评估和加固方案，保护数据安全。',
    icon: 'Shield',
    details: [
      '权限管理优化',
      '数据加密配置',
      '审计日志设置',
      '安全漏洞检测',
    ],
  },
  {
    id: 'architecture-design',
    title: '架构设计',
    description: '专业的数据库架构设计，支持高可用和高性能需求。',
    icon: 'Settings',
    details: [
      '主从复制配置',
      '读写分离设计',
      '分库分表方案',
      '容灾备份策略',
    ],
  },
] as const;

// 服务优势数据
export const SERVICE_ADVANTAGES = [
  {
    id: 'global-expertise',
    title: '🌍 #1 MySQL专家',
    description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',
    details: '覆盖全球8个地区，超过5万用户信赖',
    stats: {
      customers: '1000+',
      regions: '8',
      users: '50000+',
    },
  },
  {
    id: 'compatibility-support',
    title: '📝 兼容性与支持',
    description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',
    details: '支持MySQL 5.7到8.0的所有主流版本',
    versions: ['5.7', '8.0', '8.1', '8.2'],
  },
  {
    id: 'flexible-pricing',
    title: '💰 灵活计费',
    description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',
    details: '透明计费，性价比最高的MySQL服务',
    pricing: {
      model: 'pay-as-you-go',
      transparency: '100%',
      hidden_fees: false,
    },
  },
  {
    id: 'global-deployment',
    title: '⚡ 全球布局',
    description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',
    details: '全球用户享受一致的高速服务体验',
    datacenters: 7,
    response_time: '<100ms',
  },
  {
    id: 'service-guarantee',
    title: '⏰ 服务保障',
    description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',
    details: '专业运维团队，99.9%服务可用性保证',
    sla: '99.9%',
    support: '7x24',
  },
  {
    id: 'transparent-billing',
    title: '🎈 透明计费',
    description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',
    details: '无隐藏费用，按实际使用量计费',
    billing: {
      transparency: true,
      industry_standard: true,
      hidden_fees: false,
    },
  },
] as const;

// 功能分类
export const FEATURE_CATEGORIES = {
  main: '核心功能',
  professional: '专业特性',
  technical: '技术特性',
  advantages: '服务优势',
} as const;

// 图标映射
export const ICON_MAP = {
  Database,
  FolderOpen,
  BarChart3,
  Zap,
  Shield,
  Users,
  Clock,
  FileText,
  Settings,
  TrendingUp,
} as const;

// 颜色主题映射
export const COLOR_THEMES = {
  'mysql-primary': {
    bg: 'bg-mysql-primary',
    text: 'text-mysql-primary',
    border: 'border-mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
  },
  'mysql-accent': {
    bg: 'bg-mysql-accent',
    text: 'text-mysql-accent',
    border: 'border-mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
  },
  'mysql-success': {
    bg: 'bg-mysql-success',
    text: 'text-mysql-success',
    border: 'border-mysql-success',
    gradient: 'from-mysql-success to-green-600',
  },
  'mysql-warning': {
    bg: 'bg-mysql-warning',
    text: 'text-mysql-warning',
    border: 'border-mysql-warning',
    gradient: 'from-mysql-warning to-yellow-600',
  },
  'mysql-error': {
    bg: 'bg-mysql-error',
    text: 'text-mysql-error',
    border: 'border-mysql-error',
    gradient: 'from-mysql-error to-red-600',
  },
} as const;

// 工具函数

/**
 * 根据ID获取功能特性
 */
export function getFeatureById(id: string, category: keyof typeof FEATURE_CATEGORIES = 'main') {
  switch (category) {
    case 'main':
      return MAIN_FEATURES.find(feature => feature.id === id);
    case 'professional':
      return PROFESSIONAL_FEATURES.find(feature => feature.id === id);
    case 'technical':
      return TECHNICAL_FEATURES.find(feature => feature.id === id);
    case 'advantages':
      return SERVICE_ADVANTAGES.find(feature => feature.id === id);
    default:
      return null;
  }
}

/**
 * 获取图标组件
 */
export function getIconComponent(iconName: keyof typeof ICON_MAP) {
  return ICON_MAP[iconName];
}

/**
 * 获取颜色主题
 */
export function getColorTheme(colorName: keyof typeof COLOR_THEMES) {
  return COLOR_THEMES[colorName];
}

/**
 * 获取随机功能特性
 */
export function getRandomFeatures(count: number = 3, category: keyof typeof FEATURE_CATEGORIES = 'main') {
  let features;
  switch (category) {
    case 'main':
      features = [...MAIN_FEATURES];
      break;
    case 'professional':
      features = [...PROFESSIONAL_FEATURES];
      break;
    case 'technical':
      features = [...TECHNICAL_FEATURES];
      break;
    case 'advantages':
      features = [...SERVICE_ADVANTAGES];
      break;
    default:
      features = [...MAIN_FEATURES];
  }
  
  // 随机打乱数组
  for (let i = features.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [features[i], features[j]] = [features[j], features[i]];
  }
  
  return features.slice(0, count);
}
