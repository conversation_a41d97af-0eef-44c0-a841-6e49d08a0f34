/**
 * MySQLAi.de 主题色彩和站点配置
 * 从Web版本移植，用于桌面应用和Web版本的视觉统一
 */

// ===== 主题色彩类型定义 =====

export interface ThemeColors {
  primary: string;
  primaryDark: string;
  primaryLight: string;
  accent: string;
  text: string;
  textLight: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

// ===== MySQL主题色彩配置 =====

/**
 * MySQL主题色彩配置
 * 基于MySQL官方品牌色彩规范
 */
export const THEME_COLORS: ThemeColors = {
  primary: '#00758F',        // MySQL官方蓝
  primaryDark: '#003545',    // 深蓝色
  primaryLight: '#E6F3F7',   // 浅蓝色
  accent: '#0066CC',         // 强调色
  text: '#2D3748',           // 主文字色
  textLight: '#718096',      // 浅文字色
  border: '#E2E8F0',         // 边框色
  success: '#38A169',        // 成功色
  warning: '#D69E2E',        // 警告色
  error: '#E53E3E',          // 错误色
} as const;

/**
 * 扩展色彩配置（桌面应用专用）
 */
export const EXTENDED_COLORS = {
  background: '#FFFFFF',     // 背景色
  backgroundSecondary: '#F7FAFC', // 次要背景色
  backgroundDark: '#1A202C', // 深色背景
  overlay: 'rgba(0, 0, 0, 0.5)', // 遮罩层
  shadow: 'rgba(0, 0, 0, 0.1)',  // 阴影色
  disabled: '#A0AEC0',       // 禁用状态色
  placeholder: '#CBD5E0',    // 占位符色
} as const;

// ===== 站点配置 =====

/**
 * 站点基本信息配置
 */
export const SITE_CONFIG = {
  name: 'MySQLAi.de',
  title: 'MySQL智能分析专家',
  description: '专业的数据库知识分享与项目管理平台',
  url: 'https://mysqlai.de',
  author: 'MySQLAi Team',
  keywords: ['MySQL', '数据库', 'AI分析', '项目管理', '知识分享', '性能优化'],
} as const;

/**
 * 桌面应用配置
 */
export const DESKTOP_APP_CONFIG = {
  name: 'MySQLAi Desktop Installer',
  version: '1.0.0',
  description: 'MySQL一键安装工具桌面版',
  identifier: 'com.mysqlai.desktop-installer',
  author: SITE_CONFIG.author,
  website: SITE_CONFIG.url,
  supportEmail: '<EMAIL>',
  licenseUrl: `${SITE_CONFIG.url}/license`,
  privacyUrl: `${SITE_CONFIG.url}/privacy`,
} as const;

// ===== UI配置常量 =====

/**
 * 字体配置
 */
export const FONT_CONFIG = {
  primary: 'Inter, Avenir, Helvetica, Arial, sans-serif',
  mono: 'Consolas, Monaco, "Courier New", monospace',
  sizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
  }
} as const;

/**
 * 间距配置
 */
export const SPACING_CONFIG = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '1rem',       // 16px
  lg: '1.5rem',     // 24px
  xl: '2rem',       // 32px
  '2xl': '3rem',    // 48px
  '3xl': '4rem',    // 64px
} as const;

/**
 * 圆角配置
 */
export const BORDER_RADIUS_CONFIG = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px',
} as const;

/**
 * 阴影配置
 */
export const SHADOW_CONFIG = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
} as const;