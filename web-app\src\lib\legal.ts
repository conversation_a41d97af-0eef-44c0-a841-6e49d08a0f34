// MySQLAi.de - 法律条款数据管理文件
// 包含服务条款、隐私政策、免责声明、<PERSON>ie政策的完整内容和管理系统

// 法律页面类型定义
export type LegalPageType = 'terms' | 'privacy' | 'disclaimer' | 'cookies';

// 法律条款章节接口
export interface LegalSection {
  id: string;
  title: string;
  content: string;
  subsections?: LegalSection[];
}

// 法律页面内容接口
export interface LegalPageContent {
  type: LegalPageType;
  title: string;
  description: string;
  lastUpdated: string;
  version: string;
  sections: LegalSection[];
}

// 法律页面元数据接口
export interface LegalPageMeta {
  type: LegalPageType;
  title: string;
  description: string;
  keywords: string[];
  lastUpdated: string;
  version: string;
}

// 服务条款内容
export const TERMS_CONTENT: LegalPageContent = {
  type: 'terms',
  title: '服务条款',
  description: 'MySQLAi.de平台服务使用条款和用户协议',
  lastUpdated: '2025-06-28',
  version: '1.0',
  sections: [
    {
      id: 'acceptance',
      title: '1. 条款接受',
      content: '欢迎使用MySQLAi.de（以下简称"本平台"）提供的MySQL智能分析服务。通过访问或使用本平台的任何服务，您表示同意遵守本服务条款（以下简称"本条款"）。如果您不同意本条款的任何部分，请不要使用本平台的服务。'
    },
    {
      id: 'service-description',
      title: '2. 服务描述',
      content: 'MySQLAi.de是一个专业的MySQL数据库智能分析平台，为用户提供以下服务：',
      subsections: [
        {
          id: 'service-mysql-analysis',
          title: '2.1 MySQL智能分析',
          content: '基于AI技术的数据库性能分析、优化建议和智能诊断服务。'
        },
        {
          id: 'service-project-management',
          title: '2.2 项目管理',
          content: '提供数据库项目的任务管理、进度跟踪和团队协作功能。'
        },
        {
          id: 'service-report-display',
          title: '2.3 报告展示',
          content: '支持多媒体格式的项目报告生成、展示和分享功能。'
        },
        {
          id: 'service-technical-support',
          title: '2.4 技术支持',
          content: '7×24小时专业技术支持服务，包括在线咨询、远程协助等。'
        }
      ]
    },
    {
      id: 'user-obligations',
      title: '3. 用户义务',
      content: '使用本平台服务时，您需要遵守以下义务：',
      subsections: [
        {
          id: 'legal-compliance',
          title: '3.1 法律合规',
          content: '遵守中华人民共和国相关法律法规，不得利用本平台从事违法活动。'
        },
        {
          id: 'account-security',
          title: '3.2 账户安全',
          content: '妥善保管账户信息，对账户下的所有活动承担责任。'
        },
        {
          id: 'data-accuracy',
          title: '3.3 数据准确性',
          content: '确保提供给本平台的数据信息真实、准确、完整。'
        },
        {
          id: 'proper-usage',
          title: '3.4 合理使用',
          content: '合理使用平台资源，不得恶意攻击、滥用或干扰平台正常运行。'
        }
      ]
    },
    {
      id: 'intellectual-property',
      title: '4. 知识产权',
      content: '本平台的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护。未经授权，不得复制、传播、展示、镜像、上传、下载使用。'
    },
    {
      id: 'privacy-protection',
      title: '5. 隐私保护',
      content: '我们重视用户隐私保护，具体的隐私保护措施请参阅《隐私政策》。我们承诺按照相关法律法规和本平台隐私政策处理用户个人信息。'
    },
    {
      id: 'service-availability',
      title: '6. 服务可用性',
      content: '我们努力确保服务的连续性和稳定性，但不保证服务不会中断。因系统维护、升级或不可抗力等原因导致的服务中断，我们将尽快恢复服务。'
    },
    {
      id: 'limitation-of-liability',
      title: '7. 责任限制',
      content: '在法律允许的最大范围内，本平台对因使用或无法使用本服务而导致的任何直接、间接、偶然、特殊或后果性损害不承担责任。'
    },
    {
      id: 'terms-modification',
      title: '8. 条款修改',
      content: '我们保留随时修改本条款的权利。修改后的条款将在本平台公布，继续使用本服务即表示您接受修改后的条款。'
    },
    {
      id: 'governing-law',
      title: '9. 适用法律',
      content: '本条款的解释和执行适用中华人民共和国法律。如发生争议，应通过友好协商解决；协商不成的，提交本平台所在地人民法院管辖。'
    },
    {
      id: 'contact-information',
      title: '10. 联系方式',
      content: '如您对本条款有任何疑问，请通过以下方式联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'
    }
  ]
};

// 隐私政策内容
export const PRIVACY_CONTENT: LegalPageContent = {
  type: 'privacy',
  title: '隐私政策',
  description: 'MySQLAi.de平台用户隐私保护政策和个人信息处理规则',
  lastUpdated: '2025-06-28',
  version: '1.0',
  sections: [
    {
      id: 'introduction',
      title: '1. 引言',
      content: 'MySQLAi.de（以下简称"我们"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。'
    },
    {
      id: 'information-collection',
      title: '2. 我们收集的信息',
      content: '为了向您提供更好的服务，我们可能会收集以下类型的信息：',
      subsections: [
        {
          id: 'account-information',
          title: '2.1 账户信息',
          content: '当您注册账户时，我们会收集您的用户名、邮箱地址、手机号码等基本信息。'
        },
        {
          id: 'usage-information',
          title: '2.2 使用信息',
          content: '您使用我们服务时产生的信息，包括访问时间、使用功能、操作记录等。'
        },
        {
          id: 'device-information',
          title: '2.3 设备信息',
          content: '您使用的设备信息，包括设备型号、操作系统、浏览器类型、IP地址等。'
        },
        {
          id: 'database-information',
          title: '2.4 数据库信息',
          content: '为提供MySQL分析服务，我们可能需要访问您的数据库结构信息（不包含敏感业务数据）。'
        }
      ]
    },
    {
      id: 'information-usage',
      title: '3. 信息使用目的',
      content: '我们收集和使用您的个人信息主要用于以下目的：',
      subsections: [
        {
          id: 'service-provision',
          title: '3.1 服务提供',
          content: '为您提供MySQL智能分析、项目管理、报告展示等核心服务。'
        },
        {
          id: 'service-improvement',
          title: '3.2 服务改进',
          content: '分析用户使用习惯，优化产品功能和用户体验。'
        },
        {
          id: 'security-protection',
          title: '3.3 安全保护',
          content: '保护您的账户安全，防范欺诈、滥用等风险。'
        },
        {
          id: 'customer-support',
          title: '3.4 客户支持',
          content: '为您提供技术支持和客户服务。'
        }
      ]
    },
    {
      id: 'information-sharing',
      title: '4. 信息共享',
      content: '我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：',
      subsections: [
        {
          id: 'legal-requirement',
          title: '4.1 法律要求',
          content: '根据法律法规、法律程序、政府要求或司法裁定需要披露。'
        },
        {
          id: 'user-consent',
          title: '4.2 用户同意',
          content: '获得您的明确同意后，与第三方共享特定信息。'
        },
        {
          id: 'service-providers',
          title: '4.3 服务提供商',
          content: '与我们的服务提供商共享必要信息，以便他们为我们提供服务。'
        }
      ]
    },
    {
      id: 'information-security',
      title: '5. 信息安全',
      content: '我们采用行业标准的安全措施保护您的个人信息：',
      subsections: [
        {
          id: 'encryption',
          title: '5.1 数据加密',
          content: '使用SSL/TLS加密技术保护数据传输安全。'
        },
        {
          id: 'access-control',
          title: '5.2 访问控制',
          content: '严格限制员工对个人信息的访问权限。'
        },
        {
          id: 'security-monitoring',
          title: '5.3 安全监控',
          content: '建立完善的安全监控和应急响应机制。'
        }
      ]
    },
    {
      id: 'user-rights',
      title: '6. 您的权利',
      content: '您对自己的个人信息享有以下权利：',
      subsections: [
        {
          id: 'access-right',
          title: '6.1 知情权',
          content: '您有权了解我们收集、使用您个人信息的情况。'
        },
        {
          id: 'correction-right',
          title: '6.2 更正权',
          content: '您有权要求我们更正或补充您的个人信息。'
        },
        {
          id: 'deletion-right',
          title: '6.3 删除权',
          content: '在特定情况下，您有权要求我们删除您的个人信息。'
        }
      ]
    },
    {
      id: 'policy-updates',
      title: '7. 政策更新',
      content: '我们可能会不时更新本隐私政策。更新后的政策将在本平台公布，并通过适当方式通知您。'
    },
    {
      id: 'contact-us',
      title: '8. 联系我们',
      content: '如您对本隐私政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'
    }
  ]
};

// 免责声明内容
export const DISCLAIMER_CONTENT: LegalPageContent = {
  type: 'disclaimer',
  title: '免责声明',
  description: 'MySQLAi.de平台服务免责条款和责任限制说明',
  lastUpdated: '2025-06-28',
  version: '1.0',
  sections: [
    {
      id: 'general-disclaimer',
      title: '1. 一般免责',
      content: 'MySQLAi.de平台（以下简称"本平台"）提供的所有信息、建议和服务仅供参考，不构成任何形式的保证。用户使用本平台服务的风险由用户自行承担。'
    },
    {
      id: 'service-limitations',
      title: '2. 服务限制',
      content: '本平台的服务存在以下限制：',
      subsections: [
        {
          id: 'analysis-accuracy',
          title: '2.1 分析准确性',
          content: 'MySQL智能分析结果基于算法和数据模型，可能存在误差，不保证100%准确。'
        },
        {
          id: 'service-availability',
          title: '2.2 服务可用性',
          content: '服务可能因维护、升级、网络故障等原因暂时中断，我们不承担因此造成的损失。'
        },
        {
          id: 'data-security',
          title: '2.3 数据安全',
          content: '虽然我们采取安全措施保护数据，但无法保证绝对安全，不承担因数据泄露造成的损失。'
        }
      ]
    },
    {
      id: 'user-responsibility',
      title: '3. 用户责任',
      content: '用户在使用本平台服务时应承担以下责任：',
      subsections: [
        {
          id: 'data-backup',
          title: '3.1 数据备份',
          content: '用户应自行备份重要数据，本平台不承担数据丢失的责任。'
        },
        {
          id: 'decision-making',
          title: '3.2 决策责任',
          content: '基于本平台分析结果做出的业务决策，责任由用户自行承担。'
        },
        {
          id: 'compliance',
          title: '3.3 合规使用',
          content: '用户应确保使用本平台服务符合相关法律法规要求。'
        }
      ]
    },
    {
      id: 'third-party-services',
      title: '4. 第三方服务',
      content: '本平台可能包含第三方服务链接，对于第三方服务的内容、隐私政策或做法，我们不承担任何责任。'
    },
    {
      id: 'liability-limitation',
      title: '5. 责任限制',
      content: '在法律允许的最大范围内，本平台对任何直接、间接、偶然、特殊、后果性或惩罚性损害不承担责任，包括但不限于利润损失、数据丢失、业务中断等。'
    },
    {
      id: 'indemnification',
      title: '6. 赔偿',
      content: '用户同意就因违反本免责声明或使用本平台服务而产生的任何索赔、损失或费用，向本平台提供赔偿和保护。'
    },
    {
      id: 'governing-law',
      title: '7. 适用法律',
      content: '本免责声明受中华人民共和国法律管辖。任何争议应通过友好协商解决，协商不成的提交本平台所在地人民法院管辖。'
    }
  ]
};

// Cookie政策内容
export const COOKIES_CONTENT: LegalPageContent = {
  type: 'cookies',
  title: 'Cookie政策',
  description: 'MySQLAi.de平台Cookie使用说明和管理指南',
  lastUpdated: '2025-06-28',
  version: '1.0',
  sections: [
    {
      id: 'what-are-cookies',
      title: '1. 什么是Cookie',
      content: 'Cookie是网站存储在您设备上的小型文本文件，用于记住您的偏好设置和改善您的浏览体验。Cookie不会损害您的设备或文件。'
    },
    {
      id: 'cookie-types',
      title: '2. Cookie类型',
      content: '我们使用以下类型的Cookie：',
      subsections: [
        {
          id: 'essential-cookies',
          title: '2.1 必要Cookie',
          content: '这些Cookie对网站正常运行是必需的，包括用户身份验证、安全防护等功能。'
        },
        {
          id: 'functional-cookies',
          title: '2.2 功能Cookie',
          content: '这些Cookie用于记住您的偏好设置，如语言选择、主题设置等，以提供个性化体验。'
        },
        {
          id: 'analytics-cookies',
          title: '2.3 分析Cookie',
          content: '这些Cookie帮助我们了解用户如何使用网站，以便改进网站性能和用户体验。'
        },
        {
          id: 'marketing-cookies',
          title: '2.4 营销Cookie',
          content: '这些Cookie用于跟踪用户在网站上的活动，以便提供相关的广告和营销内容。'
        }
      ]
    },
    {
      id: 'cookie-usage',
      title: '3. Cookie使用目的',
      content: '我们使用Cookie的主要目的包括：',
      subsections: [
        {
          id: 'user-authentication',
          title: '3.1 用户认证',
          content: '保持您的登录状态，确保账户安全。'
        },
        {
          id: 'preference-storage',
          title: '3.2 偏好存储',
          content: '记住您的设置和偏好，提供个性化服务。'
        },
        {
          id: 'performance-analysis',
          title: '3.3 性能分析',
          content: '分析网站使用情况，优化网站性能。'
        },
        {
          id: 'security-protection',
          title: '3.4 安全保护',
          content: '防范恶意攻击，保护网站和用户安全。'
        }
      ]
    },
    {
      id: 'cookie-management',
      title: '4. Cookie管理',
      content: '您可以通过以下方式管理Cookie：',
      subsections: [
        {
          id: 'browser-settings',
          title: '4.1 浏览器设置',
          content: '大多数浏览器允许您控制Cookie设置，包括接受、拒绝或删除Cookie。'
        },
        {
          id: 'platform-settings',
          title: '4.2 平台设置',
          content: '您可以在账户设置中管理某些Cookie偏好。'
        },
        {
          id: 'opt-out',
          title: '4.3 退出选择',
          content: '您可以选择退出某些非必要Cookie，但这可能影响网站功能。'
        }
      ]
    },
    {
      id: 'third-party-cookies',
      title: '5. 第三方Cookie',
      content: '我们的网站可能包含第三方服务提供商设置的Cookie，如分析工具、社交媒体插件等。这些第三方Cookie受其各自隐私政策约束。'
    },
    {
      id: 'policy-updates',
      title: '6. 政策更新',
      content: '我们可能会不时更新本Cookie政策。更新后的政策将在网站上公布，建议您定期查看。'
    },
    {
      id: 'contact-information',
      title: '7. 联系我们',
      content: '如您对本Cookie政策有任何疑问，请联系我们：邮箱：<EMAIL>，电话：+86 400-888-9999。'
    }
  ]
};

// 所有法律内容的集合
export const LEGAL_CONTENTS = {
  terms: TERMS_CONTENT,
  privacy: PRIVACY_CONTENT,
  disclaimer: DISCLAIMER_CONTENT,
  cookies: COOKIES_CONTENT,
} as const;

// 工具函数：获取指定类型的法律条款内容
export function getLegalContent(type: LegalPageType): LegalPageContent {
  return LEGAL_CONTENTS[type];
}

// 工具函数：获取法律页面元数据
export function getLegalPageMeta(type: LegalPageType): LegalPageMeta {
  const content = getLegalContent(type);
  return {
    type: content.type,
    title: content.title,
    description: content.description,
    keywords: generateKeywords(type),
    lastUpdated: content.lastUpdated,
    version: content.version,
  };
}

// 工具函数：生成法律页面关键词
function generateKeywords(type: LegalPageType): string[] {
  const baseKeywords = ['MySQLAi.de', 'MySQL', '数据库', '法律声明'];

  const typeKeywords = {
    terms: ['服务条款', '用户协议', '使用条款', '服务协议'],
    privacy: ['隐私政策', '个人信息保护', '数据保护', '隐私保护'],
    disclaimer: ['免责声明', '责任限制', '法律免责', '服务限制'],
    cookies: ['Cookie政策', 'Cookie使用', '网站Cookie', 'Cookie管理'],
  };

  return [...baseKeywords, ...typeKeywords[type]];
}

// 工具函数：获取所有法律页面类型
export function getAllLegalPageTypes(): LegalPageType[] {
  return ['terms', 'privacy', 'disclaimer', 'cookies'];
}

// 工具函数：获取法律页面导航链接
export function getLegalNavigationLinks(currentType?: LegalPageType) {
  return getAllLegalPageTypes()
    .filter(type => type !== currentType)
    .map(type => {
      const meta = getLegalPageMeta(type);
      return {
        type,
        title: meta.title,
        href: `/${type}`,
        description: meta.description,
      };
    });
}

// 工具函数：格式化最后更新时间
export function formatLastUpdated(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// 工具函数：检查法律页面类型是否有效
export function isValidLegalPageType(type: string): type is LegalPageType {
  return getAllLegalPageTypes().includes(type as LegalPageType);
}
