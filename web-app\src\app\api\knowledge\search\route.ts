// MySQLAi.de - 知识库搜索 API
// 提供高级搜索功能和搜索统计

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET /api/knowledge/search - 高级搜索
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const query = searchParams.get('q') || searchParams.get('query');
    const category = searchParams.get('category');
    const tags = searchParams.get('tags');
    const difficulty = searchParams.get('difficulty');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'relevance'; // relevance, date, title
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    if (!query) {
      return NextResponse.json(
        { success: false, error: '请提供搜索关键词' },
        { status: 400 }
      );
    }

    // 记录搜索历史
    const userAgent = request.headers.get('user-agent');
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip');

    // 构建搜索查询
    let searchQuery = supabase
      .from('knowledge_articles')
      .select(`
        *,
        knowledge_categories(id, name, icon, color)
      `);

    // 全文搜索
    searchQuery = searchQuery.textSearch('title,content', query);

    // 分类筛选
    if (category) {
      searchQuery = searchQuery.eq('category_id', category);
    }

    // 难度筛选
    if (difficulty) {
      searchQuery = searchQuery.eq('difficulty', difficulty);
    }

    // 标签筛选
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      searchQuery = searchQuery.overlaps('tags', tagArray);
    }

    // 排序
    switch (sortBy) {
      case 'date':
        searchQuery = searchQuery.order('last_updated', { ascending: sortOrder === 'asc' });
        break;
      case 'title':
        searchQuery = searchQuery.order('title', { ascending: sortOrder === 'asc' });
        break;
      default: // relevance
        searchQuery = searchQuery.order('order_index', { ascending: true });
        break;
    }

    // 分页
    const offset = (page - 1) * limit;
    searchQuery = searchQuery.range(offset, offset + limit - 1);

    const { data: articles, error, count } = await searchQuery;

    if (error) {
      console.error('搜索失败:', error);
      return NextResponse.json(
        { success: false, error: '搜索失败', details: error.message },
        { status: 500 }
      );
    }

    // 异步记录搜索历史
    supabase
      .from('search_history')
      .insert({
        query,
        results_count: count || 0,
        ip_address: ip,
        user_agent: userAgent
      })
      .then(({ error }) => {
        if (error) console.error('记录搜索历史失败:', error);
      });

    // 为每个结果添加搜索高亮信息
    const resultsWithHighlight = articles?.map(article => ({
      ...article,
      highlight: {
        title: highlightText(article.title, query),
        description: highlightText(article.description || '', query)
      }
    }));

    return NextResponse.json({
      success: true,
      data: resultsWithHighlight,
      query: {
        text: query,
        category,
        tags,
        difficulty,
        sortBy,
        sortOrder
      },
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/search - 搜索建议
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, limit = 5 } = body;

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    // 搜索文章标题匹配
    const { data: titleMatches, error: titleError } = await supabase
      .from('knowledge_articles')
      .select('id, title, category_id, knowledge_categories(name)')
      .ilike('title', `%${query}%`)
      .limit(limit);

    if (titleError) {
      console.error('搜索建议失败:', titleError);
      return NextResponse.json(
        { success: false, error: '搜索建议失败' },
        { status: 500 }
      );
    }

    // 搜索热门搜索词
    const { data: popularSearches, error: popularError } = await supabase
      .from('search_history')
      .select('query, count(*)')
      .ilike('query', `%${query}%`)
      .order('count', { ascending: false })
      .limit(3);

    if (popularError) {
      console.error('获取热门搜索失败:', popularError);
    }

    const suggestions = [
      ...titleMatches?.map(article => ({
        type: 'article' as const,
        text: article.title,
        id: article.id,
        category: (article.knowledge_categories as {name: string})?.name
      })) || [],
      ...popularSearches?.map((search: {query: string}) => ({
        type: 'query' as const,
        text: search.query
      })) || []
    ];

    return NextResponse.json({
      success: true,
      data: suggestions.slice(0, limit)
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// 辅助函数：高亮搜索关键词
function highlightText(text: string, query: string): string {
  if (!text || !query) return text;
  
  const regex = new RegExp(`(${query})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}
