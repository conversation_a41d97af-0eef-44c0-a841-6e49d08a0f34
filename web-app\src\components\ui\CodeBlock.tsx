'use client';

// MySQLAi.de - CodeBlock代码高亮组件
// 集成prismjs实现SQL代码语法高亮显示，支持代码复制功能

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Copy, Check, Code } from 'lucide-react';
import { cn, copyToClipboard } from '@/lib/utils';
import { CodeBlockProps } from '@/lib/types';

// 简化的语法高亮函数 - 不依赖外部库
const highlightCode = (code: string, language: string): string => {
  console.log('highlightCode 新版本被调用，语言:', language, '代码:', code.substring(0, 50));
  if (language === 'sql') {
    // 使用占位符避免嵌套HTML标签问题
    let result = code;
    const placeholders: { [key: string]: string } = {};
    let placeholderIndex = 0;

    // 1. 先处理注释，用占位符替换
    result = result.replace(/(--.*$|\/\*[\s\S]*?\*\/)/gm, (match) => {
      const placeholder = `__COMMENT_${placeholderIndex++}__`;
      placeholders[placeholder] = `<span class="sql-comment">${match}</span>`;
      return placeholder;
    });

    // 2. 处理字符串，用占位符替换
    result = result.replace(/('[^']*'|"[^"]*")/g, (match) => {
      const placeholder = `__STRING_${placeholderIndex++}__`;
      placeholders[placeholder] = `<span class="sql-string">${match}</span>`;
      return placeholder;
    });

    // 3. 处理关键字
    result = result.replace(/\b(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|DATABASE|INDEX|PRIMARY|KEY|FOREIGN|REFERENCES|NOT|NULL|AUTO_INCREMENT|VARCHAR|INT|BIGINT|TEXT|DATETIME|TIMESTAMP|DEFAULT|UNIQUE|CONSTRAINT|JOIN|LEFT|RIGHT|INNER|OUTER|ON|GROUP|BY|ORDER|ASC|DESC|LIMIT|OFFSET|HAVING|UNION|DISTINCT|AS|AND|OR|IN|LIKE|BETWEEN|IS|EXISTS|CASE|WHEN|THEN|ELSE|END|IF|IFNULL|COUNT|SUM|AVG|MAX|MIN|NOW|CURDATE|CURTIME|SHOW|USE|COLLATE|CHARACTER|SET)\b/gi, '<span class="sql-keyword">$1</span>');

    // 4. 处理数字
    result = result.replace(/\b(\d+)\b/g, '<span class="sql-number">$1</span>');

    // 5. 恢复占位符
    Object.keys(placeholders).forEach(placeholder => {
      result = result.replace(new RegExp(placeholder, 'g'), placeholders[placeholder]);
    });

    console.log('highlightCode 新版本结果:', result.substring(0, 100));
    return result;
  }

  if (language === 'javascript' || language === 'typescript') {
    return code
      .replace(/\b(function|const|let|var|if|else|for|while|do|switch|case|break|continue|return|try|catch|finally|throw|new|this|class|extends|import|export|from|default|async|await|typeof|instanceof)\b/g, '<span class="js-keyword">$1</span>')
      .replace(/('[^']*'|"[^"]*"|`[^`]*`)/g, '<span class="js-string">$1</span>')
      .replace(/(\d+)/g, '<span class="js-number">$1</span>')
      .replace(/(\/\/.*$|\/\*[\s\S]*?\*\/)/gm, '<span class="js-comment">$1</span>');
  }

  // 默认返回原始代码
  return code;
};

const CodeBlock = React.forwardRef<HTMLDivElement, CodeBlockProps>(({
  code,
  language = 'sql',
  title,
  showLineNumbers = true,
  copyable = true,
  className,
  ...props
}, ref) => {
  const [highlightedCode, setHighlightedCode] = useState<string>('');
  const [copied, setCopied] = useState(false);

  // 注入自定义样式
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const styleId = 'code-highlight-styles';
      if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
          .sql-keyword { color: #0066cc; font-weight: bold; }
          .sql-string { color: #008000; }
          .sql-number { color: #ff6600; }
          .sql-comment { color: #808080; font-style: italic; }
          .js-keyword { color: #0066cc; font-weight: bold; }
          .js-string { color: #008000; }
          .js-number { color: #ff6600; }
          .js-comment { color: #808080; font-style: italic; }
        `;
        document.head.appendChild(style);
      }
    }
  }, []);

  // 高亮代码
  useEffect(() => {
    const highlighted = highlightCode(code, language);
    setHighlightedCode(highlighted);
  }, [code, language]);

  // 复制代码到剪贴板
  const handleCopy = async () => {
    if (!copyable) return;
    
    const success = await copyToClipboard(code);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // 生成行号
  const generateLineNumbers = () => {
    if (!showLineNumbers) return null;
    
    const lines = code.split('\n');
    return (
      <div className="flex flex-col text-mysql-text-light text-sm font-mono leading-6 pr-4 border-r border-mysql-border select-none">
        {lines.map((_, index) => (
          <span key={index + 1} className="text-right">
            {index + 1}
          </span>
        ))}
      </div>
    );
  };

  // 基础样式
  const containerStyles = cn(
    'relative group rounded-lg border border-mysql-border bg-white shadow-sm overflow-hidden',
    'hover:shadow-md transition-all duration-300',
    className
  );

  // 头部样式
  const headerStyles = cn(
    'flex items-center justify-between px-4 py-3',
    'bg-mysql-primary-light border-b border-mysql-border'
  );

  // 代码容器样式
  const codeContainerStyles = cn(
    'relative overflow-x-auto',
    'bg-gray-50'
  );

  // 代码样式
  const codeStyles = cn(
    'block p-4 font-mono text-sm leading-6',
    'text-mysql-text bg-transparent',
    showLineNumbers ? 'pl-0' : 'pl-4'
  );

  return (
    <motion.div
      ref={ref}
      className={containerStyles}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      {/* 头部 */}
      {(title || copyable) && (
        <div className={headerStyles}>
          <div className="flex items-center space-x-2">
            <Code className="w-4 h-4 text-mysql-primary" />
            {title && (
              <span className="text-sm font-medium text-mysql-text">
                {title}
              </span>
            )}
            {!title && (
              <span className="text-sm text-mysql-text-light">
                {language.toUpperCase()}
              </span>
            )}
          </div>
          
          {copyable && (
            <motion.button
              onClick={handleCopy}
              className={cn(
                'flex items-center space-x-1 px-2 py-1 rounded text-xs',
                'bg-white border border-mysql-border',
                'hover:bg-mysql-primary hover:text-white hover:border-mysql-primary',
                'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
                'transition-all duration-200'
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={false}
            >
              {copied ? (
                <>
                  <Check className="w-3 h-3" />
                  <span>已复制</span>
                </>
              ) : (
                <>
                  <Copy className="w-3 h-3" />
                  <span>复制</span>
                </>
              )}
            </motion.button>
          )}
        </div>
      )}

      {/* 代码内容 */}
      <div className={codeContainerStyles}>
        <div className="flex">
          {/* 行号 */}
          {generateLineNumbers()}
          
          {/* 代码 */}
          <div className="flex-1 overflow-x-auto">
            <pre className={codeStyles}>
              <code
                className={`language-${language}`}
                dangerouslySetInnerHTML={{ __html: highlightedCode }}
              />
            </pre>
          </div>
        </div>
      </div>

      {/* 语言标识 */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <span className="px-2 py-1 text-xs bg-mysql-primary text-white rounded">
          {language.toUpperCase()}
        </span>
      </div>
    </motion.div>
  );
});

CodeBlock.displayName = 'CodeBlock';

export default CodeBlock;
