'use client';

// MySQLAi.de - 知识库管理模块主入口
// 提供知识库模块的概览和快速操作入口

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  FileText,
  FolderOpen,
  Code,
  BarChart3,
  Search,
  Edit
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { articlesApi, categoriesApi, codeExamplesApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import StatsOverview from '@/components/admin/StatsOverview';

// 知识库统计数据类型
interface KnowledgeStats {
  totalArticles: number;
  totalCategories: number;
  totalCodeExamples: number;
  totalSearches: number;
  totalViews: number;
  totalDownloads: number;
  articlesThisMonth: number;
  searchesThisMonth: number;
}

// 快速操作配置
const QUICK_ACTIONS = [
  {
    title: '创建新文章',
    description: '添加新的知识库文章',
    href: '/admin/knowledge/articles/new',
    icon: FileText,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-200'
  },
  {
    title: '管理分类',
    description: '组织和管理文章分类',
    href: '/admin/knowledge/categories',
    icon: FolderOpen,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200'
  },
  {
    title: '添加代码示例',
    description: '创建新的代码示例',
    href: '/admin/knowledge/code-examples',
    icon: Code,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    borderColor: 'border-purple-200'
  },
  {
    title: '查看统计',
    description: '分析知识库使用情况',
    href: '/admin/stats',
    icon: BarChart3,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    borderColor: 'border-orange-200'
  }
];

// 模块导航配置
const MODULE_NAVIGATION = [
  {
    title: '文章管理',
    description: '管理知识库文章内容',
    href: '/admin/knowledge/articles',
    icon: FileText,
    stats: '文章',
    color: 'text-blue-600'
  },
  {
    title: '分类管理',
    description: '组织文章分类结构',
    href: '/admin/knowledge/categories',
    icon: FolderOpen,
    stats: '分类',
    color: 'text-green-600'
  },
  {
    title: '代码示例',
    description: '管理代码片段和示例',
    href: '/admin/knowledge/code-examples',
    icon: Code,
    stats: '示例',
    color: 'text-purple-600'
  }
];

export default function KnowledgePage() {
  const [stats, setStats] = useState<KnowledgeStats>({
    totalArticles: 0,
    totalCategories: 0,
    totalCodeExamples: 0,
    totalSearches: 0,
    totalViews: 0,
    totalDownloads: 0,
    articlesThisMonth: 0,
    searchesThisMonth: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      setError('');

      // 并行获取各模块的统计数据
      const [articlesResponse, categoriesResponse, codeExamplesResponse] = await Promise.all([
        articlesApi.getAll({ limit: 1 }),
        categoriesApi.getAll(),
        codeExamplesApi.getAll({ limit: 1 })
      ]);

      // 模拟其他统计数据
      const mockStats: KnowledgeStats = {
        totalArticles: articlesResponse.success ? (articlesResponse.pagination?.total || 0) : 0,
        totalCategories: categoriesResponse.success ? (categoriesResponse.data?.length || 0) : 0,
        totalCodeExamples: codeExamplesResponse.success ? (codeExamplesResponse.pagination?.total || 0) : 0,
        totalSearches: 1250,
        totalViews: 8900,
        totalDownloads: 450,
        articlesThisMonth: 2,
        searchesThisMonth: 320
      };

      setStats(mockStats);
    } catch (error) {
      console.error('获取统计数据失败:', error);
      setError('获取统计数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-mysql-text mb-2">
            知识库管理
          </h1>
          <p className="text-mysql-text-light">
            统一管理文章、分类和代码示例，构建完整的知识体系
          </p>
        </div>
        
        <Button
          variant="primary"
          icon={<Search className="w-5 h-5" />}
          className="shadow-lg"
        >
          搜索内容
        </Button>
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <p className="text-red-800">{error}</p>
        </motion.div>
      )}

      {/* 统计概览 */}
      <div>
        <h2 className="text-xl font-semibold text-mysql-text mb-4">
          数据概览
        </h2>
        <StatsOverview data={stats} loading={loading} />
      </div>

      {/* 快速操作 */}
      <div>
        <h2 className="text-xl font-semibold text-mysql-text mb-4">
          快速操作
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {QUICK_ACTIONS.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Link href={action.href}>
                <div className={cn(
                  'p-6 rounded-lg border-2 transition-all duration-300 cursor-pointer',
                  'hover:shadow-lg hover:scale-105',
                  action.borderColor,
                  action.bgColor
                )}>
                  <div className="flex items-center space-x-3 mb-3">
                    <action.icon className={cn('w-6 h-6', action.color)} />
                    <h3 className="font-semibold text-mysql-text">
                      {action.title}
                    </h3>
                  </div>
                  <p className="text-sm text-mysql-text-light">
                    {action.description}
                  </p>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>

      {/* 模块导航 */}
      <div>
        <h2 className="text-xl font-semibold text-mysql-text mb-4">
          功能模块
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {MODULE_NAVIGATION.map((module, index) => (
            <motion.div
              key={module.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-md border border-mysql-border p-6 hover:shadow-lg transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <module.icon className={cn('w-8 h-8', module.color)} />
                  <div>
                    <h3 className="text-lg font-semibold text-mysql-text">
                      {module.title}
                    </h3>
                    <p className="text-sm text-mysql-text-light">
                      {module.description}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-mysql-text">
                  {module.stats === '文章' ? stats.totalArticles :
                   module.stats === '分类' ? stats.totalCategories :
                   stats.totalCodeExamples}
                  <span className="text-sm font-normal text-mysql-text-light ml-1">
                    {module.stats}
                  </span>
                </div>
                
                <Link href={module.href}>
                  <Button
                    variant="outline"
                    size="sm"
                    icon={<Edit className="w-4 h-4" />}
                  >
                    管理
                  </Button>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
