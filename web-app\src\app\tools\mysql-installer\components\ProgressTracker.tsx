'use client';

/**
 * 安装进度跟踪组件
 * 显示当前安装进度和步骤状态
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  Circle, 
  AlertCircle, 
  Loader2,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { InstallationStatus, InstallationStep } from '../types/mysql-installer';

interface ProgressTrackerProps {
  currentStatus: InstallationStatus;
  steps: InstallationStep[];
  currentStepId?: string;
  className?: string;
}

/**
 * 进度跟踪组件
 */
export default function ProgressTracker({
  currentStatus,
  steps,
  currentStepId,
  className
}: ProgressTrackerProps) {
  // 获取步骤状态
  const getStepStatus = (step: InstallationStep, index: number) => {
    if (step.completed) {
      return 'completed';
    }
    
    if (step.id === currentStepId) {
      return 'current';
    }
    
    // 如果当前步骤之前的步骤，且当前状态不是错误
    if (currentStatus !== InstallationStatus.ERROR) {
      const currentIndex = steps.findIndex(s => s.id === currentStepId);
      if (currentIndex > index) {
        return 'completed';
      }
    }
    
    return 'pending';
  };

  // 获取整体进度百分比
  const getProgressPercentage = () => {
    const completedSteps = steps.filter(step => step.completed).length;
    const totalSteps = steps.length;
    
    if (currentStepId && !steps.find(s => s.id === currentStepId)?.completed) {
      const currentIndex = steps.findIndex(s => s.id === currentStepId);
      if (currentIndex >= 0) {
        return ((completedSteps + 0.5) / totalSteps) * 100;
      }
    }
    
    return (completedSteps / totalSteps) * 100;
  };

  // 渲染步骤图标
  const renderStepIcon = (step: InstallationStep, status: string) => {
    const iconClasses = "w-6 h-6";
    
    switch (status) {
      case 'completed':
        return <CheckCircle className={cn(iconClasses, "text-green-600")} />;
      case 'current':
        return <Loader2 className={cn(iconClasses, "text-mysql-primary animate-spin")} />;
      case 'error':
        return <AlertCircle className={cn(iconClasses, "text-red-600")} />;
      default:
        return <Circle className={cn(iconClasses, "text-gray-400")} />;
    }
  };

  // 渲染步骤状态文本
  const renderStepStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return <span className="text-green-600 text-sm font-medium">已完成</span>;
      case 'current':
        return <span className="text-mysql-primary text-sm font-medium">进行中</span>;
      case 'error':
        return <span className="text-red-600 text-sm font-medium">失败</span>;
      default:
        return <span className="text-gray-500 text-sm">等待中</span>;
    }
  };

  const progressPercentage = getProgressPercentage();

  return (
    <div className={cn('bg-white rounded-xl shadow-lg border border-gray-200 p-6', className)}>
      {/* 标题和整体进度 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-mysql-text">安装进度</h3>
          <span className="text-sm text-gray-600">
            {Math.round(progressPercentage)}% 完成
          </span>
        </div>
        
        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-mysql-primary rounded-full h-2"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* 步骤列表 */}
      <div className="space-y-4">
        {steps.map((step, index) => {
          const status = getStepStatus(step, index);
          const isLast = index === steps.length - 1;
          
          return (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              {/* 连接线 */}
              {!isLast && (
                <div className="absolute left-3 top-8 w-0.5 h-8 bg-gray-200" />
              )}
              
              <div className="flex items-start space-x-4">
                {/* 步骤图标 */}
                <div className="flex-shrink-0 relative z-10">
                  {renderStepIcon(step, status)}
                </div>
                
                {/* 步骤内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={cn(
                      "font-medium",
                      status === 'completed' ? "text-green-800" :
                      status === 'current' ? "text-mysql-primary" :
                      status === 'error' ? "text-red-800" :
                      "text-gray-700"
                    )}>
                      {step.title}
                    </h4>
                    {renderStepStatusText(status)}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    {step.description}
                  </p>
                  
                  {/* 预计时间 */}
                  {step.estimatedTime && status !== 'completed' && (
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      <span>预计 {step.estimatedTime} 分钟</span>
                    </div>
                  )}
                  
                  {/* 当前步骤的额外信息 */}
                  {status === 'current' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200"
                    >
                      <p className="text-sm text-blue-800">
                        正在执行此步骤，请稍候...
                      </p>
                    </motion.div>
                  )}
                  
                  {/* 已完成步骤的成功信息 */}
                  {status === 'completed' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-2 p-2 bg-green-50 rounded border border-green-200"
                    >
                      <p className="text-xs text-green-700">
                        ✓ 步骤已完成
                      </p>
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* 底部状态信息 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        {currentStatus === InstallationStatus.COMPLETED && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-green-600"
          >
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">所有步骤已完成！</span>
          </motion.div>
        )}
        
        {currentStatus === InstallationStatus.ERROR && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-red-600"
          >
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">安装过程中出现错误</span>
          </motion.div>
        )}
        
        {(currentStatus === InstallationStatus.GENERATING_CONFIG ||
          currentStatus === InstallationStatus.DOWNLOADING ||
          currentStatus === InstallationStatus.INSTALLING) && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-mysql-primary"
          >
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="font-medium">安装正在进行中...</span>
          </motion.div>
        )}
      </div>
    </div>
  );
}
