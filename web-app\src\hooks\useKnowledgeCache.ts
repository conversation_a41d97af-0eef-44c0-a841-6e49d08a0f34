'use client';

// MySQLAi.de - 知识库数据缓存Hook
// 提供智能缓存管理、数据同步和性能优化

import { useState, useEffect, useCallback, useRef } from 'react';
import { DataType } from './useKnowledgeData';

// 缓存项类型
interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expiry: number;
  key: string;
  version: number;
  metadata?: {
    source?: string;
    tags?: string[];
    dependencies?: string[];
  };
}

// 缓存配置
interface CacheConfig {
  // 默认过期时间（毫秒）
  defaultTTL?: number;
  // 最大缓存项数量
  maxItems?: number;
  // 清理间隔（毫秒）
  cleanupInterval?: number;
  // 启用持久化
  persistent?: boolean;
  // 存储键前缀
  storagePrefix?: string;
  // 调试模式
  debug?: boolean;
}

// Hook选项
interface UseKnowledgeCacheOptions extends CacheConfig {
  // 自动清理过期项
  autoCleanup?: boolean;
  // 预加载相关数据
  preloadRelated?: boolean;
  // 后台同步
  backgroundSync?: boolean;
}

// 缓存统计
interface CacheStats {
  totalItems: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  memoryUsage: number;
  oldestItem: number;
  newestItem: number;
}

// Hook返回类型
interface UseKnowledgeCacheReturn {
  // 缓存操作
  get: <T>(key: string) => T | null;
  set: <T>(key: string, data: T, ttl?: number, metadata?: any) => void;
  remove: (key: string) => void;
  clear: () => void;
  
  // 批量操作
  getMultiple: <T>(keys: string[]) => (T | null)[];
  setMultiple: <T>(items: { key: string; data: T; ttl?: number }[]) => void;
  removeMultiple: (keys: string[]) => void;
  
  // 缓存管理
  invalidate: (pattern?: string) => void;
  refresh: (key: string) => Promise<void>;
  preload: (keys: string[]) => Promise<void>;
  
  // 工具方法
  has: (key: string) => boolean;
  isExpired: (key: string) => boolean;
  getExpiry: (key: string) => number | null;
  getTTL: (key: string) => number | null;
  
  // 统计信息
  getStats: () => CacheStats;
  getKeys: () => string[];
  getSize: () => number;
  
  // 持久化
  save: () => void;
  load: () => void;
  export: () => string;
  import: (data: string) => void;
}

// 默认配置
const DEFAULT_CONFIG: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxItems: 1000,
  cleanupInterval: 60 * 1000, // 1分钟
  persistent: true,
  storagePrefix: 'knowledge_cache_',
  debug: false
};

// 生成缓存键
const generateCacheKey = (dataType: DataType, params?: any): string => {
  const baseKey = `${dataType}`;
  if (!params) return baseKey;
  
  const paramString = JSON.stringify(params, Object.keys(params).sort());
  const hash = btoa(paramString).replace(/[+/=]/g, '');
  return `${baseKey}_${hash}`;
};

export function useKnowledgeCache(
  options: UseKnowledgeCacheOptions = {}
): UseKnowledgeCacheReturn {
  const config = { ...DEFAULT_CONFIG, ...options };
  
  // 缓存存储
  const [cache, setCache] = useState<Map<string, CacheItem>>(new Map());
  
  // 统计信息
  const [stats, setStats] = useState({
    hitCount: 0,
    missCount: 0
  });
  
  // 引用管理
  const cleanupIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 调试日志
  const log = useCallback((message: string, data?: any) => {
    if (config.debug) {
      console.log(`[useKnowledgeCache] ${message}`, data);
    }
  }, [config.debug]);

  // 获取缓存项
  const get = useCallback(<T>(key: string): T | null => {
    const item = cache.get(key);
    
    if (!item) {
      setStats(prev => ({ ...prev, missCount: prev.missCount + 1 }));
      log('Cache miss', { key });
      return null;
    }
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      cache.delete(key);
      setCache(new Map(cache));
      setStats(prev => ({ ...prev, missCount: prev.missCount + 1 }));
      log('Cache expired', { key, expiry: item.expiry });
      return null;
    }
    
    setStats(prev => ({ ...prev, hitCount: prev.hitCount + 1 }));
    log('Cache hit', { key });
    return item.data as T;
  }, [cache, log]);

  // 设置缓存项
  const set = useCallback(<T>(
    key: string, 
    data: T, 
    ttl: number = config.defaultTTL!, 
    metadata?: any
  ): void => {
    const now = Date.now();
    const item: CacheItem<T> = {
      data,
      timestamp: now,
      expiry: now + ttl,
      key,
      version: 1,
      metadata
    };
    
    // 检查缓存大小限制
    if (cache.size >= config.maxItems!) {
      // 删除最旧的项目
      const oldestKey = Array.from(cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)[0][0];
      cache.delete(oldestKey);
      log('Cache eviction', { evictedKey: oldestKey, newKey: key });
    }
    
    cache.set(key, item);
    setCache(new Map(cache));
    log('Cache set', { key, ttl, dataSize: JSON.stringify(data).length });
  }, [cache, config.defaultTTL, config.maxItems, log]);

  // 删除缓存项
  const remove = useCallback((key: string): void => {
    const deleted = cache.delete(key);
    if (deleted) {
      setCache(new Map(cache));
      log('Cache remove', { key });
    }
  }, [cache, log]);

  // 清空缓存
  const clear = useCallback((): void => {
    cache.clear();
    setCache(new Map());
    setStats({ hitCount: 0, missCount: 0 });
    log('Cache cleared');
  }, [cache, log]);

  // 批量获取
  const getMultiple = useCallback(<T>(keys: string[]): (T | null)[] => {
    return keys.map(key => get<T>(key));
  }, [get]);

  // 批量设置
  const setMultiple = useCallback(<T>(
    items: { key: string; data: T; ttl?: number }[]
  ): void => {
    items.forEach(({ key, data, ttl }) => {
      set(key, data, ttl);
    });
  }, [set]);

  // 批量删除
  const removeMultiple = useCallback((keys: string[]): void => {
    keys.forEach(key => remove(key));
  }, [remove]);

  // 使缓存失效（支持模式匹配）
  const invalidate = useCallback((pattern?: string): void => {
    if (!pattern) {
      clear();
      return;
    }
    
    const regex = new RegExp(pattern);
    const keysToRemove = Array.from(cache.keys()).filter(key => regex.test(key));
    
    keysToRemove.forEach(key => cache.delete(key));
    setCache(new Map(cache));
    
    log('Cache invalidated', { pattern, removedCount: keysToRemove.length });
  }, [cache, clear, log]);

  // 刷新缓存项
  const refresh = useCallback(async (key: string): Promise<void> => {
    // 这里需要根据key重新获取数据
    // 实际实现中需要与useKnowledgeData配合
    remove(key);
    log('Cache refreshed', { key });
  }, [remove, log]);

  // 预加载数据
  const preload = useCallback(async (keys: string[]): Promise<void> => {
    // 预加载逻辑，实际实现中需要与数据获取逻辑配合
    log('Cache preload', { keys });
  }, [log]);

  // 检查是否存在
  const has = useCallback((key: string): boolean => {
    const item = cache.get(key);
    return item !== undefined && Date.now() <= item.expiry;
  }, [cache]);

  // 检查是否过期
  const isExpired = useCallback((key: string): boolean => {
    const item = cache.get(key);
    return item ? Date.now() > item.expiry : true;
  }, [cache]);

  // 获取过期时间
  const getExpiry = useCallback((key: string): number | null => {
    const item = cache.get(key);
    return item ? item.expiry : null;
  }, [cache]);

  // 获取剩余TTL
  const getTTL = useCallback((key: string): number | null => {
    const item = cache.get(key);
    if (!item) return null;
    
    const remaining = item.expiry - Date.now();
    return remaining > 0 ? remaining : 0;
  }, [cache]);

  // 获取统计信息
  const getStats = useCallback((): CacheStats => {
    const items = Array.from(cache.values());
    const totalHits = stats.hitCount + stats.missCount;
    
    return {
      totalItems: cache.size,
      hitCount: stats.hitCount,
      missCount: stats.missCount,
      hitRate: totalHits > 0 ? stats.hitCount / totalHits : 0,
      memoryUsage: JSON.stringify(Array.from(cache.entries())).length,
      oldestItem: items.length > 0 ? Math.min(...items.map(i => i.timestamp)) : 0,
      newestItem: items.length > 0 ? Math.max(...items.map(i => i.timestamp)) : 0
    };
  }, [cache, stats]);

  // 获取所有键
  const getKeys = useCallback((): string[] => {
    return Array.from(cache.keys());
  }, [cache]);

  // 获取缓存大小
  const getSize = useCallback((): number => {
    return cache.size;
  }, [cache]);

  // 保存到本地存储
  const save = useCallback((): void => {
    if (!config.persistent || typeof window === 'undefined') return;
    
    try {
      const data = JSON.stringify(Array.from(cache.entries()));
      localStorage.setItem(`${config.storagePrefix}data`, data);
      localStorage.setItem(`${config.storagePrefix}stats`, JSON.stringify(stats));
      log('Cache saved to localStorage');
    } catch (error) {
      log('Failed to save cache', error);
    }
  }, [cache, stats, config.persistent, config.storagePrefix, log]);

  // 从本地存储加载
  const load = useCallback((): void => {
    if (!config.persistent || typeof window === 'undefined') return;
    
    try {
      const data = localStorage.getItem(`${config.storagePrefix}data`);
      const savedStats = localStorage.getItem(`${config.storagePrefix}stats`);
      
      if (data) {
        const entries = JSON.parse(data);
        const loadedCache = new Map<string, CacheItem<any>>(entries);

        // 清理过期项
        const now = Date.now();
        for (const [key, item] of loadedCache.entries()) {
          if (now > item.expiry) {
            loadedCache.delete(key);
          }
        }

        setCache(loadedCache);
        log('Cache loaded from localStorage', { items: loadedCache.size });
      }
      
      if (savedStats) {
        setStats(JSON.parse(savedStats));
      }
    } catch (error) {
      log('Failed to load cache', error);
    }
  }, [config.persistent, config.storagePrefix, log]);

  // 导出缓存
  const exportCache = useCallback((): string => {
    return JSON.stringify({
      cache: Array.from(cache.entries()),
      stats,
      timestamp: Date.now()
    });
  }, [cache, stats]);

  // 导入缓存
  const importCache = useCallback((data: string): void => {
    try {
      const parsed = JSON.parse(data);
      const importedCache = new Map<string, CacheItem<any>>(parsed.cache);
      
      setCache(importedCache);
      setStats(parsed.stats || { hitCount: 0, missCount: 0 });
      log('Cache imported', { items: importedCache.size });
    } catch (error) {
      log('Failed to import cache', error);
    }
  }, [log]);

  // 清理过期项
  const cleanup = useCallback((): void => {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, item] of cache.entries()) {
      if (now > item.expiry) {
        cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      setCache(new Map(cache));
      log('Cache cleanup', { cleanedCount });
    }
  }, [cache, log]);

  // 设置清理定时器
  useEffect(() => {
    if (config.cleanupInterval && config.cleanupInterval > 0) {
      cleanupIntervalRef.current = setInterval(cleanup, config.cleanupInterval);
    }
    
    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
      }
    };
  }, [config.cleanupInterval, cleanup]);

  // 加载持久化数据
  useEffect(() => {
    load();
  }, [load]);

  // 保存数据到本地存储
  useEffect(() => {
    if (config.persistent) {
      save();
    }
  }, [cache, stats, config.persistent, save]);

  return {
    // 缓存操作
    get,
    set,
    remove,
    clear,
    
    // 批量操作
    getMultiple,
    setMultiple,
    removeMultiple,
    
    // 缓存管理
    invalidate,
    refresh,
    preload,
    
    // 工具方法
    has,
    isExpired,
    getExpiry,
    getTTL,
    
    // 统计信息
    getStats,
    getKeys,
    getSize,
    
    // 持久化
    save,
    load,
    export: exportCache,
    import: importCache
  };
}

// 导出缓存键生成函数
export { generateCacheKey };
