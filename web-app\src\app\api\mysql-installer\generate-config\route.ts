// MySQLAi.de - MySQL配置生成 API
// 提供配置文件生成和下载功能

import { NextRequest, NextResponse } from 'next/server';
import { 
  SupportedOS, 
  QuickInstallConfig, 
  AdvancedConfig 
} from '@/app/tools/mysql-installer/types/mysql-installer';
import { 
  generateMySQLConfig, 
  validateConfigParameters,
  generateDefaultConfig 
} from '@/app/tools/mysql-installer/lib/config-generator';

/**
 * 验证配置参数
 */
function validateConfig(config: QuickInstallConfig | AdvancedConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // 基本验证
  if (!config.version) {
    errors.push('MySQL版本不能为空');
  }
  
  if (!config.targetOS) {
    errors.push('目标操作系统不能为空');
  }
  
  if (!config.installPath?.trim()) {
    errors.push('安装路径不能为空');
  }
  
  if (!config.dataPath?.trim()) {
    errors.push('数据目录不能为空');
  }
  
  if (!config.port || config.port < 1024 || config.port > 65535) {
    errors.push('端口号必须在1024-65535范围内');
  }
  
  if (!config.charset?.trim()) {
    errors.push('字符集不能为空');
  }
  
  if (!config.rootPassword || config.rootPassword.length < 6) {
    errors.push('root密码长度至少6位');
  }
  
  // 高级配置验证
  if ('maxConnections' in config) {
    const advancedConfig = config as AdvancedConfig;
    
    if (advancedConfig.maxConnections && 
        (advancedConfig.maxConnections < 10 || advancedConfig.maxConnections > 10000)) {
      errors.push('最大连接数应在10-10000范围内');
    }
    
    if (advancedConfig.longQueryTime && 
        (advancedConfig.longQueryTime < 0 || advancedConfig.longQueryTime > 3600)) {
      errors.push('慢查询时间阈值应在0-3600秒范围内');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 生成配置文件名
 */
function getConfigFileName(os: SupportedOS): string {
  return os === SupportedOS.WINDOWS ? 'my.ini' : 'my.cnf';
}

/**
 * 生成配置文件响应头
 */
function getConfigFileHeaders(filename: string) {
  return {
    'Content-Type': 'text/plain; charset=utf-8',
    'Content-Disposition': `attachment; filename="${filename}"`,
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };
}

// POST /api/mysql-installer/generate-config - 生成配置文件
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { config, format = 'json' } = body;
    
    if (!config) {
      return NextResponse.json(
        { success: false, error: '缺少配置参数' },
        { status: 400 }
      );
    }
    
    // 验证配置
    const validation = validateConfig(config);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: '配置参数验证失败',
          details: validation.errors
        },
        { status: 400 }
      );
    }
    
    // 生成配置文件内容
    const configContent = generateMySQLConfig(config);
    const filename = getConfigFileName(config.targetOS);
    
    // 根据请求格式返回不同响应
    if (format === 'file') {
      // 返回文件下载
      return new NextResponse(configContent, {
        status: 200,
        headers: getConfigFileHeaders(filename)
      });
    } else {
      // 返回JSON响应
      return NextResponse.json({
        success: true,
        data: {
          content: configContent,
          filename,
          size: Buffer.byteLength(configContent, 'utf8'),
          generatedAt: new Date().toISOString(),
          config: {
            version: config.version,
            targetOS: config.targetOS,
            port: config.port,
            charset: config.charset
          }
        }
      });
    }
    
  } catch (error) {
    console.error('配置生成API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '配置生成失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// GET /api/mysql-installer/generate-config - 获取默认配置
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const os = searchParams.get('os') as SupportedOS;
    const format = searchParams.get('format') || 'json';
    
    if (!os || !Object.values(SupportedOS).includes(os)) {
      return NextResponse.json(
        { success: false, error: '无效的操作系统参数' },
        { status: 400 }
      );
    }
    
    // 生成默认配置
    const defaultConfig = generateDefaultConfig(os);
    const configContent = generateMySQLConfig(defaultConfig);
    const filename = getConfigFileName(os);
    
    if (format === 'file') {
      // 返回文件下载
      return new NextResponse(configContent, {
        status: 200,
        headers: getConfigFileHeaders(filename)
      });
    } else {
      // 返回JSON响应
      return NextResponse.json({
        success: true,
        data: {
          config: defaultConfig,
          content: configContent,
          filename,
          size: Buffer.byteLength(configContent, 'utf8'),
          generatedAt: new Date().toISOString()
        }
      });
    }
    
  } catch (error) {
    console.error('默认配置获取API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '默认配置获取失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// PUT /api/mysql-installer/generate-config/validate - 验证配置参数
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { config } = body;
    
    if (!config) {
      return NextResponse.json(
        { success: false, error: '缺少配置参数' },
        { status: 400 }
      );
    }
    
    // 验证配置
    const validation = validateConfig(config);
    
    // 使用库函数进行更详细的验证
    let detailedValidation;
    try {
      detailedValidation = validateConfigParameters(config);
    } catch (error) {
      detailedValidation = {
        isValid: false,
        errors: [error instanceof Error ? error.message : '配置验证失败']
      };
    }
    
    return NextResponse.json({
      success: true,
      data: {
        isValid: validation.isValid && detailedValidation.isValid,
        basicValidation: validation,
        detailedValidation,
        recommendations: [
          '建议定期备份配置文件',
          '确保端口未被其他服务占用',
          '使用强密码保护数据库安全'
        ]
      }
    });
    
  } catch (error) {
    console.error('配置验证API错误:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '配置验证失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
