'use client';

// MySQLAi.de - Footer页脚组件
// 包含版权信息、导航链接、社交媒体链接、法律声明等内容

import React from 'react';
import { motion } from 'framer-motion';
import { Github, Twitter, Linkedin, Mail, Phone, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FooterProps {
  className?: string;
}

// 页脚导航数据
const FOOTER_NAVIGATION = {
  products: {
    title: '产品服务',
    links: [
      { name: 'MySQL知识库', href: '/knowledge' },
      { name: '项目管理', href: '/projects' },
      { name: '报告展示', href: '/reports' },
      { name: 'AI智能分析', href: '/ai-analysis' },
    ],
  },
  company: {
    title: '关于我们',
    links: [
      { name: '公司介绍', href: '/about' },
      { name: '团队成员', href: '/team' },
      { name: '新闻动态', href: '/news' },
      { name: '招聘信息', href: '/careers' },
    ],
  },
  support: {
    title: '技术支持',
    links: [
      { name: '帮助中心', href: '/help' },
      { name: '技术文档', href: '/docs' },
      { name: '联系我们', href: '/contact' },
      { name: '在线客服', href: '/chat' },
    ],
  },
  legal: {
    title: '法律声明',
    links: [
      { name: '服务条款', href: '/terms' },
      { name: '隐私政策', href: '/privacy' },
      { name: '免责声明', href: '/disclaimer' },
      { name: 'Cookie政策', href: '/cookies' },
    ],
  },
} as const;

// 社交媒体链接
const SOCIAL_LINKS = [
  {
    name: 'GitHub',
    href: 'https://github.com/mysqlai',
    icon: Github,
    color: 'hover:text-gray-900',
  },
  {
    name: 'Twitter',
    href: 'https://twitter.com/mysqlai',
    icon: Twitter,
    color: 'hover:text-blue-400',
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/company/mysqlai',
    icon: Linkedin,
    color: 'hover:text-blue-600',
  },
  {
    name: 'Email',
    href: 'mailto:<EMAIL>',
    icon: Mail,
    color: 'hover:text-mysql-primary',
  },
] as const;

// 联系信息
const CONTACT_INFO = [
  {
    icon: Phone,
    text: '+86 ************',
    href: 'tel:+8640088899999',
  },
  {
    icon: Mail,
    text: '<EMAIL>',
    href: 'mailto:<EMAIL>',
  },
  {
    icon: MapPin,
    text: '北京市朝阳区科技园区',
    href: '#',
  },
] as const;

export default function Footer({ className }: FooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      className={cn(
        'bg-gradient-to-b from-mysql-primary-dark to-mysql-primary-dark/90 text-white',
        className
      )}
    >
      {/* 极简风格主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* 品牌和导航 - 极简一行布局 */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
          {/* 品牌信息 */}
          <div className="text-center lg:text-left">
            <h3 className="text-xl font-bold text-white mb-2">MySQLAi.de</h3>
            <p className="text-mysql-primary-light text-sm max-w-md">
              专业的MySQL智能分析平台
            </p>
          </div>

          {/* 导航链接 - 极简水平布局 */}
          <div className="flex flex-wrap justify-center lg:justify-end gap-8">
            {Object.entries(FOOTER_NAVIGATION).map(([key, section]) => (
              <div key={key} className="flex flex-col items-center lg:items-start">
                <h4 className="text-white font-medium text-sm mb-3">{section.title}</h4>
                <div className="flex flex-col space-y-2">
                  {section.links.slice(0, 3).map((link, linkIndex) => (
                    <a
                      key={linkIndex}
                      href={link.href}
                      className="text-mysql-primary-light hover:text-white transition-colors duration-200 text-xs"
                    >
                      {link.name}
                    </a>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 极简分隔线 */}
      <div className="border-t border-mysql-primary-light/10" />

      {/* 极简底部区域 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* 简化版权信息 */}
          <p className="text-mysql-primary-light/70 text-xs">
            © {currentYear} MySQLAi.de
          </p>

          {/* 简化联系方式 */}
          <div className="flex items-center gap-6 text-xs">
            <a
              href="mailto:<EMAIL>"
              className="text-mysql-primary-light/70 hover:text-white transition-colors duration-200"
            >
              <EMAIL>
            </a>
            <a
              href="tel:+8640088899999"
              className="text-mysql-primary-light/70 hover:text-white transition-colors duration-200"
            >
              +86 ************
            </a>
          </div>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      <motion.button
        initial={{ opacity: 0, scale: 0.8 }}
        whileInView={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.1, y: -2 }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        viewport={{ once: true }}
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        className="fixed bottom-8 right-8 p-3 bg-mysql-primary text-white rounded-full shadow-lg hover:bg-mysql-primary-dark transition-colors duration-300 focus:outline-none focus:ring-4 focus:ring-mysql-primary/30 z-50"
        aria-label="回到顶部"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      </motion.button>
    </footer>
  );
}
