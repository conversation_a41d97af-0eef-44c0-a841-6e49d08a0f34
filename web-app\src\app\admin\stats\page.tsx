'use client';

// MySQLAi.de - 统计分析页面
// 显示知识库的各种统计数据和分析图表

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  Download, 
  RefreshCw, 
  Calendar,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
// import { cn } from '@/lib/utils';
// import { statsApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import StatsOverview from '@/components/admin/StatsOverview';
import StatsChart from '@/components/admin/StatsChart';

// 统计数据类型
interface StatsData {
  totalArticles: number;
  totalCategories: number;
  totalCodeExamples: number;
  totalSearches: number;
  totalViews: number;
  totalDownloads: number;
  articlesThisMonth: number;
  searchesThisMonth: number;
}

interface ChartData {
  name: string;
  value: number;
}

export default function StatsPage() {
  const [statsData, setStatsData] = useState<StatsData>({
    totalArticles: 0,
    totalCategories: 0,
    totalCodeExamples: 0,
    totalSearches: 0,
    totalViews: 0,
    totalDownloads: 0,
    articlesThisMonth: 0,
    searchesThisMonth: 0
  });
  
  const [monthlyData, setMonthlyData] = useState<ChartData[]>([]);
  const [categoryData, setCategoryData] = useState<ChartData[]>([]);
  const [searchKeywords, setSearchKeywords] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      setError('');

      // 模拟API调用 - 实际项目中应该调用真实的statsApi
      // const response = await statsApi.getOverview();
      
      // 模拟数据
      const mockStatsData: StatsData = {
        totalArticles: 5,
        totalCategories: 6,
        totalCodeExamples: 8,
        totalSearches: 1250,
        totalViews: 8900,
        totalDownloads: 450,
        articlesThisMonth: 2,
        searchesThisMonth: 320
      };

      const mockMonthlyData: ChartData[] = [
        { name: '1月', value: 65 },
        { name: '2月', value: 78 },
        { name: '3月', value: 90 },
        { name: '4月', value: 81 },
        { name: '5月', value: 95 },
        { name: '6月', value: 120 }
      ];

      const mockCategoryData: ChartData[] = [
        { name: '基础知识', value: 2 },
        { name: '数据库操作', value: 1 },
        { name: '查询优化', value: 1 },
        { name: '安全配置', value: 0 },
        { name: '性能调优', value: 1 },
        { name: '故障排除', value: 0 }
      ];

      const mockSearchKeywords: ChartData[] = [
        { name: 'SELECT', value: 450 },
        { name: 'JOIN', value: 320 },
        { name: 'INDEX', value: 280 },
        { name: 'CREATE', value: 250 },
        { name: 'UPDATE', value: 200 },
        { name: 'DELETE', value: 180 },
        { name: 'ALTER', value: 150 },
        { name: 'INSERT', value: 120 }
      ];

      setStatsData(mockStatsData);
      setMonthlyData(mockMonthlyData);
      setCategoryData(mockCategoryData);
      setSearchKeywords(mockSearchKeywords);
      setLastUpdated(new Date());

    } catch (error) {
      console.error('获取统计数据失败:', error);
      setError('获取统计数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStats();
  }, []);

  // 导出数据
  const handleExportData = () => {
    const exportData = {
      overview: statsData,
      monthlyTrend: monthlyData,
      categoryDistribution: categoryData,
      searchKeywords: searchKeywords,
      exportTime: new Date().toISOString()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `mysqlai-stats-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-mysql-text mb-2">
            统计分析
          </h1>
          <p className="text-mysql-text-light">
            知识库数据统计和分析报告
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-sm text-mysql-text-light">
            <Calendar className="w-4 h-4 inline mr-1" />
            更新时间: {lastUpdated.toLocaleString()}
          </div>
          <Button
            variant="outline"
            onClick={fetchStats}
            loading={loading}
            icon={<RefreshCw className="w-4 h-4" />}
          >
            刷新数据
          </Button>
          <Button
            variant="primary"
            onClick={handleExportData}
            icon={<Download className="w-4 h-4" />}
            disabled={loading}
          >
            导出数据
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </motion.div>
      )}

      {/* 统计概览 */}
      <div>
        <h2 className="text-xl font-semibold text-mysql-text mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2" />
          数据概览
        </h2>
        <StatsOverview data={statsData} loading={loading} />
      </div>

      {/* 图表分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 月度趋势 */}
        <StatsChart
          title="月度搜索趋势"
          type="area"
          data={monthlyData}
          loading={loading}
          height={300}
        />

        {/* 分类分布 */}
        <StatsChart
          title="文章分类分布"
          type="pie"
          data={categoryData}
          loading={loading}
          height={300}
        />
      </div>

      {/* 搜索热词 */}
      <div className="grid grid-cols-1 gap-6">
        <StatsChart
          title="搜索热词排行"
          type="bar"
          data={searchKeywords}
          loading={loading}
          height={400}
        />
      </div>

      {/* 数据说明 */}
      <div className="bg-mysql-primary-light/20 border border-mysql-primary/20 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-mysql-text mb-3 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" />
          数据说明
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-mysql-text-light">
          <div>
            <h4 className="font-medium text-mysql-text mb-2">统计范围</h4>
            <ul className="space-y-1">
              <li>• 文章数据：包含所有已发布的知识库文章</li>
              <li>• 搜索数据：基于用户搜索行为统计</li>
              <li>• 浏览数据：统计文章页面访问次数</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-mysql-text mb-2">更新频率</h4>
            <ul className="space-y-1">
              <li>• 概览数据：实时更新</li>
              <li>• 趋势图表：每日更新</li>
              <li>• 搜索热词：每小时更新</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
