// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod core;
mod security;
mod utils;

use commands::*;
use security::protection::{initialize_runtime_protection, SecurityLevel};

#[tokio::main]
async fn main() {
    // 初始化日志系统
    env_logger::init();

    // 初始化运行时安全保护
    #[cfg(not(debug_assertions))]
    {
        if let Err(e) = initialize_runtime_protection(SecurityLevel::High).await {
            eprintln!("安全系统初始化失败: {}", e);
            std::process::exit(1);
        }
    }

    #[cfg(debug_assertions)]
    {
        // 开发模式使用较低的安全级别
        if let Err(e) = initialize_runtime_protection(SecurityLevel::Low).await {
            eprintln!("安全系统初始化失败: {}", e);
        }
    }
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            greet,
            get_system_info,
            test_connection,
            generate_fingerprint,
            get_machine_features,
            get_features_summary,
            get_system_performance,
            init_activation_system,
            check_activation_status,
            validate_license,
            activate_license_key,
            deactivate_license_key,
            check_is_activated,
            detect_user_location,
            get_mysql_package,
            get_supported_versions,
            download_mysql_package,
            detect_mysql_components,
            uninstall_old_mysql,
            install_mysql,
            verify_mysql_installation,
            set_mysql_config,
            get_mysql_config
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}