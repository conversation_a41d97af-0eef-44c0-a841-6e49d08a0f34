// MySQLAi.de - 单个知识库分类 API
// 提供单个分类的 CRUD 操作

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type KnowledgeCategoryUpdate = Database['public']['Tables']['knowledge_categories']['Update'];

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/knowledge/categories/[id] - 获取单个分类
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const includeArticles = searchParams.get('includeArticles') === 'true';

    const { data: category, error } = await supabase
      .from('knowledge_categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: '分类不存在' },
          { status: 404 }
        );
      }

      console.error('获取分类失败:', error);
      return NextResponse.json(
        { success: false, error: '获取分类失败', details: error.message },
        { status: 500 }
      );
    }

    const result: Record<string, unknown> = { ...category };

    // 如果需要包含文章列表
    if (includeArticles) {
      const { data: articles, error: articlesError } = await supabase
        .from('knowledge_articles')
        .select('id, title, description, tags, difficulty, order_index, last_updated')
        .eq('category_id', id)
        .order('order_index', { ascending: true });

      if (articlesError) {
        console.error('获取分类文章失败:', articlesError);
      } else {
        result.articles = articles || [];
      }
    }

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PUT /api/knowledge/categories/[id] - 更新分类
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const body = await request.json();

    // 构建更新数据
    const updateData: KnowledgeCategoryUpdate = {};
    
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.icon !== undefined) updateData.icon = body.icon;
    if (body.color !== undefined) updateData.color = body.color;
    if (body.order_index !== undefined) updateData.order_index = body.order_index;

    // 检查是否有数据需要更新
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: '没有提供需要更新的数据' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('knowledge_categories')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: '分类不存在' },
          { status: 404 }
        );
      }

      console.error('更新分类失败:', error);
      return NextResponse.json(
        { success: false, error: '更新分类失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: '分类更新成功'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// DELETE /api/knowledge/categories/[id] - 删除分类
export async function DELETE(_request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;

    // 检查分类下是否有文章
    const { count } = await supabase
      .from('knowledge_articles')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', id);

    if (count && count > 0) {
      return NextResponse.json(
        { success: false, error: `无法删除分类，该分类下还有 ${count} 篇文章` },
        { status: 409 }
      );
    }

    const { error } = await supabase
      .from('knowledge_categories')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('删除分类失败:', error);
      return NextResponse.json(
        { success: false, error: '删除分类失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '分类删除成功'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
