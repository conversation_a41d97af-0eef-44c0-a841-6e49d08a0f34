import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

// 下载相关类型定义
export interface DownloadSource {
  name: string;
  url: string;
  region: string;
  priority: number;
  available: boolean;
  speed?: number;
}

export interface MySQLPackage {
  version: string;
  os: string;
  architecture: string;
  filename: string;
  fileSize: number;
  downloadSources: DownloadSource[];
  checksum?: string;
  releaseDate: string;
}

export interface DownloadProgress {
  downloaded: number;
  total: number;
  speed: number;
  eta?: number;
  percentage: number;
  status: DownloadStatus;
}

export enum DownloadStatus {
  Pending = 'Pending',
  Downloading = 'Downloading',
  Paused = 'Paused',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
}

export interface LocationInfo {
  country: string;
  region: string;
  ip: string;
  isChina: boolean;
}

export interface UseDownloadReturn {
  // 状态
  userLocation: LocationInfo | null;
  supportedVersions: Array<[string, string, string]>;
  selectedPackage: MySQLPackage | null;
  downloadProgress: DownloadProgress | null;
  isDownloading: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 方法
  detectUserLocation: () => Promise<void>;
  getSupportedVersions: () => Promise<void>;
  getMySQLPackage: (version: string, os: string, architecture: string) => Promise<void>;
  downloadMySQLPackage: (version: string, os: string, architecture: string, downloadDir: string) => Promise<string | null>;
  refreshAll: () => Promise<void>;
}

/**
 * 下载管理Hook
 * 提供MySQL包下载、地理位置检测、版本管理等功能
 */
export function useDownload(): UseDownloadReturn {
  // 状态管理
  const [userLocation, setUserLocation] = useState<LocationInfo | null>(null);
  const [supportedVersions, setSupportedVersions] = useState<Array<[string, string, string]>>([]);
  const [selectedPackage, setSelectedPackage] = useState<MySQLPackage | null>(null);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 检测用户地理位置
  const detectUserLocation = useCallback(async () => {
    try {
      setError(null);
      
      const location = await invoke<LocationInfo>('detect_user_location');
      setUserLocation(location);
      
      console.log('User location detected:', location);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '检测地理位置失败';
      setError(errorMessage);
      console.error('Failed to detect user location:', err);
    }
  }, []);

  // 获取支持的MySQL版本列表
  const getSupportedVersions = useCallback(async () => {
    try {
      setError(null);
      
      const versions = await invoke<Array<[string, string, string]>>('get_supported_versions');
      setSupportedVersions(versions);
      
      console.log('Supported versions:', versions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取支持版本失败';
      setError(errorMessage);
      console.error('Failed to get supported versions:', err);
    }
  }, []);

  // 获取MySQL包信息
  const getMySQLPackage = useCallback(async (version: string, os: string, architecture: string) => {
    try {
      setError(null);
      
      const packageInfo = await invoke<MySQLPackage | null>('get_mysql_package', {
        version,
        os,
        architecture,
      });
      
      setSelectedPackage(packageInfo);
      
      if (packageInfo) {
        console.log('MySQL package info:', packageInfo);
      } else {
        console.warn('MySQL package not found:', { version, os, architecture });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取MySQL包信息失败';
      setError(errorMessage);
      console.error('Failed to get MySQL package:', err);
    }
  }, []);

  // 下载MySQL包
  const downloadMySQLPackage = useCallback(async (
    version: string,
    os: string,
    architecture: string,
    downloadDir: string
  ): Promise<string | null> => {
    try {
      setError(null);
      setIsDownloading(true);
      setDownloadProgress({
        downloaded: 0,
        total: 0,
        speed: 0,
        eta: undefined,
        percentage: 0,
        status: DownloadStatus.Pending,
      });

      const filePath = await invoke<string>('download_mysql_package', {
        version,
        os,
        architecture,
        downloadDir,
      });

      // 下载完成
      setDownloadProgress({
        downloaded: 100,
        total: 100,
        speed: 0,
        eta: 0,
        percentage: 100,
        status: DownloadStatus.Completed,
      });

      console.log('MySQL package downloaded:', filePath);
      return filePath;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '下载MySQL包失败';
      setError(errorMessage);
      console.error('Failed to download MySQL package:', err);
      
      setDownloadProgress({
        downloaded: 0,
        total: 0,
        speed: 0,
        eta: undefined,
        percentage: 0,
        status: DownloadStatus.Failed,
      });
      
      return null;
    } finally {
      setIsDownloading(false);
    }
  }, []);

  // 刷新所有信息
  const refreshAll = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        detectUserLocation(),
        getSupportedVersions(),
      ]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刷新信息失败';
      setError(errorMessage);
      console.error('Failed to refresh all info:', err);
    } finally {
      setIsLoading(false);
    }
  }, [detectUserLocation, getSupportedVersions]);

  // 组件挂载时自动获取信息
  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  return {
    // 状态
    userLocation,
    supportedVersions,
    selectedPackage,
    downloadProgress,
    isDownloading,
    isLoading,
    error,
    
    // 方法
    detectUserLocation,
    getSupportedVersions,
    getMySQLPackage,
    downloadMySQLPackage,
    refreshAll,
  };
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * 格式化下载速度
 */
export function formatDownloadSpeed(kbps: number): string {
  if (kbps < 1024) {
    return `${kbps.toFixed(1)} KB/s`;
  } else {
    return `${(kbps / 1024).toFixed(1)} MB/s`;
  }
}

/**
 * 格式化剩余时间
 */
export function formatETA(seconds: number | undefined): string {
  if (!seconds || seconds <= 0) {
    return '未知';
  }

  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
}

/**
 * 获取下载状态显示文本
 */
export function getDownloadStatusText(status: DownloadStatus): string {
  switch (status) {
    case DownloadStatus.Pending:
      return '等待中';
    case DownloadStatus.Downloading:
      return '下载中';
    case DownloadStatus.Paused:
      return '已暂停';
    case DownloadStatus.Completed:
      return '已完成';
    case DownloadStatus.Failed:
      return '下载失败';
    case DownloadStatus.Cancelled:
      return '已取消';
    default:
      return '未知状态';
  }
}

/**
 * 获取下载状态颜色类
 */
export function getDownloadStatusColor(status: DownloadStatus): string {
  switch (status) {
    case DownloadStatus.Pending:
      return 'text-gray-600';
    case DownloadStatus.Downloading:
      return 'text-blue-600';
    case DownloadStatus.Paused:
      return 'text-yellow-600';
    case DownloadStatus.Completed:
      return 'text-green-600';
    case DownloadStatus.Failed:
      return 'text-red-600';
    case DownloadStatus.Cancelled:
      return 'text-gray-600';
    default:
      return 'text-gray-600';
  }
}

/**
 * 获取地区显示名称
 */
export function getRegionDisplayName(region: string): string {
  switch (region) {
    case 'china':
      return '中国';
    case 'global':
      return '全球';
    default:
      return region;
  }
}

/**
 * 获取操作系统显示名称
 */
export function getOSDisplayName(os: string): string {
  switch (os.toLowerCase()) {
    case 'windows':
      return 'Windows';
    case 'macos':
      return 'macOS';
    case 'linux':
      return 'Linux';
    default:
      return os;
  }
}

/**
 * 获取架构显示名称
 */
export function getArchDisplayName(arch: string): string {
  switch (arch.toLowerCase()) {
    case 'x64':
      return '64位';
    case 'x86':
      return '32位';
    case 'arm64':
      return 'ARM64';
    default:
      return arch;
  }
}