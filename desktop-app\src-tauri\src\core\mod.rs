// Core business logic modules
// This will contain the main functionality modules:
// - activation: License verification and activation system
// - installer: MySQL installation logic
// - download: Download management
// - fingerprint: Machine fingerprint generation
// - security: Security and protection features

pub mod types;
pub mod fingerprint;
pub mod system;
pub mod activation;
pub mod database;
pub mod download;
pub mod installer;

// Re-export commonly used types and functions
pub use types::*;
pub use fingerprint::{
    EnhancedFingerprint, MachineFeatures,
    generate_machine_fingerprint, verify_machine_fingerprint,
    get_machine_features_summary, get_machine_features
};
pub use system::{
    SystemDetector, SystemPerformanceInfo, DiskUsageInfo,
    create_system_detector, get_system_info, is_mysql_installation_supported
};
pub use activation::{
    ActivationStatus, initialize_activation_system, is_activated,
    activate_license, validate_license_key, get_activation_status, deactivate_license
};
pub use database::{
    DatabaseManager, LicenseKeyRecord, ActivationRecord, ActivationStats,
    create_database_manager, get_default_db_path
};
pub use download::{
    DownloadManager, DownloadSource, MySQLPackage, DownloadProgress, DownloadStatus, LocationInfo,
    get_download_manager, detect_user_location, get_mysql_package, download_mysql, get_supported_mysql_versions
};
pub use installer::{
    MySQLInstaller, MySQLComponent, MySQLComponentType, InstallationProgress, InstallationStatus, MySQLConfig,
    get_mysql_installer, detect_mysql_components, uninstall_old_mysql, install_mysql, verify_mysql_installation,
    set_mysql_config, get_mysql_config, set_installation_progress_callback
};