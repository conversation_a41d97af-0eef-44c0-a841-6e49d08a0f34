/**
 * 多系统MySQL安装指导生成器
 * 为Windows/macOS/Linux生成详细的安装步骤和命令
 */

import {
  SupportedOS,
  QuickInstallConfig,
  InstallationStep
} from '../types/mysql-installer';
import { INSTALLATION_STEP_IDS, ESTIMATED_TIMES } from './defaults';

/**
 * 安装指导接口
 */
export interface InstallationGuide {
  /** 系统类型 */
  os: SupportedOS;
  /** 安装步骤 */
  steps: InstallationStep[];
  /** 预计总时间 */
  estimatedTotalTime: number;
  /** 前置要求 */
  prerequisites: string[];
  /** 故障排除 */
  troubleshooting: Array<{
    problem: string;
    solution: string;
  }>;
  /** 安装后配置 */
  postInstallation: Array<{
    title: string;
    description: string;
    commands?: string[];
  }>;
}

/**
 * 生成Windows安装指导
 */
export function generateWindowsGuide(config: QuickInstallConfig): InstallationGuide {
  const steps: InstallationStep[] = [
    {
      id: INSTALLATION_STEP_IDS.DOWNLOAD,
      title: '下载MySQL安装包',
      description: '从官方或镜像站点下载MySQL 8.0 Windows版本',
      command: `# 下载到指定目录
# 建议下载到: C:\\Downloads\\mysql-8.0.36-winx64.zip`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.download
    },
    {
      id: INSTALLATION_STEP_IDS.EXTRACT,
      title: '解压安装包',
      description: '将下载的ZIP文件解压到安装目录',
      command: `# 使用PowerShell解压
Expand-Archive -Path "C:\\Downloads\\mysql-8.0.36-winx64.zip" -DestinationPath "${config.installPath.replace(/\\/g, '\\\\')}"

# 或使用命令行
powershell -command "Expand-Archive -Path 'C:\\Downloads\\mysql-8.0.36-winx64.zip' -DestinationPath '${config.installPath}'"`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.extract
    },
    {
      id: INSTALLATION_STEP_IDS.CONFIGURE,
      title: '配置MySQL',
      description: '将生成的my.ini配置文件放置到MySQL安装目录',
      command: `# 将my.ini文件复制到MySQL安装目录
copy "my.ini" "${config.installPath}\\my.ini"

# 创建数据目录
mkdir "${config.dataPath}"

# 设置目录权限（可选）
icacls "${config.dataPath}" /grant "Everyone:(OI)(CI)F"`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.configure
    },
    {
      id: INSTALLATION_STEP_IDS.INITIALIZE,
      title: '初始化MySQL数据库',
      description: '初始化MySQL数据目录和系统表',
      command: `# 切换到MySQL bin目录
cd "${config.installPath}\\bin"

# 初始化数据库（会生成临时密码）
mysqld --initialize --console

# 或者初始化为空密码（不推荐）
# mysqld --initialize-insecure --console`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.initialize
    },
    {
      id: INSTALLATION_STEP_IDS.START_SERVICE,
      title: '安装并启动MySQL服务',
      description: '将MySQL安装为Windows服务并启动',
      command: `# 以管理员身份运行命令提示符
# 安装MySQL服务
"${config.installPath}\\bin\\mysqld" --install MySQL80

# 启动MySQL服务
net start MySQL80

# 设置服务自动启动
sc config MySQL80 start= auto`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.start_service
    },
    {
      id: INSTALLATION_STEP_IDS.VERIFY,
      title: '验证安装',
      description: '测试MySQL连接和基本功能',
      command: `# 连接到MySQL（使用初始化时生成的密码）
"${config.installPath}\\bin\\mysql" -u root -p

# 修改root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY '${config.rootPassword}';

# 刷新权限
FLUSH PRIVILEGES;

# 退出MySQL
EXIT;`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.verify
    },
    {
      id: INSTALLATION_STEP_IDS.SECURE,
      title: '安全配置',
      description: '配置MySQL安全设置',
      command: `# 运行安全配置脚本
"${config.installPath}\\bin\\mysql_secure_installation"

# 或手动执行安全配置
"${config.installPath}\\bin\\mysql" -u root -p -e "
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';
FLUSH PRIVILEGES;"`,
      required: false,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.secure
    }
  ];

  return {
    os: SupportedOS.WINDOWS,
    steps,
    estimatedTotalTime: steps.reduce((total, step) => total + (step.estimatedTime || 0), 0),
    prerequisites: [
      '管理员权限',
      'Windows 10 或更高版本',
      '至少 4GB 可用内存',
      '至少 2GB 可用磁盘空间',
      '.NET Framework 4.7.2 或更高版本',
      'Visual C++ Redistributable'
    ],
    troubleshooting: [
      {
        problem: '服务启动失败',
        solution: '检查端口是否被占用，确保以管理员身份运行，检查防火墙设置'
      },
      {
        problem: '初始化失败',
        solution: '确保数据目录为空，检查磁盘空间，确保有足够的权限'
      },
      {
        problem: '无法连接到MySQL',
        solution: '检查服务是否运行，确认端口配置，检查防火墙规则'
      },
      {
        problem: '密码错误',
        solution: '使用初始化时生成的临时密码，或重新初始化数据库'
      }
    ],
    postInstallation: [
      {
        title: '添加到系统PATH',
        description: '将MySQL bin目录添加到系统环境变量',
        commands: [
          `setx PATH "%PATH%;${config.installPath}\\bin" /M`
        ]
      },
      {
        title: '创建数据库用户',
        description: '创建应用程序使用的数据库用户',
        commands: [
          `mysql -u root -p -e "CREATE USER 'appuser'@'localhost' IDENTIFIED BY 'password';"`,
          `mysql -u root -p -e "GRANT ALL PRIVILEGES ON *.* TO 'appuser'@'localhost';"`,
          `mysql -u root -p -e "FLUSH PRIVILEGES;"`
        ]
      },
      {
        title: '配置远程访问',
        description: '允许远程连接到MySQL服务器',
        commands: [
          `mysql -u root -p -e "CREATE USER 'root'@'%' IDENTIFIED BY '${config.rootPassword}';"`,
          `mysql -u root -p -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;"`,
          `mysql -u root -p -e "FLUSH PRIVILEGES;"`
        ]
      },
      {
        title: '配置防火墙',
        description: '开放MySQL端口以允许外部连接',
        commands: [
          `netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=${config.port}`
        ]
      }
    ]
  };
}

/**
 * 生成macOS安装指导
 */
export function generateMacOSGuide(config: QuickInstallConfig): InstallationGuide {
  const steps: InstallationStep[] = [
    {
      id: INSTALLATION_STEP_IDS.DOWNLOAD,
      title: '下载MySQL安装包',
      description: '从官方站点下载MySQL 8.0 macOS版本',
      command: `# 下载DMG文件
# 建议下载到: ~/Downloads/mysql-8.0.36-macos13-${config.targetOS === SupportedOS.MACOS ? 'x86_64' : 'arm64'}.dmg`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.download
    },
    {
      id: INSTALLATION_STEP_IDS.EXTRACT,
      title: '安装MySQL',
      description: '挂载DMG文件并运行安装程序',
      command: `# 挂载DMG文件
hdiutil attach ~/Downloads/mysql-8.0.36-macos13-*.dmg

# 运行安装程序
sudo installer -pkg /Volumes/mysql-8.0.36-macos13/mysql-8.0.36-macos13-*.pkg -target /

# 卸载DMG
hdiutil detach /Volumes/mysql-8.0.36-macos13`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.extract
    },
    {
      id: INSTALLATION_STEP_IDS.CONFIGURE,
      title: '配置MySQL',
      description: '设置配置文件和数据目录',
      command: `# 创建配置文件目录
sudo mkdir -p /etc/mysql

# 复制配置文件
sudo cp my.cnf /etc/mysql/my.cnf

# 设置配置文件权限
sudo chown root:wheel /etc/mysql/my.cnf
sudo chmod 644 /etc/mysql/my.cnf

# 创建数据目录
sudo mkdir -p ${config.dataPath}
sudo chown _mysql:_mysql ${config.dataPath}`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.configure
    }
  ];

  return {
    os: SupportedOS.MACOS,
    steps,
    estimatedTotalTime: steps.reduce((total, step) => total + (step.estimatedTime || 0), 0),
    prerequisites: [
      'macOS 10.15 或更高版本',
      '管理员权限',
      '至少 4GB 可用内存',
      '至少 2GB 可用磁盘空间',
      'Xcode Command Line Tools'
    ],
    troubleshooting: [
      {
        problem: '安装包无法打开',
        solution: '在系统偏好设置 > 安全性与隐私中允许来自未知开发者的应用'
      },
      {
        problem: '权限被拒绝',
        solution: '使用sudo命令或在系统偏好设置中授予完全磁盘访问权限'
      }
    ],
    postInstallation: [
      {
        title: '添加到PATH',
        description: '将MySQL添加到系统PATH',
        commands: [
          `echo 'export PATH="/usr/local/mysql/bin:$PATH"' >> ~/.bash_profile`,
          `source ~/.bash_profile`
        ]
      },
      {
        title: '启动MySQL服务',
        description: '启动MySQL服务并设置开机自启',
        commands: [
          `sudo /usr/local/mysql/support-files/mysql.server start`,
          `sudo launchctl load -w /Library/LaunchDaemons/com.oracle.oss.mysql.mysqld.plist`
        ]
      }
    ]
  };
}

/**
 * 生成Linux安装指导
 */
export function generateLinuxGuide(config: QuickInstallConfig): InstallationGuide {
  const steps: InstallationStep[] = [
    {
      id: INSTALLATION_STEP_IDS.DOWNLOAD,
      title: '下载MySQL安装包',
      description: '从官方站点下载MySQL 8.0 Linux版本',
      command: `# 下载tar.xz文件
wget https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz

# 或使用curl
curl -O https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.download
    },
    {
      id: INSTALLATION_STEP_IDS.EXTRACT,
      title: '解压安装包',
      description: '解压tar.xz文件到安装目录',
      command: `# 创建安装目录
sudo mkdir -p ${config.installPath}

# 解压文件
sudo tar -xJf mysql-8.0.36-linux-glibc2.28-x86_64.tar.xz -C ${config.installPath} --strip-components=1

# 设置目录权限
sudo chown -R mysql:mysql ${config.installPath}`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.extract
    },
    {
      id: INSTALLATION_STEP_IDS.CONFIGURE,
      title: '配置MySQL',
      description: '设置配置文件和创建必要的目录',
      command: `# 创建mysql用户和组
sudo groupadd mysql
sudo useradd -r -g mysql -s /bin/false mysql

# 创建数据目录
sudo mkdir -p ${config.dataPath}
sudo chown mysql:mysql ${config.dataPath}

# 复制配置文件
sudo cp my.cnf /etc/mysql/my.cnf
sudo chown root:root /etc/mysql/my.cnf
sudo chmod 644 /etc/mysql/my.cnf

# 创建日志目录
sudo mkdir -p /var/log/mysql
sudo chown mysql:mysql /var/log/mysql`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.configure
    },
    {
      id: INSTALLATION_STEP_IDS.INITIALIZE,
      title: '初始化MySQL数据库',
      description: '初始化MySQL数据目录和系统表',
      command: `# 初始化数据库
sudo ${config.installPath}/bin/mysqld --initialize --user=mysql --datadir=${config.dataPath}

# 查看临时密码
sudo grep 'temporary password' /var/log/mysql/error.log`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.initialize
    },
    {
      id: INSTALLATION_STEP_IDS.START_SERVICE,
      title: '启动MySQL服务',
      description: '创建systemd服务并启动MySQL',
      command: `# 创建systemd服务文件
sudo tee /etc/systemd/system/mysql.service > /dev/null <<EOF
[Unit]
Description=MySQL Server
After=network.target

[Service]
Type=notify
User=mysql
ExecStart=${config.installPath}/bin/mysqld --defaults-file=/etc/mysql/my.cnf
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 启动MySQL服务
sudo systemctl start mysql

# 设置开机自启
sudo systemctl enable mysql`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.start_service
    },
    {
      id: INSTALLATION_STEP_IDS.VERIFY,
      title: '验证安装',
      description: '测试MySQL连接和基本功能',
      command: `# 连接到MySQL
${config.installPath}/bin/mysql -u root -p

# 修改root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY '${config.rootPassword}';
FLUSH PRIVILEGES;
EXIT;`,
      required: true,
      completed: false,
      estimatedTime: ESTIMATED_TIMES.verify
    }
  ];

  return {
    os: SupportedOS.LINUX,
    steps,
    estimatedTotalTime: steps.reduce((total, step) => total + (step.estimatedTime || 0), 0),
    prerequisites: [
      'Ubuntu 18.04+ / CentOS 7+ / Debian 9+',
      'sudo权限',
      '至少 2GB 可用内存',
      '至少 2GB 可用磁盘空间',
      'glibc 2.17+',
      'libssl开发库'
    ],
    troubleshooting: [
      {
        problem: '权限被拒绝',
        solution: '确保使用sudo权限，检查文件和目录的所有者和权限'
      },
      {
        problem: '服务启动失败',
        solution: '检查配置文件语法，查看系统日志：sudo journalctl -u mysql'
      },
      {
        problem: '端口被占用',
        solution: '使用netstat -tlnp | grep 3306检查端口占用，修改配置文件中的端口'
      },
      {
        problem: '依赖库缺失',
        solution: '安装必要的依赖：sudo apt-get install libaio1 libmecab2'
      }
    ],
    postInstallation: [
      {
        title: '添加到PATH',
        description: '将MySQL添加到系统PATH',
        commands: [
          `echo 'export PATH="${config.installPath}/bin:$PATH"' >> ~/.bashrc`,
          `source ~/.bashrc`
        ]
      },
      {
        title: '配置防火墙',
        description: '开放MySQL端口',
        commands: [
          `# Ubuntu/Debian`,
          `sudo ufw allow ${config.port}`,
          `# CentOS/RHEL`,
          `sudo firewall-cmd --permanent --add-port=${config.port}/tcp`,
          `sudo firewall-cmd --reload`
        ]
      }
    ]
  };
}

/**
 * 根据操作系统生成安装指导
 */
export function generateInstallationGuide(
  os: SupportedOS,
  config: QuickInstallConfig
): InstallationGuide {
  switch (os) {
    case SupportedOS.WINDOWS:
      return generateWindowsGuide(config);
    case SupportedOS.MACOS:
      return generateMacOSGuide(config);
    case SupportedOS.LINUX:
      return generateLinuxGuide(config);
    default:
      throw new Error(`不支持的操作系统: ${os}`);
  }
}

/**
 * 获取系统特定的包管理器安装命令
 */
export function getPackageManagerCommands(os: SupportedOS): {
  name: string;
  commands: string[];
  description: string;
} | null {
  switch (os) {
    case SupportedOS.MACOS:
      return {
        name: 'Homebrew',
        commands: [
          '# 安装Homebrew（如果未安装）',
          '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"',
          '',
          '# 使用Homebrew安装MySQL',
          'brew install mysql',
          '',
          '# 启动MySQL服务',
          'brew services start mysql',
          '',
          '# 运行安全配置',
          'mysql_secure_installation'
        ],
        description: '使用Homebrew包管理器安装MySQL（推荐方式）'
      };

    case SupportedOS.LINUX:
      return {
        name: 'APT/YUM',
        commands: [
          '# Ubuntu/Debian系统',
          'sudo apt update',
          'sudo apt install mysql-server',
          'sudo systemctl start mysql',
          'sudo systemctl enable mysql',
          'sudo mysql_secure_installation',
          '',
          '# CentOS/RHEL系统',
          'sudo yum install mysql-server',
          '# 或者使用dnf（较新版本）',
          'sudo dnf install mysql-server',
          'sudo systemctl start mysqld',
          'sudo systemctl enable mysqld',
          'sudo mysql_secure_installation'
        ],
        description: '使用系统包管理器安装MySQL（简单方式）'
      };

    default:
      return null;
  }
}

/**
 * 获取常见问题和解决方案
 */
export function getCommonIssues(): Array<{
  category: string;
  issues: Array<{
    problem: string;
    solution: string;
    commands?: string[];
  }>;
}> {
  return [
    {
      category: '连接问题',
      issues: [
        {
          problem: 'ERROR 2002: Can\'t connect to local MySQL server',
          solution: '检查MySQL服务是否运行',
          commands: [
            '# Windows',
            'net start MySQL80',
            '# macOS',
            'sudo /usr/local/mysql/support-files/mysql.server start',
            '# Linux',
            'sudo systemctl start mysql'
          ]
        },
        {
          problem: 'ERROR 1045: Access denied for user',
          solution: '检查用户名和密码是否正确',
          commands: [
            '# 重置root密码',
            'sudo mysqld_safe --skip-grant-tables &',
            'mysql -u root',
            'UPDATE mysql.user SET authentication_string=PASSWORD("newpassword") WHERE User="root";',
            'FLUSH PRIVILEGES;'
          ]
        }
      ]
    },
    {
      category: '性能问题',
      issues: [
        {
          problem: 'MySQL运行缓慢',
          solution: '优化配置参数',
          commands: [
            '# 增加缓冲池大小',
            'innodb_buffer_pool_size = 1G',
            '# 优化查询缓存',
            'query_cache_size = 128M',
            '# 增加最大连接数',
            'max_connections = 500'
          ]
        }
      ]
    },
    {
      category: '安全问题',
      issues: [
        {
          problem: '远程连接被拒绝',
          solution: '配置远程访问权限',
          commands: [
            'mysql -u root -p',
            'CREATE USER \'username\'@\'%\' IDENTIFIED BY \'password\';',
            'GRANT ALL PRIVILEGES ON *.* TO \'username\'@\'%\';',
            'FLUSH PRIVILEGES;'
          ]
        }
      ]
    }
  ];
}

/**
 * 生成安装验证脚本
 */
export function generateVerificationScript(config: QuickInstallConfig): {
  script: string;
  description: string;
} {
  const isWindows = config.targetOS === SupportedOS.WINDOWS;
  const mysqlPath = isWindows
    ? `"${config.installPath}\\bin\\mysql"`
    : `${config.installPath}/bin/mysql`;

  const script = `${isWindows ? '@echo off' : '#!/bin/bash'}
echo "MySQL安装验证脚本"
echo "===================="

echo "1. 检查MySQL服务状态..."
${isWindows
  ? 'sc query MySQL80 | find "RUNNING"'
  : 'sudo systemctl is-active mysql'
}

if %ERRORLEVEL% EQU 0 (
    echo "✓ MySQL服务正在运行"
) else (
    echo "✗ MySQL服务未运行"
    exit /b 1
)

echo "2. 测试MySQL连接..."
${mysqlPath} -u root -p${config.rootPassword} -e "SELECT VERSION();"

if %ERRORLEVEL% EQU 0 (
    echo "✓ MySQL连接成功"
) else (
    echo "✗ MySQL连接失败"
    exit /b 1
)

echo "3. 检查数据库..."
${mysqlPath} -u root -p${config.rootPassword} -e "SHOW DATABASES;"

echo "4. 检查用户权限..."
${mysqlPath} -u root -p${config.rootPassword} -e "SELECT User, Host FROM mysql.user;"

echo "===================="
echo "MySQL安装验证完成！"
${isWindows ? 'pause' : ''}`;

  return {
    script,
    description: '自动验证MySQL安装是否成功的脚本'
  };
}
