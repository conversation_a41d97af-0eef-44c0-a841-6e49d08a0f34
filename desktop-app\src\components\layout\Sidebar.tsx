/**
 * 侧边栏导航组件
 * 提供应用功能模块的导航
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Key, 
  Download, 
  Database, 
  Monitor, 
  Settings,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { AppPage } from '../../store/appStore';
import { useActivationStatus } from '../../store/appStore';
import { cn } from '../../lib/utils';

interface SidebarProps {
  currentPage: AppPage;
  onPageChange: (page: AppPage) => void;
}

interface NavigationItem {
  id: AppPage;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  requiresActivation?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'activation',
    label: '激活验证',
    icon: Key,
    description: '产品激活和许可证管理',
    requiresActivation: false,
  },
  {
    id: 'download',
    label: 'MySQL下载',
    icon: Download,
    description: '下载MySQL安装包',
    requiresActivation: true,
  },
  {
    id: 'installer',
    label: 'MySQL安装',
    icon: Database,
    description: '安装和配置MySQL',
    requiresActivation: true,
  },
  {
    id: 'system',
    label: '系统信息',
    icon: Monitor,
    description: '查看系统配置和状态',
    requiresActivation: false,
  },
  {
    id: 'settings',
    label: '应用设置',
    icon: Settings,
    description: '应用配置和偏好设置',
    requiresActivation: false,
  },
];

export default function Sidebar({ currentPage, onPageChange }: SidebarProps) {
  const activationStatus = useActivationStatus();

  const handleItemClick = (item: NavigationItem) => {
    // 检查是否需要激活
    if (item.requiresActivation && !activationStatus.isActivated) {
      // 如果需要激活但未激活，跳转到激活页面
      onPageChange('activation');
      return;
    }
    
    onPageChange(item.id);
  };

  const getItemStatus = (item: NavigationItem) => {
    if (!item.requiresActivation) {
      return 'available';
    }
    
    return activationStatus.isActivated ? 'available' : 'locked';
  };

  return (
    <div className="w-64 bg-white border-r border-mysql-border flex flex-col">
      {/* 侧边栏头部 */}
      <div className="p-4 border-b border-mysql-border">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-mysql-primary rounded-lg flex items-center justify-center">
            <Database className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-mysql-text">MySQL安装器</h2>
            <p className="text-sm text-mysql-text-light">MySQLAi.de</p>
          </div>
        </div>
      </div>

      {/* 激活状态指示器 */}
      <div className="p-4 border-b border-mysql-border">
        <div className={cn(
          'flex items-center space-x-2 px-3 py-2 rounded-lg',
          activationStatus.isActivated 
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-yellow-50 text-yellow-700 border border-yellow-200'
        )}>
          {activationStatus.isActivated ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <AlertCircle className="w-4 h-4" />
          )}
          <span className="text-sm font-medium">
            {activationStatus.isActivated ? '已激活' : '未激活'}
          </span>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const isActive = currentPage === item.id;
            const status = getItemStatus(item);
            const isLocked = status === 'locked';
            
            return (
              <li key={item.id}>
                <motion.button
                  whileHover={!isLocked ? { scale: 1.02 } : {}}
                  whileTap={!isLocked ? { scale: 0.98 } : {}}
                  onClick={() => handleItemClick(item)}
                  disabled={isLocked}
                  className={cn(
                    'w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200',
                    isActive && !isLocked && 'bg-mysql-primary text-white shadow-md',
                    !isActive && !isLocked && 'text-mysql-text hover:bg-mysql-primary-light',
                    isLocked && 'text-gray-400 cursor-not-allowed opacity-60'
                  )}
                >
                  <item.icon className={cn(
                    'w-5 h-5 flex-shrink-0',
                    isActive && !isLocked && 'text-white',
                    !isActive && !isLocked && 'text-mysql-text-light',
                    isLocked && 'text-gray-400'
                  )} />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="font-medium truncate">
                        {item.label}
                      </span>
                      {isLocked && (
                        <Key className="w-4 h-4 text-gray-400 flex-shrink-0" />
                      )}
                    </div>
                    <p className={cn(
                      'text-xs mt-1 truncate',
                      isActive && !isLocked && 'text-white/80',
                      !isActive && !isLocked && 'text-mysql-text-light',
                      isLocked && 'text-gray-400'
                    )}>
                      {item.description}
                    </p>
                  </div>
                </motion.button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* 侧边栏底部 */}
      <div className="p-4 border-t border-mysql-border">
        <div className="text-center">
          <p className="text-xs text-mysql-text-light">
            版本 1.0.0
          </p>
          <p className="text-xs text-mysql-text-light mt-1">
            © 2025 MySQLAi.de
          </p>
        </div>
      </div>
    </div>
  );
}