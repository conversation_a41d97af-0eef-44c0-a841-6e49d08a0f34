// MySQLAi.de - 常量配置文件
// 包含项目中使用的所有常量、配置和静态数据

import { ThemeColors, PageMetadata } from './types';

// 网站基本信息
export const SITE_CONFIG = {
  name: 'MySQLAi.de',
  title: 'MySQL智能分析专家',
  description: '专业的数据库知识分享与项目管理平台',
  url: 'https://mysqlai.de',
  author: 'MySQLAi Team',
  keywords: ['MySQL', '数据库', 'AI分析', '项目管理', '知识分享', '性能优化'],
};

// MySQL主题色彩配置
export const THEME_COLORS: ThemeColors = {
  primary: '#00758F',        // MySQL官方蓝
  primaryDark: '#003545',    // 深蓝色
  primaryLight: '#E6F3F7',   // 浅蓝色
  accent: '#0066CC',         // 强调色
  text: '#2D3748',           // 主文字色
  textLight: '#718096',      // 浅文字色
  border: '#E2E8F0',         // 边框色
  success: '#38A169',        // 成功色
  warning: '#D69E2E',        // 警告色
  error: '#E53E3E',          // 错误色
} as const;

// Chen ER图标准黑色主题配置
export const CHEN_ER_COLORS = {
  primary: '#000000',        // 黑色 - 实体边框
  primaryDark: '#000000',    // 黑色 - 深色变体
  primaryLight: '#FFFFFF',   // 白色 - 浅色背景
  accent: '#000000',         // 黑色 - 属性边框
  text: '#000000',           // 黑色 - 文字色
  textLight: '#000000',      // 黑色 - 浅文字色
  border: '#000000',         // 黑色 - 边框色
  success: '#000000',        // 黑色 - 关系边框
  warning: '#000000',        // 黑色 - 主键标记
  error: '#000000',          // 黑色 - 错误色
  white: '#FFFFFF',          // 白色 - 填充色
  background: '#FFFFFF',     // 白色 - 背景色
} as const;

// 注意：导航菜单配置已移至 @/lib/navigation.ts 中的 MAIN_NAVIGATION

// 页面元数据配置
export const PAGE_METADATA: Record<string, PageMetadata> = {
  home: {
    title: `${SITE_CONFIG.title} - ${SITE_CONFIG.description}`,
    description: '提供MySQL优化建议、项目任务管理、多媒体报告展示的专业平台。AI驱动的数据库分析，助力企业数据库性能提升。',
    keywords: [...SITE_CONFIG.keywords, '首页', '主页'],
  },
  knowledge: {
    title: `MySQL知识库 - ${SITE_CONFIG.name}`,
    description: '丰富的MySQL知识库，包含数据库优化、性能调优、最佳实践等专业内容。',
    keywords: [...SITE_CONFIG.keywords, '知识库', '教程', '最佳实践'],
  },
  projects: {
    title: `项目管理 - ${SITE_CONFIG.name}`,
    description: '专业的项目管理工具，支持任务跟踪、进度管理、团队协作。',
    keywords: [...SITE_CONFIG.keywords, '项目管理', '任务跟踪', '团队协作'],
  },
  reports: {
    title: `报告展示 - ${SITE_CONFIG.name}`,
    description: '支持图片、视频的多媒体项目报告展示平台。',
    keywords: [...SITE_CONFIG.keywords, '报告展示', '多媒体', '数据可视化'],
  },
  about: {
    title: `关于我们 - ${SITE_CONFIG.name}`,
    description: '了解MySQLAi.de团队，我们的使命是为用户提供最专业的MySQL解决方案。',
    keywords: [...SITE_CONFIG.keywords, '关于我们', '团队介绍', '公司简介'],
  },
  contact: {
    title: `联系我们 - ${SITE_CONFIG.name}`,
    description: '联系MySQLAi.de团队，获取专业的MySQL咨询和技术支持。',
    keywords: [...SITE_CONFIG.keywords, '联系我们', '技术支持', '咨询服务'],
  },
  // 法律声明页面元数据
  terms: {
    title: `服务条款 - ${SITE_CONFIG.name}`,
    description: 'MySQLAi.de平台服务使用条款和用户协议，明确用户权利义务，保障双方合法权益。',
    keywords: [...SITE_CONFIG.keywords, '服务条款', '用户协议', '使用条款', '服务协议', '法律声明'],
  },
  privacy: {
    title: `隐私政策 - ${SITE_CONFIG.name}`,
    description: 'MySQLAi.de平台用户隐私保护政策，详细说明个人信息收集、使用、保护措施。',
    keywords: [...SITE_CONFIG.keywords, '隐私政策', '个人信息保护', '数据保护', '隐私保护', '信息安全'],
  },
  disclaimer: {
    title: `免责声明 - ${SITE_CONFIG.name}`,
    description: 'MySQLAi.de平台服务免责条款和责任限制说明，明确服务范围和责任界限。',
    keywords: [...SITE_CONFIG.keywords, '免责声明', '责任限制', '法律免责', '服务限制', '风险提示'],
  },
  cookies: {
    title: `Cookie政策 - ${SITE_CONFIG.name}`,
    description: 'MySQLAi.de平台Cookie使用说明和管理指南，保障用户知情权和选择权。',
    keywords: [...SITE_CONFIG.keywords, 'Cookie政策', 'Cookie使用', '网站Cookie', 'Cookie管理', '用户隐私'],
  },
  // 工具页面元数据
  tools: {
    title: `MySQL工具集 - ${SITE_CONFIG.name}`,
    description: '专业的MySQL工具集合，包含ER图生成、数据库安装配置等实用工具，提升数据库开发效率。',
    keywords: [...SITE_CONFIG.keywords, 'MySQL工具', '数据库工具', 'ER图生成', 'MySQL安装', '开发工具'],
  },
  'tools-er-diagram': {
    title: `ER图生成工具 - ${SITE_CONFIG.name}`,
    description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式，提升数据库设计效率。',
    keywords: [...SITE_CONFIG.keywords, 'ER图生成', '数据库关系图', '数据库设计', '可视化工具', '数据库建模'],
  },
  'tools-mysql-installer': {
    title: `MySQL安装工具 - ${SITE_CONFIG.name}`,
    description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置，简化数据库部署流程。',
    keywords: [...SITE_CONFIG.keywords, 'MySQL安装', '数据库安装', '自动配置', '版本管理', '数据库部署'],
  },
} as const;

// 功能特性配置
export const FEATURES_DATA = [
  {
    title: 'MySQL知识库',
    description: '丰富的数据库知识分享，包含优化技巧、性能调优和最佳实践指南。',
    icon: 'Database',
    features: [
      '数据库性能优化',
      '查询语句调优',
      '索引设计最佳实践',
      '架构设计指南',
    ],
  },
  {
    title: '项目管理',
    description: '高效的项目任务管理系统，支持团队协作和进度跟踪。',
    icon: 'FolderOpen',
    features: [
      '任务分配与跟踪',
      '项目进度管理',
      '团队协作工具',
      '时间管理优化',
    ],
  },
  {
    title: '报告展示',
    description: '支持多媒体内容的项目报告展示，包含图片、视频和数据可视化。',
    icon: 'BarChart3',
    features: [
      '多媒体报告支持',
      '数据可视化图表',
      '实时数据展示',
      '自定义报告模板',
    ],
  },
] as const;

// 专业特性配置
export const ABOUT_FEATURES = [
  {
    title: '智能分析',
    description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',
    icon: 'Brain',
  },
  {
    title: '专业咨询',
    description: '资深数据库专家团队，提供一对一的专业咨询服务。',
    icon: 'Users',
  },
  {
    title: '高效管理',
    description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',
    icon: 'Zap',
  },
  {
    title: '透明报告',
    description: '详细的项目报告和数据分析，确保项目进展透明可控。',
    icon: 'FileText',
  },
  {
    title: '7x24支持',
    description: '全天候技术支持服务，确保您的数据库系统稳定运行。',
    icon: 'Clock',
  },
] as const;

// 优势展示配置
export const ADVANTAGES_DATA = [
  {
    title: '🌍 #1 MySQL专家',
    description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',
    details: '覆盖全球8个地区，超过5万用户信赖',
    icon: '🌍',
  },
  {
    title: '📝 兼容性与支持',
    description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',
    details: '支持MySQL 5.7到8.0的所有主流版本',
    icon: '📝',
  },
  {
    title: '💰 灵活计费',
    description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',
    details: '透明计费，性价比最高的MySQL服务',
    icon: '💰',
  },
  {
    title: '⚡ 全球布局',
    description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',
    details: '全球用户享受一致的高速服务体验',
    icon: '⚡',
  },
  {
    title: '⏰ 服务保障',
    description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',
    details: '专业运维团队，99.9%服务可用性保证',
    icon: '⏰',
  },
  {
    title: '🎈 透明计费',
    description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',
    details: '无隐藏费用，按实际使用量计费',
    icon: '🎈',
  },
] as const;

// 联系方式配置
export const CONTACT_INFO = {
  supportHours: '7×24小时全天候支持',
  email: '<EMAIL>',
  phone: '+86 ************',
  address: '中国 · 北京 · 朝阳区',
  socialLinks: [
    {
      name: 'GitHub',
      href: 'https://github.com/mysqlai',
      icon: 'Github',
    },
    {
      name: '微信',
      href: '#',
      icon: 'MessageCircle',
    },
    {
      name: 'QQ群',
      href: '#',
      icon: 'Users',
    },
  ],
} as const;

// 页脚配置
export const FOOTER_SECTIONS = [
  {
    title: '产品服务',
    links: [
      { name: 'MySQL优化', href: '/services/optimization' },
      { name: '性能调优', href: '/services/tuning' },
      { name: '架构设计', href: '/services/architecture' },
      { name: '数据迁移', href: '/services/migration' },
    ],
  },
  {
    title: '解决方案',
    links: [
      { name: '企业级方案', href: '/solutions/enterprise' },
      { name: '云数据库', href: '/solutions/cloud' },
      { name: '高可用架构', href: '/solutions/ha' },
      { name: '灾备方案', href: '/solutions/disaster-recovery' },
    ],
  },
  {
    title: '学习资源',
    links: [
      { name: '技术博客', href: '/blog' },
      { name: '视频教程', href: '/tutorials' },
      { name: 'API文档', href: '/docs' },
      { name: '最佳实践', href: '/best-practices' },
    ],
  },
  {
    title: '关于我们',
    links: [
      { name: '公司介绍', href: '/about' },
      { name: '团队成员', href: '/team' },
      { name: '招聘信息', href: '/careers' },
      { name: '联系我们', href: '/contact' },
    ],
  },
] as const;

export const FOOTER_LEGAL_LINKS = [
  { name: '服务条款', href: '/terms' },
  { name: '隐私政策', href: '/privacy' },
  { name: '免责声明', href: '/disclaimer' },
] as const;

// 动画配置
export const ANIMATION_CONFIG = {
  duration: {
    fast: 0.2,
    normal: 0.3,
    slow: 0.5,
  },
  easing: {
    easeInOut: [0.4, 0, 0.2, 1],
    easeOut: [0, 0, 0.2, 1],
    easeIn: [0.4, 0, 1, 1],
  },
  delay: {
    none: 0,
    short: 0.1,
    medium: 0.2,
    long: 0.3,
  },
} as const;

// 响应式断点配置
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// 错误消息配置
export const ERROR_MESSAGES = {
  required: '此字段为必填项',
  email: '请输入有效的邮箱地址',
  phone: '请输入有效的手机号码',
  minLength: (min: number) => `最少需要${min}个字符`,
  maxLength: (max: number) => `最多允许${max}个字符`,
  network: '网络连接失败，请稍后重试',
  server: '服务器错误，请联系技术支持',
  unknown: '未知错误，请稍后重试',
} as const;

// 成功消息配置
export const SUCCESS_MESSAGES = {
  formSubmit: '表单提交成功！',
  dataSaved: '数据保存成功！',
  emailSent: '邮件发送成功！',
  copied: '已复制到剪贴板',
} as const;
