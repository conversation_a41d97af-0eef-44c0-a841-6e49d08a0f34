{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1422", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "MySQLAi Desktop Installer", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true, "sidecar": false}, "dialog": {"all": false, "ask": true, "confirm": true, "message": true, "open": true, "save": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true, "scope": ["$APPDATA", "$APPDATA/**", "$TEMP", "$TEMP/**"]}, "path": {"all": true}, "os": {"all": false}, "http": {"all": false, "request": true, "scope": ["https://api.mysqlai.de/**", "https://download.mysql.com/**"]}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true, "setTitle": true, "setSize": true, "setPosition": true}, "globalShortcut": {"all": false}, "notification": {"all": false}}, "bundle": {"active": true, "targets": "all", "identifier": "com.mysqlai.desktop-installer", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.mysqlai.de https://download.mysql.com; object-src 'none'; base-uri 'self'; form-action 'self';", "devCsp": "default-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data: https:; connect-src 'self' ws: https:;", "freezePrototype": true, "dangerousDisableAssetCspModification": false, "dangerousRemoteDomainIpcAccess": [], "dangerousUseHttpScheme": false}, "windows": [{"fullscreen": false, "resizable": true, "title": "MySQLAi Desktop Installer", "width": 1000, "height": 700, "minWidth": 800, "minHeight": 600}]}}