import React from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
// import { PAGE_METADATA } from '@/lib/constants';
import { getKnowledgeItemById } from '@/lib/knowledge';
import ItemPageClient from '@/components/knowledge/ItemPageClient';

// 生成页面元数据
export async function generateMetadata({
  params
}: {
  params: Promise<{ category: string; item: string }>
}): Promise<Metadata> {
  try {
    const { item: itemId } = await params;
    const item = await getKnowledgeItemById(itemId);

    if (!item) {
      return {
        title: '知识点未找到 - MySQLAi.de',
        description: '您访问的知识点不存在或已被删除',
      };
    }

    return {
      title: `${item.title} - MySQL知识库 - MySQLAi.de`,
      description: item.description || '',
      keywords: [
        'MySQL',
        '数据库',
        '知识库',
        ...(item.tags || []),
        item.title,
        item.category_id || '',
        item.difficulty
      ].join(', '),
      openGraph: {
        title: `${item.title} - MySQL知识库`,
        description: item.description || '',
        type: 'article',
        publishedTime: item.last_updated,
        tags: item.tags || [],
      },
      twitter: {
        card: 'summary_large_image',
        title: `${item.title} - MySQL知识库`,
        description: item.description || '',
      },
    };
  } catch (error) {
    console.error('生成元数据失败:', error);
    return {
      title: '知识点详情 - MySQLAi.de',
      description: 'MySQL知识库详情页面',
    };
  }
}

// 生成静态路径（可选，用于静态生成）
export async function generateStaticParams() {
  try {
    // const categories = getKnowledgeCategories();
    const params: { category: string; item: string }[] = [];
    
    // 这里可以根据需要生成静态路径
    // 由于知识点较多，建议使用动态生成
    
    return params;
  } catch (error) {
    console.error('生成静态路径失败:', error);
    return [];
  }
}

// 知识点详情页面组件
export default async function KnowledgeItemPage({
  params
}: {
  params: Promise<{ category: string; item: string }>
}) {
  try {
    const { category, item: itemId } = await params;

    // 获取知识点数据
    const item = await getKnowledgeItemById(itemId);

    // 如果知识点不存在，返回404
    if (!item) {
      notFound();
    }

    // 验证分类是否匹配
    if (item.category_id !== category) {
      notFound();
    }

    return (
      <ItemPageClient
        item={item}
        categorySlug={category}
        itemSlug={itemId}
      />
    );
  } catch (error) {
    console.error('加载知识点页面失败:', error);
    notFound();
  }
}
