'use client';

// MySQLAi.de - Button按钮组件
// 可复用的按钮组件，支持多种变体和尺寸

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ButtonProps } from '@/lib/types';

const Button = React.forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  ButtonProps
>(({
  className,
  variant = 'primary',
  size = 'md',
  href,
  onClick,
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  type = 'button',
  children,
  ...props
}, ref) => {
  // 基础样式
  const baseStyles = [
    'inline-flex items-center justify-center font-semibold rounded-lg',
    'transition-all duration-300 ease-out',
    'focus:outline-none focus:ring-4',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'disabled:hover:scale-100 disabled:hover:shadow-none'
  ].join(' ');

  // 变体样式
  const variantStyles = {
    primary: [
      'bg-mysql-primary text-white shadow-lg',
      'hover:bg-mysql-primary-dark hover:shadow-xl hover:scale-105',
      'focus:ring-mysql-primary/30',
      'active:scale-95'
    ].join(' '),
    
    secondary: [
      'bg-white text-mysql-primary border-2 border-mysql-primary shadow-lg',
      'hover:bg-mysql-primary hover:text-white hover:shadow-xl hover:scale-105',
      'focus:ring-mysql-primary/30',
      'active:scale-95'
    ].join(' '),
    
    outline: [
      'bg-transparent text-mysql-primary border-2 border-mysql-primary',
      'hover:bg-mysql-primary hover:text-white hover:shadow-lg hover:scale-105',
      'focus:ring-mysql-primary/30',
      'active:scale-95'
    ].join(' '),
    
    ghost: [
      'bg-transparent text-mysql-primary',
      'hover:bg-mysql-primary-light hover:scale-105',
      'focus:ring-mysql-primary/30',
      'active:scale-95'
    ].join(' ')
  };

  // 尺寸样式
  const sizeStyles = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  // 组合样式
  const buttonStyles = cn(
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    className
  );

  // 图标渲染
  const renderIcon = () => {
    if (loading) {
      return <Loader2 className="w-4 h-4 animate-spin" />;
    }
    return icon;
  };

  // 内容渲染
  const renderContent = () => (
    <>
      {(icon || loading) && iconPosition === 'left' && (
        <span className={cn('flex items-center', children && 'mr-2')}>
          {renderIcon()}
        </span>
      )}
      {children}
      {icon && iconPosition === 'right' && (
        <span className={cn('flex items-center', children && 'ml-2')}>
          {renderIcon()}
        </span>
      )}
    </>
  );

  // 如果有href，渲染为Link
  if (href) {
    return (
      <motion.div
        whileHover={{ scale: disabled ? 1 : 1.05, y: disabled ? 0 : -2 }}
        whileTap={{ scale: disabled ? 1 : 0.95 }}
      >
        <Link
          href={href}
          className={buttonStyles}
          {...(props as any)}
          ref={ref as any}
        >
          {renderContent()}
        </Link>
      </motion.div>
    );
  }

  // 渲染为button
  return (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05, y: disabled ? 0 : -2 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      className={buttonStyles}
      onClick={onClick}
      disabled={disabled || loading}
      type={type}
      {...(props as any)}
      ref={ref as any}
    >
      {renderContent()}
    </motion.button>
  );
});

Button.displayName = 'Button';

export default Button;
