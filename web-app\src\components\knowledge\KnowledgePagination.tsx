'use client';

// MySQLAi.de - 知识库分页组件
// 管理后台专用的统一分页组件

import React from 'react';
import { motion } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  MoreHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

interface KnowledgePaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  pageSizeOptions?: number[];
  className?: string;
}

export default function KnowledgePagination({
  current,
  total,
  pageSize,
  onChange,
  onPageSizeChange,
  showSizeChanger = true,
  showQuickJumper = false,
  showTotal = true,
  pageSizeOptions = [10, 20, 50, 100],
  className
}: KnowledgePaginationProps) {
  const totalPages = Math.ceil(total / pageSize);
  const startItem = (current - 1) * pageSize + 1;
  const endItem = Math.min(current * pageSize, total);

  // 生成页码数组
  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisible = 7; // 最多显示7个页码

    if (totalPages <= maxVisible) {
      // 如果总页数不超过最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 复杂的页码生成逻辑
      pages.push(1);

      if (current <= 4) {
        // 当前页在前面
        for (let i = 2; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (current >= totalPages - 3) {
        // 当前页在后面
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push('...');
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  // 处理页码点击
  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== current) {
      onChange(page);
    }
  };

  // 处理页面大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize);
      // 调整当前页码，确保不超出范围
      const newTotalPages = Math.ceil(total / newPageSize);
      if (current > newTotalPages) {
        onChange(newTotalPages);
      }
    }
  };

  // 快速跳转处理
  const handleQuickJump = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const value = parseInt((e.target as HTMLInputElement).value);
      if (value >= 1 && value <= totalPages) {
        onChange(value);
        (e.target as HTMLInputElement).value = '';
      }
    }
  };

  if (total === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex items-center justify-between flex-wrap gap-4 p-4 bg-white border-t border-mysql-border',
        className
      )}
    >
      {/* 左侧信息 */}
      <div className="flex items-center space-x-4">
        {/* 总数显示 */}
        {showTotal && (
          <div className="text-sm text-mysql-text-light">
            显示 <span className="font-medium text-mysql-text">{startItem}</span> 到{' '}
            <span className="font-medium text-mysql-text">{endItem}</span> 条，
            共 <span className="font-medium text-mysql-text">{total}</span> 条
          </div>
        )}

        {/* 页面大小选择器 */}
        {showSizeChanger && onPageSizeChange && (
          <div className="flex items-center space-x-2">
            <label htmlFor="page-size-selector" className="text-sm text-mysql-text-light">
              每页
            </label>
            <select
              id="page-size-selector"
              value={pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
              aria-label="选择每页显示条数"
              className="px-2 py-1 text-sm border border-mysql-border rounded focus:outline-none focus:ring-2 focus:ring-mysql-primary/20 focus:border-mysql-primary"
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className="text-sm text-mysql-text-light">条</span>
          </div>
        )}
      </div>

      {/* 右侧分页控件 */}
      <div className="flex items-center space-x-2">
        {/* 快速跳转 */}
        {showQuickJumper && (
          <div className="flex items-center space-x-2 mr-4">
            <label htmlFor="quick-jump-input" className="text-sm text-mysql-text-light">
              跳至
            </label>
            <input
              id="quick-jump-input"
              type="number"
              min={1}
              max={totalPages}
              onKeyDown={handleQuickJump}
              aria-label="跳转到指定页面"
              className="w-16 px-2 py-1 text-sm border border-mysql-border rounded focus:outline-none focus:ring-2 focus:ring-mysql-primary/20 focus:border-mysql-primary"
              placeholder="页码"
            />
            <span className="text-sm text-mysql-text-light">页</span>
          </div>
        )}

        {/* 分页按钮 */}
        <div className="flex items-center space-x-1">
          {/* 首页 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePageClick(1)}
            disabled={current === 1}
            icon={<ChevronsLeft className="w-4 h-4" />}
            className="px-2"
            aria-label="跳转到首页"
          />

          {/* 上一页 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePageClick(current - 1)}
            disabled={current === 1}
            icon={<ChevronLeft className="w-4 h-4" />}
            className="px-2"
            aria-label="上一页"
          />

          {/* 页码 */}
          {pageNumbers.map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-3 py-1 text-mysql-text-light">
                  <MoreHorizontal className="w-4 h-4" />
                </span>
              ) : (
                <Button
                  variant={current === page ? "primary" : "ghost"}
                  size="sm"
                  onClick={() => handlePageClick(page as number)}
                  className="px-3 min-w-[2rem]"
                >
                  {page}
                </Button>
              )}
            </React.Fragment>
          ))}

          {/* 下一页 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePageClick(current + 1)}
            disabled={current === totalPages}
            icon={<ChevronRight className="w-4 h-4" />}
            className="px-2"
            aria-label="下一页"
          />

          {/* 末页 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePageClick(totalPages)}
            disabled={current === totalPages}
            icon={<ChevronsRight className="w-4 h-4" />}
            className="px-2"
            aria-label="跳转到末页"
          />
        </div>
      </div>
    </motion.div>
  );
}
