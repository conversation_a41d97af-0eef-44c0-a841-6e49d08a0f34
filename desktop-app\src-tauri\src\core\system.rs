use serde::{Deserialize, Serialize};
use sysinfo::{System, SystemExt, CpuExt, DiskExt};
use crate::core::types::{SystemInfo, SystemRequirement, SupportedOS, SystemArchitecture};

/// 系统检测器
#[derive(Debug)]
pub struct SystemDetector {
    system: System,
}

impl SystemDetector {
    /// 创建新的系统检测器
    pub fn new() -> Self {
        let mut system = System::new_all();
        system.refresh_all();
        
        Self { system }
    }

    /// 刷新系统信息
    pub fn refresh(&mut self) {
        self.system.refresh_all();
    }

    /// 检测操作系统
    pub fn detect_os(&self) -> SupportedOS {
        match std::env::consts::OS {
            "windows" => SupportedOS::Windows,
            "macos" => SupportedOS::Macos,
            "linux" => SupportedOS::Linux,
            _ => SupportedOS::Unknown,
        }
    }

    /// 检测系统架构
    pub fn detect_architecture(&self) -> SystemArchitecture {
        match std::env::consts::ARCH {
            "x86_64" => SystemArchitecture::X64,
            "aarch64" => SystemArchitecture::ARM64,
            "x86" => SystemArchitecture::X86,
            _ => SystemArchitecture::Unknown,
        }
    }

    /// 获取操作系统版本
    pub fn get_os_version(&self) -> Option<String> {
        self.system.os_version()
    }

    /// 获取系统信息
    pub fn get_system_info(&self) -> SystemInfo {
        let os = self.detect_os();
        let architecture = self.detect_architecture();
        let requirements = self.check_system_requirements(&os);
        let is_supported = self.is_mysql_supported(&os, &architecture);

        SystemInfo {
            os,
            architecture,
            os_version: self.get_os_version(),
            browser: None, // 桌面应用不需要浏览器信息
            is_supported,
            requirements,
        }
    }

    /// 检查MySQL安装是否支持
    pub fn is_mysql_supported(&self, os: &SupportedOS, architecture: &SystemArchitecture) -> bool {
        match (os, architecture) {
            // Windows支持
            (SupportedOS::Windows, SystemArchitecture::X64) => true,
            (SupportedOS::Windows, SystemArchitecture::X86) => true,
            
            // macOS支持
            (SupportedOS::Macos, SystemArchitecture::X64) => true,
            (SupportedOS::Macos, SystemArchitecture::ARM64) => true, // Apple Silicon
            
            // Linux支持
            (SupportedOS::Linux, SystemArchitecture::X64) => true,
            (SupportedOS::Linux, SystemArchitecture::ARM64) => true,
            
            // 其他组合不支持
            _ => false,
        }
    }

    /// 检查系统要求
    pub fn check_system_requirements(&self, os: &SupportedOS) -> Vec<SystemRequirement> {
        let mut requirements = Vec::new();

        // 1. 检查内存要求
        requirements.push(self.check_memory_requirement());

        // 2. 检查磁盘空间要求
        requirements.push(self.check_disk_space_requirement());

        // 3. 检查操作系统版本要求
        requirements.push(self.check_os_version_requirement(os));

        // 4. 检查管理员权限要求
        requirements.push(self.check_admin_privileges_requirement());

        // 5. 检查网络连接要求
        requirements.push(self.check_network_requirement());

        requirements
    }

    /// 检查内存要求
    fn check_memory_requirement(&self) -> SystemRequirement {
        let total_memory = self.system.total_memory();
        let required_memory = 2 * 1024 * 1024 * 1024; // 2GB in bytes
        
        let satisfied = total_memory >= required_memory;
        let description = if satisfied {
            format!("系统内存: {:.1} GB (满足要求)", total_memory as f64 / (1024.0 * 1024.0 * 1024.0))
        } else {
            format!("系统内存: {:.1} GB (需要至少 2 GB)", total_memory as f64 / (1024.0 * 1024.0 * 1024.0))
        };

        SystemRequirement {
            name: "内存要求".to_string(),
            satisfied,
            description,
            suggestion: if satisfied {
                None
            } else {
                Some("建议升级系统内存至少2GB以确保MySQL正常运行".to_string())
            },
        }
    }

    /// 检查磁盘空间要求
    fn check_disk_space_requirement(&self) -> SystemRequirement {
        let mut total_available = 0u64;
        let mut has_system_disk = false;

        for disk in self.system.disks() {
            let available_space = disk.available_space();
            total_available += available_space;
            
            // 检查是否是系统盘
            let mount_point = disk.mount_point().to_string_lossy();
            if mount_point == "/" || mount_point.starts_with("C:") {
                has_system_disk = true;
            }
        }

        let required_space = 5 * 1024 * 1024 * 1024; // 5GB in bytes
        let satisfied = total_available >= required_space && has_system_disk;
        
        let description = if satisfied {
            format!("可用磁盘空间: {:.1} GB (满足要求)", total_available as f64 / (1024.0 * 1024.0 * 1024.0))
        } else {
            format!("可用磁盘空间: {:.1} GB (需要至少 5 GB)", total_available as f64 / (1024.0 * 1024.0 * 1024.0))
        };

        SystemRequirement {
            name: "磁盘空间要求".to_string(),
            satisfied,
            description,
            suggestion: if satisfied {
                None
            } else {
                Some("请清理磁盘空间，确保至少有5GB可用空间用于MySQL安装".to_string())
            },
        }
    }

    /// 检查操作系统版本要求
    fn check_os_version_requirement(&self, os: &SupportedOS) -> SystemRequirement {
        let os_version = self.get_os_version().unwrap_or_default();
        let (satisfied, suggestion) = match os {
            SupportedOS::Windows => {
                // Windows 10 或更高版本
                let satisfied = self.check_windows_version(&os_version);
                (satisfied, if satisfied { None } else { 
                    Some("建议使用Windows 10或更高版本以获得最佳兼容性".to_string()) 
                })
            },
            SupportedOS::Macos => {
                // macOS 10.15 或更高版本
                let satisfied = self.check_macos_version(&os_version);
                (satisfied, if satisfied { None } else { 
                    Some("建议使用macOS 10.15 (Catalina)或更高版本".to_string()) 
                })
            },
            SupportedOS::Linux => {
                // 大多数现代Linux发行版都支持
                (true, None)
            },
            SupportedOS::Unknown => {
                (false, Some("不支持的操作系统".to_string()))
            },
        };

        SystemRequirement {
            name: "操作系统版本".to_string(),
            satisfied,
            description: format!("当前系统: {} {}", 
                match os {
                    SupportedOS::Windows => "Windows",
                    SupportedOS::Macos => "macOS",
                    SupportedOS::Linux => "Linux",
                    SupportedOS::Unknown => "Unknown",
                },
                os_version
            ),
            suggestion,
        }
    }

    /// 检查Windows版本
    fn check_windows_version(&self, version: &str) -> bool {
        // 简单的版本检查，实际应用中可能需要更复杂的逻辑
        version.contains("10") || version.contains("11") || version.contains("2019") || version.contains("2022")
    }

    /// 检查macOS版本
    fn check_macos_version(&self, version: &str) -> bool {
        // 简单的版本检查
        if let Some(version_num) = self.parse_macos_version(version) {
            version_num >= 10.15
        } else {
            false
        }
    }

    /// 解析macOS版本号
    fn parse_macos_version(&self, version: &str) -> Option<f32> {
        // 尝试从版本字符串中提取版本号
        let parts: Vec<&str> = version.split('.').collect();
        if parts.len() >= 2 {
            if let (Ok(major), Ok(minor)) = (parts[0].parse::<u32>(), parts[1].parse::<u32>()) {
                return Some(major as f32 + minor as f32 / 100.0);
            }
        }
        None
    }

    /// 检查管理员权限要求
    fn check_admin_privileges_requirement(&self) -> SystemRequirement {
        let satisfied = self.check_admin_privileges();
        
        SystemRequirement {
            name: "管理员权限".to_string(),
            satisfied,
            description: if satisfied {
                "具有管理员权限".to_string()
            } else {
                "需要管理员权限".to_string()
            },
            suggestion: if satisfied {
                None
            } else {
                Some("请以管理员身份运行此应用程序以安装MySQL服务".to_string())
            },
        }
    }

    /// 检查是否具有管理员权限
    fn check_admin_privileges(&self) -> bool {
        #[cfg(target_os = "windows")]
        {
            self.check_windows_admin()
        }
        
        #[cfg(target_os = "linux")]
        {
            self.check_linux_admin()
        }
        
        #[cfg(target_os = "macos")]
        {
            self.check_macos_admin()
        }
        
        #[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
        {
            false
        }
    }

    /// 检查Windows管理员权限
    #[cfg(target_os = "windows")]
    fn check_windows_admin(&self) -> bool {
        use std::process::Command;
        
        // 尝试执行需要管理员权限的命令
        match Command::new("net")
            .args(&["session"])
            .output()
        {
            Ok(output) => output.status.success(),
            Err(_) => false,
        }
    }

    /// 检查Linux管理员权限
    #[cfg(target_os = "linux")]
    fn check_linux_admin(&self) -> bool {
        use std::process::Command;
        
        // 检查是否为root用户或具有sudo权限
        if let Ok(output) = Command::new("id").arg("-u").output() {
            if let Ok(uid_str) = String::from_utf8(output.stdout) {
                if let Ok(uid) = uid_str.trim().parse::<u32>() {
                    return uid == 0; // root用户
                }
            }
        }
        
        // 检查sudo权限
        match Command::new("sudo").args(&["-n", "true"]).output() {
            Ok(output) => output.status.success(),
            Err(_) => false,
        }
    }

    /// 检查macOS管理员权限
    #[cfg(target_os = "macos")]
    fn check_macos_admin(&self) -> bool {
        use std::process::Command;
        
        // 检查是否为管理员组成员
        match Command::new("groups").output() {
            Ok(output) => {
                if let Ok(groups_str) = String::from_utf8(output.stdout) {
                    return groups_str.contains("admin");
                }
            }
            Err(_) => {}
        }
        
        false
    }

    /// 检查网络连接要求
    fn check_network_requirement(&self) -> SystemRequirement {
        let satisfied = self.check_network_connectivity();
        
        SystemRequirement {
            name: "网络连接".to_string(),
            satisfied,
            description: if satisfied {
                "网络连接正常".to_string()
            } else {
                "网络连接异常".to_string()
            },
            suggestion: if satisfied {
                None
            } else {
                Some("请检查网络连接，确保能够下载MySQL安装包".to_string())
            },
        }
    }

    /// 检查网络连接
    fn check_network_connectivity(&self) -> bool {
        use std::process::Command;
        use std::time::Duration;
        
        // 尝试ping一个可靠的服务器
        let ping_targets = ["*******", "*******", "***************"];
        
        for target in &ping_targets {
            let result = if cfg!(target_os = "windows") {
                Command::new("ping")
                    .args(&["-n", "1", "-w", "3000", target])
                    .output()
            } else {
                Command::new("ping")
                    .args(&["-c", "1", "-W", "3", target])
                    .output()
            };
            
            if let Ok(output) = result {
                if output.status.success() {
                    return true;
                }
            }
        }
        
        false
    }

    /// 获取系统性能信息
    pub fn get_system_performance_info(&self) -> SystemPerformanceInfo {
        SystemPerformanceInfo {
            cpu_count: self.system.cpus().len(),
            total_memory: self.system.total_memory(),
            available_memory: self.system.available_memory(),
            cpu_usage: self.get_average_cpu_usage(),
            disk_usage: self.get_disk_usage_info(),
        }
    }

    /// 获取平均CPU使用率
    fn get_average_cpu_usage(&self) -> f32 {
        let total_usage: f32 = self.system.cpus().iter()
            .map(|cpu| cpu.cpu_usage())
            .sum();
        
        if self.system.cpus().is_empty() {
            0.0
        } else {
            total_usage / self.system.cpus().len() as f32
        }
    }

    /// 获取磁盘使用信息
    fn get_disk_usage_info(&self) -> Vec<DiskUsageInfo> {
        self.system.disks().iter()
            .map(|disk| {
                let total = disk.total_space();
                let available = disk.available_space();
                let used = total - available;
                let usage_percent = if total > 0 {
                    (used as f64 / total as f64) * 100.0
                } else {
                    0.0
                };

                DiskUsageInfo {
                    name: disk.name().to_string_lossy().to_string(),
                    mount_point: disk.mount_point().to_string_lossy().to_string(),
                    total_space: total,
                    available_space: available,
                    used_space: used,
                    usage_percent,
                }
            })
            .collect()
    }
}

/// 系统性能信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemPerformanceInfo {
    pub cpu_count: usize,
    pub total_memory: u64,
    pub available_memory: u64,
    pub cpu_usage: f32,
    pub disk_usage: Vec<DiskUsageInfo>,
}

/// 磁盘使用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskUsageInfo {
    pub name: String,
    pub mount_point: String,
    pub total_space: u64,
    pub available_space: u64,
    pub used_space: u64,
    pub usage_percent: f64,
}

/// 创建系统检测器实例
pub fn create_system_detector() -> SystemDetector {
    SystemDetector::new()
}

/// 获取系统信息（便捷函数）
pub fn get_system_info() -> SystemInfo {
    let detector = SystemDetector::new();
    detector.get_system_info()
}

/// 检查系统是否支持MySQL安装（便捷函数）
pub fn is_mysql_installation_supported() -> bool {
    let detector = SystemDetector::new();
    let os = detector.detect_os();
    let arch = detector.detect_architecture();
    detector.is_mysql_supported(&os, &arch)
}