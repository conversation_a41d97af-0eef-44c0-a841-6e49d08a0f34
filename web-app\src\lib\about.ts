// MySQLAi.de - 关于我们数据配置文件
// 管理关于我们页面相关的数据和配置

import { Zap, Users, TrendingUp, FileText, Clock, Shield, Database, Settings, Award, Globe } from 'lucide-react';

// 专业特性数据
export const PROFESSIONAL_FEATURES = [
  {
    id: 'ai-analysis',
    title: '智能分析',
    description: 'AI驱动的MySQL性能分析，提供精准的优化建议和解决方案。',
    icon: 'Zap',
    color: 'mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
    details: [
      '自动性能瓶颈检测',
      '智能SQL优化建议',
      '实时监控告警',
      '预测性维护方案',
    ],
  },
  {
    id: 'expert-consulting',
    title: '专业咨询',
    description: '资深数据库专家团队，提供一对一的专业咨询服务。',
    icon: 'Users',
    color: 'mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
    details: [
      '15年+数据库经验专家',
      '一对一专属咨询服务',
      '定制化解决方案',
      '7×24小时技术支持',
    ],
  },
  {
    id: 'efficient-management',
    title: '高效管理',
    description: '现代化的项目管理工具，提升团队协作效率和项目成功率。',
    icon: 'TrendingUp',
    color: 'mysql-success',
    gradient: 'from-mysql-success to-green-600',
    details: [
      '敏捷项目管理方法',
      '可视化进度跟踪',
      '团队协作工具集成',
      '自动化工作流程',
    ],
  },
  {
    id: 'transparent-reporting',
    title: '透明报告',
    description: '详细的项目报告和数据分析，确保项目进展透明可控。',
    icon: 'FileText',
    color: 'mysql-warning',
    gradient: 'from-mysql-warning to-yellow-600',
    details: [
      '实时数据可视化',
      '多维度分析报告',
      '自定义报告模板',
      '数据导出多格式',
    ],
  },
  {
    id: '24-7-support',
    title: '7×24支持',
    description: '全天候技术支持服务，确保您的数据库系统稳定运行。',
    icon: 'Clock',
    color: 'mysql-error',
    gradient: 'from-mysql-error to-red-600',
    details: [
      '全年无休技术支持',
      '5分钟快速响应',
      '远程故障诊断',
      '紧急事件处理',
    ],
  },
] as const;

// 核心优势数据
export const CORE_ADVANTAGES = [
  {
    id: 'security',
    title: '企业级安全',
    description: '银行级安全保障，多重加密保护您的数据安全。',
    icon: 'Shield',
    stats: '99.99%',
    details: [
      'SSL/TLS加密传输',
      '数据库访问控制',
      '审计日志记录',
      '定期安全评估',
    ],
  },
  {
    id: 'performance',
    title: '卓越性能',
    description: '优化的数据库架构，确保高性能和高可用性。',
    icon: 'Database',
    stats: '<100ms',
    details: [
      '查询响应优化',
      '索引智能建议',
      '缓存策略优化',
      '负载均衡配置',
    ],
  },
  {
    id: 'scalability',
    title: '弹性扩展',
    description: '灵活的扩展方案，轻松应对业务增长需求。',
    icon: 'Settings',
    stats: '1000+',
    details: [
      '水平扩展支持',
      '自动故障转移',
      '读写分离配置',
      '分库分表方案',
    ],
  },
  {
    id: 'expertise',
    title: '专业团队',
    description: '资深数据库专家团队，丰富的行业经验。',
    icon: 'Award',
    stats: '15年+',
    details: [
      'MySQL认证专家',
      '大型项目经验',
      '行业最佳实践',
      '持续技术创新',
    ],
  },
] as const;

// 统计数据
export const STATISTICS = [
  {
    id: 'customers',
    value: '1000+',
    label: '企业客户',
    description: '服务全球1000+企业客户',
  },
  {
    id: 'availability',
    value: '99.9%',
    label: '服务可用性',
    description: '行业领先的服务可用性保证',
  },
  {
    id: 'response-time',
    value: '5分钟',
    label: '响应时间',
    description: '平均5分钟快速响应时间',
  },
  {
    id: 'experience',
    value: '15年+',
    label: '专业经验',
    description: '团队平均15年+数据库经验',
  },
] as const;

// 团队成员数据
export const TEAM_MEMBERS = [
  {
    id: 'cto',
    name: '赵清荷',
    role: '首席技术官',
    avatar: '/team/cto.jpg',
    bio: '15年数据库架构经验，MySQL官方认证专家',
    expertise: ['数据库架构', 'MySQL优化', '高可用设计'],
    social: {
      linkedin: '#',
      github: '#',
      twitter: '#',
    },
  },
  {
    id: 'lead-dba',
    name: '李数据',
    role: '首席数据库工程师',
    avatar: '/team/lead-dba.jpg',
    bio: '专注MySQL性能优化，服务过多家大型互联网公司',
    expertise: ['性能调优', 'SQL优化', '监控告警'],
    social: {
      linkedin: '#',
      github: '#',
    },
  },
  {
    id: 'pm',
    name: '刘牛X',
    role: '项目管理总监',
    avatar: '/team/pm.jpg',
    bio: '敏捷项目管理专家，PMP认证，成功交付100+项目',
    expertise: ['项目管理', '敏捷开发', '团队协作'],
    social: {
      linkedin: '#',
    },
  },
] as const;

// 公司里程碑
export const MILESTONES = [
  {
    year: '2020',
    title: '公司成立',
    description: 'MySQLAi.de正式成立，专注MySQL优化服务',
  },
  {
    year: '2021',
    title: '技术突破',
    description: '推出AI驱动的MySQL性能分析平台',
  },
  {
    year: '2022',
    title: '规模扩张',
    description: '服务客户突破500家，团队扩展至50人',
  },
  {
    year: '2023',
    title: '国际化',
    description: '业务拓展至全球8个国家和地区',
  },
  {
    year: '2024',
    title: '行业领先',
    description: '成为MySQL优化服务领域的领导者',
  },
] as const;

// 图标映射
export const ABOUT_ICON_MAP = {
  Zap,
  Users,
  TrendingUp,
  FileText,
  Clock,
  Shield,
  Database,
  Settings,
  Award,
  Globe,
} as const;

// 工具函数

/**
 * 根据ID获取专业特性
 */
export function getProfessionalFeatureById(id: string) {
  return PROFESSIONAL_FEATURES.find(feature => feature.id === id);
}

/**
 * 根据ID获取核心优势
 */
export function getCoreAdvantageById(id: string) {
  return CORE_ADVANTAGES.find(advantage => advantage.id === id);
}

/**
 * 获取图标组件
 */
export function getAboutIconComponent(iconName: keyof typeof ABOUT_ICON_MAP) {
  return ABOUT_ICON_MAP[iconName];
}

/**
 * 获取团队成员
 */
export function getTeamMemberById(id: string) {
  return TEAM_MEMBERS.find(member => member.id === id);
}

/**
 * 获取统计数据
 */
export function getStatisticById(id: string) {
  return STATISTICS.find(stat => stat.id === id);
}

/**
 * 获取里程碑数据
 */
export function getMilestoneByYear(year: string) {
  return MILESTONES.find(milestone => milestone.year === year);
}

/**
 * 格式化统计数值
 */
export function formatStatValue(value: string): string {
  // 如果是数字，添加千分位分隔符
  if (/^\d+$/.test(value)) {
    return parseInt(value).toLocaleString();
  }
  return value;
}

/**
 * 获取随机专业特性
 */
export function getRandomProfessionalFeatures(count: number = 3) {
  const features = [...PROFESSIONAL_FEATURES];
  
  // 随机打乱数组
  for (let i = features.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [features[i], features[j]] = [features[j], features[i]];
  }
  
  return features.slice(0, count);
}

/**
 * 获取特性的颜色主题
 */
export function getFeatureColorTheme(color: string) {
  const themes = {
    'mysql-primary': {
      bg: 'bg-mysql-primary',
      text: 'text-mysql-primary',
      border: 'border-mysql-primary',
      gradient: 'from-mysql-primary to-mysql-primary-dark',
    },
    'mysql-accent': {
      bg: 'bg-mysql-accent',
      text: 'text-mysql-accent',
      border: 'border-mysql-accent',
      gradient: 'from-mysql-accent to-blue-600',
    },
    'mysql-success': {
      bg: 'bg-mysql-success',
      text: 'text-mysql-success',
      border: 'border-mysql-success',
      gradient: 'from-mysql-success to-green-600',
    },
    'mysql-warning': {
      bg: 'bg-mysql-warning',
      text: 'text-mysql-warning',
      border: 'border-mysql-warning',
      gradient: 'from-mysql-warning to-yellow-600',
    },
    'mysql-error': {
      bg: 'bg-mysql-error',
      text: 'text-mysql-error',
      border: 'border-mysql-error',
      gradient: 'from-mysql-error to-red-600',
    },
  };
  
  return themes[color as keyof typeof themes] || themes['mysql-primary'];
}
