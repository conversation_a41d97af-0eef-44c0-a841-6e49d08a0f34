import React, { useState } from 'react';
import {
  useInstaller,
  getComponentTypeDisplayName,
  getInstallationStatusText,
  getInstallationStatusColor,
  getComponentTypeColor,
  createDefaultMySQLConfig,
  MySQLConfig,
  InstallationStatus
} from '../hooks/useInstaller';
import { Button } from './ui';

interface InstallerPanelProps {
  className?: string;
}

export const InstallerPanel: React.FC<InstallerPanelProps> = ({ className = '' }) => {
  const {
    mysqlComponents,
    mysqlConfig,
    installationProgress,
    isDetecting,
    isUninstalling,
    isInstalling,
    isVerifying,
    isLoading,
    error,
    detectMySQLComponents,
    uninstallOldMySQL,
    installMySQL,
    verifyInstallation,
    setMySQLConfig,
    refreshAll
  } = useInstaller();

  const [showConfig, setShowConfig] = useState(false);
  const [configForm, setConfigForm] = useState<MySQLConfig>(createDefaultMySQLConfig());
  const [zipFilePath, setZipFilePath] = useState('C:\\MySQL\\Downloads\\mysql-8.0.36-winx64.zip');
  const [verificationResult, setVerificationResult] = useState<boolean | null>(null);

  // 处理配置更新
  const handleConfigUpdate = (field: keyof MySQLConfig, value: string | number) => {
    setConfigForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 保存配置
  const handleSaveConfig = async () => {
    await setMySQLConfig(configForm);
    setShowConfig(false);
  };

  // 处理卸载
  const handleUninstall = async () => {
    if (mysqlComponents.length === 0) {
      return;
    }
    await uninstallOldMySQL(mysqlComponents);
  };

  // 处理安装
  const handleInstall = async () => {
    if (!zipFilePath.trim()) {
      return;
    }
    await installMySQL(zipFilePath);
  };

  // 处理验证
  const handleVerify = async () => {
    const result = await verifyInstallation();
    setVerificationResult(result);
  };

  return (
    <div className={`p-6 bg-white rounded-lg shadow-md ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-800">MySQL安装器</h2>
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => setShowConfig(!showConfig)}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
          >
            配置
          </button>
          <button
            type="button"
            onClick={refreshAll}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded hover:bg-gray-200 disabled:opacity-50"
          >
            {isLoading ? '刷新中...' : '刷新'}
          </button>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
          <div className="text-red-700 text-sm">{error}</div>
        </div>
      )}

      {/* 配置面板 */}
      {showConfig && (
        <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">MySQL配置</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                安装路径
              </label>
              <input
                type="text"
                value={configForm.installPath}
                onChange={(e) => handleConfigUpdate('installPath', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                数据路径
              </label>
              <input
                type="text"
                value={configForm.dataPath}
                onChange={(e) => handleConfigUpdate('dataPath', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                端口
              </label>
              <input
                type="number"
                value={configForm.port}
                onChange={(e) => handleConfigUpdate('port', parseInt(e.target.value) || 3306)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Root密码
              </label>
              <input
                type="password"
                value={configForm.rootPassword}
                onChange={(e) => handleConfigUpdate('rootPassword', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                字符集
              </label>
              <select
                value={configForm.characterSet}
                onChange={(e) => handleConfigUpdate('characterSet', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="utf8mb4">utf8mb4</option>
                <option value="utf8">utf8</option>
                <option value="latin1">latin1</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                最大连接数
              </label>
              <input
                type="number"
                value={configForm.maxConnections}
                onChange={(e) => handleConfigUpdate('maxConnections', parseInt(e.target.value) || 151)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="mt-4 flex space-x-3">
            <Button
              variant="primary"
              onClick={handleSaveConfig}
            >
              保存配置
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowConfig(false)}
            >
              取消
            </Button>
          </div>
        </div>
      )}

      {/* 当前配置显示 */}
      {mysqlConfig && !showConfig && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">当前配置</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">安装路径:</span>
                <span className="ml-2 font-medium">{mysqlConfig.installPath}</span>
              </div>
              <div>
                <span className="text-gray-600">端口:</span>
                <span className="ml-2 font-medium">{mysqlConfig.port}</span>
              </div>
              <div>
                <span className="text-gray-600">字符集:</span>
                <span className="ml-2 font-medium">{mysqlConfig.characterSet}</span>
              </div>
              <div>
                <span className="text-gray-600">最大连接:</span>
                <span className="ml-2 font-medium">{mysqlConfig.maxConnections}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* MySQL组件检测 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-700">MySQL组件检测</h3>
          <button
            type="button"
            onClick={detectMySQLComponents}
            disabled={isDetecting}
            className="px-3 py-1 text-sm bg-green-100 text-green-600 rounded hover:bg-green-200 disabled:opacity-50"
          >
            {isDetecting ? '检测中...' : '重新检测'}
          </button>
        </div>

        {mysqlComponents.length > 0 ? (
          <div className="space-y-2">
            {mysqlComponents.map((component, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded border">
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${getComponentTypeColor(component.componentType)}`}>
                      {getComponentTypeDisplayName(component.componentType)}
                    </span>
                    <span className="ml-2 text-sm text-gray-600">
                      {component.location}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {component.description}
                  </div>
                </div>
              </div>
            ))}
            
            <div className="mt-4">
              <Button
                variant="outline"
                onClick={handleUninstall}
                disabled={isUninstalling}
                loading={isUninstalling}
                className="bg-red-600 text-white hover:bg-red-700 border-red-600"
              >
                {isUninstalling ? '卸载中...' : `卸载所有组件 (${mysqlComponents.length}个)`}
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {isDetecting ? '正在检测MySQL组件...' : '未检测到MySQL组件'}
          </div>
        )}
      </div>

      {/* 安装进度 */}
      {installationProgress && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">安装进度</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className={`font-medium ${getInstallationStatusColor(installationProgress.status)}`}>
                {installationProgress.currentStep} - {getInstallationStatusText(installationProgress.status)}
              </div>
              <div className="text-sm text-gray-600">
                {installationProgress.progressPercentage.toFixed(1)}%
              </div>
            </div>

            {/* 进度条 */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  installationProgress.status === InstallationStatus.Completed ? 'bg-green-500' :
                  installationProgress.status === InstallationStatus.Failed ? 'bg-red-500' :
                  installationProgress.status === InstallationStatus.InProgress ? 'bg-blue-500' : 'bg-gray-400'
                }`}
                style={{ width: `${installationProgress.progressPercentage}%` }}
              ></div>
            </div>

            <div className="text-sm text-gray-600">
              {installationProgress.message}
            </div>
          </div>
        </div>
      )}

      {/* MySQL安装 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">MySQL安装</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              MySQL安装包路径
            </label>
            <input
              type="text"
              value={zipFilePath}
              onChange={(e) => setZipFilePath(e.target.value)}
              placeholder="请输入MySQL ZIP文件的完整路径"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isInstalling}
            />
          </div>

          <div className="flex space-x-3">
            <Button
              variant="primary"
              onClick={handleInstall}
              disabled={!zipFilePath.trim() || isInstalling}
              loading={isInstalling}
              size="lg"
            >
              {isInstalling ? '安装中...' : '开始安装'}
            </Button>

            <Button
              variant="secondary"
              onClick={handleVerify}
              disabled={isVerifying}
              loading={isVerifying}
              className="bg-green-600 text-white hover:bg-green-700 border-green-600"
            >
              {isVerifying ? '验证中...' : '验证安装'}
            </Button>
          </div>

          {/* 验证结果 */}
          {verificationResult !== null && (
            <div className={`p-3 rounded ${verificationResult ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <div className={`text-sm ${verificationResult ? 'text-green-700' : 'text-red-700'}`}>
                {verificationResult ? '✓ MySQL安装验证成功' : '✗ MySQL安装验证失败'}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="text-sm font-semibold text-blue-800 mb-2">安装说明</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 安装前会自动检测并卸载旧版本MySQL</li>
          <li>• 支持自定义安装路径、端口、密码等配置</li>
          <li>• 自动创建配置文件并初始化数据目录</li>
          <li>• 自动安装并启动MySQL服务</li>
          <li>• 安装完成后可进行验证确保正常工作</li>
        </ul>
      </div>
    </div>
  );
};