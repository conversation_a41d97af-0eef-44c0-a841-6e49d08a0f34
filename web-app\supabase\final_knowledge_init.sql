-- MySQLAi.de - 知识库数据库初始化脚本
-- 基于前端组件分析设计的完整数据库结构

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建枚举类型
CREATE TYPE difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced');

-- ============================================================================
-- 核心表结构
-- ============================================================================

-- 1. 知识库分类表
CREATE TABLE public.knowledge_categories (
  id TEXT PRIMARY KEY, -- 使用字符串ID，与前端保持一致
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT, -- 图标名称（如 'Database', 'Server'）
  color TEXT, -- 颜色主题（如 'mysql-primary', 'mysql-accent'）
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 知识库文章表
CREATE TABLE public.knowledge_articles (
  id TEXT PRIMARY KEY, -- 使用字符串ID，与前端保持一致
  title TEXT NOT NULL,
  description TEXT,
  content TEXT NOT NULL, -- Markdown格式内容
  category_id TEXT REFERENCES public.knowledge_categories(id) ON DELETE SET NULL,
  tags TEXT[], -- 标签数组
  difficulty difficulty_level DEFAULT 'beginner',
  order_index INTEGER DEFAULT 0,
  last_updated DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 代码示例表
CREATE TABLE public.code_examples (
  id TEXT PRIMARY KEY,
  article_id TEXT REFERENCES public.knowledge_articles(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  code TEXT NOT NULL,
  language TEXT NOT NULL DEFAULT 'sql',
  description TEXT,
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 文章关联表（相关推荐）
CREATE TABLE public.article_relations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  source_article_id TEXT REFERENCES public.knowledge_articles(id) ON DELETE CASCADE NOT NULL,
  target_article_id TEXT REFERENCES public.knowledge_articles(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(source_article_id, target_article_id)
);

-- 5. 搜索历史表（可选，用于统计分析）
CREATE TABLE public.search_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  query TEXT NOT NULL,
  results_count INTEGER DEFAULT 0,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 创建索引（性能优化）
-- ============================================================================

-- 知识库分类索引
CREATE INDEX idx_categories_order ON public.knowledge_categories(order_index);

-- 知识库文章索引
CREATE INDEX idx_articles_category ON public.knowledge_articles(category_id);
CREATE INDEX idx_articles_tags ON public.knowledge_articles USING GIN(tags);
CREATE INDEX idx_articles_difficulty ON public.knowledge_articles(difficulty);
CREATE INDEX idx_articles_order ON public.knowledge_articles(order_index);
CREATE INDEX idx_articles_updated ON public.knowledge_articles(last_updated DESC);

-- 全文搜索索引
CREATE INDEX idx_articles_search ON public.knowledge_articles USING GIN(
  to_tsvector('english', title || ' ' || COALESCE(description, '') || ' ' || content)
);

-- 代码示例索引
CREATE INDEX idx_code_examples_article ON public.code_examples(article_id);
CREATE INDEX idx_code_examples_language ON public.code_examples(language);
CREATE INDEX idx_code_examples_order ON public.code_examples(order_index);

-- 文章关联索引
CREATE INDEX idx_relations_source ON public.article_relations(source_article_id);
CREATE INDEX idx_relations_target ON public.article_relations(target_article_id);

-- 搜索历史索引
CREATE INDEX idx_search_query ON public.search_history(query);
CREATE INDEX idx_search_created ON public.search_history(created_at DESC);

-- ============================================================================
-- 创建触发器函数
-- ============================================================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================================================
-- 应用触发器
-- ============================================================================

-- 更新时间戳触发器
CREATE TRIGGER update_articles_updated_at 
  BEFORE UPDATE ON public.knowledge_articles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 插入示例数据（从前端硬编码数据迁移）
-- ============================================================================

-- 插入知识库分类
INSERT INTO public.knowledge_categories (id, name, description, icon, color, order_index) VALUES
  ('basics', '基础知识', 'MySQL简介、安装配置、基本概念', 'Database', 'mysql-primary', 1),
  ('database-operations', '数据库操作', '创建、删除、选择数据库', 'Server', 'mysql-accent', 2),
  ('table-operations', '数据表操作', '创建、修改、删除表结构', 'Table', 'mysql-primary', 3),
  ('data-operations', '数据操作', 'INSERT、SELECT、UPDATE、DELETE', 'Edit', 'mysql-accent', 4),
  ('advanced-queries', '高级查询', 'JOIN、子查询、聚合函数、索引', 'Search', 'mysql-primary', 5),
  ('management', '管理维护', '用户权限、备份恢复、性能优化', 'Settings', 'mysql-accent', 6);

-- 插入知识库文章
INSERT INTO public.knowledge_articles (
  id, title, description, content, category_id, tags, difficulty, order_index, last_updated
) VALUES
  (
    'mysql-introduction', 
    'MySQL简介', 
    '了解MySQL数据库管理系统的基本概念、特点和应用场景',
    '# MySQL简介

MySQL是世界上最流行的开源关系型数据库管理系统（RDBMS）之一。它由瑞典MySQL AB公司开发，目前属于Oracle公司。

## 什么是MySQL？

MySQL是一个关系型数据库管理系统，关联数据库将数据保存在不同的表中，而不是将所有数据放在一个大仓库内，这样就增加了速度并提高了灵活性。

## MySQL的主要特点

- **开源免费**：MySQL是开源软件，可以免费使用
- **跨平台**：支持多种操作系统，包括Windows、Linux、macOS等
- **高性能**：优化的SQL查询算法，支持大型数据库
- **可靠性**：数据安全性高，支持事务处理
- **易于使用**：提供了友好的管理工具和丰富的文档

## 应用场景

MySQL广泛应用于：
- Web应用程序
- 数据仓库
- 电子商务
- 日志记录应用
- 内容管理系统

## MySQL与其他数据库的比较

| 特性 | MySQL | PostgreSQL | Oracle |
|------|-------|------------|--------|
| 开源 | ✓ | ✓ | ✗ |
| 性能 | 高 | 高 | 很高 |
| 成本 | 免费 | 免费 | 昂贵 |
| 易用性 | 简单 | 中等 | 复杂 |',
    'basics',
    ARRAY['MySQL', '数据库', '简介', 'RDBMS'],
    'beginner',
    1,
    '2025-06-28'
  ),
  (
    'database-concepts', 
    '数据库基本概念', 
    '理解数据库、表、行、列、主键、外键等核心概念',
    '# 数据库基本概念

在学习MySQL之前，我们需要了解一些数据库的基本概念。

## 数据库（Database）

数据库是按照数据结构来组织、存储和管理数据的仓库。每个数据库都有一个或多个不同的API用于创建、访问、管理、搜索和复制所保存的数据。

## 数据表（Table）

表是数据的矩阵。在一个数据库中的表看起来像一个简单的电子表格。

## 列（Column）

一列（数据元素）包含了相同类型的数据，例如邮政编码的数据。

## 行（Row）

一行（元组，或记录）是一组相关的数据，例如一条用户订阅的数据。

## 主键（Primary Key）

主键是唯一的。一个数据表中只能包含一个主键。你可以使用主键来查询数据。

## 外键（Foreign Key）

外键用于关联两个表。

## 索引（Index）

使用索引可快速访问数据库表中的特定信息。索引是对数据库表中一列或多列的值进行排序的一种结构。

## RDBMS术语

- **冗余**：存储两倍数据，冗余降低了性能，但提高了数据的安全性
- **复合键**：复合键（组合键）将多个列作为一个索引键，一般用于复合索引
- **参照完整性**：参照的完整性要求关系中不允许引用不存在的实体',
    'basics',
    ARRAY['数据库', '概念', '表', '主键', '外键'],
    'beginner',
    2,
    '2025-06-28'
  ),
  (
    'mysql-installation', 
    'MySQL安装配置', 
    '学习如何在不同操作系统上安装和配置MySQL',
    '# MySQL安装配置

本章将介绍如何在不同操作系统上安装MySQL数据库。

## Windows安装

### 下载MySQL

1. 访问MySQL官网：https://dev.mysql.com/downloads/
2. 选择MySQL Community Server
3. 选择适合的Windows版本

### 安装步骤

1. 运行下载的安装程序
2. 选择安装类型（推荐选择"Developer Default"）
3. 配置MySQL服务器
4. 设置root用户密码
5. 完成安装

## Linux安装

### Ubuntu/Debian系统

```bash
# 更新包列表
sudo apt update

# 安装MySQL服务器
sudo apt install mysql-server

# 启动MySQL服务
sudo systemctl start mysql

# 设置开机自启
sudo systemctl enable mysql
```

### CentOS/RHEL系统

```bash
# 安装MySQL仓库
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysqld

# 设置开机自启
sudo systemctl enable mysqld
```

## macOS安装

### 使用Homebrew

```bash
# 安装MySQL
brew install mysql

# 启动MySQL服务
brew services start mysql
```

## 初始配置

安装完成后，运行安全配置脚本：

```bash
mysql_secure_installation
```

## 连接测试

```bash
mysql -u root -p
```',
    'basics',
    ARRAY['安装', '配置', 'Windows', 'Linux', 'macOS'],
    'beginner',
    3,
    '2025-06-28'
  ),
  (
    'create-database',
    '创建数据库',
    '学习如何创建和管理MySQL数据库',
    '# 创建数据库

在MySQL中，创建数据库是数据库管理的第一步。

## 基本语法

```sql
CREATE DATABASE database_name;
```

## 完整语法

```sql
CREATE DATABASE [IF NOT EXISTS] database_name
[CHARACTER SET charset_name]
[COLLATE collation_name];
```

## 实际示例

```sql
-- 创建一个名为 mycompany 的数据库
CREATE DATABASE mycompany;

-- 创建数据库时指定字符集
CREATE DATABASE mycompany
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 安全创建（如果不存在才创建）
CREATE DATABASE IF NOT EXISTS mycompany;
```

## 最佳实践

1. **使用有意义的数据库名称**
2. **指定UTF8MB4字符集**：支持完整的Unicode字符
3. **遵循命名规范**：使用小写字母和下划线
4. **使用IF NOT EXISTS**：避免重复创建错误

## 字符集选择

- **utf8mb4**：推荐使用，支持完整的UTF-8字符集
- **utf8**：旧版本，不支持4字节字符（如emoji）
- **latin1**：仅支持西欧字符

## 验证创建结果

```sql
-- 查看所有数据库
SHOW DATABASES;

-- 查看数据库创建语句
SHOW CREATE DATABASE mycompany;
```',
    'database-operations',
    ARRAY['CREATE DATABASE', '数据库', '字符集', 'UTF8'],
    'beginner',
    1,
    '2025-06-28'
  ),
  (
    'select-basics',
    'SELECT查询基础',
    '掌握MySQL中最重要的SELECT语句的基本用法',
    '# SELECT查询基础

SELECT语句是MySQL中最常用的语句，用于从数据库表中检索数据。

## 基本语法

```sql
SELECT column1, column2, ...
FROM table_name
WHERE condition;
```

## 查询所有列

```sql
-- 查询表中所有列
SELECT * FROM users;
```

## 查询指定列

```sql
-- 查询指定列
SELECT name, email FROM users;
```

## 使用WHERE条件

```sql
-- 条件查询
SELECT * FROM users WHERE age > 18;

-- 多个条件
SELECT * FROM users
WHERE age > 18 AND city = ''北京'';
```

## 排序结果

```sql
-- 升序排序
SELECT * FROM users ORDER BY age ASC;

-- 降序排序
SELECT * FROM users ORDER BY age DESC;
```

## 限制结果数量

```sql
-- 限制返回前10条记录
SELECT * FROM users LIMIT 10;

-- 分页查询（跳过前10条，返回接下来的10条）
SELECT * FROM users LIMIT 10 OFFSET 10;
```

## 使用别名

```sql
-- 列别名
SELECT name AS 用户名, email AS 邮箱 FROM users;

-- 表别名
SELECT u.name, u.email FROM users AS u;
```',
    'data-operations',
    ARRAY['SELECT', '查询', 'WHERE', 'ORDER BY', 'LIMIT'],
    'beginner',
    1,
    '2025-06-28'
  );

-- 插入代码示例
INSERT INTO public.code_examples (id, article_id, title, code, language, description, order_index) VALUES
  (
    'ubuntu-install',
    'mysql-installation',
    'Ubuntu安装MySQL',
    '# 更新包列表
sudo apt update

# 安装MySQL服务器
sudo apt install mysql-server

# 启动MySQL服务
sudo systemctl start mysql

# 设置开机自启
sudo systemctl enable mysql

# 运行安全配置
sudo mysql_secure_installation',
    'bash',
    '在Ubuntu系统上安装MySQL的完整步骤',
    1
  ),
  (
    'centos-install',
    'mysql-installation',
    'CentOS安装MySQL',
    '# 安装MySQL仓库
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysqld

# 设置开机自启
sudo systemctl enable mysqld

# 查看临时密码
sudo grep ''temporary password'' /var/log/mysqld.log

# 运行安全配置
mysql_secure_installation',
    'bash',
    '在CentOS/RHEL系统上安装MySQL',
    2
  ),
  (
    'create-db-basic',
    'create-database',
    '创建基本数据库',
    '-- 创建数据库
CREATE DATABASE mycompany;

-- 查看创建结果
SHOW DATABASES;

-- 查看数据库详细信息
SHOW CREATE DATABASE mycompany;',
    'sql',
    '创建基本数据库的示例',
    1
  ),
  (
    'create-db-charset',
    'create-database',
    '指定字符集创建数据库',
    '-- 创建支持完整UTF-8的数据库
CREATE DATABASE mycompany
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 验证字符集设置
SELECT
  SCHEMA_NAME as ''数据库名'',
  DEFAULT_CHARACTER_SET_NAME as ''字符集'',
  DEFAULT_COLLATION_NAME as ''排序规则''
FROM information_schema.SCHEMATA
WHERE SCHEMA_NAME = ''mycompany'';',
    'sql',
    '创建数据库时指定字符集和排序规则',
    2
  ),
  (
    'select-all-columns',
    'select-basics',
    '查询所有列',
    '-- 查询users表的所有列
SELECT * FROM users;

-- 查询products表的所有列
SELECT * FROM products;

-- 查询orders表的所有列并限制结果数量
SELECT * FROM orders LIMIT 10;',
    'sql',
    '使用SELECT *查询表中所有列的数据',
    1
  ),
  (
    'select-specific-columns',
    'select-basics',
    '查询指定列',
    '-- 查询用户的姓名和邮箱
SELECT name, email FROM users;

-- 查询产品的名称和价格
SELECT product_name, price FROM products;

-- 使用列别名
SELECT
  name AS ''用户名'',
  email AS ''邮箱地址'',
  created_at AS ''注册时间''
FROM users;',
    'sql',
    '选择特定列进行查询，提高查询效率',
    2
  ),
  (
    'select-with-conditions',
    'select-basics',
    '条件查询',
    '-- 查询年龄大于18的用户
SELECT * FROM users WHERE age > 18;

-- 查询特定城市的用户
SELECT name, email FROM users WHERE city = ''北京'';

-- 多条件查询
SELECT * FROM users
WHERE age >= 18 AND age <= 65 AND city IN (''北京'', ''上海'', ''广州'');

-- 模糊查询
SELECT * FROM users WHERE name LIKE ''%张%'';',
    'sql',
    '使用WHERE子句进行条件筛选',
    3
  );

-- 插入文章关联关系
INSERT INTO public.article_relations (source_article_id, target_article_id) VALUES
  ('mysql-introduction', 'database-concepts'),
  ('mysql-introduction', 'mysql-installation'),
  ('database-concepts', 'mysql-introduction'),
  ('database-concepts', 'create-database'),
  ('mysql-installation', 'mysql-introduction'),
  ('mysql-installation', 'create-database'),
  ('create-database', 'mysql-installation'),
  ('create-database', 'select-basics'),
  ('select-basics', 'create-database');
