'use client';

/**
 * MySQLAi.de - FeatureCard功能卡片组件
 * 从Web版本移植，适配桌面应用环境
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, ArrowRight, CheckCircle } from 'lucide-react';
import { cn } from '../../lib/utils';
import { FeatureCardProps, SimpleFeatureCardProps, CompactFeatureCardProps } from '../../lib/types';

interface FeatureCardComponentProps extends Omit<FeatureCardProps, 'icon'> {
  icon: LucideIcon;
  features?: string[];
  color?: string;
  gradient?: string;
  index?: number;
  expandable?: boolean;
  onClick?: () => void;
}

export default function FeatureCard({
  title,
  description,
  icon: IconComponent,
  features = [],
  color: _color = 'mysql-primary',
  gradient = 'from-mysql-primary to-mysql-primary-dark',
  index = 0,
  expandable = true,
  onClick,
  className,
}: FeatureCardComponentProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleClick = () => {
    if (expandable) {
      setIsExpanded(!isExpanded);
    }
    onClick?.();
  };

  const cardContent = (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.2,
        ease: "easeOut" 
      }}
      viewport={{ once: true }}
      className="group h-full"
    >
      <div
        className={cn(
          'relative bg-white rounded-2xl shadow-lg border border-mysql-border h-full',
          'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out',
          expandable && 'cursor-pointer',
          'overflow-hidden',
          isExpanded && 'ring-2 ring-mysql-primary/50',
          className
        )}
        onClick={handleClick}
      >
        {/* 渐变背景装饰 */}
        <div className={cn(
          'absolute top-0 left-0 right-0 h-2 bg-gradient-to-r',
          gradient
        )} />

        {/* 卡片内容 */}
        <div className="p-6">
          {/* 图标区域 */}
          <div className="flex items-center justify-center mb-6">
            <div className={cn(
              'flex items-center justify-center w-16 h-16 rounded-2xl',
              'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
              gradient,
            )}>
              <IconComponent className="w-8 h-8 text-white" />
            </div>
          </div>

          {/* 标题和描述 */}
          <div className="text-center mb-4">
            <h3 className="text-xl font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300">
              {title}
            </h3>
            <p className="text-mysql-text-light leading-relaxed">
              {description}
            </p>
          </div>

          {/* 功能列表 */}
          {features.length > 0 && (
            <motion.div
              initial={false}
              animate={{ 
                height: isExpanded ? 'auto' : 0,
                opacity: isExpanded ? 1 : 0
              }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="border-t border-mysql-border pt-4 mt-4">
                <ul className="space-y-2">
                  {features.map((feature, featureIndex) => (
                    <motion.li
                      key={featureIndex}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ 
                        opacity: isExpanded ? 1 : 0,
                        x: isExpanded ? 0 : -20
                      }}
                      transition={{ 
                        duration: 0.3,
                        delay: isExpanded ? featureIndex * 0.1 : 0
                      }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="w-4 h-4 text-mysql-success flex-shrink-0" />
                      <span className="text-sm text-mysql-text">{feature}</span>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </motion.div>
          )}

          {/* 展开/收起指示器 */}
          {expandable && features.length > 0 && (
            <div className="flex items-center justify-center mt-4 pt-4 border-t border-mysql-border">
              <motion.div
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.3 }}
                className="flex items-center space-x-2 text-mysql-primary text-sm font-medium"
              >
                <span>{isExpanded ? '收起' : '展开'}</span>
                <ArrowRight className="w-4 h-4" />
              </motion.div>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );

  return cardContent;
}

// 简单功能卡片组件
interface SimpleFeatureCardComponentProps extends Omit<SimpleFeatureCardProps, 'icon'> {
  icon: LucideIcon;
  color?: string;
  onClick?: () => void;
}

export function SimpleFeatureCard({
  title,
  description,
  icon: IconComponent,
  className,
  color = 'mysql-primary',
  onClick
}: SimpleFeatureCardComponentProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -4 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        'bg-white rounded-xl p-6 shadow-md border border-mysql-border',
        'hover:shadow-lg transition-all duration-300 ease-out cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        <div className={cn(
          'flex items-center justify-center w-12 h-12 rounded-lg flex-shrink-0',
          `bg-${color} text-white`
        )}>
          <IconComponent className="w-6 h-6" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-mysql-text mb-2">
            {title}
          </h3>
          <p className="text-mysql-text-light text-sm leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </motion.div>
  );
}

// 紧凑功能卡片组件
interface CompactFeatureCardComponentProps extends Omit<CompactFeatureCardProps, 'icon'> {
  icon: LucideIcon;
  color?: string;
  onClick?: () => void;
}

export function CompactFeatureCard({
  title,
  icon: IconComponent,
  className,
  color = 'mysql-primary',
  onClick
}: CompactFeatureCardComponentProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={cn(
        'bg-white rounded-lg p-4 shadow-sm border border-mysql-border',
        'hover:shadow-md transition-all duration-200 ease-out cursor-pointer',
        'flex items-center space-x-3',
        className
      )}
      onClick={onClick}
    >
      <div className={cn(
        'flex items-center justify-center w-10 h-10 rounded-lg',
        `bg-${color} text-white flex-shrink-0`
      )}>
        <IconComponent className="w-5 h-5" />
      </div>
      <span className="text-mysql-text font-medium">
        {title}
      </span>
    </motion.div>
  );
}