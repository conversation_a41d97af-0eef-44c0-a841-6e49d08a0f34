'use client';

// MySQLAi.de - Header导航组件
// 专业的响应式导航栏，支持桌面和移动端

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Database, ChevronDown, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SITE_CONFIG } from '@/lib/constants';
import { NavigationProps } from '@/lib/types';
import { MAIN_NAVIGATION, NAV_ANIMATIONS, NAV_STYLES } from '@/lib/navigation';
import DropdownMenu from '@/components/ui/DropdownMenu';

interface HeaderProps {
  className?: string;
}

export default function Header({ className }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [expandedSubmenus, setExpandedSubmenus] = useState<string[]>([]);

  // 监听滚动事件，改变Header背景
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 切换移动端菜单
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // 关闭移动端菜单
  const closeMenu = () => {
    setIsMenuOpen(false);
    setExpandedSubmenus([]);
  };

  // 切换子菜单展开状态
  const toggleSubmenu = (itemName: string) => {
    setExpandedSubmenus(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-mysql-border'
          : 'bg-transparent',
        className
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo和品牌标识 */}
          <Link
            href="/"
            className="flex items-center space-x-3 group"
            onClick={closeMenu}
          >
            <div className="flex items-center justify-center w-10 h-10 bg-mysql-primary rounded-lg group-hover:bg-mysql-primary-dark transition-colors duration-200">
              <Database className="w-6 h-6 text-white" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-200">
                {SITE_CONFIG.name}
              </span>
              <span className="text-xs text-mysql-text-light hidden sm:block">
                MySQL智能分析专家
              </span>
            </div>
          </Link>

          {/* 桌面端导航菜单 */}
          <nav className="hidden lg:flex items-center space-x-8">
            {MAIN_NAVIGATION.map((item) => {
              // 检查是否有子菜单
              if (item.children && item.children.length > 0) {
                return (
                  <DropdownMenu
                    key={item.name}
                    trigger={<span>{item.name}</span>}
                    items={item.children}
                    align="left"
                    className="relative"
                  />
                );
              }

              // 普通导航项
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'relative px-3 py-2 text-sm font-medium transition-all duration-200',
                    'text-mysql-text hover:text-mysql-primary',
                    'before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5',
                    'before:bg-mysql-primary before:transition-all before:duration-300',
                    'hover:before:w-full'
                  )}
                >
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* 右侧操作区域 */}
          <div className="flex items-center space-x-4">
            {/* 移动端汉堡菜单按钮 */}
            <button
              type="button"
              onClick={toggleMenu}
              className="lg:hidden p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200"
              aria-label="切换菜单"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 移动端菜单 */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm lg:hidden"
              onClick={closeMenu}
            />

            {/* 侧边栏菜单 */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl lg:hidden"
            >
              <div className="flex flex-col h-full">
                {/* 菜单头部 */}
                <div className="flex items-center justify-between p-6 border-b border-mysql-border">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-mysql-primary rounded-lg">
                      <Database className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-lg font-bold text-mysql-text">
                      {SITE_CONFIG.name}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={closeMenu}
                    className="p-2 rounded-lg text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light transition-all duration-200"
                    aria-label="关闭菜单"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* 菜单内容 */}
                <nav className="flex-1 px-6 py-6">
                  <div className="space-y-2">
                    {MAIN_NAVIGATION.map((item, index) => (
                      <motion.div
                        key={item.name}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        {/* 检查是否有子菜单 */}
                        {item.children && item.children.length > 0 ? (
                          <div>
                            {/* 父菜单项 */}
                            <button
                              type="button"
                              onClick={() => toggleSubmenu(item.name)}
                              className="flex items-center justify-between w-full px-4 py-3 text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200"
                            >
                              <span className="font-medium">{item.name}</span>
                              <ChevronRight
                                className={cn(
                                  'w-4 h-4 transition-transform duration-200',
                                  expandedSubmenus.includes(item.name) && 'rotate-90'
                                )}
                              />
                            </button>

                            {/* 子菜单 */}
                            <AnimatePresence>
                              {expandedSubmenus.includes(item.name) && (
                                <motion.div
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: 'auto' }}
                                  exit={{ opacity: 0, height: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className="ml-4 mt-2 space-y-1"
                                >
                                  {item.children.map((subItem) => (
                                    <Link
                                      key={subItem.name}
                                      href={subItem.href}
                                      onClick={closeMenu}
                                      className="flex items-center px-4 py-2 text-sm text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200"
                                    >
                                      <span>{subItem.name}</span>
                                    </Link>
                                  ))}
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        ) : (
                          /* 普通菜单项 */
                          <Link
                            href={item.href}
                            onClick={closeMenu}
                            className="flex items-center px-4 py-3 text-mysql-text hover:text-mysql-primary hover:bg-mysql-primary-light rounded-lg transition-all duration-200"
                          >
                            <span className="font-medium">{item.name}</span>
                          </Link>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </nav>

                {/* 菜单底部 */}
                <div className="p-6 border-t border-mysql-border">
                  <Link
                    href="/contact"
                    onClick={closeMenu}
                    className="flex items-center justify-center w-full px-4 py-3 bg-mysql-primary text-white font-medium rounded-lg hover:bg-mysql-primary-dark transition-colors duration-200"
                  >
                    联系我们
                  </Link>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </header>
  );
}
