'use client';

// MySQLAi.de - 代码示例表单组件
// 用于创建和编辑代码示例

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Save, X, Code, FileText, Tag, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { articlesApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import CodeBlock from '@/components/ui/CodeBlock';
import type { Database } from '@/lib/database.types';

type CodeExample = Database['public']['Tables']['code_examples']['Row'];
type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];

interface CodeExampleFormData {
  id: string;
  title: string;
  description: string;
  code: string;
  language: string;
  article_id: string;
  tags: string[];
  order_index: number;
}

interface CodeExampleFormProps {
  codeExample?: CodeExample | null;
  onSave: (data: CodeExampleFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

// 支持的编程语言
const LANGUAGE_OPTIONS = [
  { value: 'sql', label: 'SQL' },
  { value: 'mysql', label: 'MySQL' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'php', label: 'PHP' },
  { value: 'bash', label: 'Bash' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' }
] as const;

export default function CodeExampleForm({ codeExample, onSave, onCancel, loading = false }: CodeExampleFormProps) {
  const [articles, setArticles] = useState<KnowledgeArticle[]>([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [tagInput, setTagInput] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<CodeExampleFormData>({
    defaultValues: {
      id: codeExample?.id || '',
      title: codeExample?.title || '',
      description: codeExample?.description || '',
      code: codeExample?.code || '',
      language: codeExample?.language || 'sql',
      article_id: codeExample?.article_id || '',
      tags: [],
      order_index: codeExample?.order_index || 0
    }
  });

  const watchedCode = watch('code');
  const watchedLanguage = watch('language');
  const watchedTags = watch('tags');

  // 获取文章列表
  useEffect(() => {
    const fetchArticles = async () => {
      try {
        const response = await articlesApi.getAll({
          includeCodeExamples: false,
          includeRelated: false
        });
        if (response.success) {
          setArticles(response.data || []);
        }
      } catch (error) {
        console.error('获取文章列表失败:', error);
      }
    };

    fetchArticles();
  }, []);

  // 处理表单提交
  const onSubmit = async (data: CodeExampleFormData) => {
    try {
      await onSave(data);
    } catch (error) {
      console.error('保存代码示例失败:', error);
    }
  };

  // 添加标签
  const addTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      const newTags = [...watchedTags, tagInput.trim()];
      setValue('tags', newTags, { shouldDirty: true });
      setTagInput('');
    }
  };

  // 删除标签
  const removeTag = (tagToRemove: string) => {
    const newTags = watchedTags.filter(tag => tag !== tagToRemove);
    setValue('tags', newTags, { shouldDirty: true });
  };

  // 处理标签输入键盘事件
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-xl shadow-lg border border-mysql-border"
    >
      {/* 表单头部 */}
      <div className="flex items-center justify-between p-6 border-b border-mysql-border">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-mysql-primary-light rounded-lg">
            <Code className="w-5 h-5 text-mysql-primary" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-mysql-text">
              {codeExample ? '编辑代码示例' : '创建新代码示例'}
            </h2>
            <p className="text-sm text-mysql-text-light">
              {codeExample ? '修改现有代码示例' : '添加新的代码片段'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            icon={<Eye className="w-4 h-4" />}
            className="text-mysql-text-light hover:text-mysql-primary"
          >
            {isPreviewMode ? '编辑模式' : '预览模式'}
          </Button>
        </div>
      </div>

      {/* 表单内容 */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* 基本信息 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 代码示例ID */}
          <div>
            <label htmlFor="id" className="block text-sm font-medium text-mysql-text mb-2">
              代码示例ID *
            </label>
            <input
              id="id"
              type="text"
              {...register('id', { 
                required: '代码示例ID不能为空',
                pattern: {
                  value: /^[a-zA-Z0-9_-]+$/,
                  message: '代码示例ID只能包含字母、数字、下划线和连字符'
                }
              })}
              placeholder="例如: mysql-select-example"
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200',
                errors.id && 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
              )}
              disabled={loading || !!codeExample}
            />
            {errors.id && (
              <p className="mt-1 text-sm text-red-600">{errors.id.message}</p>
            )}
          </div>

          {/* 代码示例标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-mysql-text mb-2">
              代码示例标题 *
            </label>
            <input
              id="title"
              type="text"
              {...register('title', { required: '代码示例标题不能为空' })}
              placeholder="请输入代码示例标题"
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200',
                errors.title && 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
              )}
              disabled={loading}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>
        </div>

        {/* 代码示例描述 */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-mysql-text mb-2">
            代码示例描述
          </label>
          <textarea
            id="description"
            {...register('description')}
            placeholder="请输入代码示例的详细描述..."
            rows={3}
            className={cn(
              'w-full px-4 py-3 text-base',
              'bg-white border-2 border-mysql-border rounded-lg',
              'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
              'placeholder-mysql-text-light text-mysql-text',
              'transition-all duration-200 resize-none'
            )}
            disabled={loading}
          />
        </div>

        {/* 编程语言和关联文章 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 编程语言 */}
          <div>
            <label htmlFor="language" className="block text-sm font-medium text-mysql-text mb-2">
              编程语言 *
            </label>
            <select
              id="language"
              {...register('language', { required: '请选择编程语言' })}
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'text-mysql-text transition-all duration-200'
              )}
              disabled={loading}
            >
              {LANGUAGE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 关联文章 */}
          <div>
            <label htmlFor="article_id" className="block text-sm font-medium text-mysql-text mb-2">
              <FileText className="w-4 h-4 inline mr-1" />
              关联文章
            </label>
            <select
              id="article_id"
              {...register('article_id')}
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'text-mysql-text transition-all duration-200'
              )}
              disabled={loading}
            >
              <option value="">选择关联文章</option>
              {articles.map(article => (
                <option key={article.id} value={article.id}>
                  {article.title}
                </option>
              ))}
            </select>
          </div>

          {/* 排序索引 */}
          <div>
            <label htmlFor="order_index" className="block text-sm font-medium text-mysql-text mb-2">
              排序索引
            </label>
            <input
              id="order_index"
              type="number"
              {...register('order_index', { valueAsNumber: true })}
              placeholder="0"
              min="0"
              className={cn(
                'w-full px-4 py-3 text-base',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200'
              )}
              disabled={loading}
            />
          </div>
        </div>

        {/* 标签管理 */}
        <div>
          <label className="block text-sm font-medium text-mysql-text mb-2">
            <Tag className="w-4 h-4 inline mr-1" />
            代码标签
          </label>
          
          {/* 标签输入 */}
          <div className="flex space-x-2 mb-3">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={handleTagKeyPress}
              placeholder="输入标签后按回车添加"
              className={cn(
                'flex-1 px-4 py-2 text-sm',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200'
              )}
              disabled={loading}
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addTag}
              disabled={!tagInput.trim() || loading}
            >
              添加
            </Button>
          </div>

          {/* 标签列表 */}
          <div className="flex flex-wrap gap-2">
            {watchedTags.map((tag, index) => (
              <motion.span
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-mysql-primary-light text-mysql-primary"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  aria-label={`删除标签 ${tag}`}
                  className="ml-2 text-mysql-primary hover:text-mysql-primary-dark"
                  disabled={loading}
                >
                  <X className="w-3 h-3" />
                </button>
              </motion.span>
            ))}
          </div>
        </div>

        {/* 代码编辑器 */}
        <div>
          <label className="block text-sm font-medium text-mysql-text mb-2">
            代码内容 *
          </label>
          
          {isPreviewMode ? (
            // 预览模式
            <div className="border-2 border-mysql-border rounded-lg overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b border-mysql-border">
                <span className="text-sm text-mysql-text-light">预览效果</span>
              </div>
              <div className="p-4">
                {watchedCode ? (
                  <CodeBlock
                    code={watchedCode}
                    language={watchedLanguage}
                    showLineNumbers={true}
                  />
                ) : (
                  <p className="text-mysql-text-light">请输入代码内容...</p>
                )}
              </div>
            </div>
          ) : (
            // 编辑模式
            <textarea
              id="code"
              {...register('code', { required: '代码内容不能为空' })}
              placeholder="请输入代码内容..."
              rows={15}
              className={cn(
                'w-full px-4 py-3 text-sm font-mono',
                'bg-white border-2 border-mysql-border rounded-lg',
                'focus:outline-none focus:border-mysql-primary focus:ring-4 focus:ring-mysql-primary/20',
                'placeholder-mysql-text-light text-mysql-text',
                'transition-all duration-200 resize-none',
                errors.code && 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
              )}
              disabled={loading}
            />
          )}
          
          {errors.code && (
            <p className="mt-1 text-sm text-red-600">{errors.code.message}</p>
          )}
        </div>

        {/* 表单操作按钮 */}
        <div className="flex items-center justify-between pt-6 border-t border-mysql-border">
          <div className="text-sm text-mysql-text-light">
            {isDirty && '* 表单有未保存的更改'}
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              icon={<X className="w-4 h-4" />}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
              icon={<Save className="w-4 h-4" />}
            >
              {loading ? '保存中...' : (codeExample ? '更新代码示例' : '创建代码示例')}
            </Button>
          </div>
        </div>
      </form>
    </motion.div>
  );
}
