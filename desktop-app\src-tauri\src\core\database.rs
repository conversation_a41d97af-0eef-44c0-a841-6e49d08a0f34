use sqlx::{SqlitePool, Row};
use serde::{Deserialize, Serialize};
use std::path::Path;
use chrono::{DateTime, Utc, NaiveDateTime};

/// 数据库管理器
pub struct DatabaseManager {
    pool: SqlitePool,
}

/// 许可证密钥记录
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LicenseKeyRecord {
    pub id: i32,
    pub license_key: String,
    pub is_valid: bool,
    pub is_used: bool,
    pub duration_hours: i32,
    pub created_at: String,
}

/// 激活记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivationRecord {
    pub id: i32,
    pub license_key: String,
    pub machine_id: String,
    pub machine_fingerprint: Option<String>,
    pub activated_at: String,
    pub expires_at: String,
    pub is_active: bool,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub async fn new(db_path: &str) -> Result<Self, sqlx::Error> {
        // 确保数据库目录存在
        if let Some(parent) = Path::new(db_path).parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent).map_err(|e| {
                    sqlx::Error::Io(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("Failed to create database directory: {}", e),
                    ))
                })?;
            }
        }

        let database_url = format!("sqlite:{}", db_path);
        let pool = SqlitePool::connect(&database_url).await?;

        let manager = Self { pool };
        
        // 初始化数据库表
        manager.initialize_tables().await?;
        
        Ok(manager)
    }

    /// 初始化数据库表结构
    async fn initialize_tables(&self) -> Result<(), sqlx::Error> {
        // 创建license_keys表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS license_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT NOT NULL UNIQUE,
                is_valid INTEGER DEFAULT 1,
                is_used INTEGER DEFAULT 0,
                duration_hours INTEGER DEFAULT 24,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // 创建activations表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS activations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT NOT NULL,
                machine_id TEXT NOT NULL,
                machine_fingerprint TEXT,
                activated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_active INTEGER DEFAULT 1,
                FOREIGN KEY (license_key) REFERENCES license_keys (license_key)
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // 创建索引以提高查询性能
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_license_key ON activations (license_key)")
            .execute(&self.pool)
            .await?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_machine_id ON activations (machine_id)")
            .execute(&self.pool)
            .await?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_expires_at ON activations (expires_at)")
            .execute(&self.pool)
            .await?;

        // 创建配置表用于存储应用设置
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS app_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        log::info!("数据库表初始化完成");
        Ok(())
    }

    /// 插入许可证密钥
    pub async fn insert_license_key(
        &self,
        license_key: &str,
        duration_hours: i32,
    ) -> Result<i64, sqlx::Error> {
        let query = r#"
            INSERT INTO license_keys (license_key, duration_hours)
            VALUES (?, ?)
        "#;

        let result = sqlx::query(query)
            .bind(license_key)
            .bind(duration_hours)
            .execute(&self.pool)
            .await?;

        Ok(result.last_insert_rowid())
    }

    /// 验证许可证密钥是否有效
    pub async fn validate_license_key(&self, license_key: &str) -> Result<bool, sqlx::Error> {
        let query = "SELECT is_valid FROM license_keys WHERE license_key = ?";
        
        let row = sqlx::query(query)
            .bind(license_key)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            let is_valid: i32 = row.get("is_valid");
            Ok(is_valid == 1)
        } else {
            Ok(false)
        }
    }

    /// 获取许可证密钥信息
    pub async fn get_license_key_info(&self, license_key: &str) -> Result<Option<LicenseKeyRecord>, sqlx::Error> {
        let query = r#"
            SELECT id, license_key, is_valid, is_used, duration_hours, created_at
            FROM license_keys 
            WHERE license_key = ?
        "#;

        let row = sqlx::query(query)
            .bind(license_key)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            Ok(Some(LicenseKeyRecord {
                id: row.get("id"),
                license_key: row.get("license_key"),
                is_valid: row.get::<i32, _>("is_valid") == 1,
                is_used: row.get::<i32, _>("is_used") == 1,
                duration_hours: row.get("duration_hours"),
                created_at: row.get("created_at"),
            }))
        } else {
            Ok(None)
        }
    }

    /// 检查许可证是否已激活
    pub async fn is_license_activated(
        &self,
        license_key: &str,
        machine_id: &str,
    ) -> Result<bool, sqlx::Error> {
        let query = r#"
            SELECT COUNT(*) as count 
            FROM activations 
            WHERE license_key = ? AND machine_id = ? AND is_active = 1
        "#;

        let row = sqlx::query(query)
            .bind(license_key)
            .bind(machine_id)
            .fetch_one(&self.pool)
            .await?;

        let count: i32 = row.get("count");
        Ok(count > 0)
    }

    /// 激活许可证
    pub async fn activate_license(
        &self,
        license_key: &str,
        machine_id: &str,
        machine_fingerprint: &str,
        expires_at: &NaiveDateTime,
    ) -> Result<i64, sqlx::Error> {
        // 开始事务
        let mut tx = self.pool.begin().await?;

        // 插入激活记录
        let insert_query = r#"
            INSERT INTO activations (license_key, machine_id, machine_fingerprint, expires_at)
            VALUES (?, ?, ?, ?)
        "#;

        let result = sqlx::query(insert_query)
            .bind(license_key)
            .bind(machine_id)
            .bind(machine_fingerprint)
            .bind(expires_at.format("%Y-%m-%d %H:%M:%S").to_string())
            .execute(&mut *tx)
            .await?;

        let activation_id = result.last_insert_rowid();

        // 标记许可证为已使用
        let update_query = "UPDATE license_keys SET is_used = 1 WHERE license_key = ?";
        sqlx::query(update_query)
            .bind(license_key)
            .execute(&mut *tx)
            .await?;

        // 提交事务
        tx.commit().await?;

        Ok(activation_id)
    }

    /// 获取机器的有效激活记录
    pub async fn get_active_licenses(&self, machine_id: &str) -> Result<Vec<ActivationRecord>, sqlx::Error> {
        let query = r#"
            SELECT a.id, a.license_key, a.machine_id, a.machine_fingerprint, 
                   a.activated_at, a.expires_at, a.is_active
            FROM activations a
            JOIN license_keys lk ON a.license_key = lk.license_key
            WHERE a.machine_id = ? AND a.is_active = 1 AND lk.is_valid = 1
            ORDER BY a.expires_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(machine_id)
            .fetch_all(&self.pool)
            .await?;

        let mut records = Vec::new();
        for row in rows {
            records.push(ActivationRecord {
                id: row.get("id"),
                license_key: row.get("license_key"),
                machine_id: row.get("machine_id"),
                machine_fingerprint: row.get("machine_fingerprint"),
                activated_at: row.get("activated_at"),
                expires_at: row.get("expires_at"),
                is_active: row.get::<i32, _>("is_active") == 1,
            });
        }

        Ok(records)
    }

    /// 停用许可证
    pub async fn deactivate_license(
        &self,
        license_key: &str,
        machine_id: &str,
    ) -> Result<u64, sqlx::Error> {
        let query = r#"
            UPDATE activations 
            SET is_active = 0 
            WHERE license_key = ? AND machine_id = ?
        "#;

        let result = sqlx::query(query)
            .bind(license_key)
            .bind(machine_id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected())
    }

    /// 清理过期的激活记录
    pub async fn cleanup_expired_activations(&self) -> Result<u64, sqlx::Error> {
        let current_time = Utc::now().naive_utc();
        
        let query = r#"
            UPDATE activations 
            SET is_active = 0 
            WHERE expires_at < ? AND is_active = 1
        "#;

        let result = sqlx::query(query)
            .bind(current_time.format("%Y-%m-%d %H:%M:%S").to_string())
            .execute(&self.pool)
            .await?;

        let affected_rows = result.rows_affected();
        if affected_rows > 0 {
            log::info!("清理了 {} 条过期的激活记录", affected_rows);
        }

        Ok(affected_rows)
    }

    /// 获取激活统计信息
    pub async fn get_activation_stats(&self) -> Result<ActivationStats, sqlx::Error> {
        // 总许可证数量
        let total_licenses = sqlx::query("SELECT COUNT(*) as count FROM license_keys")
            .fetch_one(&self.pool)
            .await?
            .get::<i32, _>("count");

        // 有效许可证数量
        let valid_licenses = sqlx::query("SELECT COUNT(*) as count FROM license_keys WHERE is_valid = 1")
            .fetch_one(&self.pool)
            .await?
            .get::<i32, _>("count");

        // 已使用许可证数量
        let used_licenses = sqlx::query("SELECT COUNT(*) as count FROM license_keys WHERE is_used = 1")
            .fetch_one(&self.pool)
            .await?
            .get::<i32, _>("count");

        // 活跃激活数量
        let active_activations = sqlx::query("SELECT COUNT(*) as count FROM activations WHERE is_active = 1")
            .fetch_one(&self.pool)
            .await?
            .get::<i32, _>("count");

        Ok(ActivationStats {
            total_licenses,
            valid_licenses,
            used_licenses,
            active_activations,
        })
    }

    /// 设置配置值
    pub async fn set_config(&self, key: &str, value: &str) -> Result<(), sqlx::Error> {
        let query = r#"
            INSERT OR REPLACE INTO app_config (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        "#;

        sqlx::query(query)
            .bind(key)
            .bind(value)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    /// 获取配置值
    pub async fn get_config(&self, key: &str) -> Result<Option<String>, sqlx::Error> {
        let query = "SELECT value FROM app_config WHERE key = ?";
        
        let row = sqlx::query(query)
            .bind(key)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            Ok(Some(row.get("value")))
        } else {
            Ok(None)
        }
    }

    /// 获取数据库连接池
    pub fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }

    /// 关闭数据库连接
    pub async fn close(self) {
        self.pool.close().await;
    }
}

/// 激活统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivationStats {
    pub total_licenses: i32,
    pub valid_licenses: i32,
    pub used_licenses: i32,
    pub active_activations: i32,
}

/// 创建数据库管理器实例
pub async fn create_database_manager(db_path: &str) -> Result<DatabaseManager, sqlx::Error> {
    DatabaseManager::new(db_path).await
}

/// 获取默认数据库路径
pub fn get_default_db_path() -> String {
    let app_data_dir = if cfg!(target_os = "windows") {
        std::env::var("APPDATA").unwrap_or_else(|_| ".".to_string())
    } else if cfg!(target_os = "macos") {
        format!("{}/.config", std::env::var("HOME").unwrap_or_else(|_| ".".to_string()))
    } else {
        format!("{}/.config", std::env::var("HOME").unwrap_or_else(|_| ".".to_string()))
    };

    format!("{}/MySQLAi/activation.db", app_data_dir)
}