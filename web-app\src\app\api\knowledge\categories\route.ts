// MySQLAi.de - 知识库分类 API
// 提供知识库分类的 CRUD 操作

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/lib/database.types';

type KnowledgeCategoryInsert = Database['public']['Tables']['knowledge_categories']['Insert'];

// GET /api/knowledge/categories - 获取所有分类
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeStats = searchParams.get('includeStats') === 'true';

    const query = supabase
      .from('knowledge_categories')
      .select('*')
      .order('order_index', { ascending: true });

    const { data: categories, error } = await query;

    if (error) {
      console.error('获取分类失败:', error);
      return NextResponse.json(
        { success: false, error: '获取分类失败', details: error.message },
        { status: 500 }
      );
    }

    // 如果需要统计信息，获取每个分类下的文章数量
    if (includeStats && categories) {
      const categoriesWithStats = await Promise.all(
        categories.map(async (category) => {
          const { count } = await supabase
            .from('knowledge_articles')
            .select('*', { count: 'exact', head: true })
            .eq('category_id', category.id);

          return {
            ...category,
            article_count: count || 0
          };
        })
      );

      return NextResponse.json({
        success: true,
        data: categoriesWithStats,
        total: categoriesWithStats.length
      });
    }

    return NextResponse.json({
      success: true,
      data: categories,
      total: categories?.length || 0
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/categories - 创建新分类
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    if (!body.id || !body.name) {
      return NextResponse.json(
        { success: false, error: '缺少必需字段: id, name' },
        { status: 400 }
      );
    }

    const categoryData: KnowledgeCategoryInsert = {
      id: body.id,
      name: body.name,
      description: body.description || null,
      icon: body.icon || null,
      color: body.color || null,
      order_index: body.order_index || 0
    };

    const { data, error } = await supabase
      .from('knowledge_categories')
      .insert(categoryData)
      .select()
      .single();

    if (error) {
      console.error('创建分类失败:', error);
      
      // 处理唯一约束错误
      if (error.code === '23505') {
        return NextResponse.json(
          { success: false, error: '分类ID已存在' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { success: false, error: '创建分类失败', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: '分类创建成功'
    }, { status: 201 });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PUT /api/knowledge/categories - 批量更新分类排序
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!Array.isArray(body.categories)) {
      return NextResponse.json(
        { success: false, error: '请提供分类数组' },
        { status: 400 }
      );
    }

    // 批量更新排序
    const updates = body.categories.map((category: { id: string }, index: number) =>
      supabase
        .from('knowledge_categories')
        .update({ order_index: index })
        .eq('id', category.id)
    );

    const results = await Promise.all(updates);
    
    // 检查是否有错误
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      console.error('批量更新失败:', errors);
      return NextResponse.json(
        { success: false, error: '批量更新失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '分类排序更新成功'
    });

  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
