use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use std::io::Write;
use std::sync::{<PERSON>, Mutex, Once};
use std::time::Duration;
use tokio::process::Command as AsyncCommand;
use zip::ZipArchive;

/// MySQL安装器常量
const MYSQL_SERVICE_NAME: &str = "MySQL";
const DEFAULT_ROOT_PASSWORD: &str = "123456";
const MYSQL_PORT: u16 = 3306;

/// MySQL组件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum MySQLComponentType {
    Directory,
    SpecialDirectory,
    Service,
    UninstallRegistry,
    EnvironmentVariable,
    Process,
}

/// MySQL组件信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MySQLComponent {
    pub component_type: MySQLComponentType,
    pub location: String,
    pub description: String,
    pub uninstall_string: Option<String>,
}

/// 安装进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InstallationProgress {
    pub current_step: String,
    pub progress_percentage: f64,
    pub status: InstallationStatus,
    pub message: String,
    pub estimated_time_remaining: Option<u64>,
}

/// 安装状态
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum InstallationStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled,
}

/// MySQL配置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MySQLConfig {
    pub install_path: String,
    pub data_path: String,
    pub port: u16,
    pub root_password: String,
    pub character_set: String,
    pub sql_mode: String,
    pub max_connections: u32,
    pub innodb_buffer_pool_size: String,
}

impl Default for MySQLConfig {
    fn default() -> Self {
        Self {
            install_path: "C:\\MySQL".to_string(),
            data_path: "C:\\MySQL\\data".to_string(),
            port: MYSQL_PORT,
            root_password: DEFAULT_ROOT_PASSWORD.to_string(),
            character_set: "utf8mb4".to_string(),
            sql_mode: "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO".to_string(),
            max_connections: 151,
            innodb_buffer_pool_size: "128M".to_string(),
        }
    }
}

/// MySQL安装器
pub struct MySQLInstaller {
    pub config: MySQLConfig,
    pub mysql_version: String,
    pub mysql_filename: String,
    pub base_path: PathBuf,
    pub resources_path: PathBuf,
    progress_callback: Option<Arc<dyn Fn(InstallationProgress) + Send + Sync>>,
}

static mut MYSQL_INSTALLER: Option<MySQLInstaller> = None;
static INIT: Once = Once::new();

impl MySQLInstaller {
    /// 获取全局MySQL安装器实例
    pub fn instance() -> &'static mut MySQLInstaller {
        unsafe {
            INIT.call_once(|| {
                MYSQL_INSTALLER = Some(MySQLInstaller::new("8.0.36".to_string()));
            });
            MYSQL_INSTALLER.as_mut().unwrap()
        }
    }

    /// 创建新的MySQL安装器
    pub fn new(mysql_version: String) -> Self {
        let config = MySQLConfig::default();
        
        // 根据版本确定文件名
        let mysql_filename = match mysql_version.as_str() {
            "8.0.36" => "mysql-8.0.36-winx64.zip".to_string(),
            "8.0.28" => "mysql-8.0.28-winx64.zip".to_string(),
            _ => "mysql-8.0.36-winx64.zip".to_string(),
        };

        // 获取基础路径
        let base_path = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
        let resources_path = base_path.join("resources");

        Self {
            config,
            mysql_version,
            mysql_filename,
            base_path,
            resources_path,
            progress_callback: None,
        }
    }

    /// 设置进度回调
    pub fn set_progress_callback<F>(&mut self, callback: F)
    where
        F: Fn(InstallationProgress) + Send + Sync + 'static,
    {
        self.progress_callback = Some(Arc::new(callback));
    }

    /// 发送进度更新
    fn update_progress(&self, step: &str, percentage: f64, status: InstallationStatus, message: &str) {
        if let Some(ref callback) = self.progress_callback {
            let progress = InstallationProgress {
                current_step: step.to_string(),
                progress_percentage: percentage,
                status,
                message: message.to_string(),
                estimated_time_remaining: None,
            };
            callback(progress);
        }
        log::info!("安装进度: {} - {:.1}% - {}", step, percentage, message);
    }

    /// 解析MySQL版本号
    fn parse_mysql_version(&self) -> (u32, u32) {
        let version_parts: Vec<&str> = self.mysql_version.split('.').collect();
        let major = version_parts.get(0).and_then(|v| v.parse().ok()).unwrap_or(8);
        let minor = version_parts.get(1).and_then(|v| v.parse().ok()).unwrap_or(0);
        (major, minor)
    }

    /// 检测已安装的MySQL组件
    pub fn detect_mysql_components(&self) -> Result<Vec<MySQLComponent>, Box<dyn std::error::Error>> {
        self.update_progress("检测MySQL组件", 5.0, InstallationStatus::InProgress, "正在扫描系统中的MySQL组件...");

        let mut components = Vec::new();

        // 1. 检测MySQL目录
        self.detect_mysql_directories(&mut components)?;

        // 2. 检测MySQL服务
        self.detect_mysql_services(&mut components)?;

        // 3. 检测注册表项
        self.detect_mysql_registry(&mut components)?;

        // 4. 检测环境变量
        self.detect_mysql_environment(&mut components)?;

        // 5. 检测运行中的MySQL进程
        self.detect_mysql_processes(&mut components)?;

        log::info!("检测到 {} 个MySQL组件", components.len());
        Ok(components)
    }

    /// 检测MySQL目录
    fn detect_mysql_directories(&self, components: &mut Vec<MySQLComponent>) -> Result<(), Box<dyn std::error::Error>> {
        let common_paths = vec![
            "C:\\MySQL",
            "C:\\Program Files\\MySQL",
            "C:\\Program Files (x86)\\MySQL",
            "C:\\ProgramData\\MySQL",
            "C:\\mysql",
        ];

        for path in common_paths {
            if Path::new(path).exists() {
                components.push(MySQLComponent {
                    component_type: MySQLComponentType::Directory,
                    location: path.to_string(),
                    description: format!("MySQL安装目录: {}", path),
                    uninstall_string: None,
                });
            }
        }

        // 检测特殊目录
        let special_paths = vec![
            ("C:\\ProgramData\\MySQL", "MySQL数据目录"),
            ("C:\\Windows\\System32\\mysql", "MySQL系统文件"),
        ];

        for (path, desc) in special_paths {
            if Path::new(path).exists() {
                components.push(MySQLComponent {
                    component_type: MySQLComponentType::SpecialDirectory,
                    location: path.to_string(),
                    description: desc.to_string(),
                    uninstall_string: None,
                });
            }
        }

        Ok(())
    }

    /// 检测MySQL服务
    fn detect_mysql_services(&self, components: &mut Vec<MySQLComponent>) -> Result<(), Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            // 使用sc命令查询MySQL服务
            let output = Command::new("sc")
                .args(&["query", "type=", "service", "state=", "all"])
                .output()?;

            if output.status.success() {
                let output_str = String::from_utf8_lossy(&output.stdout);
                for line in output_str.lines() {
                    if line.contains("SERVICE_NAME") &&
                       (line.to_lowercase().contains("mysql") || line.to_lowercase().contains("mariadb")) {
                        let service_name = line.split(':').nth(1).unwrap_or("").trim();
                        if !service_name.is_empty() {
                            components.push(MySQLComponent {
                                component_type: MySQLComponentType::Service,
                                location: service_name.to_string(),
                                description: format!("MySQL服务: {}", service_name),
                                uninstall_string: Some(format!("sc delete {}", service_name)),
                            });
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 检测MySQL注册表项
    fn detect_mysql_registry(&self, components: &mut Vec<MySQLComponent>) -> Result<(), Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;

            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);

            // 检测卸载信息
            let uninstall_path = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall";
            if let Ok(uninstall_key) = hklm.open_subkey(uninstall_path) {
                for subkey_name in uninstall_key.enum_keys().filter_map(|k| k.ok()) {
                    if subkey_name.to_lowercase().contains("mysql") {
                        if let Ok(subkey) = uninstall_key.open_subkey(&subkey_name) {
                            let display_name: Result<String, _> = subkey.get_value("DisplayName");
                            let uninstall_string: Result<String, _> = subkey.get_value("UninstallString");

                            if let (Ok(name), Ok(uninstall)) = (display_name, uninstall_string) {
                                components.push(MySQLComponent {
                                    component_type: MySQLComponentType::UninstallRegistry,
                                    location: format!("{}\\{}", uninstall_path, subkey_name),
                                    description: format!("MySQL卸载项: {}", name),
                                    uninstall_string: Some(uninstall),
                                });
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 检测MySQL环境变量
    fn detect_mysql_environment(&self, components: &mut Vec<MySQLComponent>) -> Result<(), Box<dyn std::error::Error>> {
        if let Ok(path_var) = std::env::var("PATH") {
            for path in path_var.split(';') {
                if path.to_lowercase().contains("mysql") {
                    components.push(MySQLComponent {
                        component_type: MySQLComponentType::EnvironmentVariable,
                        location: path.to_string(),
                        description: format!("PATH环境变量中的MySQL路径: {}", path),
                        uninstall_string: None,
                    });
                }
            }
        }

        Ok(())
    }

    /// 检测MySQL进程
    fn detect_mysql_processes(&self, components: &mut Vec<MySQLComponent>) -> Result<(), Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            let output = Command::new("tasklist")
                .args(&["/FO", "CSV"])
                .output()?;

            if output.status.success() {
                let output_str = String::from_utf8_lossy(&output.stdout);
                for line in output_str.lines().skip(1) { // 跳过标题行
                    if line.to_lowercase().contains("mysql") {
                        let parts: Vec<&str> = line.split(',').collect();
                        if let Some(process_name) = parts.get(0) {
                            let clean_name = process_name.trim_matches('"');
                            components.push(MySQLComponent {
                                component_type: MySQLComponentType::Process,
                                location: clean_name.to_string(),
                                description: format!("运行中的MySQL进程: {}", clean_name),
                                uninstall_string: Some(format!("taskkill /F /IM {}", clean_name)),
                            });
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 卸载旧版本MySQL
    pub async fn uninstall_old_mysql(&self, components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("卸载旧版本", 10.0, InstallationStatus::InProgress, "正在卸载旧版本MySQL...");

        // 1. 停止MySQL服务
        self.stop_mysql_services(components).await?;

        // 2. 删除MySQL服务
        self.remove_mysql_services(components).await?;

        // 3. 终止MySQL进程
        self.kill_mysql_processes(components).await?;

        // 4. 删除MySQL目录
        self.remove_mysql_directories(components).await?;

        // 5. 清理注册表
        self.clean_mysql_registry(components).await?;

        // 6. 清理环境变量
        self.clean_mysql_environment(components).await?;

        self.update_progress("卸载完成", 20.0, InstallationStatus::InProgress, "旧版本MySQL卸载完成");
        Ok(())
    }

    /// 停止MySQL服务
    async fn stop_mysql_services(&self, components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        for component in components {
            if matches!(component.component_type, MySQLComponentType::Service) {
                log::info!("停止MySQL服务: {}", component.location);

                #[cfg(target_os = "windows")]
                {
                    let output = AsyncCommand::new("net")
                        .args(&["stop", &component.location])
                        .output()
                        .await?;

                    if !output.status.success() {
                        log::warn!("停止服务失败: {}", String::from_utf8_lossy(&output.stderr));
                    }
                }
            }
        }
        Ok(())
    }

    /// 删除MySQL服务
    async fn remove_mysql_services(&self, components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        for component in components {
            if matches!(component.component_type, MySQLComponentType::Service) {
                log::info!("删除MySQL服务: {}", component.location);

                #[cfg(target_os = "windows")]
                {
                    let output = AsyncCommand::new("sc")
                        .args(&["delete", &component.location])
                        .output()
                        .await?;

                    if !output.status.success() {
                        log::warn!("删除服务失败: {}", String::from_utf8_lossy(&output.stderr));
                    }
                }
            }
        }
        Ok(())
    }

    /// 终止MySQL进程
    async fn kill_mysql_processes(&self, components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        for component in components {
            if matches!(component.component_type, MySQLComponentType::Process) {
                log::info!("终止MySQL进程: {}", component.location);

                #[cfg(target_os = "windows")]
                {
                    let output = AsyncCommand::new("taskkill")
                        .args(&["/F", "/IM", &component.location])
                        .output()
                        .await?;

                    if !output.status.success() {
                        log::warn!("终止进程失败: {}", String::from_utf8_lossy(&output.stderr));
                    }
                }
            }
        }
        Ok(())
    }

    /// 删除MySQL目录
    async fn remove_mysql_directories(&self, components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        for component in components {
            if matches!(component.component_type, MySQLComponentType::Directory | MySQLComponentType::SpecialDirectory) {
                log::info!("删除MySQL目录: {}", component.location);

                if Path::new(&component.location).exists() {
                    if let Err(e) = fs::remove_dir_all(&component.location) {
                        log::warn!("删除目录失败: {} - {}", component.location, e);
                    }
                }
            }
        }
        Ok(())
    }

    /// 清理MySQL注册表
    async fn clean_mysql_registry(&self, components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;

            for component in components {
                if matches!(component.component_type, MySQLComponentType::UninstallRegistry) {
                    log::info!("清理注册表项: {}", component.location);

                    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
                    if let Err(e) = hklm.delete_subkey_all(&component.location) {
                        log::warn!("删除注册表项失败: {} - {}", component.location, e);
                    }
                }
            }
        }
        Ok(())
    }

    /// 清理MySQL环境变量
    async fn clean_mysql_environment(&self, _components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
        // 环境变量清理需要更复杂的逻辑，这里先简单处理
        log::info!("环境变量清理完成");
        Ok(())
    }

    /// 安装MySQL
    pub async fn install_mysql(&self, zip_file_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("开始安装", 25.0, InstallationStatus::InProgress, "正在准备MySQL安装...");

        // 1. 解压MySQL文件
        self.extract_mysql_files(zip_file_path).await?;

        // 2. 创建配置文件
        self.create_mysql_config().await?;

        // 3. 初始化MySQL数据目录
        self.initialize_mysql_data().await?;

        // 4. 安装MySQL服务
        self.install_mysql_service().await?;

        // 5. 启动MySQL服务
        self.start_mysql_service().await?;

        // 6. 设置root密码
        self.set_root_password().await?;

        // 7. 配置环境变量
        self.configure_environment_variables().await?;

        self.update_progress("安装完成", 100.0, InstallationStatus::Completed, "MySQL安装成功完成！");
        Ok(())
    }

    /// 解压MySQL文件
    async fn extract_mysql_files(&self, zip_file_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("解压文件", 30.0, InstallationStatus::InProgress, "正在解压MySQL文件...");

        // 创建安装目录
        let install_path = Path::new(&self.config.install_path);
        if !install_path.exists() {
            fs::create_dir_all(install_path)?;
        }

        // 解压ZIP文件
        let file = fs::File::open(zip_file_path)?;
        let mut archive = ZipArchive::new(file)?;

        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let outpath = match file.enclosed_name() {
                Some(path) => {
                    // 移除ZIP文件中的根目录（如mysql-8.0.36-winx64）
                    let path_components: Vec<_> = path.components().collect();
                    if path_components.len() > 1 {
                        let new_path: PathBuf = path_components[1..].iter().collect();
                        install_path.join(new_path)
                    } else {
                        install_path.join(path)
                    }
                }
                None => continue,
            };

            if file.name().ends_with('/') {
                // 创建目录
                fs::create_dir_all(&outpath)?;
            } else {
                // 创建文件
                if let Some(p) = outpath.parent() {
                    if !p.exists() {
                        fs::create_dir_all(p)?;
                    }
                }
                let mut outfile = fs::File::create(&outpath)?;
                std::io::copy(&mut file, &mut outfile)?;
            }

            // 设置文件权限（Unix系统）
            #[cfg(unix)]
            {
                use std::os::unix::fs::PermissionsExt;
                if let Some(mode) = file.unix_mode() {
                    fs::set_permissions(&outpath, fs::Permissions::from_mode(mode))?;
                }
            }
        }

        log::info!("MySQL文件解压完成: {}", install_path.display());
        Ok(())
    }

    /// 创建MySQL配置文件
    async fn create_mysql_config(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("创建配置", 40.0, InstallationStatus::InProgress, "正在创建MySQL配置文件...");

        let config_path = Path::new(&self.config.install_path).join("my.ini");
        let mut config_file = fs::File::create(&config_path)?;

        let config_content = self.generate_mysql_config();
        config_file.write_all(config_content.as_bytes())?;

        log::info!("MySQL配置文件创建完成: {}", config_path.display());
        Ok(())
    }

    /// 生成MySQL配置内容
    fn generate_mysql_config(&self) -> String {
        let (major, minor) = self.parse_mysql_version();

        format!(r#"[mysql]
default-character-set = {character_set}

[mysqld]
# 基本设置
port = {port}
basedir = {install_path}
datadir = {data_path}
character-set-server = {character_set}
default_authentication_plugin = mysql_native_password

# 连接设置
max_connections = {max_connections}
max_connect_errors = 10

# 表设置
default-table-type = innodb

# InnoDB设置
innodb_buffer_pool_size = {innodb_buffer_pool_size}
innodb_log_file_size = 50M
innodb_log_buffer_size = 8M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

# SQL模式
sql_mode = {sql_mode}

# 日志设置
log-error = {data_path}\mysql_error.log
slow_query_log = 1
slow_query_log_file = {data_path}\mysql_slow.log
long_query_time = 2

# 二进制日志
log-bin = {data_path}\mysql-bin
binlog_format = ROW
expire_logs_days = 7

[mysql_safe]
log-error = {data_path}\mysql_error.log
pid-file = {data_path}\mysql.pid

[mysqldump]
quick
max_allowed_packet = 16M

[myisamchk]
key_buffer_size = 20M
sort_buffer_size = 20M
read_buffer = 2M
write_buffer = 2M
"#,
            character_set = self.config.character_set,
            port = self.config.port,
            install_path = self.config.install_path.replace("\\", "/"),
            data_path = self.config.data_path.replace("\\", "/"),
            max_connections = self.config.max_connections,
            innodb_buffer_pool_size = self.config.innodb_buffer_pool_size,
            sql_mode = self.config.sql_mode,
        )
    }

    /// 初始化MySQL数据目录
    async fn initialize_mysql_data(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("初始化数据", 50.0, InstallationStatus::InProgress, "正在初始化MySQL数据目录...");

        // 创建数据目录
        let data_path = Path::new(&self.config.data_path);
        if !data_path.exists() {
            fs::create_dir_all(data_path)?;
        }

        // 运行mysqld --initialize-insecure
        let mysqld_path = Path::new(&self.config.install_path).join("bin").join("mysqld.exe");
        let output = AsyncCommand::new(&mysqld_path)
            .args(&[
                "--initialize-insecure",
                &format!("--basedir={}", self.config.install_path),
                &format!("--datadir={}", self.config.data_path),
            ])
            .output()
            .await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(format!("MySQL数据目录初始化失败: {}", error_msg).into());
        }

        log::info!("MySQL数据目录初始化完成");
        Ok(())
    }

    /// 安装MySQL服务
    async fn install_mysql_service(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("安装服务", 60.0, InstallationStatus::InProgress, "正在安装MySQL服务...");

        let mysqld_path = Path::new(&self.config.install_path).join("bin").join("mysqld.exe");
        let config_path = Path::new(&self.config.install_path).join("my.ini");

        let output = AsyncCommand::new(&mysqld_path)
            .args(&[
                "--install",
                MYSQL_SERVICE_NAME,
                &format!("--defaults-file={}", config_path.display()),
            ])
            .output()
            .await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(format!("MySQL服务安装失败: {}", error_msg).into());
        }

        log::info!("MySQL服务安装完成");
        Ok(())
    }

    /// 启动MySQL服务
    async fn start_mysql_service(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("启动服务", 70.0, InstallationStatus::InProgress, "正在启动MySQL服务...");

        #[cfg(target_os = "windows")]
        {
            let output = AsyncCommand::new("net")
                .args(&["start", MYSQL_SERVICE_NAME])
                .output()
                .await?;

            if !output.status.success() {
                let error_msg = String::from_utf8_lossy(&output.stderr);
                return Err(format!("MySQL服务启动失败: {}", error_msg).into());
            }
        }

        // 等待服务启动
        tokio::time::sleep(Duration::from_secs(5)).await;

        log::info!("MySQL服务启动完成");
        Ok(())
    }

    /// 设置root密码
    async fn set_root_password(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("设置密码", 80.0, InstallationStatus::InProgress, "正在设置root密码...");

        let mysql_path = Path::new(&self.config.install_path).join("bin").join("mysql.exe");

        // 创建SQL脚本
        let sql_script = format!(
            "ALTER USER 'root'@'localhost' IDENTIFIED BY '{}'; FLUSH PRIVILEGES;",
            self.config.root_password
        );

        let output = AsyncCommand::new(&mysql_path)
            .args(&[
                "-u", "root",
                "--skip-password",
                "-e", &sql_script,
            ])
            .output()
            .await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            log::warn!("设置root密码可能失败: {}", error_msg);
        }

        log::info!("root密码设置完成");
        Ok(())
    }

    /// 配置环境变量
    async fn configure_environment_variables(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.update_progress("配置环境", 90.0, InstallationStatus::InProgress, "正在配置环境变量...");

        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;

            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            let environment = hklm.open_subkey_with_flags(
                "SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment",
                KEY_READ | KEY_WRITE,
            )?;

            // 获取当前PATH
            let current_path: String = environment.get_value("Path").unwrap_or_default();
            let mysql_bin_path = format!("{}\\bin", self.config.install_path);

            // 检查是否已经包含MySQL路径
            if !current_path.contains(&mysql_bin_path) {
                let new_path = if current_path.is_empty() {
                    mysql_bin_path
                } else {
                    format!("{};{}", current_path, mysql_bin_path)
                };
                environment.set_value("Path", &new_path)?;
            }

            // 设置MYSQL_HOME
            environment.set_value("MYSQL_HOME", &self.config.install_path)?;
        }

        log::info!("环境变量配置完成");
        Ok(())
    }

    /// 验证MySQL安装
    pub async fn verify_installation(&self) -> Result<bool, Box<dyn std::error::Error>> {
        log::info!("验证MySQL安装...");

        // 1. 检查服务状态
        if !self.check_service_status().await? {
            return Ok(false);
        }

        // 2. 检查数据库连接
        if !self.check_database_connection().await? {
            return Ok(false);
        }

        // 3. 检查基本功能
        if !self.check_basic_functionality().await? {
            return Ok(false);
        }

        log::info!("MySQL安装验证成功");
        Ok(true)
    }

    /// 检查服务状态
    async fn check_service_status(&self) -> Result<bool, Box<dyn std::error::Error>> {
        #[cfg(target_os = "windows")]
        {
            let output = AsyncCommand::new("sc")
                .args(&["query", MYSQL_SERVICE_NAME])
                .output()
                .await?;

            if output.status.success() {
                let output_str = String::from_utf8_lossy(&output.stdout);
                return Ok(output_str.contains("RUNNING"));
            }
        }

        Ok(false)
    }

    /// 检查数据库连接
    async fn check_database_connection(&self) -> Result<bool, Box<dyn std::error::Error>> {
        let mysql_path = Path::new(&self.config.install_path).join("bin").join("mysql.exe");

        let output = AsyncCommand::new(&mysql_path)
            .args(&[
                "-u", "root",
                &format!("-p{}", self.config.root_password),
                "-e", "SELECT 1;",
            ])
            .output()
            .await?;

        Ok(output.status.success())
    }

    /// 检查基本功能
    async fn check_basic_functionality(&self) -> Result<bool, Box<dyn std::error::Error>> {
        let mysql_path = Path::new(&self.config.install_path).join("bin").join("mysql.exe");

        let sql_commands = vec![
            "SHOW DATABASES;",
            "SELECT VERSION();",
            "SHOW VARIABLES LIKE 'character_set_server';",
        ];

        for sql in sql_commands {
            let output = AsyncCommand::new(&mysql_path)
                .args(&[
                    "-u", "root",
                    &format!("-p{}", self.config.root_password),
                    "-e", sql,
                ])
                .output()
                .await?;

            if !output.status.success() {
                return Ok(false);
            }
        }

        Ok(true)
    }
}

/// 全局MySQL安装器函数

/// 获取MySQL安装器实例
pub fn get_mysql_installer() -> &'static mut MySQLInstaller {
    MySQLInstaller::instance()
}

/// 检测MySQL组件
pub fn detect_mysql_components() -> Result<Vec<MySQLComponent>, Box<dyn std::error::Error>> {
    let installer = get_mysql_installer();
    installer.detect_mysql_components()
}

/// 卸载旧版本MySQL
pub async fn uninstall_old_mysql(components: &[MySQLComponent]) -> Result<(), Box<dyn std::error::Error>> {
    let installer = get_mysql_installer();
    installer.uninstall_old_mysql(components).await
}

/// 安装MySQL
pub async fn install_mysql(zip_file_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    let installer = get_mysql_installer();
    installer.install_mysql(zip_file_path).await
}

/// 验证MySQL安装
pub async fn verify_mysql_installation() -> Result<bool, Box<dyn std::error::Error>> {
    let installer = get_mysql_installer();
    installer.verify_installation().await
}

/// 设置MySQL配置
pub fn set_mysql_config(config: MySQLConfig) {
    let installer = get_mysql_installer();
    installer.config = config;
}

/// 获取MySQL配置
pub fn get_mysql_config() -> MySQLConfig {
    let installer = get_mysql_installer();
    installer.config.clone()
}

/// 设置进度回调
pub fn set_installation_progress_callback<F>(callback: F)
where
    F: Fn(InstallationProgress) + Send + Sync + 'static,
{
    let installer = get_mysql_installer();
    installer.set_progress_callback(callback);
}