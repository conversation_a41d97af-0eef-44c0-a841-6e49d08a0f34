// MySQLAi.de - 隐私政策页面
// 展示平台用户隐私保护政策和个人信息处理规则

import { Metadata } from 'next';
import { getLegalContent } from '@/lib/legal';
import { PAGE_METADATA } from '@/lib/constants';
import { generatePageMetadata } from '@/app/metadata';
import LegalPageLayout from '@/components/layout/LegalPageLayout';

// 生成页面元数据
export function generateMetadata(): Metadata {
  const pageData = PAGE_METADATA.privacy;
  return generatePageMetadata(
    pageData.title,
    pageData.description,
    '/privacy'
  );
}

export default function PrivacyPage() {
  // 获取隐私政策内容
  const privacyContent = getLegalContent('privacy');

  return (
    <LegalPageLayout
      type="privacy"
      title={privacyContent.title}
      lastUpdated={privacyContent.lastUpdated}
      pathname="/privacy"
    >
      {/* 渲染隐私政策内容 */}
      {privacyContent.sections.map((section) => (
        <div key={section.id} className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {section.title}
          </h2>
          <p className="text-gray-700 leading-relaxed mb-4">
            {section.content}
          </p>
          
          {/* 渲染子章节 */}
          {section.subsections && section.subsections.length > 0 && (
            <div className="ml-4 space-y-4">
              {section.subsections.map((subsection) => (
                <div key={subsection.id}>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">
                    {subsection.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {subsection.content}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}

      {/* 隐私保护特殊说明 */}
      <div className="mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          个人信息保护承诺
        </h3>
        <div className="text-blue-800 space-y-2">
          <p>• 我们严格遵守《中华人民共和国个人信息保护法》等相关法律法规</p>
          <p>• 采用行业领先的加密技术保护您的个人信息安全</p>
          <p>• 绝不向第三方出售或泄露您的个人信息</p>
          <p>• 您有权随时查询、更正、删除您的个人信息</p>
        </div>
      </div>

      {/* 联系方式特殊说明 */}
      <div className="mt-8 p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          隐私问题联系方式
        </h3>
        <div className="text-gray-700 space-y-2">
          <p><strong>隐私保护专员邮箱：</strong> <EMAIL></p>
          <p><strong>客服热线：</strong> +86 400-888-9999</p>
          <p><strong>工作时间：</strong> 周一至周五 9:00-18:00</p>
          <p className="text-sm text-gray-600 mt-3">
            如您对个人信息处理有任何疑问或需要行使相关权利，请通过上述方式联系我们，我们将在15个工作日内回复。
          </p>
        </div>
      </div>
    </LegalPageLayout>
  );
}
