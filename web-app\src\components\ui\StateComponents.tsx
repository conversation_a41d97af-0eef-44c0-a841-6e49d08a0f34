'use client';

// MySQLAi.de - 统一状态组件
// 提供加载、错误、空状态等统一的用户反馈组件

import React from 'react';
import { motion } from 'framer-motion';
import { 
  AlertCircle, 
  Loader2, 
  RefreshCw, 
  Database,
  FileX,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from './Button';
import { SkeletonLoader } from './PageLoader';

// 基础状态组件Props
interface BaseStateProps {
  className?: string;
}

// 加载状态组件Props
interface LoadingStateProps extends BaseStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'skeleton' | 'pulse';
  itemCount?: number;
}

// 错误状态组件Props
interface ErrorStateProps extends BaseStateProps {
  error?: string;
  title?: string;
  onRetry?: () => void;
  retryLabel?: string;
  variant?: 'network' | 'server' | 'generic';
}

// 空状态组件Props
interface EmptyStateProps extends BaseStateProps {
  title?: string;
  message?: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * 加载状态组件
 */
export function LoadingState({
  message = '正在加载...',
  size = 'md',
  variant = 'spinner',
  itemCount = 6,
  className
}: LoadingStateProps) {
  const sizeConfig = {
    sm: { spinner: 'w-4 h-4', text: 'text-sm', spacing: 'space-y-2' },
    md: { spinner: 'w-6 h-6', text: 'text-base', spacing: 'space-y-4' },
    lg: { spinner: 'w-8 h-8', text: 'text-lg', spacing: 'space-y-6' }
  };

  if (variant === 'skeleton') {
    return (
      <div className={cn('space-y-4', className)}>
        <SkeletonLoader lines={1} className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(itemCount)].map((_, i) => (
            <SkeletonLoader key={i} lines={1} className="h-64 rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className={cn('flex items-center justify-center py-12', className)}>
        <motion.div
          animate={{ scale: [1, 1.1, 1], opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="flex flex-col items-center space-y-4"
        >
          <div className="w-16 h-16 bg-mysql-primary rounded-full flex items-center justify-center">
            <Database className="w-8 h-8 text-white" />
          </div>
          <p className={cn('text-mysql-text-light', sizeConfig[size].text)}>
            {message}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center justify-center py-12', className)}>
      <div className={cn('flex items-center', sizeConfig[size].spacing)}>
        <Loader2 className={cn('text-mysql-primary animate-spin', sizeConfig[size].spinner)} />
        <span className={cn('text-mysql-text-light', sizeConfig[size].text)}>
          {message}
        </span>
      </div>
    </div>
  );
}

/**
 * 错误状态组件
 */
export function ErrorState({
  error = '加载失败',
  title = '出现错误',
  onRetry,
  retryLabel = '重试',
  variant = 'generic',
  className
}: ErrorStateProps) {
  const getIcon = () => {
    switch (variant) {
      case 'network':
        return <WifiOff className="w-12 h-12 text-red-500" />;
      case 'server':
        return <Database className="w-12 h-12 text-red-500" />;
      default:
        return <AlertCircle className="w-12 h-12 text-red-500" />;
    }
  };

  const getTitle = () => {
    switch (variant) {
      case 'network':
        return '网络连接失败';
      case 'server':
        return '服务器错误';
      default:
        return title;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn('flex flex-col items-center justify-center py-12', className)}
    >
      {getIcon()}
      <h3 className="text-lg font-semibold text-mysql-text mb-2 mt-4">
        {getTitle()}
      </h3>
      <p className="text-mysql-text-light mb-6 text-center max-w-md">
        {error}
      </p>
      {onRetry && (
        <Button
          onClick={onRetry}
          variant="primary"
          size="md"
          icon={<RefreshCw className="w-4 h-4" />}
        >
          {retryLabel}
        </Button>
      )}
    </motion.div>
  );
}

/**
 * 空状态组件
 */
export function EmptyState({
  title = '暂无数据',
  message = '当前没有可显示的内容',
  icon,
  action,
  className
}: EmptyStateProps) {
  const defaultIcon = <FileX className="w-12 h-12 text-mysql-text-light" />;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn('flex flex-col items-center justify-center py-12', className)}
    >
      {icon || defaultIcon}
      <h3 className="text-lg font-semibold text-mysql-text mb-2 mt-4">
        {title}
      </h3>
      <p className="text-mysql-text-light mb-6 text-center max-w-md">
        {message}
      </p>
      {action && (
        <Button
          onClick={action.onClick}
          variant="primary"
          size="md"
        >
          {action.label}
        </Button>
      )}
    </motion.div>
  );
}

/**
 * 网络状态指示器
 */
export function NetworkStatus({ isOnline = true }: { isOnline?: boolean }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        'fixed bottom-4 right-4 flex items-center px-3 py-2 rounded-lg shadow-lg z-50',
        isOnline
          ? 'bg-green-500 text-white'
          : 'bg-red-500 text-white'
      )}
    >
      {isOnline ? (
        <Wifi className="w-4 h-4 mr-2" />
      ) : (
        <WifiOff className="w-4 h-4 mr-2" />
      )}
      <span className="text-sm">
        {isOnline ? '已连接' : '网络断开'}
      </span>
    </motion.div>
  );
}

// 组合状态组件Props
interface StateWrapperProps {
  loading: boolean;
  error?: string;
  isEmpty?: boolean;
  children: React.ReactNode;
  loadingProps?: Partial<LoadingStateProps>;
  errorProps?: Partial<ErrorStateProps>;
  emptyProps?: Partial<EmptyStateProps>;
  className?: string;
}

/**
 * 状态包装器组件 - 统一处理加载、错误、空状态
 */
export function StateWrapper({
  loading,
  error,
  isEmpty = false,
  children,
  loadingProps = {},
  errorProps = {},
  emptyProps = {},
  className
}: StateWrapperProps) {
  if (loading) {
    return <LoadingState {...loadingProps} className={className} />;
  }

  if (error) {
    return <ErrorState error={error} {...errorProps} className={className} />;
  }

  if (isEmpty) {
    return <EmptyState {...emptyProps} className={className} />;
  }

  return <div className={className}>{children}</div>;
}

// 侧边栏骨架屏
export function SidebarSkeleton({ itemCount = 6 }: { itemCount?: number }) {
  return (
    <div className="p-4 space-y-2">
      {[...Array(itemCount)].map((_, i) => (
        <div key={i} className="space-y-1">
          <SkeletonLoader lines={1} className="h-10 rounded-lg" />
        </div>
      ))}
    </div>
  );
}

// 卡片网格骨架屏
export function CardGridSkeleton({ itemCount = 6 }: { itemCount?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(itemCount)].map((_, i) => (
        <div key={i} className="space-y-3">
          <SkeletonLoader lines={1} className="h-48 rounded-lg" />
          <SkeletonLoader lines={2} />
        </div>
      ))}
    </div>
  );
}

// 列表骨架屏
export function ListSkeleton({ itemCount = 5 }: { itemCount?: number }) {
  return (
    <div className="space-y-4">
      {[...Array(itemCount)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4">
          <SkeletonLoader lines={1} className="w-12 h-12 rounded-full" />
          <div className="flex-1">
            <SkeletonLoader lines={2} />
          </div>
        </div>
      ))}
    </div>
  );
}
