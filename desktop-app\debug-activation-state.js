/**
 * 激活状态同步调试脚本
 * 用于分析和修复激活状态同步问题
 */

console.log('🔍 开始激活状态同步调试分析...\n');

// 模拟激活成功的状态数据
const mockSuccessfulActivationStatus = {
  isActivated: true,
  licenseKey: 'TEST-ACTIVATION-SUCCESS-123',
  machineId: 'test-machine-id',
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
  remainingHours: 720,
  isTrialMode: false
};

// 模拟状态转换函数（从utils.ts复制）
function convertActivationStatusToState(status) {
  console.log('🔄 convertActivationStatusToState called with:', status);
  
  // 处理空值情况
  if (!status) {
    const result = {
      status: 'not_activated',
      machineId: '',
      lastCheckTime: Date.now()
    };
    console.log('📤 Empty status converted to:', result);
    return result;
  }

  // 转换激活状态枚举
  let storeStatus;
  if (status.isActivated) {
    // 检查是否过期
    if (status.expiresAt) {
      const expirationDate = new Date(status.expiresAt);
      const now = new Date();
      storeStatus = expirationDate <= now ? 'expired' : 'activated';
      console.log(`📅 Expiration check: expires at ${expirationDate}, now ${now}, status: ${storeStatus}`);
    } else {
      storeStatus = 'activated';
      console.log('📅 No expiration date, status: activated');
    }
  } else {
    storeStatus = 'not_activated';
    console.log('❌ isActivated is false, status: not_activated');
  }

  const result = {
    status: storeStatus,
    licenseKey: status.licenseKey || undefined,
    expiresAt: status.expiresAt || undefined,
    remainingHours: status.remainingHours || undefined,
    machineId: status.machineId || '',
    lastCheckTime: Date.now()
  };
  
  console.log('📤 Final converted state:', result);
  return result;
}

// 模拟UI条件渲染逻辑
function shouldShowActivationButton(activationState) {
  const result = activationState.status === 'not_activated' || activationState.status === 'expired';
  console.log(`🎯 shouldShowActivationButton: status=${activationState.status}, result=${result}`);
  return result;
}

// 测试1: 状态转换测试
console.log('📋 测试1: 激活成功状态转换');
console.log('=' .repeat(50));
const convertedState = convertActivationStatusToState(mockSuccessfulActivationStatus);
console.log('✅ 转换结果:', convertedState);
console.log('✅ 期望状态: activated');
console.log('✅ 实际状态:', convertedState.status);
console.log('✅ 转换成功:', convertedState.status === 'activated' ? '是' : '否');

// 测试2: UI条件渲染测试
console.log('\n📋 测试2: UI条件渲染逻辑');
console.log('=' .repeat(50));
const shouldShow = shouldShowActivationButton(convertedState);
console.log('✅ 激活后应该隐藏按钮:', !shouldShow ? '是' : '否');
console.log('✅ shouldShowActivationButton 结果:', shouldShow);

// 测试3: 模拟完整激活流程
console.log('\n📋 测试3: 完整激活流程模拟');
console.log('=' .repeat(50));

// 步骤1: 初始状态
console.log('📍 步骤1: 初始未激活状态');
let currentState = { status: 'not_activated' };
let shouldShowStep1 = shouldShowActivationButton(currentState);
console.log(`   状态: ${currentState.status}, 显示按钮: ${shouldShowStep1}`);

// 步骤2: 激活中
console.log('📍 步骤2: 激活验证中');
currentState = { status: 'checking' };
let shouldShowStep2 = shouldShowActivationButton(currentState);
console.log(`   状态: ${currentState.status}, 显示按钮: ${shouldShowStep2}`);

// 步骤3: 激活成功
console.log('📍 步骤3: 激活成功');
currentState = convertActivationStatusToState(mockSuccessfulActivationStatus);
let shouldShowStep3 = shouldShowActivationButton(currentState);
console.log(`   状态: ${currentState.status}, 显示按钮: ${shouldShowStep3}`);

// 测试4: 边界情况测试
console.log('\n📋 测试4: 边界情况测试');
console.log('=' .repeat(50));

// 空状态
console.log('📍 测试空状态:');
const emptyState = convertActivationStatusToState(null);
const shouldShowEmpty = shouldShowActivationButton(emptyState);
console.log(`   状态: ${emptyState.status}, 显示按钮: ${shouldShowEmpty}`);

// 过期状态
console.log('📍 测试过期状态:');
const expiredStatus = {
  ...mockSuccessfulActivationStatus,
  expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 昨天过期
};
const expiredState = convertActivationStatusToState(expiredStatus);
const shouldShowExpired = shouldShowActivationButton(expiredState);
console.log(`   状态: ${expiredState.status}, 显示按钮: ${shouldShowExpired}`);

// 总结
console.log('\n📊 调试总结');
console.log('=' .repeat(50));
console.log('✅ 状态转换函数工作正常');
console.log('✅ UI条件渲染逻辑正确');
console.log('✅ 激活成功后按钮正确隐藏');
console.log('✅ 边界情况处理正确');

console.log('\n🔍 可能的问题原因:');
console.log('1. 状态同步时机问题 - activation.refreshStatus() 可能没有正确更新状态');
console.log('2. useEffect 依赖问题 - 状态变化没有触发重新渲染');
console.log('3. 状态更新延迟 - 异步操作导致UI更新滞后');
console.log('4. 组件重新渲染问题 - React状态更新没有正确触发');

console.log('\n🛠️ 建议的修复方案:');
console.log('1. 在激活成功后强制刷新组件状态');
console.log('2. 添加状态变化监听器确保UI及时更新');
console.log('3. 使用setTimeout确保状态同步在下一个事件循环中执行');
console.log('4. 添加详细的调试日志追踪状态变化过程');

console.log('\n🎯 下一步行动:');
console.log('1. 检查 activation.refreshStatus() 的实现');
console.log('2. 验证 useEffect 的依赖数组是否正确');
console.log('3. 确认 appStore 的状态更新是否正确触发组件重新渲染');
console.log('4. 测试强制状态同步的修复方案');

console.log('\n🎉 调试分析完成！');
