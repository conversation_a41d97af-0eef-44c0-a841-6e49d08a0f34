# 知识库数据管理 Hooks

这个目录包含了用于知识库数据管理的自定义 React Hooks，提供统一的数据获取、缓存、错误处理和搜索功能。

## 核心 Hooks

### useKnowledgeData

统一的数据管理 hook，提供 CRUD 操作、缓存、错误处理和加载状态管理。

```typescript
import { useKnowledgeData } from '@/hooks';

// 基本使用
const {
  data: articles,
  loading,
  error,
  refetch,
  create,
  update,
  remove
} = useKnowledgeData('articles');

// 带参数使用
const {
  data: filteredArticles,
  loading,
  error
} = useKnowledgeData('articles', {
  category: 'mysql',
  difficulty: 'beginner'
}, {
  autoFetch: true,
  cacheTime: 10 * 60 * 1000, // 10分钟缓存
  optimisticUpdates: true
});

// CRUD 操作
const handleCreate = async () => {
  const success = await create({
    id: 'new-article',
    title: '新文章',
    content: '文章内容...'
  });
  
  if (success) {
    console.log('文章创建成功');
  }
};

const handleUpdate = async (id: string) => {
  const success = await update(id, {
    title: '更新的标题'
  });
  
  if (success) {
    console.log('文章更新成功');
  }
};

const handleDelete = async (id: string) => {
  const success = await remove(id);
  
  if (success) {
    console.log('文章删除成功');
  }
};

// 批量操作
const handleBatchDelete = async (ids: string[]) => {
  const success = await batchRemove(ids);
  
  if (success) {
    console.log('批量删除成功');
  }
};
```

### useKnowledgeCache

智能缓存管理 hook，提供数据缓存、过期管理和持久化功能。

```typescript
import { useKnowledgeCache } from '@/hooks';

const cache = useKnowledgeCache({
  defaultTTL: 5 * 60 * 1000, // 5分钟默认过期时间
  maxItems: 1000, // 最大缓存项数
  persistent: true, // 启用持久化
  debug: true // 调试模式
});

// 基本缓存操作
cache.set('articles_list', articles, 10 * 60 * 1000); // 缓存10分钟
const cachedArticles = cache.get('articles_list');

// 批量操作
cache.setMultiple([
  { key: 'articles_page_1', data: page1Data },
  { key: 'articles_page_2', data: page2Data }
]);

const [page1, page2] = cache.getMultiple(['articles_page_1', 'articles_page_2']);

// 缓存管理
cache.invalidate('articles_*'); // 清除所有文章相关缓存
cache.clear(); // 清空所有缓存

// 获取统计信息
const stats = cache.getStats();
console.log(`缓存命中率: ${(stats.hitRate * 100).toFixed(2)}%`);
```

### useKnowledgeSearch

智能搜索功能 hook，提供搜索、筛选、排序和历史记录功能。

```typescript
import { useKnowledgeSearch } from '@/hooks';

const {
  results,
  loading,
  error,
  query,
  setQuery,
  filters,
  setFilter,
  clearFilters,
  history,
  suggestions,
  loadMore,
  hasResults,
  canLoadMore
} = useKnowledgeSearch({
  debounceDelay: 300,
  enableHistory: true,
  enableSuggestions: true,
  enableCache: true
});

// 搜索操作
const handleSearch = (searchQuery: string) => {
  setQuery(searchQuery);
};

// 筛选操作
const handleFilter = (category: string) => {
  setFilter('category', category);
};

// 清除筛选
const handleClearFilters = () => {
  clearFilters();
};

// 加载更多结果
const handleLoadMore = async () => {
  if (canLoadMore) {
    await loadMore();
  }
};

// 从历史记录搜索
const handleHistorySearch = (historyItem) => {
  searchFromHistory(historyItem);
};
```

## 组合使用示例

```typescript
import { useKnowledgeData, useKnowledgeSearch, useKnowledgeCache } from '@/hooks';

function KnowledgeManagement() {
  // 数据管理
  const {
    data: articles,
    loading: articlesLoading,
    create: createArticle,
    update: updateArticle,
    remove: removeArticle
  } = useKnowledgeData('articles', {}, {
    optimisticUpdates: true,
    backgroundRefresh: true
  });

  // 搜索功能
  const {
    results: searchResults,
    loading: searchLoading,
    query,
    setQuery,
    filters,
    setFilter
  } = useKnowledgeSearch({
    enableHistory: true,
    enableSuggestions: true
  });

  // 缓存管理
  const cache = useKnowledgeCache({
    persistent: true,
    debug: process.env.NODE_ENV === 'development'
  });

  // 组合逻辑
  const displayData = query ? searchResults?.articles : articles;
  const isLoading = articlesLoading || searchLoading;

  return (
    <div>
      {/* 搜索界面 */}
      <SearchBar
        value={query}
        onChange={setQuery}
        loading={searchLoading}
      />
      
      {/* 筛选界面 */}
      <FilterBar
        filters={filters}
        onFilterChange={setFilter}
      />
      
      {/* 数据展示 */}
      <DataList
        data={displayData}
        loading={isLoading}
        onEdit={updateArticle}
        onDelete={removeArticle}
      />
    </div>
  );
}
```

## 配置选项

### useKnowledgeData 选项

- `autoFetch`: 自动获取数据 (默认: true)
- `cacheTime`: 缓存时间，毫秒 (默认: 5分钟)
- `retryCount`: 重试次数 (默认: 3)
- `retryDelay`: 重试延迟，毫秒 (默认: 1000)
- `optimisticUpdates`: 乐观更新 (默认: true)
- `backgroundRefresh`: 后台刷新 (默认: false)
- `debug`: 调试模式 (默认: false)

### useKnowledgeCache 选项

- `defaultTTL`: 默认过期时间，毫秒 (默认: 5分钟)
- `maxItems`: 最大缓存项数 (默认: 1000)
- `cleanupInterval`: 清理间隔，毫秒 (默认: 1分钟)
- `persistent`: 启用持久化 (默认: true)
- `storagePrefix`: 存储键前缀 (默认: 'knowledge_cache_')
- `debug`: 调试模式 (默认: false)

### useKnowledgeSearch 选项

- `debounceDelay`: 防抖延迟，毫秒 (默认: 300)
- `minQueryLength`: 最小查询长度 (默认: 2)
- `maxHistoryItems`: 最大历史记录数 (默认: 20)
- `enableSuggestions`: 启用搜索建议 (默认: true)
- `enableHistory`: 启用搜索历史 (默认: true)
- `enableCache`: 启用缓存 (默认: true)
- `cacheTime`: 缓存时间，毫秒 (默认: 5分钟)
- `debug`: 调试模式 (默认: false)

## 最佳实践

1. **性能优化**
   - 合理设置缓存时间，避免过度缓存
   - 使用乐观更新提升用户体验
   - 启用后台刷新保持数据新鲜度

2. **错误处理**
   - 始终检查 error 状态
   - 提供重试机制
   - 显示用户友好的错误信息

3. **搜索体验**
   - 设置合适的防抖延迟
   - 启用搜索历史和建议
   - 提供清晰的筛选选项

4. **缓存策略**
   - 根据数据更新频率设置TTL
   - 定期清理过期缓存
   - 监控缓存命中率

5. **调试和监控**
   - 开发环境启用调试模式
   - 监控API调用频率
   - 跟踪用户搜索行为
