'use client';

/**
 * MySQL一键安装主组件
 * 提供简洁的一键安装界面，支持系统自动检测和默认配置安装
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Download,
  Settings,
  CheckCircle,
  Monitor,
  Database,
  Zap,
  ArrowRight,
  ExternalLink,
  Copy,
  FileDown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { LoadingState, ErrorState } from '@/components/ui/StateComponents';
import {
  SupportedOS,
  InstallationStatus,
  QuickInstallConfig,
  SystemInfo,
  InstallationResult
} from '../types/mysql-installer';
import { getSystemInfo } from '../lib/system-detection';
import { generateDefaultConfig, generateMySQLConfig } from '../lib/config-generator';
import { getOptimalDownloadUrl } from '../lib/download-manager';

interface QuickInstallerProps {
  onAdvancedMode?: () => void;
  onConfigComplete?: (result: InstallationResult) => void;
  className?: string;
}

/**
 * 一键安装主组件
 */
export default function QuickInstaller({
  onAdvancedMode,
  onConfigComplete,
  className
}: QuickInstallerProps) {
  // 状态管理
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [installConfig, setInstallConfig] = useState<QuickInstallConfig | null>(null);
  const [installationStatus, setInstallationStatus] = useState<InstallationStatus>(InstallationStatus.DETECTING);
  const [installationResult, setInstallationResult] = useState<InstallationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 系统检测
  const detectSystem = useCallback(async () => {
    try {
      setInstallationStatus(InstallationStatus.DETECTING);
      setError(null);

      // 检测系统信息
      const sysInfo = getSystemInfo();
      setSystemInfo(sysInfo);

      if (!sysInfo.isSupported) {
        throw new Error('当前操作系统不支持MySQL安装');
      }

      // 生成默认配置
      const config = generateDefaultConfig(sysInfo.os);
      setInstallConfig(config);

      setInstallationStatus(InstallationStatus.READY);
    } catch (err) {
      setError(err instanceof Error ? err.message : '系统检测失败');
      setInstallationStatus(InstallationStatus.ERROR);
    }
  }, []);

  // 一键安装处理
  const handleQuickInstall = useCallback(async () => {
    if (!systemInfo || !installConfig) return;

    try {
      setInstallationStatus(InstallationStatus.GENERATING_CONFIG);
      setError(null);

      // 生成配置文件
      const configContent = generateMySQLConfig(installConfig);

      // 获取最佳下载链接
      setInstallationStatus(InstallationStatus.DOWNLOADING);
      const downloadInfo = await getOptimalDownloadUrl(
        installConfig.version,
        installConfig.targetOS,
        systemInfo.architecture
      );

      if (!downloadInfo) {
        throw new Error('无法获取下载链接，请稍后重试');
      }

      // 生成安装结果
      const result: InstallationResult = {
        status: InstallationStatus.COMPLETED,
        config: installConfig,
        configFileContent: configContent,
        installationSteps: [
          {
            id: 'download',
            title: '下载MySQL安装包',
            description: `从 ${downloadInfo.recommendedSource.name} 下载`,
            command: `下载链接: ${downloadInfo.recommendedSource.url}`,
            required: true,
            completed: false,
            estimatedTime: 5
          },
          {
            id: 'extract',
            title: '解压安装包',
            description: '将下载的安装包解压到指定目录',
            required: true,
            completed: false,
            estimatedTime: 2
          },
          {
            id: 'configure',
            title: '配置MySQL',
            description: '使用生成的配置文件配置MySQL',
            required: true,
            completed: false,
            estimatedTime: 3
          },
          {
            id: 'initialize',
            title: '初始化数据库',
            description: '初始化MySQL数据目录和系统表',
            required: true,
            completed: false,
            estimatedTime: 5
          },
          {
            id: 'start',
            title: '启动MySQL服务',
            description: '启动MySQL服务并设置开机自启',
            required: true,
            completed: false,
            estimatedTime: 2
          }
        ],
        downloadUrl: downloadInfo.recommendedSource.url,
        timestamp: new Date().toISOString()
      };

      setInstallationResult(result);
      setInstallationStatus(InstallationStatus.COMPLETED);

      // 调用完成回调
      onConfigComplete?.(result);

    } catch (err) {
      setError(err instanceof Error ? err.message : '安装准备失败');
      setInstallationStatus(InstallationStatus.ERROR);
    }
  }, [systemInfo, installConfig]);

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 这里可以添加成功提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  }, []);

  // 下载配置文件
  const downloadConfig = useCallback(() => {
    if (!installationResult?.configFileContent || !installConfig) return;

    const blob = new Blob([installationResult.configFileContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = installConfig.targetOS === SupportedOS.WINDOWS ? 'my.ini' : 'my.cnf';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [installationResult, installConfig]);

  // 组件挂载时检测系统
  useEffect(() => {
    detectSystem();
  }, [detectSystem]);

  // 渲染系统信息卡片
  const renderSystemInfo = () => {
    if (!systemInfo) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-6"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Monitor className="w-6 h-6 text-mysql-primary" />
          <h3 className="text-lg font-semibold text-mysql-text">系统信息</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">操作系统</span>
            <span className="font-medium">{systemInfo.osVersion}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm text-gray-600">架构</span>
            <span className="font-medium">{systemInfo.architecture}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span className="text-sm text-gray-600">浏览器</span>
            <span className="font-medium">{systemInfo.browser}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">兼容性</span>
            <span className="font-medium text-green-600">✓ 支持</span>
          </div>
        </div>
      </motion.div>
    );
  };

  // 渲染配置预览
  const renderConfigPreview = () => {
    if (!installConfig) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-6"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Settings className="w-6 h-6 text-mysql-primary" />
          <h3 className="text-lg font-semibold text-mysql-text">安装配置</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">MySQL版本</span>
              <span className="font-medium">{installConfig.version}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">安装路径</span>
              <span className="font-medium text-xs">{installConfig.installPath}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">数据目录</span>
              <span className="font-medium text-xs">{installConfig.dataPath}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">端口</span>
              <span className="font-medium">{installConfig.port}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">字符集</span>
              <span className="font-medium">{installConfig.charset}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">root密码</span>
              <span className="font-medium">••••••••</span>
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className={cn('max-w-4xl mx-auto', className)}>
      {/* 错误状态 */}
      {installationStatus === InstallationStatus.ERROR && (
        <ErrorState
          error={error || '未知错误'}
          onRetry={detectSystem}
          className="mb-6"
        />
      )}

      {/* 检测中状态 */}
      {installationStatus === InstallationStatus.DETECTING && (
        <LoadingState
          message="正在检测系统环境..."
          size="lg"
          variant="pulse"
          className="py-12"
        />
      )}

      {/* 主要内容 */}
      {installationStatus !== InstallationStatus.DETECTING && 
       installationStatus !== InstallationStatus.ERROR && (
        <AnimatePresence mode="wait">
          {/* 系统信息和配置预览 */}
          {installationStatus === InstallationStatus.READY && (
            <motion.div
              key="ready"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {renderSystemInfo()}
              {renderConfigPreview()}
              
              {/* 一键安装按钮 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center space-y-6"
              >
                <Button
                  size="xl"
                  onClick={handleQuickInstall}
                  className="px-12 py-4 text-xl font-bold shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                  icon={<Zap className="w-8 h-8" />}
                >
                  一键安装 MySQL 8.0
                </Button>
                
                <div className="flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onAdvancedMode}
                    icon={<Settings className="w-4 h-4" />}
                  >
                    高级选项
                  </Button>
                </div>
              </motion.div>
            </motion.div>
          )}
          {/* 安装进行中状态 */}
          {(installationStatus === InstallationStatus.GENERATING_CONFIG ||
            installationStatus === InstallationStatus.DOWNLOADING) && (
            <motion.div
              key="installing"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <LoadingState
                message={
                  installationStatus === InstallationStatus.GENERATING_CONFIG
                    ? "正在生成配置文件..."
                    : "正在获取下载链接..."
                }
                size="lg"
                variant="spinner"
              />
            </motion.div>
          )}

          {/* 安装完成状态 */}
          {installationStatus === InstallationStatus.COMPLETED && installationResult && (
            <motion.div
              key="completed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {/* 成功提示 */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="bg-green-50 border border-green-200 rounded-xl p-6 mb-6"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                  <div>
                    <h3 className="text-lg font-semibold text-green-800">安装准备完成！</h3>
                    <p className="text-sm text-green-600">配置文件已生成，下载链接已准备就绪</p>
                  </div>
                </div>
              </motion.div>

              {/* 下载信息 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-6"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <Download className="w-6 h-6 text-mysql-primary" />
                  <h3 className="text-lg font-semibold text-mysql-text">下载信息</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">MySQL {installConfig?.version}</p>
                      <p className="text-sm text-gray-600">
                        适用于 {systemInfo?.osVersion} ({systemInfo?.architecture})
                      </p>
                    </div>
                    <Button
                      href={installationResult.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      icon={<ExternalLink className="w-4 h-4" />}
                    >
                      立即下载
                    </Button>
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={downloadConfig}
                      icon={<FileDown className="w-4 h-4" />}
                    >
                      下载配置文件
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(installationResult.downloadUrl || '')}
                      icon={<Copy className="w-4 h-4" />}
                    >
                      复制下载链接
                    </Button>
                  </div>
                </div>
              </motion.div>

              {/* 安装步骤 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-6"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <Database className="w-6 h-6 text-mysql-primary" />
                  <h3 className="text-lg font-semibold text-mysql-text">安装步骤</h3>
                </div>

                <div className="space-y-3">
                  {installationResult.installationSteps.map((step, index) => (
                    <div key={step.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                      <div className="flex-shrink-0 w-6 h-6 bg-mysql-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-mysql-text">{step.title}</h4>
                        <p className="text-sm text-gray-600">{step.description}</p>
                        {step.command && (
                          <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono">
                            {step.command}
                          </div>
                        )}
                        {step.estimatedTime && (
                          <p className="text-xs text-gray-500 mt-1">
                            预计耗时: {step.estimatedTime} 分钟
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>

              {/* 重新开始按钮 */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-center"
              >
                <Button
                  variant="outline"
                  onClick={detectSystem}
                  icon={<ArrowRight className="w-4 h-4" />}
                >
                  重新开始
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
}
