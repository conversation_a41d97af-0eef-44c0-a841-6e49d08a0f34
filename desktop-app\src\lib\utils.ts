/**
 * 工具函数库
 * 从Web版本移植的通用工具函数
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 合并CSS类名
 * 使用clsx和tailwind-merge优化类名合并
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 */
export function formatTime(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
  }
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
}

/**
 * 生成随机ID
 */
export function generateId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成UUID
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 检查是否为空值
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

/**
 * 安全的JSON解析
 */
export function safeJsonParse<T>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str);
  } catch {
    return defaultValue;
  }
}

/**
 * 安全的JSON字符串化
 */
export function safeJsonStringify(obj: any, defaultValue: string = '{}'): string {
  try {
    return JSON.stringify(obj);
  } catch {
    return defaultValue;
  }
}

/**
 * 获取嵌套对象属性
 */
export function getNestedValue(obj: any, path: string, defaultValue: any = undefined): any {
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current;
}

/**
 * 设置嵌套对象属性
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
}

/**
 * 数组去重
 */
export function uniqueArray<T>(array: T[], key?: keyof T): T[] {
  if (!key) {
    return [...new Set(array)];
  }
  
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}

/**
 * 数组分组
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

/**
 * 数组排序
 */
export function sortBy<T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
  return [...array].sort((a, b) => {
    const aValue = a[key];
    const bValue = b[key];
    
    if (aValue < bValue) return order === 'asc' ? -1 : 1;
    if (aValue > bValue) return order === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * 检查是否为有效的URL
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查是否为有效的邮箱
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 截断文本
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * 首字母大写
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * 驼峰命名转换
 */
export function camelCase(str: string): string {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, '');
}

/**
 * 短横线命名转换
 */
export function kebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * 下划线命名转换
 */
export function snakeCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase();
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * 获取文件名（不含扩展名）
 */
export function getFileName(filename: string): string {
  return filename.replace(/\.[^/.]+$/, '');
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * 获取当前时间戳
 */
export function getCurrentTimestamp(): number {
  return Date.now();
}

/**
 * 格式化日期
 */
export function formatDate(date: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = new Date(date);
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 相对时间格式化
 */
export function formatRelativeTime(date: Date | string | number): string {
  const now = new Date();
  const target = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return '刚刚';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months}个月前`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years}年前`;
  }
}

/**
 * 颜色工具函数
 */
export const colorUtils = {
  /**
   * 十六进制转RGB
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  /**
   * RGB转十六进制
   */
  rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  },

  /**
   * 获取对比色
   */
  getContrastColor(hex: string): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return '#000000';

    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }
};

/**
 * 激活状态转换工具函数
 * 用于在useActivation Hook的ActivationStatus和appStore的ActivationState之间进行转换
 */

// 导入类型定义
import type { ActivationStatus as HookActivationStatus } from '../types';
import type { ActivationState, ActivationStatus as StoreActivationStatus } from '../store/appStore';

/**
 * 将useActivation Hook的ActivationStatus转换为appStore的ActivationState
 * @param status - useActivation Hook返回的激活状态
 * @returns 转换后的appStore激活状态
 */
export function convertActivationStatusToState(
  status: HookActivationStatus | null | undefined
): Partial<ActivationState> {
  // 处理空值情况
  if (!status) {
    return {
      status: 'not_activated',
      machineId: '',
      lastCheckTime: Date.now()
    };
  }

  // 转换激活状态枚举
  let storeStatus: StoreActivationStatus;
  if (status.isActivated) {
    // 检查是否过期
    if (status.expiresAt) {
      const expirationDate = new Date(status.expiresAt);
      const now = new Date();
      storeStatus = expirationDate <= now ? 'expired' : 'activated';
    } else {
      storeStatus = 'activated';
    }
  } else {
    storeStatus = 'not_activated';
  }

  return {
    status: storeStatus,
    licenseKey: status.licenseKey || undefined,
    expiresAt: status.expiresAt || undefined,
    remainingHours: status.remainingHours || undefined,
    machineId: status.machineId || '',
    lastCheckTime: Date.now()
  };
}

/**
 * 将appStore的ActivationState转换为useActivation Hook的ActivationStatus
 * @param state - appStore的激活状态
 * @returns 转换后的Hook激活状态
 */
export function convertActivationStateToStatus(
  state: ActivationState | null | undefined
): Partial<HookActivationStatus> {
  // 处理空值情况
  if (!state) {
    return {
      isActivated: false,
      machineId: '',
      remainingHours: 0
    };
  }

  return {
    isActivated: state.status === 'activated',
    licenseKey: state.licenseKey || undefined,
    expiresAt: state.expiresAt || undefined,
    machineId: state.machineId || '',
    isTrialMode: false, // 默认值，可根据业务逻辑调整
    remainingTrials: undefined,
    remainingHours: state.remainingHours || 0
  };
}

/**
 * 检查激活状态是否有效
 * @param status - 激活状态
 * @returns 是否有效
 */
export function isActivationValid(status: HookActivationStatus | ActivationState | null | undefined): boolean {
  if (!status) return false;

  // 处理Hook类型的状态
  if ('isActivated' in status) {
    if (!status.isActivated) return false;

    // 检查过期时间
    if (status.expiresAt) {
      const expirationDate = new Date(status.expiresAt);
      const now = new Date();
      return expirationDate > now;
    }

    return true;
  }

  // 处理Store类型的状态
  if ('status' in status) {
    return status.status === 'activated';
  }

  return false;
}

/**
 * 获取激活状态的显示文本
 * @param status - 激活状态
 * @returns 显示文本
 */
export function getActivationStatusText(status: HookActivationStatus | ActivationState | null | undefined): string {
  if (!status) return '未激活';

  // 处理Hook类型的状态
  if ('isActivated' in status) {
    if (!status.isActivated) return '未激活';

    if (status.expiresAt) {
      const expirationDate = new Date(status.expiresAt);
      const now = new Date();
      if (expirationDate <= now) return '已过期';
    }

    return '已激活';
  }

  // 处理Store类型的状态
  if ('status' in status) {
    switch (status.status) {
      case 'activated': return '已激活';
      case 'expired': return '已过期';
      case 'checking': return '检查中';
      case 'not_activated':
      default:
        return '未激活';
    }
  }

  return '未知状态';
}

/**
 * 获取激活状态的颜色类名
 * @param status - 激活状态
 * @returns Tailwind CSS颜色类名
 */
export function getActivationStatusColor(status: HookActivationStatus | ActivationState | null | undefined): string {
  if (!status) return 'text-gray-500';

  // 处理Hook类型的状态
  if ('isActivated' in status) {
    if (!status.isActivated) return 'text-red-500';

    if (status.expiresAt) {
      const expirationDate = new Date(status.expiresAt);
      const now = new Date();
      if (expirationDate <= now) return 'text-red-500';
    }

    return 'text-green-500';
  }

  // 处理Store类型的状态
  if ('status' in status) {
    switch (status.status) {
      case 'activated': return 'text-green-500';
      case 'expired': return 'text-red-500';
      case 'checking': return 'text-blue-500';
      case 'not_activated':
      default:
        return 'text-gray-500';
    }
  }

  return 'text-gray-500';
}