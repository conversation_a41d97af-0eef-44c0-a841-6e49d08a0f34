import { useState, useEffect, useCallback } from 'react';
import { createTauriAPI, isTauriEnvironment } from '../lib/tauri-mock';
import type { ActivationStatus } from '../types';

export interface UseActivationReturn {
  // 激活状态
  activationStatus: ActivationStatus | null;
  isActivated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  initializeActivation: () => Promise<void>;
  checkActivationStatus: () => Promise<void>;
  validateLicense: (licenseKey: string) => Promise<boolean>;
  activateLicense: (licenseKey: string) => Promise<boolean>;
  deactivateLicense: (licenseKey: string) => Promise<boolean>;
  refreshStatus: () => Promise<void>;
}

/**
 * 激活管理Hook
 * 提供许可证激活、验证、状态管理等功能
 */
export function useActivation(): UseActivationReturn {
  // 状态管理
  const [activationStatus, setActivationStatus] = useState<ActivationStatus | null>(null);
  const [isActivated, setIsActivated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 初始化激活系统
  const initializeActivation = useCallback(async () => {
    try {
      setError(null);
      setIsLoading(true);
      
      const api = await createTauriAPI();
      const result = await api.init_activation_system();
      console.log('Activation system initialized:', result);
      
      // 初始化后立即检查状态
      await checkActivationStatus();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '初始化激活系统失败';
      setError(errorMessage);
      console.error('Failed to initialize activation system:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 检查激活状态
  const checkActivationStatus = useCallback(async () => {
    try {
      setError(null);
      
      const api = await createTauriAPI();
      const status = await api.check_activation_status();
      setActivationStatus(status);
      setIsActivated(status.isActivated);
      
      console.log('Activation status:', status);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取激活状态失败';
      setError(errorMessage);
      console.error('Failed to check activation status:', err);
      
      // 如果获取状态失败，设置默认值
      setActivationStatus(null);
      setIsActivated(false);
    }
  }, []);

  // 验证许可证密钥
  const validateLicense = useCallback(async (licenseKey: string): Promise<boolean> => {
    try {
      setError(null);
      
      if (!licenseKey.trim()) {
        throw new Error('许可证密钥不能为空');
      }
      
      const api = await createTauriAPI();
      const result = await api.validate_license(licenseKey);
      const isValid = result.valid;
      console.log('License validation result:', isValid);
      
      return isValid;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证许可证失败';
      setError(errorMessage);
      console.error('Failed to validate license:', err);
      return false;
    }
  }, []);

  // 激活许可证
  const activateLicense = useCallback(async (licenseKey: string): Promise<boolean> => {
    try {
      setError(null);
      setIsLoading(true);
      
      if (!licenseKey.trim()) {
        throw new Error('许可证密钥不能为空');
      }
      
      // 先验证许可证
      const isValid = await validateLicense(licenseKey);
      if (!isValid) {
        throw new Error('无效的许可证密钥');
      }
      
      // 激活许可证
      const api = await createTauriAPI();
      const result = await api.activate_license_key(licenseKey);
      const success = result.success;
      
      if (success) {
        console.log('License activated successfully');
        // 激活成功后刷新状态
        await checkActivationStatus();
        return true;
      } else {
        throw new Error('激活失败，请检查许可证密钥');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '激活许可证失败';
      setError(errorMessage);
      console.error('Failed to activate license:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [validateLicense, checkActivationStatus]);

  // 停用许可证
  const deactivateLicense = useCallback(async (licenseKey: string): Promise<boolean> => {
    try {
      setError(null);
      setIsLoading(true);
      
      if (!licenseKey.trim()) {
        throw new Error('许可证密钥不能为空');
      }
      
      const api = await createTauriAPI();
      const result = await api.deactivate_license_key();
      const success = result.success;
      
      if (success) {
        console.log('License deactivated successfully');
        // 停用成功后刷新状态
        await checkActivationStatus();
        return true;
      } else {
        throw new Error('停用失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '停用许可证失败';
      setError(errorMessage);
      console.error('Failed to deactivate license:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [checkActivationStatus]);

  // 刷新状态
  const refreshStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      await checkActivationStatus();
    } finally {
      setIsLoading(false);
    }
  }, [checkActivationStatus]);

  // 组件挂载时初始化
  useEffect(() => {
    initializeActivation();
  }, [initializeActivation]);

  return {
    // 状态
    activationStatus,
    isActivated,
    isLoading,
    error,
    
    // 方法
    initializeActivation,
    checkActivationStatus,
    validateLicense,
    activateLicense,
    deactivateLicense,
    refreshStatus,
  };
}

/**
 * 格式化剩余时间
 */
export function formatRemainingTime(hours: number): string {
  if (hours <= 0) {
    return '已过期';
  }
  
  if (hours < 1) {
    const minutes = Math.floor(hours * 60);
    return `${minutes}分钟`;
  }
  
  if (hours < 24) {
    return `${hours.toFixed(1)}小时`;
  }
  
  const days = Math.floor(hours / 24);
  const remainingHours = Math.floor(hours % 24);
  
  if (remainingHours === 0) {
    return `${days}天`;
  }
  
  return `${days}天${remainingHours}小时`;
}

/**
 * 格式化过期时间
 */
export function formatExpiryDate(expiresAt: string | null): string {
  if (!expiresAt) {
    return '未知';
  }
  
  try {
    const date = new Date(expiresAt);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch {
    return '格式错误';
  }
}

/**
 * 验证许可证密钥格式
 */
export function validateLicenseKeyFormat(licenseKey: string): boolean {
  // 基本格式验证：非空且长度合理
  if (!licenseKey || licenseKey.trim().length < 8) {
    return false;
  }
  
  // 可以添加更复杂的格式验证逻辑
  // 例如：检查特定的模式、校验位等
  
  return true;
}

/**
 * 获取激活状态显示文本
 */
export function getActivationStatusText(status: ActivationStatus | null): string {
  if (!status) {
    return '未知状态';
  }
  
  if (status.isActivated) {
    if (status.remainingHours > 0) {
      return `已激活 (剩余: ${formatRemainingTime(status.remainingHours)})`;
    } else {
      return '已过期';
    }
  }

  if (status.isTrialMode) {
    const trials = status.remainingTrials || 0;
    return `试用模式 (剩余: ${trials}次)`;
  }
  
  return '未激活';
}

/**
 * 获取激活状态颜色类
 */
export function getActivationStatusColor(status: ActivationStatus | null): string {
  if (!status) {
    return 'text-gray-500';
  }
  
  if (status.isActivated) {
    if (status.remainingHours > 24) {
      return 'text-green-600';
    } else if (status.remainingHours > 0) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  }

  if (status.isTrialMode) {
    return 'text-blue-600';
  }
  
  return 'text-gray-500';
}