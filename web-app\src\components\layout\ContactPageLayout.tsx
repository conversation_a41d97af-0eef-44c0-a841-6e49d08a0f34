'use client';

// MySQLAi.de - ContactPageLayout联系页面布局组件
// 参考LegalPageLayout设计，为联系我们页面提供统一的布局结构和视觉风格

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowLeft, Phone, Mail, MessageCircle, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import Breadcrumb from '@/components/ui/Breadcrumb';

interface ContactPageLayoutProps {
  title: string;
  description: string;
  pathname: string;
  className?: string;
}

// 联系方式配置
const contactMethods = [
  {
    id: 'phone',
    title: '电话支持',
    contact: '+86 ************',
    icon: Phone,
    available: '7×24小时',
    href: 'tel:+8640088899999',
    description: '紧急问题请直接拨打，我们的专家团队将在5分钟内响应',
  },
  {
    id: 'email',
    title: '邮件支持',
    contact: '<EMAIL>',
    icon: Mail,
    available: '24小时内回复',
    href: 'mailto:<EMAIL>',
    description: '详细技术咨询和解决方案，我们会在24小时内回复',
  },
  {
    id: 'chat',
    title: '在线客服',
    contact: '点击开始对话',
    icon: MessageCircle,
    available: '工作日 9:00-18:00',
    href: '#',
    description: '实时沟通，快速响应您的技术需求和问题',
  },
];

// 相关页面链接
const relatedPages = [
  {
    title: '服务条款',
    description: '了解我们的服务条款和用户协议',
    href: '/terms',
  },
  {
    title: '隐私政策',
    description: '查看我们的隐私保护政策',
    href: '/privacy',
  },
];

const ContactPageLayout: React.FC<ContactPageLayoutProps> = React.memo(({
  title,
  description,
  pathname,
  className,
  ...props
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
      className={cn(
        'min-h-screen bg-gradient-to-br from-gray-50 to-white',
        className
      )}
      {...props}
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* 面包屑导航 */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="mb-8"
        >
          <Breadcrumb pathname={pathname} />
        </motion.div>

        {/* 返回按钮 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2, duration: 0.3 }}
          className="mb-8"
        >
          <Link
            href="/"
            className={cn(
              'inline-flex items-center space-x-2',
              'text-mysql-primary hover:text-mysql-primary-dark',
              'transition-colors duration-200',
              'group'
            )}
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-200" />
            <span className="text-sm font-medium">返回首页</span>
          </Link>
        </motion.div>

        {/* 页面标题区域 */}
        <motion.header
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="mb-12"
        >
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {title}
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {description}
            </p>
          </div>
        </motion.header>

        {/* 主要内容区域 */}
        <motion.main
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
          className={cn(
            'bg-white rounded-xl shadow-lg',
            'border border-gray-200',
            'p-6 md:p-8 lg:p-12',
            'mb-12'
          )}
        >
          {/* 联系方式网格 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon;

              return (
                <motion.div
                  key={method.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 + index * 0.1, duration: 0.3 }}
                >
                  <a
                    href={method.href}
                    className={cn(
                      'block p-6 rounded-lg border border-gray-200',
                      'hover:border-mysql-primary hover:shadow-md',
                      'transition-all duration-200',
                      'group h-full'
                    )}
                  >
                    {/* 图标区域 */}
                    <div className="flex items-center justify-center mb-4">
                      <div className="flex items-center justify-center w-12 h-12 bg-mysql-primary rounded-lg group-hover:scale-110 transition-transform duration-200">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                    </div>

                    {/* 内容区域 */}
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-mysql-primary transition-colors duration-200">
                        {method.title}
                      </h3>
                      <div className="text-mysql-primary font-medium mb-1">
                        {method.contact}
                      </div>
                      <div className="text-sm text-gray-500 mb-3">
                        {method.available}
                      </div>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {method.description}
                      </p>
                    </div>
                  </a>
                </motion.div>
              );
            })}
          </div>

          {/* 技术支持承诺 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.4 }}
            className="bg-gradient-to-r from-mysql-primary/5 to-mysql-primary-dark/5 rounded-lg p-6 text-center"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              专业技术支持承诺
            </h3>
            <p className="text-gray-700 leading-relaxed">
              我们拥有15年+数据库经验的专家团队，为您提供最专业的MySQL解决方案。
              紧急问题5分钟内响应，一般咨询24小时内回复，全年无休的技术支持。
            </p>
          </motion.div>
        </motion.main>

        {/* 相关页面导航 */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
          className="mb-12"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            相关信息
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {relatedPages.map((page) => (
              <motion.div
                key={page.href}
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link
                  href={page.href}
                  className={cn(
                    'block p-4 rounded-lg border border-gray-200',
                    'bg-white hover:bg-gray-50',
                    'transition-all duration-200',
                    'group'
                  )}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 group-hover:text-mysql-primary transition-colors duration-200">
                        {page.title}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {page.description}
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-mysql-primary group-hover:translate-x-1 transition-all duration-200" />
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* 底部提示信息 */}
        <motion.footer
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.4 }}
          className={cn(
            'text-center py-8 px-6',
            'bg-gray-50 rounded-lg',
            'border border-gray-200'
          )}
        >
          <p className="text-gray-600 text-sm leading-relaxed">
            我们致力于为每一位用户提供最优质的MySQL技术支持服务。
            如有任何疑问，请随时通过以上方式与我们联系。
          </p>
          <p className="text-gray-500 text-xs mt-2">
            MySQLAi.de - 您身边的MySQL专家
          </p>
        </motion.footer>
      </div>
    </motion.div>
  );
});

ContactPageLayout.displayName = 'ContactPageLayout';

export default ContactPageLayout;
