{"name": "mysqlai-desktop", "version": "1.0.0", "description": "MySQLAi Desktop MySQL Installer", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "clean": "rm -rf dist && rm -rf node_modules", "type-check": "tsc --noEmit"}, "dependencies": {"@tauri-apps/api": "^1.5.3", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^1.5.10", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.4.19"}}