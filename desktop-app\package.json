{"name": "mysqlai-desktop", "version": "1.0.0", "description": "MySQLAi Desktop MySQL Installer", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^1.5.3", "clsx": "^2.1.1", "framer-motion": "^12.22.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^1.5.10", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.4.19"}}