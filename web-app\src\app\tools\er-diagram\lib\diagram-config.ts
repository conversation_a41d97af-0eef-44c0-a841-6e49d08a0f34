/**
 * GoJS图形配置和样式定义
 * 基于Chen ER图标准的黑色主题视觉规范
 */

import * as go from 'gojs';
import type { DiagramLayoutConfig, DiagramStyleConfig } from '../types/er-diagram';

// Chen ER图标准黑色主题常量
export const MYSQL_THEME_COLORS = {
  primary: '#000000',        // 黑色 - 实体边框
  primaryDark: '#000000',    // 黑色 - 深色变体
  primaryLight: '#FFFFFF',   // 白色 - 浅色背景
  accent: '#000000',         // 黑色 - 属性边框
  text: '#000000',           // 黑色 - 文字色
  textLight: '#000000',      // 黑色 - 浅文字色
  border: '#000000',         // 黑色 - 边框色
  success: '#000000',        // 黑色 - 关系边框
  warning: '#000000',        // 黑色 - 主键标记
  error: '#000000',          // 黑色 - 错误色
  white: '#FFFFFF',          // 白色 - 填充色
  background: '#FFFFFF',     // 白色 - 背景色
} as const;

// ER图节点类型
export const NODE_TYPES = {
  ENTITY: 'Entity',
  ATTRIBUTE: 'Attribute',
  RELATIONSHIP: 'Relationship'
} as const;

// 连接线类型
export const LINK_TYPES = {
  ENTITY_ATTRIBUTE: 'EntityAttribute',
  ENTITY_RELATIONSHIP: 'EntityRelationship',
  FOREIGN_KEY: 'ForeignKey'
} as const;

/**
 * 默认布局配置
 */
export const DEFAULT_LAYOUT_CONFIG: DiagramLayoutConfig = {
  layoutType: 'ForceDirected',
  nodeSpacing: 80,
  layerSpacing: 120,
  animationEnabled: true
};

/**
 * Chen ER图标准样式配置 - 统一黑色主题
 */
export const DEFAULT_STYLE_CONFIG: DiagramStyleConfig = {
  entityColor: MYSQL_THEME_COLORS.primary,        // 黑色 - 实体边框
  attributeColor: MYSQL_THEME_COLORS.accent,      // 黑色 - 属性边框
  primaryKeyColor: MYSQL_THEME_COLORS.warning,    // 黑色 - 主键标记
  relationshipColor: MYSQL_THEME_COLORS.success,  // 黑色 - 关系边框
  linkColor: MYSQL_THEME_COLORS.text,             // 黑色 - 连接线
  fontSize: 14,
  fontFamily: '"PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif' // 统一使用项目标准字体
};

/**
 * 创建GoJS图形实例的基础配置
 */
export function createDiagramConfig(): Partial<go.DiagramInitOptions> {
  return {
    // 基础设置
    initialContentAlignment: go.Spot.Center,
    'undoManager.isEnabled': true,
    
    // 布局算法 - 力导向布局
    layout: new go.ForceDirectedLayout({
      defaultSpringLength: DEFAULT_LAYOUT_CONFIG.nodeSpacing,
      defaultElectricalCharge: 150,
      maxIterations: 200,
      epsilonDistance: 1,
      infinityDistance: 1000
    }),
    
    // 交互设置
    'toolManager.mouseWheelBehavior': go.ToolManager.WheelZoom,
    'draggingTool.isEnabled': true,
    allowSelect: true,
    allowHorizontalScroll: true,
    allowVerticalScroll: true,
    allowZoom: true,
    allowCopy: false,
    allowDelete: false,
    
    // 画布设置
    padding: 40,
    minScale: 0.1,
    maxScale: 3,
    
    // 动画设置 - 统一使用标准动画参数
    'animationManager.isEnabled': DEFAULT_LAYOUT_CONFIG.animationEnabled,
    'animationManager.duration': 300, // 0.3秒，与项目标准一致
    
    // 网格设置
    'grid.visible': true,
    'grid.gridCellSize': new go.Size(20, 20),
    'grid.gridOrigin': new go.Point(0, 0),

    // 背景设置 - 移除有问题的背景色配置
    // 'div.style.backgroundColor': MYSQL_THEME_COLORS.background,
    
    // 选择工具配置
    'toolManager.dragSelectingTool': new go.DragSelectingTool({
      isEnabled: true,
      delay: 100,
      box: new go.Part({
        layerName: 'Tool'
      }).add(
        new go.Shape({
          name: 'SHAPE',
          strokeWidth: 2,
          stroke: MYSQL_THEME_COLORS.primary,
          fill: 'rgba(255, 255, 255, 0.1)' // 白色半透明背景
        })
      )
    })
  };
}

/**
 * 创建实体节点模板
 */
export function createEntityNodeTemplate(): go.Node {
  const $ = go.GraphObject.make;

  return $(go.Node, 'Auto',
    {
      // 选择装饰
      selectionAdornmentTemplate:
        $(go.Adornment, 'Auto',
          $(go.Shape, {
            fill: null,
            stroke: MYSQL_THEME_COLORS.primary,
            strokeWidth: 3,
            strokeDashArray: [5, 5]
          }),
          $(go.Placeholder)
        ),

      // 启用拖动功能 - 属性跟随移动将由全局拖动行为处理
      movable: true,

      // 拖动行为配置 - 确保实体可以被正常拖动
      dragComputation: (part: go.Part, pt: go.Point, gridpt: go.Point) => {
        // 使用默认的拖动计算，属性跟随移动由setupEntityDragBehavior处理
        return gridpt;
      }
    },

    // 实体形状 - 矩形
    $(go.Shape, 'RoundedRectangle', {
      name: 'SHAPE',
      fill: MYSQL_THEME_COLORS.white,
      stroke: MYSQL_THEME_COLORS.primary,
      strokeWidth: 2,
      width: 140,
      height: 80,
      parameter1: 8 // 圆角半径
    }),

    // 实体名称文本
    $(go.TextBlock, {
      name: 'TEXT',
      margin: 12,
      font: `bold ${DEFAULT_STYLE_CONFIG.fontSize + 2}px ${DEFAULT_STYLE_CONFIG.fontFamily}`,
      stroke: MYSQL_THEME_COLORS.text,
      textAlign: 'center',
      verticalAlignment: go.Spot.Center,
      editable: false,
      overflow: go.TextBlock.OverflowEllipsis,
      maxLines: 2
    }, new go.Binding('text', 'name'))
  );
}

/**
 * 创建属性节点模板
 */
export function createAttributeNodeTemplate(): go.Node {
  const $ = go.GraphObject.make;

  return $(go.Node, 'Auto',
    {
      // 选择装饰
      selectionAdornmentTemplate:
        $(go.Adornment, 'Auto',
          $(go.Shape, {
            fill: null,
            stroke: MYSQL_THEME_COLORS.accent,
            strokeWidth: 2,
            strokeDashArray: [3, 3]
          }),
          $(go.Placeholder)
        )
    },

    // 属性形状 - Chen ER图标准统一椭圆
    $(go.Shape, 'Ellipse', {
      name: 'SHAPE',
      fill: MYSQL_THEME_COLORS.white, // 统一白色填充
      stroke: MYSQL_THEME_COLORS.text, // 统一黑色边框
      strokeWidth: 2,
      width: 100,
      height: 50
    }),

    // 属性名称文本 - Chen ER图标准主键粗体
    $(go.TextBlock, {
      name: 'TEXT',
      margin: 8,
      font: `${DEFAULT_STYLE_CONFIG.fontSize}px ${DEFAULT_STYLE_CONFIG.fontFamily}`,
      stroke: MYSQL_THEME_COLORS.text, // 统一黑色文字
      textAlign: 'center',
      verticalAlignment: go.Spot.Center,
      editable: false,
      overflow: go.TextBlock.OverflowEllipsis,
      maxLines: 1
    },
    new go.Binding('text', 'name'),
    // 主键粗体字体绑定
    new go.Binding('font', 'isPrimaryKey', (isPK: boolean) =>
      isPK ? `bold ${DEFAULT_STYLE_CONFIG.fontSize}px ${DEFAULT_STYLE_CONFIG.fontFamily}`
           : `${DEFAULT_STYLE_CONFIG.fontSize}px ${DEFAULT_STYLE_CONFIG.fontFamily}`
    )),

    // Chen ER图标准：主键下划线 - 使用Shape绘制
    $(go.Shape, 'Rectangle', {
      name: 'UNDERLINE',
      height: 1,
      width: 60,
      fill: MYSQL_THEME_COLORS.text,
      stroke: null,
      alignment: go.Spot.Bottom,
      alignmentFocus: go.Spot.Bottom
    },
    // 只有主键才显示下划线
    new go.Binding('visible', 'isPrimaryKey'))
  );
}

/**
 * 创建关系节点模板
 */
export function createRelationshipNodeTemplate(): go.Node {
  const $ = go.GraphObject.make;

  return $(go.Node, 'Auto',
    {
      // 选择装饰
      selectionAdornmentTemplate:
        $(go.Adornment, 'Auto',
          $(go.Shape, {
            fill: null,
            stroke: MYSQL_THEME_COLORS.success,
            strokeWidth: 2,
            strokeDashArray: [4, 4]
          }),
          $(go.Placeholder)
        )
    },

    // 关系形状 - 菱形
    $(go.Shape, 'Diamond', {
      name: 'SHAPE',
      fill: MYSQL_THEME_COLORS.white,
      stroke: MYSQL_THEME_COLORS.success,
      strokeWidth: 2,
      width: 80,
      height: 60
    }),

    // 关系名称文本
    $(go.TextBlock, {
      name: 'TEXT',
      margin: 6,
      font: `${DEFAULT_STYLE_CONFIG.fontSize}px ${DEFAULT_STYLE_CONFIG.fontFamily}`,
      stroke: MYSQL_THEME_COLORS.text,
      textAlign: 'center',
      verticalAlignment: go.Spot.Center,
      editable: false,
      overflow: go.TextBlock.OverflowEllipsis,
      maxLines: 1
    }, new go.Binding('text', 'name'))
  );
}

/**
 * 创建连接线模板
 */
export function createLinkTemplate(): go.Link {
  const $ = go.GraphObject.make;

  return $(go.Link,
    {
      routing: go.Link.Normal, // 使用直线连接
      curve: go.Link.None, // 无曲线
      relinkableFrom: false,
      relinkableTo: false,
      reshapable: false,

      // 选择装饰
      selectionAdornmentTemplate:
        $(go.Adornment,
          $(go.Shape, {
            isPanelMain: true,
            stroke: MYSQL_THEME_COLORS.primary,
            strokeWidth: 3
          })
        )
    },

    // 连接线形状 - Chen ER图标准无箭头直线
    $(go.Shape, {
      name: 'SHAPE',
      strokeWidth: 2,
      stroke: MYSQL_THEME_COLORS.text, // 使用黑色连接线
      toArrow: "",      // 明确禁用终点箭头
      fromArrow: ""     // 明确禁用起点箭头
    }),

    // 连接线标签
    $(go.TextBlock, {
      name: 'LABEL',
      segmentIndex: 0,
      segmentFraction: 0.5,
      font: `${DEFAULT_STYLE_CONFIG.fontSize - 2}px ${DEFAULT_STYLE_CONFIG.fontFamily}`,
      stroke: MYSQL_THEME_COLORS.text,
      background: MYSQL_THEME_COLORS.white,
      margin: 4,
      editable: false
    }, new go.Binding('text', 'text'))
  );
}

/**
 * 创建外键连接线模板
 */
export function createForeignKeyLinkTemplate(): go.Link {
  const $ = go.GraphObject.make;

  return $(go.Link,
    {
      routing: go.Link.Normal, // 使用直线连接
      curve: go.Link.None, // 无曲线
      relinkableFrom: false,
      relinkableTo: false,
      reshapable: false
    },

    // 外键连接线 - Chen ER图标准无箭头虚线
    $(go.Shape, {
      name: 'SHAPE',
      strokeWidth: 2,
      stroke: MYSQL_THEME_COLORS.text, // 使用黑色连接线
      strokeDashArray: [8, 4], // 保持虚线样式以区分外键关系
      toArrow: "",      // 明确禁用终点箭头
      fromArrow: ""     // 明确禁用起点箭头
    }),

    // 外键标签 - Chen ER图标准黑色主题
    $(go.TextBlock, {
      name: 'LABEL',
      segmentIndex: 0,
      segmentFraction: 0.5,
      font: `bold ${DEFAULT_STYLE_CONFIG.fontSize - 1}px ${DEFAULT_STYLE_CONFIG.fontFamily}`,
      stroke: MYSQL_THEME_COLORS.text, // 黑色文字
      background: MYSQL_THEME_COLORS.white, // 白色背景
      margin: 6,
      editable: false
    }, new go.Binding('text', 'text'))
  );
}

/**
 * 配置图形模板
 */
export function configureDiagramTemplates(diagram: go.Diagram): void {
  // 设置节点模板
  diagram.nodeTemplateMap.add(NODE_TYPES.ENTITY, createEntityNodeTemplate());
  diagram.nodeTemplateMap.add(NODE_TYPES.ATTRIBUTE, createAttributeNodeTemplate());
  diagram.nodeTemplateMap.add(NODE_TYPES.RELATIONSHIP, createRelationshipNodeTemplate());

  // 设置连接线模板
  diagram.linkTemplate = createLinkTemplate();
  diagram.linkTemplateMap.add(LINK_TYPES.FOREIGN_KEY, createForeignKeyLinkTemplate());

  // 设置实体拖动行为 - 实现属性跟随移动
  try {
    // 验证图形实例存在且已正确初始化
    if (diagram && diagram.div) {
      setupEntityDragBehavior(diagram);
      console.log('实体拖动行为设置成功 - 属性将跟随实体移动');
    } else {
      console.warn('图形实例未正确初始化，跳过拖动行为设置');
    }
  } catch (error) {
    console.error('设置实体拖动行为失败:', error);
    // 不抛出错误，避免影响整个图形初始化
  }
}

/**
 * 获取实体连接的所有属性节点
 * @param diagram GoJS图形实例
 * @param entityNode 实体节点
 * @returns 连接的属性节点数组
 */
export function getConnectedAttributes(diagram: go.Diagram, entityNode: go.Node): go.Node[] {
  const attributes: go.Node[] = [];

  // 查找所有连接到该实体的链接
  entityNode.findLinksConnected().each((link: go.Link) => {
    // 获取连接的另一端节点
    const otherNode = link.fromNode === entityNode ? link.toNode : link.fromNode;

    // 检查是否为属性节点
    if (otherNode && otherNode.data && otherNode.data.category === NODE_TYPES.ATTRIBUTE) {
      attributes.push(otherNode);
    }
  });

  return attributes;
}

/**
 * 计算属性相对于实体的偏移量
 * @param entityNode 实体节点
 * @param attributeNode 属性节点
 * @returns 相对偏移量 {x, y}
 */
export function calculateAttributeOffset(entityNode: go.Node, attributeNode: go.Node): { x: number; y: number } {
  // 使用actualBounds获取准确的节点位置和大小
  const entityBounds = entityNode.actualBounds;
  const attributeBounds = attributeNode.actualBounds;

  // 计算中心点偏移
  const entityCenterX = entityBounds.centerX;
  const entityCenterY = entityBounds.centerY;
  const attributeCenterX = attributeBounds.centerX;
  const attributeCenterY = attributeBounds.centerY;

  return {
    x: attributeCenterX - entityCenterX,
    y: attributeCenterY - entityCenterY
  };
}

/**
 * 移动连接的属性节点
 * @param diagram GoJS图形实例
 * @param entityNode 实体节点
 * @param newLocation 实体的新位置
 * @param attributeOffsets 属性的相对偏移量映射
 */
export function moveConnectedAttributes(
  diagram: go.Diagram,
  entityNode: go.Node,
  newLocation: go.Point,
  attributeOffsets: Map<string, { x: number; y: number }>
): void {
  // 获取连接的属性节点
  const attributes = getConnectedAttributes(diagram, entityNode);

  // 计算实体的新中心点
  const entityBounds = entityNode.actualBounds;
  const newCenterX = newLocation.x + entityBounds.width / 2;
  const newCenterY = newLocation.y + entityBounds.height / 2;

  // 移动每个属性节点
  attributes.forEach((attributeNode) => {
    const attributeKey = attributeNode.data.key;
    const offset = attributeOffsets.get(attributeKey);

    if (offset) {
      // 计算属性的新位置
      const newAttributeCenterX = newCenterX + offset.x;
      const newAttributeCenterY = newCenterY + offset.y;

      // 计算属性节点的新左上角位置
      const attributeBounds = attributeNode.actualBounds;
      let newAttributeX = newAttributeCenterX - attributeBounds.width / 2;
      let newAttributeY = newAttributeCenterY - attributeBounds.height / 2;

      // 边界处理 - 确保属性节点不会移动到画布边界外
      const diagramBounds = diagram.documentBounds;
      const margin = 20; // 边界边距

      // 限制X坐标
      newAttributeX = Math.max(margin, Math.min(newAttributeX, diagramBounds.width - attributeBounds.width - margin));

      // 限制Y坐标
      newAttributeY = Math.max(margin, Math.min(newAttributeY, diagramBounds.height - attributeBounds.height - margin));

      // 更新属性节点位置
      diagram.model.setDataProperty(attributeNode.data, 'loc', `${newAttributeX} ${newAttributeY}`);
    }
  });
}

/**
 * 设置实体拖动行为 - 实现属性跟随移动
 * @param diagram GoJS图形实例
 */
export function setupEntityDragBehavior(diagram: go.Diagram): void {
  // 存储实体及其属性的初始偏移量
  const entityAttributeOffsets = new Map<string, Map<string, { x: number; y: number }>>();

  // 节流控制 - 避免频繁更新影响性能
  let isUpdating = false;
  const pendingUpdates = new Set<string>();

  // 创建节流更新函数
  const throttledUpdate = () => {
    if (isUpdating || pendingUpdates.size === 0) return;

    isUpdating = true;

    // 使用requestAnimationFrame确保流畅的动画效果
    requestAnimationFrame(() => {
      try {
        // 处理所有待更新的实体
        pendingUpdates.forEach((entityKey) => {
          const entityData = diagram.model.findNodeDataForKey(entityKey);
          if (entityData && entityData.category === NODE_TYPES.ENTITY) {
            const entityNode = diagram.findNodeForData(entityData);

            if (entityNode) {
              const offsetMap = entityAttributeOffsets.get(entityKey);
              if (offsetMap && offsetMap.size > 0) {
                const currentLocation = entityNode.location;
                moveConnectedAttributes(diagram, entityNode, currentLocation, offsetMap);
              }
            }
          }
        });

        // 清空待更新列表
        pendingUpdates.clear();
      } catch (error) {
        console.warn('属性跟随更新失败:', error);
      } finally {
        isUpdating = false;

        // 如果在更新过程中有新的更新请求，继续处理
        if (pendingUpdates.size > 0) {
          setTimeout(throttledUpdate, 16); // 约60fps
        }
      }
    });
  };

  // 监听模型数据变化事件 - 当节点位置改变时触发
  diagram.addModelChangedListener((e: go.ChangedEvent) => {
    // 只处理位置变化事件
    if (e.change === go.ChangedEvent.Property && e.propertyName === 'loc') {
      const nodeData = e.object;

      // 检查是否为实体节点
      if (nodeData && nodeData.category === NODE_TYPES.ENTITY) {
        const entityNode = diagram.findNodeForData(nodeData);

        if (entityNode) {
          // 获取或计算属性偏移量
          let offsetMap = entityAttributeOffsets.get(nodeData.key);

          if (!offsetMap) {
            // 首次移动时计算并存储偏移量
            offsetMap = new Map<string, { x: number; y: number }>();
            const attributes = getConnectedAttributes(diagram, entityNode);

            attributes.forEach((attributeNode) => {
              const offset = calculateAttributeOffset(entityNode, attributeNode);
              offsetMap!.set(attributeNode.data.key, offset);
            });

            entityAttributeOffsets.set(nodeData.key, offsetMap);
          }

          // 将实体添加到待更新列表，使用节流更新
          if (offsetMap.size > 0) {
            pendingUpdates.add(nodeData.key);
            throttledUpdate();
          }
        }
      }
    }
  });

  console.log('实体拖动行为设置完成 - 属性将跟随实体移动 (已优化性能)');
}

/**
 * 测试实体拖动和属性跟随功能
 * @param diagram GoJS图形实例
 * @returns 测试结果
 */
export function testEntityDragBehavior(diagram: go.Diagram): {
  success: boolean;
  message: string;
  details: {
    entityCount: number;
    attributeCount: number;
    relationshipCount: number;
    hasEntityDragBehavior: boolean;
  };
} {
  try {
    // 统计节点数量
    let entityCount = 0;
    let attributeCount = 0;
    let relationshipCount = 0;

    diagram.nodes.each((node: go.Node) => {
      if (node.data) {
        switch (node.data.category) {
          case NODE_TYPES.ENTITY:
            entityCount++;
            break;
          case NODE_TYPES.ATTRIBUTE:
            attributeCount++;
            break;
          case NODE_TYPES.RELATIONSHIP:
            relationshipCount++;
            break;
        }
      }
    });

    // 检查是否有实体节点
    if (entityCount === 0) {
      return {
        success: false,
        message: '测试失败：图形中没有实体节点',
        details: {
          entityCount,
          attributeCount,
          relationshipCount,
          hasEntityDragBehavior: false
        }
      };
    }

    // 检查实体节点是否可拖动
    let hasEntityDragBehavior = false;
    diagram.nodes.each((node: go.Node) => {
      if (node.data && node.data.category === NODE_TYPES.ENTITY) {
        if (node.movable) {
          hasEntityDragBehavior = true;
        }
      }
    });

    // 测试属性跟随功能
    let attributeFollowTest = false;
    diagram.nodes.each((entityNode: go.Node) => {
      if (entityNode.data && entityNode.data.category === NODE_TYPES.ENTITY) {
        const attributes = getConnectedAttributes(diagram, entityNode);
        if (attributes.length > 0) {
          attributeFollowTest = true;
        }
      }
    });

    const success = hasEntityDragBehavior && (attributeCount === 0 || attributeFollowTest);
    const message = success
      ? `测试成功：实体拖动和属性跟随功能正常 (实体:${entityCount}, 属性:${attributeCount}, 关系:${relationshipCount})`
      : `测试警告：功能可能存在问题 (实体可拖动:${hasEntityDragBehavior}, 属性跟随:${attributeFollowTest})`;

    return {
      success,
      message,
      details: {
        entityCount,
        attributeCount,
        relationshipCount,
        hasEntityDragBehavior
      }
    };

  } catch (error) {
    return {
      success: false,
      message: `测试失败：${error instanceof Error ? error.message : '未知错误'}`,
      details: {
        entityCount: 0,
        attributeCount: 0,
        relationshipCount: 0,
        hasEntityDragBehavior: false
      }
    };
  }
}

/**
 * 应用主题样式
 */
export function applyThemeStyles(diagram: go.Diagram, styleConfig?: Partial<DiagramStyleConfig>): void {
  const config = { ...DEFAULT_STYLE_CONFIG, ...styleConfig };

  // 更新图形背景 - 只设置div背景色
  try {
    // 直接设置div的背景色，避免GoJS属性冲突
    if (diagram.div && diagram.div.style) {
      diagram.div.style.backgroundColor = MYSQL_THEME_COLORS.background;
    }
  } catch (error) {
    console.warn('设置背景色失败，使用默认样式:', error);
  }

  // 更新网格颜色
  try {
    if (diagram.grid) {
      diagram.grid.gridCellSize = new go.Size(20, 20);
    }
  } catch (error) {
    console.warn('设置网格失败:', error);
  }

  console.log('应用MySQL主题样式配置:', config);
}
