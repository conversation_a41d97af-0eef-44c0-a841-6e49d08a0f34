/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // MySQL主题色彩
        'mysql-primary': '#00758F',
        'mysql-primary-dark': '#003545',
        'mysql-primary-light': '#E6F3F7',
        'mysql-accent': '#0066CC',
        'mysql-text': '#2D3748',
        'mysql-text-light': '#718096',
        'mysql-border': '#E2E8F0',
        'mysql-success': '#38A169',
        'mysql-warning': '#D69E2E',
        'mysql-error': '#E53E3E',
        'mysql-info': '#3182CE',
      },
      fontFamily: {
        sans: ['Inter', 'Avenir', 'Helvetica', 'Arial', 'sans-serif'],
        mono: ['Consolas', 'Monaco', 'Courier New', 'monospace'],
      },
      animation: {
        'spin-slow': 'spin 3s linear infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
      },
      boxShadow: {
        'mysql': '0 4px 6px -1px rgba(0, 117, 143, 0.1), 0 2px 4px -1px rgba(0, 117, 143, 0.06)',
        'mysql-lg': '0 10px 15px -3px rgba(0, 117, 143, 0.1), 0 4px 6px -2px rgba(0, 117, 143, 0.05)',
      },
      backdropBlur: {
        xs: '2px',
      },
    },
  },
  plugins: [],
}