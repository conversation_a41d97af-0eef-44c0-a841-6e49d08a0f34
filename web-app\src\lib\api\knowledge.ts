// MySQLAi.de - 知识库 API 客户端
// 提供类型安全的 API 调用方法

import type { Database } from '@/lib/database.types';

type KnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];
type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];
type CodeExample = Database['public']['Tables']['code_examples']['Row'];

// API 响应类型
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  details?: string;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface SearchResponse extends PaginatedResponse<KnowledgeArticle> {
  query?: {
    text: string;
    category?: string;
    tags?: string;
    difficulty?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

// 基础 API 调用函数
async function apiCall<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {
  try {
    // 构建正确的API URL
    let apiUrl: string;

    if (typeof window !== 'undefined') {
      // 客户端：使用绝对URL
      apiUrl = `${window.location.origin}/api/knowledge${endpoint}`;
    } else {
      // 服务端：构建完整的URL
      const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000';
      apiUrl = `${baseUrl}/api/knowledge${endpoint}`;
    }

    const response = await fetch(apiUrl, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API调用失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误'
    };
  }
}

// 知识库分类 API
export const categoriesApi = {
  // 获取所有分类
  getAll: async (includeStats = false): Promise<ApiResponse<KnowledgeCategory[]>> => {
    const params = includeStats ? '?includeStats=true' : '';
    return apiCall<KnowledgeCategory[]>(`/categories${params}`);
  },

  // 获取单个分类
  getById: async (id: string, includeArticles = false): Promise<ApiResponse<KnowledgeCategory>> => {
    const params = includeArticles ? '?includeArticles=true' : '';
    return apiCall<KnowledgeCategory>(`/categories/${id}${params}`);
  },

  // 创建分类
  create: async (category: Omit<KnowledgeCategory, 'created_at'>): Promise<ApiResponse<KnowledgeCategory>> => {
    return apiCall<KnowledgeCategory>('/categories', {
      method: 'POST',
      body: JSON.stringify(category),
    });
  },

  // 更新分类
  update: async (id: string, updates: Partial<KnowledgeCategory>): Promise<ApiResponse<KnowledgeCategory>> => {
    return apiCall<KnowledgeCategory>(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // 删除分类
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/categories/${id}`, {
      method: 'DELETE',
    });
  },

  // 批量更新排序
  updateOrder: async (categories: { id: string; order_index: number }[]): Promise<ApiResponse<void>> => {
    return apiCall<void>('/categories', {
      method: 'PUT',
      body: JSON.stringify({ categories }),
    });
  },
};

// 知识库文章 API
export const articlesApi = {
  // 获取文章列表
  getAll: async (params?: {
    category?: string;
    search?: string;
    tags?: string;
    difficulty?: string;
    page?: number;
    limit?: number;
    includeCodeExamples?: boolean;
    includeRelated?: boolean;
  }): Promise<PaginatedResponse<KnowledgeArticle>> => {
    const searchParams = new URLSearchParams();
    
    if (params?.category) searchParams.set('category', params.category);
    if (params?.search) searchParams.set('search', params.search);
    if (params?.tags) searchParams.set('tags', params.tags);
    if (params?.difficulty) searchParams.set('difficulty', params.difficulty);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.includeCodeExamples) searchParams.set('includeCodeExamples', 'true');
    if (params?.includeRelated) searchParams.set('includeRelated', 'true');

    const query = searchParams.toString();
    return apiCall<KnowledgeArticle[]>(`/articles${query ? `?${query}` : ''}`);
  },

  // 获取单个文章
  getById: async (id: string, options?: {
    includeCodeExamples?: boolean;
    includeRelated?: boolean;
  }): Promise<ApiResponse<KnowledgeArticle>> => {
    const params = new URLSearchParams();
    if (options?.includeCodeExamples === false) params.set('includeCodeExamples', 'false');
    if (options?.includeRelated === false) params.set('includeRelated', 'false');
    
    const query = params.toString();
    return apiCall<KnowledgeArticle>(`/articles/${id}${query ? `?${query}` : ''}`);
  },

  // 创建文章
  create: async (article: Omit<KnowledgeArticle, 'created_at' | 'updated_at' | 'last_updated'>): Promise<ApiResponse<KnowledgeArticle>> => {
    return apiCall<KnowledgeArticle>('/articles', {
      method: 'POST',
      body: JSON.stringify(article),
    });
  },

  // 更新文章
  update: async (id: string, updates: Partial<KnowledgeArticle>): Promise<ApiResponse<KnowledgeArticle>> => {
    return apiCall<KnowledgeArticle>(`/articles/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // 删除文章
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/articles/${id}`, {
      method: 'DELETE',
    });
  },

  // 搜索文章
  search: async (query: string, params?: {
    category?: string;
    tags?: string;
    difficulty?: string;
    page?: number;
    limit?: number;
  }): Promise<SearchResponse> => {
    const searchParams = new URLSearchParams();
    searchParams.set('search', query);

    if (params?.category) searchParams.set('category', params.category);
    if (params?.tags) searchParams.set('tags', params.tags);
    if (params?.difficulty) searchParams.set('difficulty', params.difficulty);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    return apiCall<KnowledgeArticle[]>(`/articles?${searchParams.toString()}`);
  },
};

// 代码示例 API
export const codeExamplesApi = {
  // 获取代码示例列表
  getAll: async (params?: {
    articleId?: string;
    language?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<CodeExample>> => {
    const searchParams = new URLSearchParams();

    if (params?.articleId) searchParams.set('articleId', params.articleId);
    if (params?.language) searchParams.set('language', params.language);
    if (params?.search) searchParams.set('search', params.search);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    const query = searchParams.toString();
    return apiCall<CodeExample[]>(`/code-examples${query ? `?${query}` : ''}`);
  },

  // 创建代码示例
  create: async (example: Omit<CodeExample, 'created_at'>): Promise<ApiResponse<CodeExample>> => {
    return apiCall<CodeExample>('/code-examples', {
      method: 'POST',
      body: JSON.stringify(example),
    });
  },

  // 更新代码示例
  update: async (id: string, updates: Partial<CodeExample>): Promise<ApiResponse<CodeExample>> => {
    return apiCall<CodeExample>(`/code-examples/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // 删除代码示例
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/code-examples/${id}`, {
      method: 'DELETE',
    });
  },

  // 批量更新排序
  updateOrder: async (examples: { id: string; order_index: number }[]): Promise<ApiResponse<void>> => {
    return apiCall<void>('/code-examples', {
      method: 'PUT',
      body: JSON.stringify({ examples }),
    });
  },
};

// 搜索 API
export const searchApi = {
  // 搜索文章
  search: async (params: {
    query: string;
    category?: string;
    tags?: string;
    difficulty?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<SearchResponse> => {
    const searchParams = new URLSearchParams();
    
    searchParams.set('q', params.query);
    if (params.category) searchParams.set('category', params.category);
    if (params.tags) searchParams.set('tags', params.tags);
    if (params.difficulty) searchParams.set('difficulty', params.difficulty);
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    return apiCall<KnowledgeArticle[]>(`/search?${searchParams.toString()}`);
  },

  // 获取搜索建议
  getSuggestions: async (query: string, limit = 5): Promise<ApiResponse<Array<{
    type: 'article' | 'query';
    text: string;
    id?: string;
    category?: string;
  }>>> => {
    return apiCall('/search', {
      method: 'POST',
      body: JSON.stringify({ query, limit }),
    });
  },
};

// 统计 API
export const statsApi = {
  // 获取统计数据
  get: async (params?: {
    period?: string;
    includeSearchStats?: boolean;
  }): Promise<ApiResponse<{
    overview: {
      totalCategories: number;
      totalArticles: number;
      totalCodeExamples: number;
      totalRelations: number;
    };
    categoryStats: Array<{
      id: string;
      name: string;
      articleCount: number;
    }>;
    difficultyStats: Record<string, number>;
    languageStats: Record<string, number>;
    recentArticles: Array<{
      id: string;
      title: string;
      last_updated: string;
      knowledge_categories: { name: string };
    }>;
    searchStats?: {
      totalSearches: number;
      popularQueries: Array<{ query: string; count: number }>;
      searchTrends: Record<string, number>;
    };
  }>> => {
    const searchParams = new URLSearchParams();
    
    if (params?.period) searchParams.set('period', params.period);
    if (params?.includeSearchStats) searchParams.set('includeSearchStats', 'true');

    const query = searchParams.toString();
    return apiCall(`/stats${query ? `?${query}` : ''}`);
  },

  // 导出统计数据
  export: async (format = 'json', includeDetails = false): Promise<ApiResponse<any>> => {
    return apiCall('/stats/export', {
      method: 'POST',
      body: JSON.stringify({ format, includeDetails }),
    });
  },
};
