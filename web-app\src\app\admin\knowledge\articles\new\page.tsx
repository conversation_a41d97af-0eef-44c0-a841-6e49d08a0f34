'use client';

// MySQLAi.de - 知识库文章创建页面
// 专门用于创建新文章的页面

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { ArrowLeft, AlertCircle, FileText } from 'lucide-react';
import { articlesApi } from '@/lib/api/knowledge';
import Button from '@/components/ui/Button';
import ArticleForm from '@/components/admin/ArticleForm';

interface ArticleFormData {
  id: string;
  title: string;
  description: string;
  content: string;
  category_id: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  order_index: number;
}

export default function KnowledgeArticleNewPage() {
  const router = useRouter();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  // 处理保存文章
  const handleSaveArticle = async (data: ArticleFormData) => {
    try {
      setSaving(true);
      setError('');

      // 创建新文章
      const response = await articlesApi.create({
        id: data.id,
        title: data.title,
        description: data.description || null,
        content: data.content,
        category_id: data.category_id || null,
        tags: data.tags.length > 0 ? data.tags : null,
        difficulty: data.difficulty,
        order_index: data.order_index,

      });

      if (response.success) {
        // 创建成功，返回列表页面
        router.push('/admin/knowledge/articles');
      } else {
        setError(response.error || '创建失败，请稍后重试');
      }
    } catch (error) {
      console.error('创建文章失败:', error);
      setError('创建失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    router.push('/admin/knowledge/articles');
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => router.push('/admin/knowledge/articles')}
            icon={<ArrowLeft className="w-5 h-5" />}
            className="text-mysql-text-light hover:text-mysql-primary"
          >
            返回列表
          </Button>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-mysql-primary-light rounded-lg">
              <FileText className="w-5 h-5 text-mysql-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-mysql-text">
                创建新文章
              </h1>
              <p className="text-mysql-text-light">
                添加新的知识库文章，丰富您的知识体系
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </motion.div>
      )}

      {/* 创建提示 */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-blue-50 border border-blue-200 rounded-lg p-4"
      >
        <div className="flex items-start space-x-3">
          <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-blue-800 mb-1">
              创建文章提示
            </h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 文章ID必须唯一，建议使用英文和数字组合</li>
              <li>• 选择合适的分类和难度等级，便于用户查找</li>
              <li>• 添加相关标签，提高文章的可发现性</li>
              <li>• 使用Markdown格式编写内容，支持代码高亮</li>
            </ul>
          </div>
        </div>
      </motion.div>

      {/* 文章表单 */}
      <ArticleForm
        article={null}
        onSave={handleSaveArticle}
        onCancel={handleCancel}
        loading={saving}
      />
    </div>
  );
}
