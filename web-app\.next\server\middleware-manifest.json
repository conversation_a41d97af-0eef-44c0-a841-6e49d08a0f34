{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__ac3b1bf0._.js", "server/edge/chunks/edge-wrapper_87b9efd6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zPoCPJCHs0MJi9I9GbECbpyZg0imm9R51PG8cMBs+dU=", "__NEXT_PREVIEW_MODE_ID": "cebb480d10234332367960d1fd0c1e59", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cf7aec44ed630dba2b2ba756d2d81fc4c9a67d464742f4b5a3242febc986c710", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "85452f29997eef74fd8f8d9e264fd842b33070576fb2bc699e3c7c3789651a40"}}}, "sortedMiddleware": ["/"], "functions": {}}