/**
 * 代码混淆和字符串保护模块
 * 提供字符串加密、常量保护和敏感数据混淆功能
 */

use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};
use rand::{Rng, thread_rng};
use std::collections::HashMap;
use std::sync::Mutex;

/// 混淆密钥（在实际应用中应该更复杂）
const OBFUSCATION_KEY: &[u8; 32] = b"MySQLAi_Desktop_Obfuscation_Key!";

/// 全局字符串缓存
static STRING_CACHE: Mutex<Option<HashMap<String, String>>> = Mutex::new(None);

/// 混淆字符串
pub fn obfuscate_string(input: &str) -> String {
    let key = Key::from_slice(OBFUSCATION_KEY);
    let cipher = Aes256Gcm::new(key);
    
    // 生成随机nonce
    let mut nonce_bytes = [0u8; 12];
    thread_rng().fill(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);
    
    // 加密字符串
    match cipher.encrypt(nonce, input.as_bytes()) {
        Ok(ciphertext) => {
            // 将nonce和密文组合
            let mut result = nonce_bytes.to_vec();
            result.extend_from_slice(&ciphertext);
            base64::encode(result)
        }
        Err(_) => {
            // 如果加密失败，返回简单的XOR混淆
            xor_obfuscate(input)
        }
    }
}

/// 解混淆字符串
pub fn deobfuscate_string(obfuscated: &str) -> Result<String, Box<dyn std::error::Error>> {
    // 首先尝试AES解密
    if let Ok(data) = base64::decode(obfuscated) {
        if data.len() > 12 {
            let key = Key::from_slice(OBFUSCATION_KEY);
            let cipher = Aes256Gcm::new(key);
            
            let nonce = Nonce::from_slice(&data[0..12]);
            let ciphertext = &data[12..];
            
            if let Ok(plaintext) = cipher.decrypt(nonce, ciphertext) {
                return Ok(String::from_utf8(plaintext)?);
            }
        }
    }
    
    // 如果AES解密失败，尝试XOR解混淆
    Ok(xor_deobfuscate(obfuscated))
}

/// 简单的XOR混淆（作为备用方案）
fn xor_obfuscate(input: &str) -> String {
    let key = b"MySQLAi";
    let mut result = Vec::new();
    
    for (i, byte) in input.bytes().enumerate() {
        result.push(byte ^ key[i % key.len()]);
    }
    
    base64::encode(result)
}

/// 简单的XOR解混淆
fn xor_deobfuscate(obfuscated: &str) -> String {
    if let Ok(data) = base64::decode(obfuscated) {
        let key = b"MySQLAi";
        let mut result = Vec::new();
        
        for (i, byte) in data.iter().enumerate() {
            result.push(byte ^ key[i % key.len()]);
        }
        
        String::from_utf8_lossy(&result).to_string()
    } else {
        obfuscated.to_string()
    }
}

/// 保护敏感数据结构
pub struct ProtectedData {
    encrypted_data: Vec<u8>,
    checksum: u32,
}

impl ProtectedData {
    /// 创建受保护的数据
    pub fn new(data: &[u8]) -> Result<Self, Box<dyn std::error::Error>> {
        let key = Key::from_slice(OBFUSCATION_KEY);
        let cipher = Aes256Gcm::new(key);
        
        // 生成随机nonce
        let mut nonce_bytes = [0u8; 12];
        thread_rng().fill(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);
        
        // 加密数据
        let ciphertext = cipher.encrypt(nonce, data)?;
        
        // 组合nonce和密文
        let mut encrypted_data = nonce_bytes.to_vec();
        encrypted_data.extend_from_slice(&ciphertext);
        
        // 计算校验和
        let checksum = calculate_checksum(&encrypted_data);
        
        Ok(ProtectedData {
            encrypted_data,
            checksum,
        })
    }
    
    /// 解密受保护的数据
    pub fn decrypt(&self) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // 验证校验和
        if calculate_checksum(&self.encrypted_data) != self.checksum {
            return Err("数据完整性验证失败".into());
        }
        
        if self.encrypted_data.len() < 12 {
            return Err("数据格式错误".into());
        }
        
        let key = Key::from_slice(OBFUSCATION_KEY);
        let cipher = Aes256Gcm::new(key);
        
        let nonce = Nonce::from_slice(&self.encrypted_data[0..12]);
        let ciphertext = &self.encrypted_data[12..];
        
        let plaintext = cipher.decrypt(nonce, ciphertext)?;
        Ok(plaintext)
    }
}

/// 计算简单校验和
fn calculate_checksum(data: &[u8]) -> u32 {
    data.iter().fold(0u32, |acc, &byte| {
        acc.wrapping_add(byte as u32).wrapping_mul(31)
    })
}

/// 敏感字符串管理器
pub struct SensitiveStringManager {
    strings: HashMap<&'static str, String>,
}

impl SensitiveStringManager {
    pub fn new() -> Self {
        let mut manager = SensitiveStringManager {
            strings: HashMap::new(),
        };
        
        // 预定义一些敏感字符串（已混淆）
        manager.add_string("api_key", "YourObfuscatedApiKeyHere");
        manager.add_string("license_server", "aHR0cHM6Ly9hcGkubXlzcWxhaS5kZQ=="); // base64编码的URL
        manager.add_string("encryption_salt", "TXlTUUxBaV9TYWx0XzIwMjU=");
        
        manager
    }
    
    /// 添加混淆字符串
    pub fn add_string(&mut self, key: &'static str, obfuscated_value: &str) {
        self.strings.insert(key, obfuscated_value.to_string());
    }
    
    /// 获取解混淆的字符串
    pub fn get_string(&self, key: &str) -> Option<String> {
        self.strings.get(key).and_then(|obfuscated| {
            deobfuscate_string(obfuscated).ok()
        })
    }
    
    /// 清理敏感数据
    pub fn clear(&mut self) {
        // 用随机数据覆盖内存
        for (_, value) in self.strings.iter_mut() {
            let mut rng = thread_rng();
            let random_bytes: Vec<u8> = (0..value.len()).map(|_| rng.gen()).collect();
            *value = base64::encode(random_bytes);
        }
        self.strings.clear();
    }
}

/// 代码流程混淆
pub struct FlowObfuscator {
    dummy_operations: Vec<fn() -> u32>,
}

impl FlowObfuscator {
    pub fn new() -> Self {
        FlowObfuscator {
            dummy_operations: vec![
                || { thread_rng().gen::<u32>() },
                || { std::thread::sleep(std::time::Duration::from_nanos(1)); 42 },
                || { "dummy".len() as u32 },
                || { std::process::id() % 1000 },
            ],
        }
    }
    
    /// 执行虚假操作以混淆控制流
    pub fn execute_dummy_operation(&self) -> u32 {
        let index = thread_rng().gen_range(0..self.dummy_operations.len());
        self.dummy_operations[index]()
    }
    
    /// 条件混淆执行
    pub fn obfuscated_if<T, F1, F2>(&self, condition: bool, true_branch: F1, false_branch: F2) -> T
    where
        F1: FnOnce() -> T,
        F2: FnOnce() -> T,
    {
        // 添加一些虚假操作
        let _dummy = self.execute_dummy_operation();
        
        // 使用位运算来混淆条件判断
        let mask = if condition { 0xFFFFFFFF } else { 0x00000000 };
        let anti_mask = !mask;
        
        // 这里实际上还是会执行正确的分支，但增加了分析难度
        if (mask & 1) != 0 {
            true_branch()
        } else {
            false_branch()
        }
    }
}

/// 内存保护工具
pub struct MemoryProtector {
    protected_regions: Vec<(*mut u8, usize)>,
}

impl MemoryProtector {
    pub fn new() -> Self {
        MemoryProtector {
            protected_regions: Vec::new(),
        }
    }
    
    /// 保护内存区域（简化实现）
    pub fn protect_memory(&mut self, data: &mut [u8]) {
        // 在实际实现中，这里会使用系统API来设置内存保护
        // 例如Windows的VirtualProtect或Linux的mprotect
        
        // 记录受保护的区域
        self.protected_regions.push((data.as_mut_ptr(), data.len()));
        
        // 简单的内存混淆
        for byte in data.iter_mut() {
            *byte ^= 0xAA;
        }
    }
    
    /// 取消内存保护
    pub fn unprotect_memory(&mut self, data: &mut [u8]) {
        // 恢复内存内容
        for byte in data.iter_mut() {
            *byte ^= 0xAA;
        }
        
        // 从保护列表中移除
        self.protected_regions.retain(|(ptr, len)| {
            !(*ptr == data.as_mut_ptr() && *len == data.len())
        });
    }
}

/// 全局敏感数据保护
pub fn protect_sensitive_data() {
    // 初始化字符串缓存
    let mut cache = STRING_CACHE.lock().unwrap();
    if cache.is_none() {
        let mut new_cache = HashMap::new();
        
        // 预加载一些混淆的敏感字符串
        new_cache.insert("debug_mode".to_string(), obfuscate_string("false"));
        new_cache.insert("admin_mode".to_string(), obfuscate_string("disabled"));
        new_cache.insert("license_check".to_string(), obfuscate_string("enabled"));
        
        *cache = Some(new_cache);
    }
}

/// 获取受保护的字符串
pub fn get_protected_string(key: &str) -> Option<String> {
    let cache = STRING_CACHE.lock().unwrap();
    if let Some(ref cache_map) = *cache {
        cache_map.get(key).and_then(|obfuscated| {
            deobfuscate_string(obfuscated).ok()
        })
    } else {
        None
    }
}

/// 清理敏感数据
pub fn cleanup_sensitive_data() {
    let mut cache = STRING_CACHE.lock().unwrap();
    if let Some(ref mut cache_map) = *cache {
        // 用随机数据覆盖
        let mut rng = thread_rng();
        for (_, value) in cache_map.iter_mut() {
            let random_bytes: Vec<u8> = (0..value.len()).map(|_| rng.gen()).collect();
            *value = base64::encode(random_bytes);
        }
        cache_map.clear();
    }
    *cache = None;
}

/// 字符串常量混淆宏（编译时）
#[macro_export]
macro_rules! obfuscated_string {
    ($s:expr) => {{
        // 这是一个简化的编译时字符串混淆
        // 在实际应用中，可以使用更复杂的编译时混淆技术
        const OBFUSCATED: &str = $s;
        OBFUSCATED
    }};
}

/// 运行时字符串解混淆宏
#[macro_export]
macro_rules! deobfuscated_string {
    ($key:expr) => {{
        crate::security::obfuscation::get_protected_string($key)
            .unwrap_or_else(|| "".to_string())
    }};
}