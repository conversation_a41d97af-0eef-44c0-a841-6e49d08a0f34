/**
 * 测试激活码设置脚本
 * 为测试环境生成有效的激活码
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 生成测试激活码
function generateTestLicenseKey() {
  // 生成格式: MYSQL-XXXX-XXXX-XXXX-XXXX
  const segments = [];
  for (let i = 0; i < 4; i++) {
    const segment = crypto.randomBytes(2).toString('hex').toUpperCase();
    segments.push(segment);
  }
  return `MYSQL-${segments.join('-')}`;
}

// 生成多个测试激活码
const testLicenses = [
  {
    key: 'MYSQL-TEST-2024-DEMO-0001',
    duration: 24,
    description: '24小时测试许可证'
  },
  {
    key: 'MYSQL-PROF-2024-FULL-0001', 
    duration: 8760, // 1年
    description: '专业版年度许可证'
  },
  {
    key: 'MYSQL-EVAL-2024-TRIAL-001',
    duration: 168, // 1周
    description: '评估版周试用许可证'
  },
  {
    key: generateTestLicenseKey(),
    duration: 72,
    description: '随机生成的3天许可证'
  }
];

// 创建SQLite初始化脚本
const sqliteScript = `
-- 测试激活码数据库初始化脚本
-- 用于MySQL桌面安装程序的激活测试

-- 创建license_keys表
CREATE TABLE IF NOT EXISTS license_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL UNIQUE,
    is_valid INTEGER DEFAULT 1,
    is_used INTEGER DEFAULT 0,
    duration_hours INTEGER DEFAULT 24,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建activations表
CREATE TABLE IF NOT EXISTS activations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL,
    machine_id TEXT NOT NULL,
    machine_fingerprint TEXT,
    activated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    is_active INTEGER DEFAULT 1,
    FOREIGN KEY (license_key) REFERENCES license_keys (license_key)
);

-- 插入测试激活码
${testLicenses.map(license => 
  `INSERT OR REPLACE INTO license_keys (license_key, is_valid, is_used, duration_hours) 
   VALUES ('${license.key}', 1, 0, ${license.duration}); -- ${license.description}`
).join('\n')}

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_license_key ON activations (license_key);
CREATE INDEX IF NOT EXISTS idx_machine_id ON activations (machine_id);
CREATE INDEX IF NOT EXISTS idx_expires_at ON activations (expires_at);
`;

// 保存SQL脚本
const sqlPath = path.join(__dirname, 'test-activation-data.sql');
fs.writeFileSync(sqlPath, sqliteScript);

// 创建激活码信息文件
const licenseInfo = {
  testLicenses,
  instructions: {
    setup: "运行 'sqlite3 activation.db < test-activation-data.sql' 来初始化测试数据",
    usage: "使用以下任一激活码进行测试",
    recommended: "MYSQL-TEST-2024-DEMO-0001 (24小时测试许可证)"
  },
  notes: [
    "所有激活码都是有效的 (is_valid = 1)",
    "所有激活码都未被使用 (is_used = 0)", 
    "激活后会自动标记为已使用",
    "可以重复激活同一台机器"
  ]
};

const infoPath = path.join(__dirname, 'test-activation-info.json');
fs.writeFileSync(infoPath, JSON.stringify(licenseInfo, null, 2));

console.log('🎉 测试激活码设置完成！');
console.log('\n📋 可用的测试激活码:');
testLicenses.forEach((license, index) => {
  console.log(`${index + 1}. ${license.key}`);
  console.log(`   ⏱️  有效期: ${license.duration}小时 (${license.description})`);
  console.log('');
});

console.log('📁 生成的文件:');
console.log(`   - ${sqlPath}`);
console.log(`   - ${infoPath}`);

console.log('\n🚀 推荐使用的测试激活码:');
console.log('   MYSQL-TEST-2024-DEMO-0001');
console.log('   (24小时测试许可证，适合功能验证)');

console.log('\n💡 使用说明:');
console.log('1. 启动桌面应用程序');
console.log('2. 在激活界面输入上述任一激活码');
console.log('3. 点击"验证激活"按钮');
console.log('4. 激活成功后即可使用安装功能');
