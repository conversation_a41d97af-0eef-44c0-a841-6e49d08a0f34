use serde::{Deserialize, Serialize};
use sqlx::{SqlitePool, Row};
use std::collections::HashMap;
use std::sync::{<PERSON>, Mutex, Once};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use chrono::{DateTime, Utc, NaiveDateTime};
use sha2::{Digest, Sha256};
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};
use rand::{Rng, thread_rng};
use base64::{Engine as _, engine::general_purpose};
use crate::core::fingerprint::generate_machine_fingerprint;

/// 激活系统常量
const CACHE_VALIDITY_PERIOD: u64 = 1800; // 30分钟
const MAX_RETRY_COUNT: u32 = 3;
const CRITICAL_THREAT_THRESHOLD: u32 = 10;
const MAX_DELAY_TIME: u64 = 60;
const VERIFICATION_DELAY_MIN: u64 = 1;
const VERIFICATION_DELAY_MAX: u64 = 5;

/// 激活状态
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ActivationStatus {
    pub is_activated: bool,
    pub license_key: Option<String>,
    pub expires_at: Option<String>,
    pub machine_id: String,
    pub is_trial_mode: Option<bool>,
    pub remaining_trials: Option<u32>,
    pub remaining_hours: f64,
}

/// 许可证信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseInfo {
    pub license_key: String,
    pub is_valid: bool,
    pub is_used: bool,
    pub duration_hours: i32,
    pub created_at: String,
}

/// 激活记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivationRecord {
    pub license_key: String,
    pub machine_id: String,
    pub machine_fingerprint: Option<String>,
    pub activated_at: String,
    pub expires_at: String,
    pub is_active: bool,
}

/// 网络时间响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkTimeResponse {
    pub datetime: String,
    pub timezone: String,
    pub timestamp: i64,
}

/// 激活缓存
#[derive(Debug)]
struct ActivationCacheData {
    active_keys: Vec<(String, String)>, // (license_key, key_hash)
    is_valid: bool,
    remaining_hours: f64,
    expiry_time: Option<NaiveDateTime>,
    last_check_time: Option<u64>,
    verification_count: u32,
    last_verification_time: u64,
    machine_id: String,
    machine_fingerprint: String,
}

/// 激活缓存管理器（单例模式）
pub struct ActivationCache {
    data: Arc<Mutex<ActivationCacheData>>,
    db_pool: Option<SqlitePool>,
}

static mut ACTIVATION_CACHE: Option<ActivationCache> = None;
static INIT: Once = Once::new();

impl ActivationCache {
    /// 获取全局激活缓存实例
    pub fn instance() -> &'static mut ActivationCache {
        unsafe {
            INIT.call_once(|| {
                ACTIVATION_CACHE = Some(ActivationCache::new());
            });
            ACTIVATION_CACHE.as_mut().unwrap()
        }
    }

    /// 创建新的激活缓存实例
    fn new() -> Self {
        let machine_id = generate_machine_fingerprint();
        let machine_fingerprint = machine_id.clone();

        let data = ActivationCacheData {
            active_keys: Vec::new(),
            is_valid: false,
            remaining_hours: 0.0,
            expiry_time: None,
            last_check_time: None,
            verification_count: 0,
            last_verification_time: 0,
            machine_id,
            machine_fingerprint,
        };

        Self {
            data: Arc::new(Mutex::new(data)),
            db_pool: None,
        }
    }

    /// 初始化数据库连接
    pub async fn initialize_database(&mut self, db_path: &str) -> Result<(), Box<dyn std::error::Error>> {
        // 创建SQLite连接池
        let database_url = format!("sqlite:{}", db_path);
        let pool = SqlitePool::connect(&database_url).await?;

        // 创建表结构
        self.create_tables(&pool).await?;

        self.db_pool = Some(pool);
        
        // 加载激活信息
        self.load_activation_info().await?;

        Ok(())
    }

    /// 创建数据库表结构
    async fn create_tables(&self, pool: &SqlitePool) -> Result<(), sqlx::Error> {
        // 创建license_keys表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS license_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT NOT NULL UNIQUE,
                is_valid INTEGER DEFAULT 1,
                is_used INTEGER DEFAULT 0,
                duration_hours INTEGER DEFAULT 24,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(pool)
        .await?;

        // 创建activations表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS activations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT NOT NULL,
                machine_id TEXT NOT NULL,
                machine_fingerprint TEXT,
                activated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_active INTEGER DEFAULT 1,
                FOREIGN KEY (license_key) REFERENCES license_keys (license_key)
            )
            "#,
        )
        .execute(pool)
        .await?;

        // 创建索引
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_license_key ON activations (license_key)")
            .execute(pool)
            .await?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_machine_id ON activations (machine_id)")
            .execute(pool)
            .await?;

        Ok(())
    }

    /// 应用安全延迟（防暴力破解）
    async fn apply_security_delay(&self) {
        let mut data = self.data.lock().unwrap();
        
        // 计算延迟时间
        let delay_ms = if data.verification_count > CRITICAL_THREAT_THRESHOLD {
            MAX_DELAY_TIME * 1000
        } else {
            let base_delay = VERIFICATION_DELAY_MIN + 
                (data.verification_count as u64 * VERIFICATION_DELAY_MAX) / CRITICAL_THREAT_THRESHOLD as u64;
            std::cmp::min(base_delay, MAX_DELAY_TIME) * 1000
        };

        // 添加随机延迟
        let mut rng = thread_rng();
        let random_delay = rng.gen_range(0..=delay_ms);
        
        drop(data); // 释放锁

        if random_delay > 0 {
            tokio::time::sleep(Duration::from_millis(random_delay)).await;
        }
    }

    /// 从数据库加载激活信息
    async fn load_activation_info(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.apply_security_delay().await;

        let pool = match &self.db_pool {
            Some(pool) => pool,
            None => return Err("Database not initialized".into()),
        };

        let mut data = self.data.lock().unwrap();
        
        // 重置缓存
        data.active_keys.clear();
        data.is_valid = false;
        data.remaining_hours = 0.0;
        data.expiry_time = None;

        // 查询有效的激活记录
        let query = r#"
            SELECT a.license_key, a.expires_at, a.machine_fingerprint
            FROM activations a
            JOIN license_keys lk ON a.license_key = lk.license_key
            WHERE a.machine_id = ? AND a.is_active = 1 AND lk.is_valid = 1
            ORDER BY a.expires_at DESC
        "#;

        let rows = sqlx::query(query)
            .bind(&data.machine_id)
            .fetch_all(pool)
            .await?;

        let current_time = self.get_network_time().await?;

        for row in rows {
            let license_key: String = row.get("license_key");
            let expires_at_str: String = row.get("expires_at");
            let machine_fingerprint: Option<String> = row.get("machine_fingerprint");

            // 解析过期时间
            if let Ok(expires_at) = NaiveDateTime::parse_from_str(&expires_at_str, "%Y-%m-%d %H:%M:%S") {
                // 验证机器指纹
                if let Some(stored_fingerprint) = machine_fingerprint {
                    if stored_fingerprint != data.machine_fingerprint {
                        log::warn!("机器指纹不匹配，跳过许可证: {}", license_key);
                        continue;
                    }
                }

                // 检查是否过期
                if expires_at > current_time {
                    let remaining = expires_at.signed_duration_since(current_time);
                    let remaining_hours = remaining.num_seconds() as f64 / 3600.0;

                    // 添加有效的许可证
                    let key_hash = format!("{:x}", Sha256::digest(license_key.as_bytes()));
                    data.active_keys.push((license_key.clone(), key_hash));
                    data.is_valid = true;

                    // 保存最长剩余时间的许可证信息
                    if remaining_hours > data.remaining_hours {
                        data.remaining_hours = remaining_hours;
                        data.expiry_time = Some(expires_at);
                    }

                    log::info!("许可证密钥 {} 有效，剩余时间: {:.2}小时", license_key, remaining_hours);
                }
            }
        }

        // 记录最后检查时间
        data.last_check_time = Some(
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        );

        // 重置验证计数
        data.verification_count = 0;

        if data.is_valid {
            log::info!("从数据库加载激活信息成功，许可证有效，剩余时间: {:.2}小时", data.remaining_hours);
        } else {
            log::info!("从数据库加载激活信息成功，未找到有效的许可证");
        }

        Ok(())
    }

    /// 获取网络时间
    async fn get_network_time(&self) -> Result<NaiveDateTime, Box<dyn std::error::Error>> {
        // 尝试从多个时间服务器获取时间
        let time_servers = vec![
            "http://worldtimeapi.org/api/timezone/Asia/Shanghai",
            "http://worldclockapi.com/api/json/utc/now",
        ];

        for server in time_servers {
            if let Ok(response) = reqwest::get(server).await {
                if let Ok(time_data) = response.json::<NetworkTimeResponse>().await {
                    if let Ok(datetime) = DateTime::parse_from_rfc3339(&time_data.datetime) {
                        return Ok(datetime.naive_utc());
                    }
                }
            }
        }

        // 如果网络时间获取失败，使用本地时间
        log::warn!("无法获取网络时间，使用本地时间");
        Ok(Utc::now().naive_utc())
    }

    /// 检查是否已激活
    pub async fn is_activated(&self) -> Result<bool, Box<dyn std::error::Error>> {
        self.apply_security_delay().await;

        let data = self.data.lock().unwrap();

        // 如果没有激活信息，直接返回false
        if !data.is_valid {
            return Ok(false);
        }

        // 检查缓存是否过期
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        if let Some(last_check) = data.last_check_time {
            if current_time - last_check > CACHE_VALIDITY_PERIOD {
                drop(data);
                log::info!("缓存过期，重新从数据库加载激活信息");
                self.load_activation_info().await?;
                let data = self.data.lock().unwrap();
                return Ok(data.is_valid);
            }
        }

        // 检查许可证是否过期
        if let Some(expiry_time) = data.expiry_time {
            let network_time = self.get_network_time().await?;
            if network_time < expiry_time {
                // 更新剩余时间
                let remaining = expiry_time.signed_duration_since(network_time);
                let remaining_hours = remaining.num_seconds() as f64 / 3600.0;
                drop(data);
                
                let mut data = self.data.lock().unwrap();
                data.remaining_hours = remaining_hours;
                return Ok(true);
            } else {
                drop(data);
                log::info!("许可证已过期，重新从数据库加载激活信息");
                self.load_activation_info().await?;
                let data = self.data.lock().unwrap();
                return Ok(data.is_valid);
            }
        }

        Ok(false)
    }

    /// 验证许可证密钥
    pub async fn validate_license_key(&self, license_key: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let pool = match &self.db_pool {
            Some(pool) => pool,
            None => return Err("Database not initialized".into()),
        };

        let query = "SELECT is_valid FROM license_keys WHERE license_key = ?";
        
        let row = sqlx::query(query)
            .bind(license_key)
            .fetch_optional(pool)
            .await?;

        if let Some(row) = row {
            let is_valid: i32 = row.get("is_valid");
            Ok(is_valid == 1)
        } else {
            Ok(false)
        }
    }

    /// 激活许可证
    pub async fn activate_license(&self, license_key: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let pool = match &self.db_pool {
            Some(pool) => pool,
            None => return Err("Database not initialized".into()),
        };

        // 验证许可证是否有效
        if !self.validate_license_key(license_key).await? {
            return Ok(false);
        }

        let data = self.data.lock().unwrap();
        let machine_id = data.machine_id.clone();
        let machine_fingerprint = data.machine_fingerprint.clone();
        drop(data);

        // 检查是否已经激活
        let check_query = r#"
            SELECT COUNT(*) as count FROM activations 
            WHERE license_key = ? AND machine_id = ? AND is_active = 1
        "#;

        let row = sqlx::query(check_query)
            .bind(license_key)
            .bind(&machine_id)
            .fetch_one(pool)
            .await?;

        let count: i32 = row.get("count");
        if count > 0 {
            log::info!("许可证已在当前机器上激活");
            return Ok(true);
        }

        // 获取许可证持续时间
        let duration_query = "SELECT duration_hours FROM license_keys WHERE license_key = ?";
        let row = sqlx::query(duration_query)
            .bind(license_key)
            .fetch_one(pool)
            .await?;

        let duration_hours: i32 = row.get("duration_hours");

        // 计算过期时间
        let current_time = self.get_network_time().await?;
        let expires_at = current_time + chrono::Duration::hours(duration_hours as i64);

        // 插入激活记录
        let insert_query = r#"
            INSERT INTO activations (license_key, machine_id, machine_fingerprint, expires_at)
            VALUES (?, ?, ?, ?)
        "#;

        sqlx::query(insert_query)
            .bind(license_key)
            .bind(&machine_id)
            .bind(&machine_fingerprint)
            .bind(expires_at.format("%Y-%m-%d %H:%M:%S").to_string())
            .execute(pool)
            .await?;

        // 标记许可证为已使用
        let update_query = "UPDATE license_keys SET is_used = 1 WHERE license_key = ?";
        sqlx::query(update_query)
            .bind(license_key)
            .execute(pool)
            .await?;

        log::info!("许可证 {} 激活成功，过期时间: {}", license_key, expires_at);

        // 重新加载激活信息
        self.load_activation_info().await?;

        Ok(true)
    }

    /// 获取激活状态
    pub async fn get_activation_status(&self) -> Result<ActivationStatus, Box<dyn std::error::Error>> {
        let is_activated = self.is_activated().await?;
        let data = self.data.lock().unwrap();

        let license_key = if is_activated && !data.active_keys.is_empty() {
            Some(data.active_keys[0].0.clone())
        } else {
            None
        };

        let expires_at = if let Some(expiry_time) = data.expiry_time {
            Some(expiry_time.format("%Y-%m-%d %H:%M:%S").to_string())
        } else {
            None
        };

        Ok(ActivationStatus {
            is_activated,
            license_key,
            expires_at,
            machine_id: data.machine_id.clone(),
            is_trial_mode: Some(false), // 暂时不支持试用模式
            remaining_trials: None,
            remaining_hours: data.remaining_hours,
        })
    }

    /// 停用许可证
    pub async fn deactivate_license(&self, license_key: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let pool = match &self.db_pool {
            Some(pool) => pool,
            None => return Err("Database not initialized".into()),
        };

        let data = self.data.lock().unwrap();
        let machine_id = data.machine_id.clone();
        drop(data);

        let query = r#"
            UPDATE activations 
            SET is_active = 0 
            WHERE license_key = ? AND machine_id = ?
        "#;

        let result = sqlx::query(query)
            .bind(license_key)
            .bind(&machine_id)
            .execute(pool)
            .await?;

        if result.rows_affected() > 0 {
            log::info!("许可证 {} 已停用", license_key);
            // 重新加载激活信息
            self.load_activation_info().await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }
}

/// 全局激活管理函数

/// 初始化激活系统
pub async fn initialize_activation_system(db_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let cache = ActivationCache::instance();
    cache.initialize_database(db_path).await
}

/// 检查是否已激活
pub async fn is_activated() -> Result<bool, Box<dyn std::error::Error>> {
    let cache = ActivationCache::instance();
    cache.is_activated().await
}

/// 激活许可证
pub async fn activate_license(license_key: &str) -> Result<bool, Box<dyn std::error::Error>> {
    let cache = ActivationCache::instance();
    cache.activate_license(license_key).await
}

/// 验证许可证密钥
pub async fn validate_license_key(license_key: &str) -> Result<bool, Box<dyn std::error::Error>> {
    let cache = ActivationCache::instance();
    cache.validate_license_key(license_key).await
}

/// 获取激活状态
pub async fn get_activation_status() -> Result<ActivationStatus, Box<dyn std::error::Error>> {
    let cache = ActivationCache::instance();
    cache.get_activation_status().await
}

/// 停用许可证
pub async fn deactivate_license(license_key: &str) -> Result<bool, Box<dyn std::error::Error>> {
    let cache = ActivationCache::instance();
    cache.deactivate_license(license_key).await
}