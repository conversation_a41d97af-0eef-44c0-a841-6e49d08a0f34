'use client';

/**
 * 安装进度跟踪组件
 * 从Web版本移植，适配桌面应用环境
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  Circle, 
  AlertCircle, 
  Loader2,
  Clock
} from 'lucide-react';
import { cn } from '../../lib/utils';
// import { InstallationProgress } from '../../lib/types';

// 安装状态枚举
export enum InstallationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'inProgress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

// 安装步骤接口
export interface InstallationStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  estimatedTime?: number; // 分钟
}

interface ProgressTrackerProps {
  currentStatus: InstallationStatus;
  steps: InstallationStep[];
  currentStepId?: string;
  className?: string;
}

/**
 * 进度跟踪组件
 */
export default function ProgressTracker({
  currentStatus,
  steps,
  currentStepId,
  className
}: ProgressTrackerProps) {
  // 获取步骤状态
  const getStepStatus = (step: InstallationStep, index: number) => {
    if (step.completed) {
      return 'completed';
    }
    
    if (step.id === currentStepId) {
      return currentStatus === InstallationStatus.FAILED ? 'error' : 'current';
    }
    
    // 如果当前步骤之前的步骤，且当前状态不是错误
    if (currentStatus !== InstallationStatus.FAILED) {
      const currentIndex = steps.findIndex(s => s.id === currentStepId);
      if (currentIndex > index) {
        return 'completed';
      }
    }
    
    return 'pending';
  };

  // 计算整体进度
  const progressPercentage = React.useMemo(() => {
    const completedSteps = steps.filter(step => step.completed).length;
    const currentStepIndex = steps.findIndex(step => step.id === currentStepId);
    
    if (currentStatus === InstallationStatus.COMPLETED) {
      return 100;
    }
    
    if (currentStepIndex >= 0 && currentStatus === InstallationStatus.IN_PROGRESS) {
      return ((completedSteps + 0.5) / steps.length) * 100;
    }
    
    return (completedSteps / steps.length) * 100;
  }, [steps, currentStepId, currentStatus]);

  // 渲染步骤图标
  const renderStepIcon = (_step: InstallationStep, status: string) => {
    const iconClasses = "w-6 h-6";
    
    switch (status) {
      case 'completed':
        return <CheckCircle className={cn(iconClasses, "text-green-600")} />;
      case 'current':
        return <Loader2 className={cn(iconClasses, "text-mysql-primary animate-spin")} />;
      case 'error':
        return <AlertCircle className={cn(iconClasses, "text-red-600")} />;
      default:
        return <Circle className={cn(iconClasses, "text-gray-400")} />;
    }
  };

  // 渲染步骤状态文本
  const renderStepStatusText = (status: string) => {
    const baseClasses = "text-xs font-medium";
    
    switch (status) {
      case 'completed':
        return <span className={cn(baseClasses, "text-green-600")}>已完成</span>;
      case 'current':
        return <span className={cn(baseClasses, "text-mysql-primary")}>进行中</span>;
      case 'error':
        return <span className={cn(baseClasses, "text-red-600")}>失败</span>;
      default:
        return <span className={cn(baseClasses, "text-gray-500")}>等待中</span>;
    }
  };

  return (
    <div className={cn('bg-white rounded-xl shadow-lg border border-gray-200 p-6', className)}>
      {/* 标题和整体进度 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-mysql-text">安装进度</h3>
          <span className="text-sm text-gray-600">
            {Math.round(progressPercentage)}% 完成
          </span>
        </div>
        
        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className={cn(
              "rounded-full h-2",
              currentStatus === InstallationStatus.FAILED ? "bg-red-500" :
              currentStatus === InstallationStatus.COMPLETED ? "bg-green-500" :
              "bg-mysql-primary"
            )}
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* 步骤列表 */}
      <div className="space-y-4">
        {steps.map((step, index) => {
          const status = getStepStatus(step, index);
          const isLast = index === steps.length - 1;
          
          return (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              {/* 连接线 */}
              {!isLast && (
                <div className={cn(
                  "absolute left-3 top-8 w-0.5 h-8",
                  status === 'completed' ? "bg-green-200" : "bg-gray-200"
                )} />
              )}
              
              <div className="flex items-start space-x-4">
                {/* 步骤图标 */}
                <div className="flex-shrink-0 relative z-10">
                  {renderStepIcon(step, status)}
                </div>
                
                {/* 步骤内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={cn(
                      "font-medium",
                      status === 'completed' ? "text-green-800" :
                      status === 'current' ? "text-mysql-primary" :
                      status === 'error' ? "text-red-800" :
                      "text-gray-700"
                    )}>
                      {step.title}
                    </h4>
                    {renderStepStatusText(status)}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    {step.description}
                  </p>
                  
                  {/* 预计时间 */}
                  {step.estimatedTime && status !== 'completed' && (
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      <span>预计 {step.estimatedTime} 分钟</span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* 底部状态信息 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        {currentStatus === InstallationStatus.COMPLETED && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-green-600"
          >
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">所有步骤已完成！</span>
          </motion.div>
        )}
        
        {currentStatus === InstallationStatus.FAILED && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-red-600"
          >
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">安装过程中出现错误</span>
          </motion.div>
        )}
        
        {currentStatus === InstallationStatus.IN_PROGRESS && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-mysql-primary"
          >
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="font-medium">正在安装中...</span>
          </motion.div>
        )}
        
        {currentStatus === InstallationStatus.CANCELLED && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 text-gray-600"
          >
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">安装已取消</span>
          </motion.div>
        )}
      </div>
    </div>
  );
}

// 简化版进度条组件
interface SimpleProgressBarProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
  className?: string;
}

export function SimpleProgressBar({
  value,
  max = 100,
  size = 'md',
  variant = 'primary',
  showLabel = false,
  label,
  animated = false,
  className
}: SimpleProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100);
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };
  
  const variantClasses = {
    primary: 'bg-mysql-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500'
  };
  
  return (
    <div className={cn('w-full', className)}>
      {showLabel && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">
            {label || '进度'}
          </span>
          <span className="text-sm text-gray-500">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
      
      <div className={cn(
        'w-full bg-gray-200 rounded-full overflow-hidden',
        sizeClasses[size]
      )}>
        <motion.div
          className={cn(
            'h-full rounded-full transition-all duration-300',
            variantClasses[variant],
            animated && 'animate-pulse'
          )}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
    </div>
  );
}