'use client';

// MySQLAi.de - FeatureItem特性项组件
// 可复用的特性展示组件

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FeatureItemProps {
  title: string;
  description: string;
  icon: LucideIcon;
  details?: string[];
  color?: string;
  gradient?: string;
  index?: number;
  className?: string;
  layout?: 'vertical' | 'horizontal';
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
  onClick?: () => void;
}

export default function FeatureItem({
  title,
  description,
  icon: IconComponent,
  details = [],
  color = 'mysql-primary',
  gradient = 'from-mysql-primary to-mysql-primary-dark',
  index = 0,
  className,
  layout = 'vertical',
  size = 'md',
  showDetails = true,
  onClick
}: FeatureItemProps) {
  // 尺寸配置
  const sizeConfig = {
    sm: {
      container: 'p-6',
      icon: 'w-12 h-12',
      iconSize: 'w-6 h-6',
      title: 'text-lg',
      description: 'text-sm',
    },
    md: {
      container: 'p-8',
      icon: 'w-16 h-16',
      iconSize: 'w-8 h-8',
      title: 'text-xl',
      description: 'text-base',
    },
    lg: {
      container: 'p-10',
      icon: 'w-20 h-20',
      iconSize: 'w-10 h-10',
      title: 'text-2xl',
      description: 'text-lg',
    },
  };

  const config = sizeConfig[size];

  if (layout === 'horizontal') {
    return (
      <motion.div
        initial={{ opacity: 0, x: -30 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ 
          duration: 0.6, 
          delay: index * 0.1,
          ease: "easeOut" 
        }}
        viewport={{ once: true }}
        className={cn(
          'group bg-white rounded-xl shadow-md border border-mysql-border',
          'hover:shadow-lg hover:scale-102 transition-all duration-300 ease-out',
          onClick && 'cursor-pointer',
          className
        )}
        onClick={onClick}
      >
        <div className={cn('flex items-start space-x-4', config.container)}>
          {/* 图标 */}
          <div className={cn(
            'flex items-center justify-center rounded-lg flex-shrink-0',
            'group-hover:scale-110 transition-transform duration-300',
            `bg-gradient-to-br ${gradient}`,
            config.icon
          )}>
            <IconComponent className={cn('text-white', config.iconSize)} />
          </div>

          {/* 内容 */}
          <div className="flex-1">
            <h4 className={cn(
              'font-bold text-mysql-text mb-2 group-hover:text-mysql-primary transition-colors duration-300',
              config.title
            )}>
              {title}
            </h4>
            <p className={cn(
              'text-mysql-text-light leading-relaxed',
              config.description
            )}>
              {description}
            </p>

            {/* 详细信息 */}
            {showDetails && details.length > 0 && (
              <div className="mt-3 space-y-1">
                {details.map((detail, detailIndex) => (
                  <div
                    key={detailIndex}
                    className="flex items-center text-xs text-mysql-text"
                  >
                    <div className={cn(
                      'w-1.5 h-1.5 rounded-full mr-2 flex-shrink-0',
                      `bg-gradient-to-r ${gradient}`
                    )} />
                    <span>{detail}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  // 垂直布局（默认）
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.15,
        ease: "easeOut" 
      }}
      viewport={{ once: true }}
      className={cn(
        'group bg-white rounded-2xl shadow-lg border border-mysql-border',
        'hover:shadow-2xl hover:scale-105 transition-all duration-300 ease-out overflow-hidden',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {/* 渐变背景装饰 */}
      <div className={cn(
        'absolute top-0 left-0 right-0 h-1 bg-gradient-to-r',
        gradient
      )} />

      <div className={cn('relative', config.container)}>
        {/* 图标区域 */}
        <div className="flex items-center justify-center mb-6">
          <div className={cn(
            'flex items-center justify-center rounded-2xl',
            'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
            gradient,
            config.icon
          )}>
            <IconComponent className={cn('text-white', config.iconSize)} />
          </div>
        </div>

        {/* 标题和描述 */}
        <div className="text-center mb-6">
          <h3 className={cn(
            'font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300',
            config.title
          )}>
            {title}
          </h3>
          <p className={cn(
            'text-mysql-text-light leading-relaxed',
            config.description
          )}>
            {description}
          </p>
        </div>

        {/* 详细特性列表 */}
        {showDetails && details.length > 0 && (
          <div className="space-y-2">
            {details.map((detail, detailIndex) => (
              <motion.div
                key={detailIndex}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ 
                  duration: 0.3, 
                  delay: (index * 0.15) + (detailIndex * 0.1) + 0.3
                }}
                viewport={{ once: true }}
                className="flex items-center text-sm text-mysql-text"
              >
                <div className={cn(
                  'w-2 h-2 rounded-full mr-3 flex-shrink-0',
                  `bg-gradient-to-r ${gradient}`
                )} />
                <span>{detail}</span>
              </motion.div>
            ))}
          </div>
        )}

        {/* 悬停时的光效 */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
      </div>
    </motion.div>
  );
}

// 简化版特性项组件
interface SimpleFeatureItemProps {
  title: string;
  description: string;
  icon: LucideIcon;
  className?: string;
  color?: string;
  onClick?: () => void;
}

export function SimpleFeatureItem({
  title,
  description,
  icon: IconComponent,
  className,
  color = 'mysql-primary',
  onClick
}: SimpleFeatureItemProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        'flex items-center space-x-4 p-4 bg-white rounded-lg shadow-sm border border-mysql-border',
        'hover:shadow-md transition-all duration-200 ease-out',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <div className={cn(
        'flex items-center justify-center w-10 h-10 rounded-lg flex-shrink-0',
        `bg-${color} text-white`
      )}>
        <IconComponent className="w-5 h-5" />
      </div>
      <div className="flex-1">
        <h4 className="font-semibold text-mysql-text mb-1">
          {title}
        </h4>
        <p className="text-sm text-mysql-text-light">
          {description}
        </p>
      </div>
    </motion.div>
  );
}

// 统计数据项组件
interface StatItemProps {
  value: string;
  label: string;
  index?: number;
  className?: string;
}

export function StatItem({
  value,
  label,
  index = 0,
  className
}: StatItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        ease: "easeOut" 
      }}
      viewport={{ once: true }}
      className={cn('text-center', className)}
    >
      <div className="text-3xl font-bold text-mysql-primary mb-2">
        {value}
      </div>
      <div className="text-mysql-text-light">
        {label}
      </div>
    </motion.div>
  );
}
