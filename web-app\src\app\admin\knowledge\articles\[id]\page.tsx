'use client';

// MySQLAi.de - 知识库文章编辑页面
// 使用新的知识库模块结构的文章编辑功能

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { ArrowLeft, Eye, AlertCircle } from 'lucide-react';
import { articlesApi } from '@/lib/api/knowledge';
import { KNOWLEDGE_ROUTES } from '@/lib/knowledge-routes';
import Button from '@/components/ui/Button';
import ArticleForm from '@/components/admin/ArticleForm';
import type { Database } from '@/lib/database.types';

type KnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];

interface ArticleFormData {
  id: string;
  title: string;
  description: string;
  content: string;
  category_id: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  order_index: number;
}

export default function KnowledgeArticleEditPage() {
  const router = useRouter();
  const params = useParams();
  const articleId = params.id as string;
  
  const [article, setArticle] = useState<KnowledgeArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  const isNewArticle = articleId === 'new';

  // 获取文章数据
  useEffect(() => {
    if (isNewArticle) {
      setLoading(false);
      return;
    }

    const fetchArticle = async () => {
      try {
        setLoading(true);
        const response = await articlesApi.getById(articleId, {
          includeCodeExamples: false,
          includeRelated: false
        });

        if (response.success && response.data) {
          setArticle(response.data);
        } else {
          setError(response.error || '文章不存在');
        }
      } catch (error) {
        console.error('获取文章失败:', error);
        setError('获取文章失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [articleId, isNewArticle]);

  // 处理保存文章
  const handleSaveArticle = async (data: ArticleFormData) => {
    try {
      setSaving(true);
      setError('');

      let response;
      if (isNewArticle) {
        // 创建新文章
        response = await articlesApi.create({
          id: data.id,
          title: data.title,
          description: data.description || null,
          content: data.content,
          category_id: data.category_id || null,
          tags: data.tags.length > 0 ? data.tags : null,
          difficulty: data.difficulty,
          order_index: data.order_index,

        });
      } else {
        // 更新现有文章
        response = await articlesApi.update(articleId, {
          title: data.title,
          description: data.description || null,
          content: data.content,
          category_id: data.category_id || null,
          tags: data.tags.length > 0 ? data.tags : null,
          difficulty: data.difficulty,
          order_index: data.order_index
        });
      }

      if (response.success) {
        // 保存成功，返回列表页面
        router.push('/admin/knowledge/articles');
      } else {
        setError(response.error || '保存失败，请稍后重试');
      }
    } catch (error) {
      console.error('保存文章失败:', error);
      setError('保存失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    router.push('/admin/knowledge/articles');
  };

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-mysql-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-mysql-text-light">
            {isNewArticle ? '准备创建新文章...' : '加载文章数据...'}
          </p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error && !isNewArticle && !article) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-mysql-text mb-2">
          加载失败
        </h3>
        <p className="text-mysql-text-light mb-6">
          {error}
        </p>
        <div className="flex items-center justify-center space-x-3">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/knowledge/articles')}
            icon={<ArrowLeft className="w-4 h-4" />}
          >
            返回列表
          </Button>
          <Button
            variant="primary"
            onClick={() => window.location.reload()}
          >
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => router.push('/admin/knowledge/articles')}
            icon={<ArrowLeft className="w-5 h-5" />}
            className="text-mysql-text-light hover:text-mysql-primary"
          >
            返回列表
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold text-mysql-text">
              {isNewArticle ? '创建新文章' : '编辑文章'}
            </h1>
            <p className="text-mysql-text-light">
              {isNewArticle 
                ? '添加新的知识库文章' 
                : `编辑文章: ${article?.title || articleId}`
              }
            </p>
          </div>
        </div>

        {/* 快速操作 */}
        {!isNewArticle && article && (
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => window.open(KNOWLEDGE_ROUTES.ARTICLES.PREVIEW(article.id), '_blank')}
              icon={<Eye className="w-4 h-4" />}
            >
              预览文章
            </Button>
          </div>
        )}
      </motion.div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </motion.div>
      )}

      {/* 文章表单 */}
      <ArticleForm
        article={article}
        onSave={handleSaveArticle}
        onCancel={handleCancel}
        loading={saving}
      />
    </div>
  );
}
