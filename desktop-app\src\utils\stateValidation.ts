/**
 * 状态验证和同步工具
 * 用于测试和验证状态同步机制的正确性
 */

import { useAppStore } from '../store/appStore';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 验证激活状态的一致性
 */
export function validateActivationState(): ValidationResult {
  const store = useAppStore.getState();
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // 检查激活状态一致性
  if (store.activation.status === 'activated') {
    if (!store.activation.licenseKey) {
      result.errors.push('激活状态为已激活，但缺少许可证密钥');
      result.isValid = false;
    }
    
    if (!store.activation.machineId) {
      result.errors.push('激活状态为已激活，但缺少机器ID');
      result.isValid = false;
    }
    
    if (store.activation.expiresAt) {
      const expirationDate = new Date(store.activation.expiresAt);
      const now = new Date();
      if (expirationDate <= now && store.activation.status !== 'expired') {
        result.warnings.push('许可证已过期，但状态未更新');
      }
    }
  }

  return result;
}

/**
 * 验证安装状态的一致性
 */
export function validateInstallationState(): ValidationResult {
  const store = useAppStore.getState();
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // 检查安装状态一致性
  if (store.installation.status === 'completed') {
    if (store.installation.progressPercentage !== 100) {
      result.warnings.push('安装状态为完成，但进度不是100%');
    }
    
    if (!store.installation.message.includes('成功')) {
      result.warnings.push('安装状态为完成，但消息不包含成功信息');
    }
  }

  if (store.installation.status === 'failed') {
    if (store.installation.progressPercentage === 100) {
      result.warnings.push('安装状态为失败，但进度是100%');
    }
  }

  if (store.installation.progressPercentage < 0 || store.installation.progressPercentage > 100) {
    result.errors.push('安装进度超出有效范围 (0-100)');
    result.isValid = false;
  }

  return result;
}

/**
 * 验证整体状态一致性
 */
export function validateOverallState(): ValidationResult {
  const activationResult = validateActivationState();
  const installationResult = validateInstallationState();
  
  const result: ValidationResult = {
    isValid: activationResult.isValid && installationResult.isValid,
    errors: [...activationResult.errors, ...installationResult.errors],
    warnings: [...activationResult.warnings, ...installationResult.warnings]
  };

  return result;
}

/**
 * 自动修复状态不一致问题
 */
export function autoFixStateInconsistencies(): void {
  const store = useAppStore.getState();
  
  // 修复激活状态不一致
  if (store.activation.status === 'activated' && !store.activation.licenseKey) {
    console.warn('Auto-fixing activation state inconsistency');
    store.updateActivation({ status: 'not_activated' });
  }
  
  // 修复安装状态不一致
  if (store.installation.status === 'completed' && store.installation.progressPercentage !== 100) {
    console.warn('Auto-fixing installation progress inconsistency');
    store.setInstallationProgress('完成', 100, 'MySQL安装成功完成！');
  }
  
  if (store.installation.status === 'failed' && store.installation.progressPercentage === 100) {
    console.warn('Auto-fixing failed installation progress inconsistency');
    store.setInstallationProgress('失败', 0, '安装失败');
  }
}

/**
 * 测试状态同步机制
 */
export function testStateSynchronization(): Promise<ValidationResult> {
  return new Promise((resolve) => {
    console.log('开始测试状态同步机制...');
    
    const initialValidation = validateOverallState();
    console.log('初始状态验证:', initialValidation);
    
    // 模拟状态变化
    const store = useAppStore.getState();
    
    // 测试激活状态变化
    store.updateActivation({
      status: 'activated',
      licenseKey: 'TEST-KEY-123',
      machineId: 'test-machine'
    });
    
    // 测试安装状态变化
    store.setInstallationStatus('downloading');
    store.setInstallationProgress('下载中', 50, '正在下载MySQL...');
    
    // 延迟验证，确保状态已同步
    setTimeout(() => {
      const finalValidation = validateOverallState();
      console.log('最终状态验证:', finalValidation);
      
      // 清理测试状态
      store.resetToDefaults();
      
      resolve(finalValidation);
    }, 1000);
  });
}

/**
 * 错误恢复测试
 */
export function testErrorRecovery(): void {
  console.log('开始测试错误恢复机制...');
  
  const store = useAppStore.getState();
  
  // 模拟错误状态
  store.setInstallationStatus('failed');
  store.setInstallationProgress('失败', 0, '模拟错误');
  
  console.log('错误状态设置完成，开始恢复...');
  
  // 测试错误恢复
  store.recoverFromError();
  
  const recoveryValidation = validateInstallationState();
  console.log('错误恢复验证:', recoveryValidation);
}

/**
 * 性能测试：大量状态更新
 */
export function performanceTest(): void {
  console.log('开始性能测试...');
  
  const store = useAppStore.getState();
  const startTime = performance.now();
  
  // 执行大量状态更新
  for (let i = 0; i < 1000; i++) {
    store.setInstallationProgress(`测试 ${i}`, i % 101, `进度 ${i}`);
  }
  
  const endTime = performance.now();
  console.log(`性能测试完成，耗时: ${endTime - startTime}ms`);
  
  // 清理
  store.resetInstallation();
}
