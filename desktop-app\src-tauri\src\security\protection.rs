/**
 * 运行时保护管理模块
 * 统一管理所有安全防护功能，提供安全策略执行
 */

use std::sync::{Arc, Mutex, atomic::{AtomicBool, Ordering}};
use std::time::{Duration, Instant};
use tokio::time::interval;
use crate::security::{
    anti_debug::{check_debugger_presence, check_execution_timing, check_virtual_environment, AntiDebugResult},
    integrity::{verify_binary_integrity, verify_config_integrity, IntegrityResult},
    obfuscation::{protect_sensitive_data, cleanup_sensitive_data},
};

/// 安全级别
#[derive(Debug, Clone, PartialEq)]
pub enum SecurityLevel {
    Low,      // 基础保护
    Medium,   // 标准保护
    High,     // 高级保护
    Paranoid, // 偏执级保护
}

/// 保护状态
#[derive(Debug, Clone)]
pub struct ProtectionStatus {
    pub is_active: bool,
    pub security_level: SecurityLevel,
    pub last_check: Option<Instant>,
    pub threat_count: u32,
    pub violations: Vec<SecurityViolation>,
}

/// 安全违规记录
#[derive(Debug, Clone)]
pub struct SecurityViolation {
    pub violation_type: ViolationType,
    pub timestamp: Instant,
    pub description: String,
    pub severity: ViolationSeverity,
}

/// 违规类型
#[derive(Debug, Clone, PartialEq)]
pub enum ViolationType {
    DebuggerDetected,
    IntegrityFailure,
    TimingAnomaly,
    VirtualEnvironment,
    UnauthorizedAccess,
    TamperingAttempt,
}

/// 违规严重程度
#[derive(Debug, Clone, PartialEq)]
pub enum ViolationSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 运行时保护管理器
pub struct RuntimeProtectionManager {
    status: Arc<Mutex<ProtectionStatus>>,
    monitoring_active: AtomicBool,
    security_level: SecurityLevel,
    violation_threshold: u32,
    check_interval: Duration,
}

impl RuntimeProtectionManager {
    /// 创建新的保护管理器
    pub fn new(security_level: SecurityLevel) -> Self {
        let check_interval = match security_level {
            SecurityLevel::Low => Duration::from_secs(60),      // 1分钟
            SecurityLevel::Medium => Duration::from_secs(30),   // 30秒
            SecurityLevel::High => Duration::from_secs(10),     // 10秒
            SecurityLevel::Paranoid => Duration::from_secs(5),  // 5秒
        };

        let violation_threshold = match security_level {
            SecurityLevel::Low => 10,
            SecurityLevel::Medium => 5,
            SecurityLevel::High => 3,
            SecurityLevel::Paranoid => 1,
        };

        RuntimeProtectionManager {
            status: Arc::new(Mutex::new(ProtectionStatus {
                is_active: false,
                security_level: security_level.clone(),
                last_check: None,
                threat_count: 0,
                violations: Vec::new(),
            })),
            monitoring_active: AtomicBool::new(false),
            security_level,
            violation_threshold,
            check_interval,
        }
    }

    /// 初始化运行时保护
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error>> {
        log::info!("初始化运行时保护系统，安全级别: {:?}", self.security_level);

        // 保护敏感数据
        protect_sensitive_data();

        // 执行初始安全检查
        let initial_check = self.perform_security_check().await;
        if !initial_check.is_secure {
            log::warn!("初始安全检查发现威胁: {:?}", initial_check.violations);
            
            // 根据安全级别决定是否继续
            if self.security_level == SecurityLevel::Paranoid {
                return Err("初始安全检查失败，应用无法启动".into());
            }
        }

        // 更新状态
        {
            let mut status = self.status.lock().unwrap();
            status.is_active = true;
            status.last_check = Some(Instant::now());
            status.violations.extend(initial_check.violations);
        }

        log::info!("运行时保护系统初始化完成");
        Ok(())
    }

    /// 启动监控
    pub async fn start_monitoring(&self) {
        if self.monitoring_active.load(Ordering::Relaxed) {
            log::warn!("监控已经在运行中");
            return;
        }

        self.monitoring_active.store(true, Ordering::Relaxed);
        log::info!("启动安全监控，检查间隔: {:?}", self.check_interval);

        let status = Arc::clone(&self.status);
        let check_interval = self.check_interval;
        let violation_threshold = self.violation_threshold;
        let security_level = self.security_level.clone();
        let monitoring_active = &self.monitoring_active;

        // 启动监控任务
        let mut interval_timer = interval(check_interval);
        
        while monitoring_active.load(Ordering::Relaxed) {
            interval_timer.tick().await;
            
            // 执行安全检查
            let check_result = self.perform_security_check().await;
            
            // 更新状态
            {
                let mut status_guard = status.lock().unwrap();
                status_guard.last_check = Some(Instant::now());
                status_guard.violations.extend(check_result.violations.clone());
                
                // 清理旧的违规记录（保留最近1小时的记录）
                let one_hour_ago = Instant::now() - Duration::from_secs(3600);
                status_guard.violations.retain(|v| v.timestamp > one_hour_ago);
                
                // 更新威胁计数
                status_guard.threat_count = status_guard.violations.len() as u32;
            }

            // 检查是否超过违规阈值
            if check_result.violations.len() as u32 >= violation_threshold {
                log::error!("检测到严重安全威胁，违规数量: {}", check_result.violations.len());
                
                // 执行安全响应
                self.handle_security_threat(&check_result.violations).await;
                
                // 如果是偏执级安全，立即退出
                if security_level == SecurityLevel::Paranoid {
                    log::error!("偏执级安全模式下检测到威胁，应用将退出");
                    std::process::exit(1);
                }
            }

            // 根据安全级别调整检查频率
            if check_result.violations.len() > 0 {
                // 检测到威胁时增加检查频率
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        }

        log::info!("安全监控已停止");
    }

    /// 停止监控
    pub fn stop_monitoring(&self) {
        self.monitoring_active.store(false, Ordering::Relaxed);
        log::info!("正在停止安全监控");
    }

    /// 执行安全检查
    async fn perform_security_check(&self) -> SecurityCheckResult {
        let mut violations = Vec::new();
        let start_time = Instant::now();

        // 反调试检查
        match check_debugger_presence() {
            AntiDebugResult::DebuggerDetected => {
                violations.push(SecurityViolation {
                    violation_type: ViolationType::DebuggerDetected,
                    timestamp: Instant::now(),
                    description: "检测到调试器存在".to_string(),
                    severity: ViolationSeverity::Critical,
                });
            }
            AntiDebugResult::Suspicious => {
                violations.push(SecurityViolation {
                    violation_type: ViolationType::DebuggerDetected,
                    timestamp: Instant::now(),
                    description: "检测到可疑的调试活动".to_string(),
                    severity: ViolationSeverity::High,
                });
            }
            _ => {}
        }

        // 执行时间检查
        match check_execution_timing().await {
            AntiDebugResult::Suspicious => {
                violations.push(SecurityViolation {
                    violation_type: ViolationType::TimingAnomaly,
                    timestamp: Instant::now(),
                    description: "检测到执行时间异常".to_string(),
                    severity: ViolationSeverity::Medium,
                });
            }
            _ => {}
        }

        // 虚拟环境检查
        match check_virtual_environment() {
            AntiDebugResult::Suspicious => {
                violations.push(SecurityViolation {
                    violation_type: ViolationType::VirtualEnvironment,
                    timestamp: Instant::now(),
                    description: "检测到虚拟环境".to_string(),
                    severity: ViolationSeverity::Low,
                });
            }
            _ => {}
        }

        // 完整性检查（较少频率）
        if self.should_perform_integrity_check() {
            match verify_binary_integrity().await {
                IntegrityResult::Invalid => {
                    violations.push(SecurityViolation {
                        violation_type: ViolationType::IntegrityFailure,
                        timestamp: Instant::now(),
                        description: "二进制文件完整性验证失败".to_string(),
                        severity: ViolationSeverity::Critical,
                    });
                }
                _ => {}
            }

            match verify_config_integrity() {
                IntegrityResult::Invalid => {
                    violations.push(SecurityViolation {
                        violation_type: ViolationType::TamperingAttempt,
                        timestamp: Instant::now(),
                        description: "配置文件被篡改".to_string(),
                        severity: ViolationSeverity::High,
                    });
                }
                _ => {}
            }
        }

        let is_secure = violations.is_empty();
        
        SecurityCheckResult {
            is_secure,
            violations,
            check_duration: start_time.elapsed(),
        }
    }

    /// 判断是否应该执行完整性检查
    fn should_perform_integrity_check(&self) -> bool {
        let status = self.status.lock().unwrap();
        if let Some(last_check) = status.last_check {
            // 根据安全级别决定完整性检查频率
            let integrity_interval = match self.security_level {
                SecurityLevel::Low => Duration::from_secs(300),     // 5分钟
                SecurityLevel::Medium => Duration::from_secs(120),  // 2分钟
                SecurityLevel::High => Duration::from_secs(60),     // 1分钟
                SecurityLevel::Paranoid => Duration::from_secs(30), // 30秒
            };
            
            Instant::now() - last_check > integrity_interval
        } else {
            true // 首次检查
        }
    }

    /// 处理安全威胁
    async fn handle_security_threat(&self, violations: &[SecurityViolation]) {
        for violation in violations {
            match violation.severity {
                ViolationSeverity::Critical => {
                    log::error!("严重安全威胁: {}", violation.description);
                    // 可以选择立即退出或限制功能
                    if self.security_level == SecurityLevel::Paranoid {
                        std::process::exit(1);
                    }
                }
                ViolationSeverity::High => {
                    log::warn!("高级安全威胁: {}", violation.description);
                    // 增加监控频率
                }
                ViolationSeverity::Medium => {
                    log::warn!("中级安全威胁: {}", violation.description);
                }
                ViolationSeverity::Low => {
                    log::info!("低级安全威胁: {}", violation.description);
                }
            }
        }

        // 根据威胁类型执行相应的响应措施
        for violation in violations {
            match violation.violation_type {
                ViolationType::DebuggerDetected => {
                    // 可以选择退出或执行反调试措施
                    if self.security_level >= SecurityLevel::High {
                        log::error!("检测到调试器，应用将退出");
                        std::process::exit(1);
                    }
                }
                ViolationType::IntegrityFailure => {
                    log::error!("完整性验证失败，应用可能被篡改");
                    if self.security_level >= SecurityLevel::Medium {
                        std::process::exit(1);
                    }
                }
                _ => {
                    // 其他威胁的处理
                }
            }
        }
    }

    /// 获取当前保护状态
    pub fn get_status(&self) -> ProtectionStatus {
        self.status.lock().unwrap().clone()
    }

    /// 清理资源
    pub fn cleanup(&self) {
        self.stop_monitoring();
        cleanup_sensitive_data();
        log::info!("运行时保护系统已清理");
    }
}

/// 安全检查结果
#[derive(Debug)]
struct SecurityCheckResult {
    is_secure: bool,
    violations: Vec<SecurityViolation>,
    check_duration: Duration,
}

/// 全局保护管理器实例
static mut GLOBAL_PROTECTION_MANAGER: Option<RuntimeProtectionManager> = None;
static INIT_ONCE: std::sync::Once = std::sync::Once::new();

/// 初始化运行时保护
pub async fn initialize_runtime_protection(security_level: SecurityLevel) -> Result<(), Box<dyn std::error::Error>> {
    INIT_ONCE.call_once(|| {
        unsafe {
            GLOBAL_PROTECTION_MANAGER = Some(RuntimeProtectionManager::new(security_level));
        }
    });

    if let Some(ref manager) = unsafe { &GLOBAL_PROTECTION_MANAGER } {
        manager.initialize().await?;
        
        // 启动监控任务
        let manager_clone = unsafe { GLOBAL_PROTECTION_MANAGER.as_ref().unwrap() };
        tokio::spawn(async move {
            manager_clone.start_monitoring().await;
        });
    }

    Ok(())
}

/// 检查运行时安全状态
pub fn check_runtime_security() -> Option<ProtectionStatus> {
    unsafe {
        GLOBAL_PROTECTION_MANAGER.as_ref().map(|manager| manager.get_status())
    }
}

/// 清理运行时保护
pub fn cleanup_runtime_protection() {
    unsafe {
        if let Some(ref manager) = GLOBAL_PROTECTION_MANAGER {
            manager.cleanup();
        }
    }
}