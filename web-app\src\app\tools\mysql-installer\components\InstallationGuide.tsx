'use client';

/**
 * MySQL安装指导显示组件
 * 根据检测到的系统显示对应的安装指导，支持命令复制和配置文件下载
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Copy, 
  Download, 
  FileText, 
  Terminal, 
  CheckCircle, 
  AlertTriangle,
  HelpCircle,
  ChevronDown,
  ChevronRight,
  Play,
  Package,
  Shield,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { 
  SupportedOS, 
  QuickInstallConfig, 
  InstallationStep 
} from '../types/mysql-installer';
import { 
  generateInstallationGuide, 
  getPackageManagerCommands, 
  getCommonIssues,
  generateVerificationScript,
  type InstallationGuide as IInstallationGuide
} from '../lib/installation-guide';

interface InstallationGuideProps {
  config: QuickInstallConfig;
  configFileContent: string;
  onStepComplete?: (stepId: string) => void;
  className?: string;
}

/**
 * 安装指导组件
 */
export default function InstallationGuide({
  config,
  configFileContent,
  onStepComplete,
  className
}: InstallationGuideProps) {
  const [guide] = useState<IInstallationGuide>(() => 
    generateInstallationGuide(config.targetOS, config)
  );
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['steps']));
  const [copiedCommand, setCopiedCommand] = useState<string | null>(null);

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string, commandId?: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCommand(commandId || text);
      setTimeout(() => setCopiedCommand(null), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  }, []);

  // 下载配置文件
  const downloadConfigFile = useCallback(() => {
    const filename = config.targetOS === SupportedOS.WINDOWS ? 'my.ini' : 'my.cnf';
    const blob = new Blob([configFileContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [configFileContent, config.targetOS]);

  // 下载验证脚本
  const downloadVerificationScript = useCallback(() => {
    const { script } = generateVerificationScript(config);
    const filename = config.targetOS === SupportedOS.WINDOWS ? 'verify_mysql.bat' : 'verify_mysql.sh';
    const blob = new Blob([script], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [config]);

  // 标记步骤完成
  const markStepComplete = useCallback((stepId: string) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));
    onStepComplete?.(stepId);
  }, [onStepComplete]);

  // 切换展开状态
  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  }, []);

  // 渲染步骤
  const renderStep = (step: InstallationStep, index: number) => {
    const isCompleted = completedSteps.has(step.id);
    const stepId = `step-${step.id}`;

    return (
      <motion.div
        key={step.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className={cn(
          'border rounded-lg p-6 transition-all duration-200',
          isCompleted 
            ? 'border-green-200 bg-green-50' 
            : 'border-gray-200 bg-white hover:border-mysql-primary/30'
        )}
      >
        <div className="flex items-start space-x-4">
          {/* 步骤图标 */}
          <div className={cn(
            'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
            isCompleted 
              ? 'bg-green-600 text-white' 
              : 'bg-mysql-primary text-white'
          )}>
            {isCompleted ? <CheckCircle className="w-5 h-5" /> : index + 1}
          </div>

          {/* 步骤内容 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h4 className={cn(
                'font-semibold',
                isCompleted ? 'text-green-800' : 'text-mysql-text'
              )}>
                {step.title}
              </h4>
              
              <div className="flex items-center space-x-2">
                {step.estimatedTime && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {step.estimatedTime}分钟
                  </span>
                )}
                
                {!isCompleted && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => markStepComplete(step.id)}
                    icon={<CheckCircle className="w-4 h-4" />}
                  >
                    标记完成
                  </Button>
                )}
              </div>
            </div>

            <p className="text-gray-600 mb-4">{step.description}</p>

            {/* 命令区域 */}
            {step.command && (
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Terminal className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-green-400 font-medium">命令</span>
                  </div>
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyToClipboard(step.command!, stepId)}
                    icon={<Copy className="w-4 h-4" />}
                    className="text-gray-400 hover:text-white"
                  >
                    {copiedCommand === stepId ? '已复制' : '复制'}
                  </Button>
                </div>
                
                <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-x-auto">
                  {step.command}
                </pre>
              </div>
            )}

            {/* 必需标识 */}
            {step.required && (
              <div className="flex items-center space-x-1 text-orange-600">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm font-medium">必需步骤</span>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  // 渲染可折叠部分
  const renderCollapsibleSection = (
    id: string,
    title: string,
    icon: React.ReactNode,
    children: React.ReactNode
  ) => {
    const isExpanded = expandedSections.has(id);

    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <button
          onClick={() => toggleSection(id)}
          className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center space-x-3">
            {icon}
            <span className="font-semibold text-mysql-text">{title}</span>
          </div>
          
          {isExpanded ? (
            <ChevronDown className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-500" />
          )}
        </button>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="p-4 bg-white">
                {children}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const packageManagerCommands = getPackageManagerCommands(config.targetOS);
  const commonIssues = getCommonIssues();

  return (
    <div className={cn('space-y-6', className)}>
      {/* 标题和下载按钮 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-mysql-text">
          {config.targetOS === SupportedOS.WINDOWS ? 'Windows' :
           config.targetOS === SupportedOS.MACOS ? 'macOS' : 'Linux'} 安装指导
        </h2>
        
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={downloadConfigFile}
            icon={<FileText className="w-4 h-4" />}
          >
            下载配置文件
          </Button>
          
          <Button
            variant="outline"
            onClick={downloadVerificationScript}
            icon={<Download className="w-4 h-4" />}
          >
            下载验证脚本
          </Button>
        </div>
      </div>

      {/* 前置要求 */}
      {renderCollapsibleSection(
        'prerequisites',
        '前置要求',
        <Shield className="w-5 h-5 text-mysql-primary" />,
        <div className="space-y-2">
          {guide.prerequisites.map((req, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-mysql-primary rounded-full"></div>
              <span className="text-gray-700">{req}</span>
            </div>
          ))}
        </div>
      )}

      {/* 包管理器安装（如果支持） */}
      {packageManagerCommands && renderCollapsibleSection(
        'package-manager',
        `使用 ${packageManagerCommands.name} 安装（推荐）`,
        <Package className="w-5 h-5 text-green-600" />,
        <div>
          <p className="text-gray-600 mb-4">{packageManagerCommands.description}</p>
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Terminal className="w-4 h-4 text-green-400" />
                <span className="text-sm text-green-400 font-medium">命令</span>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => copyToClipboard(packageManagerCommands.commands.join('\n'))}
                icon={<Copy className="w-4 h-4" />}
                className="text-gray-400 hover:text-white"
              >
                复制全部
              </Button>
            </div>
            <pre className="text-sm text-gray-300 whitespace-pre-wrap">
              {packageManagerCommands.commands.join('\n')}
            </pre>
          </div>
        </div>
      )}

      {/* 手动安装步骤 */}
      {renderCollapsibleSection(
        'steps',
        '手动安装步骤',
        <Play className="w-5 h-5 text-mysql-primary" />,
        <div className="space-y-4">
          {guide.steps.map((step, index) => renderStep(step, index))}
        </div>
      )}

      {/* 安装后配置 */}
      {guide.postInstallation.length > 0 && renderCollapsibleSection(
        'post-installation',
        '安装后配置',
        <Zap className="w-5 h-5 text-blue-600" />,
        <div className="space-y-4">
          {guide.postInstallation.map((item, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-mysql-text mb-2">{item.title}</h4>
              <p className="text-gray-600 mb-3">{item.description}</p>

              {item.commands && (
                <div className="bg-gray-900 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Terminal className="w-4 h-4 text-green-400" />
                      <span className="text-sm text-green-400 font-medium">命令</span>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(item.commands!.join('\n'))}
                      icon={<Copy className="w-4 h-4" />}
                      className="text-gray-400 hover:text-white"
                    >
                      复制
                    </Button>
                  </div>
                  <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                    {item.commands.join('\n')}
                  </pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 故障排除 */}
      {renderCollapsibleSection(
        'troubleshooting',
        '故障排除',
        <HelpCircle className="w-5 h-5 text-orange-600" />,
        <div className="space-y-4">
          {/* 系统特定问题 */}
          <div>
            <h4 className="font-semibold text-mysql-text mb-3">系统特定问题</h4>
            <div className="space-y-3">
              {guide.troubleshooting.map((item, index) => (
                <div key={index} className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-orange-600 flex-shrink-0 mt-0.5" />
                    <div className="flex-1">
                      <h5 className="font-medium text-orange-800 mb-1">{item.problem}</h5>
                      <p className="text-orange-700 text-sm">{item.solution}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 常见问题 */}
          <div>
            <h4 className="font-semibold text-mysql-text mb-3">常见问题</h4>
            <div className="space-y-3">
              {commonIssues.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <h5 className="font-medium text-gray-800 mb-2">{category.category}</h5>
                  <div className="space-y-2">
                    {category.issues.map((issue, issueIndex) => (
                      <div key={issueIndex} className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                        <div className="flex items-start space-x-3">
                          <HelpCircle className="w-4 h-4 text-gray-600 flex-shrink-0 mt-0.5" />
                          <div className="flex-1">
                            <h6 className="font-medium text-gray-800 text-sm mb-1">{issue.problem}</h6>
                            <p className="text-gray-600 text-sm mb-2">{issue.solution}</p>

                            {issue.commands && (
                              <div className="bg-gray-900 rounded p-2">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-xs text-green-400">解决命令</span>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => copyToClipboard(issue.commands!.join('\n'))}
                                    icon={<Copy className="w-3 h-3" />}
                                    className="text-gray-400 hover:text-white text-xs px-2 py-1"
                                  >
                                    复制
                                  </Button>
                                </div>
                                <pre className="text-xs text-gray-300 whitespace-pre-wrap">
                                  {issue.commands.join('\n')}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 安装总结 */}
      <div className="bg-mysql-primary/5 border border-mysql-primary/20 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <CheckCircle className="w-6 h-6 text-mysql-primary" />
          <h3 className="text-lg font-semibold text-mysql-text">安装总结</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">预计总时间:</span>
            <span className="font-medium ml-2">{guide.estimatedTotalTime} 分钟</span>
          </div>
          <div>
            <span className="text-gray-600">安装步骤:</span>
            <span className="font-medium ml-2">{guide.steps.length} 个步骤</span>
          </div>
          <div>
            <span className="text-gray-600">MySQL版本:</span>
            <span className="font-medium ml-2">{config.version}</span>
          </div>
          <div>
            <span className="text-gray-600">安装路径:</span>
            <span className="font-medium ml-2 text-xs">{config.installPath}</span>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
          <p className="text-blue-800 text-sm">
            <strong>提示:</strong> 安装完成后，建议运行验证脚本确保MySQL正常工作。
            如遇到问题，请参考上方的故障排除部分。
          </p>
        </div>
      </div>
    </div>
  );
}
