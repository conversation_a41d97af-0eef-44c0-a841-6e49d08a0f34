/**
 * 多系统检测工具函数
 * 支持Windows/macOS/Linux系统的智能检测和兼容性检查
 */

import { 
  SupportedOS, 
  SystemArchitecture, 
  SystemInfo, 
  SystemRequirement 
} from '../types/mysql-installer';
import { 
  DEFAULT_INSTALL_PATHS, 
  DEFAULT_DATA_PATHS, 
  SYSTEM_REQUIREMENTS 
} from './defaults';

/**
 * 检测操作系统
 * 基于navigator.userAgent进行准确的系统识别
 */
export function detectOperatingSystem(): SupportedOS {
  if (typeof window === 'undefined') {
    return SupportedOS.UNKNOWN;
  }

  const userAgent = window.navigator.userAgent.toLowerCase();
  const platform = window.navigator.platform?.toLowerCase() || '';

  // Windows检测
  if (userAgent.includes('windows') || platform.includes('win')) {
    return SupportedOS.WINDOWS;
  }

  // macOS检测
  if (userAgent.includes('mac') || platform.includes('mac') || userAgent.includes('darwin')) {
    return SupportedOS.MACOS;
  }

  // Linux检测
  if (userAgent.includes('linux') || platform.includes('linux') || userAgent.includes('x11')) {
    return SupportedOS.LINUX;
  }

  return SupportedOS.UNKNOWN;
}

/**
 * 检测系统架构
 * 识别x64/arm64/x86架构
 */
export function getSystemArchitecture(): SystemArchitecture {
  if (typeof window === 'undefined') {
    return SystemArchitecture.UNKNOWN;
  }

  const userAgent = window.navigator.userAgent.toLowerCase();
  const platform = window.navigator.platform?.toLowerCase() || '';

  // ARM64检测 (Apple Silicon, ARM Windows等)
  if (userAgent.includes('arm64') || 
      userAgent.includes('aarch64') || 
      platform.includes('arm')) {
    return SystemArchitecture.ARM64;
  }

  // x64检测
  if (userAgent.includes('x86_64') || 
      userAgent.includes('win64') || 
      userAgent.includes('wow64') ||
      platform.includes('x64') ||
      platform.includes('amd64')) {
    return SystemArchitecture.X64;
  }

  // x86检测
  if (userAgent.includes('i386') || 
      userAgent.includes('i686') ||
      platform.includes('x86') ||
      userAgent.includes('win32')) {
    return SystemArchitecture.X86;
  }

  return SystemArchitecture.UNKNOWN;
}

/**
 * 获取操作系统版本信息
 */
export function getOperatingSystemVersion(): string {
  if (typeof window === 'undefined') {
    return 'Unknown';
  }

  const userAgent = window.navigator.userAgent;
  
  // Windows版本检测
  const windowsMatch = userAgent.match(/Windows NT (\d+\.\d+)/);
  if (windowsMatch) {
    const version = windowsMatch[1];
    const versionMap: Record<string, string> = {
      '10.0': 'Windows 10/11',
      '6.3': 'Windows 8.1',
      '6.2': 'Windows 8',
      '6.1': 'Windows 7'
    };
    return versionMap[version] || `Windows NT ${version}`;
  }

  // macOS版本检测
  const macMatch = userAgent.match(/Mac OS X (\d+[._]\d+[._]?\d*)/);
  if (macMatch) {
    const version = macMatch[1].replace(/_/g, '.');
    return `macOS ${version}`;
  }

  // Linux检测
  if (userAgent.includes('Linux')) {
    return 'Linux';
  }

  return 'Unknown';
}

/**
 * 获取浏览器信息
 */
export function getBrowserInfo(): string {
  if (typeof window === 'undefined') {
    return 'Unknown';
  }

  const userAgent = window.navigator.userAgent;

  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
    return chromeMatch ? `Chrome ${chromeMatch[1]}` : 'Chrome';
  }

  if (userAgent.includes('Firefox')) {
    const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
    return firefoxMatch ? `Firefox ${firefoxMatch[1]}` : 'Firefox';
  }

  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    const safariMatch = userAgent.match(/Version\/(\d+)/);
    return safariMatch ? `Safari ${safariMatch[1]}` : 'Safari';
  }

  if (userAgent.includes('Edg')) {
    const edgeMatch = userAgent.match(/Edg\/(\d+)/);
    return edgeMatch ? `Edge ${edgeMatch[1]}` : 'Edge';
  }

  return 'Unknown';
}

/**
 * 获取指定操作系统的默认安装路径
 */
export function getDefaultInstallPath(os: SupportedOS): string {
  return DEFAULT_INSTALL_PATHS[os];
}

/**
 * 获取指定操作系统的默认数据路径
 */
export function getDefaultDataPath(os: SupportedOS): string {
  return DEFAULT_DATA_PATHS[os];
}

/**
 * 检查系统要求
 * 验证系统是否满足MySQL安装的基本要求
 */
export function checkSystemRequirements(os: SupportedOS): SystemRequirement[] {
  const requirements: SystemRequirement[] = [];
  const systemReqs = SYSTEM_REQUIREMENTS[os];

  if (!systemReqs) {
    requirements.push({
      name: '操作系统支持',
      satisfied: false,
      description: '不支持的操作系统',
      suggestion: '请使用Windows 10+、macOS 10.15+或主流Linux发行版'
    });
    return requirements;
  }

  // 操作系统版本检查
  requirements.push({
    name: '操作系统版本',
    satisfied: true, // Web环境下假设满足，实际检查需要更复杂的逻辑
    description: `要求: ${systemReqs.minVersion}`,
    suggestion: systemReqs.minVersion
  });

  // 内存要求检查
  requirements.push({
    name: '内存要求',
    satisfied: true, // Web环境下无法直接检测，假设满足
    description: `要求: ${systemReqs.minRAM}`,
    suggestion: `建议至少${systemReqs.minRAM}内存`
  });

  // 磁盘空间检查
  requirements.push({
    name: '磁盘空间',
    satisfied: true, // Web环境下无法直接检测，假设满足
    description: `要求: ${systemReqs.minDisk}`,
    suggestion: `确保有至少${systemReqs.minDisk}可用磁盘空间`
  });

  // 必需软件检查
  systemReqs.requiredSoftware.forEach(software => {
    requirements.push({
      name: software,
      satisfied: true, // Web环境下无法直接检测，假设满足
      description: `要求安装: ${software}`,
      suggestion: `请确保已安装${software}`
    });
  });

  return requirements;
}

/**
 * 检测系统是否支持MySQL安装
 */
export function isSystemSupported(os: SupportedOS): boolean {
  return os !== SupportedOS.UNKNOWN && SYSTEM_REQUIREMENTS[os] !== undefined;
}

/**
 * 获取完整的系统信息
 * 整合所有检测结果
 */
export function getSystemInfo(): SystemInfo {
  const os = detectOperatingSystem();
  const architecture = getSystemArchitecture();
  const osVersion = getOperatingSystemVersion();
  const browser = getBrowserInfo();
  const isSupported = isSystemSupported(os);
  const requirements = checkSystemRequirements(os);

  return {
    os,
    architecture,
    osVersion,
    browser,
    isSupported,
    requirements
  };
}

/**
 * 检测是否为移动设备
 * 移动设备不支持MySQL安装
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  const userAgent = window.navigator.userAgent.toLowerCase();
  
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent) ||
         (window.navigator.maxTouchPoints && window.navigator.maxTouchPoints > 2);
}

/**
 * 获取系统特定的配置建议
 */
export function getSystemSpecificRecommendations(os: SupportedOS): string[] {
  const recommendations: string[] = [];

  switch (os) {
    case SupportedOS.WINDOWS:
      recommendations.push('建议以管理员身份运行安装程序');
      recommendations.push('确保Windows Defender或其他杀毒软件不会阻止安装');
      recommendations.push('建议安装在非系统盘（如D盘）以获得更好的性能');
      break;

    case SupportedOS.MACOS:
      recommendations.push('可能需要在系统偏好设置中允许来自未知开发者的应用');
      recommendations.push('建议使用Homebrew进行安装以简化依赖管理');
      recommendations.push('确保有足够的磁盘权限访问安装目录');
      break;

    case SupportedOS.LINUX:
      recommendations.push('建议使用包管理器安装以获得更好的系统集成');
      recommendations.push('确保有sudo权限进行系统级安装');
      recommendations.push('检查防火墙设置，确保MySQL端口可访问');
      break;

    default:
      recommendations.push('请确认您的操作系统支持MySQL 8.0');
      break;
  }

  return recommendations;
}

/**
 * 验证安装路径的有效性
 */
export function validateInstallationPath(path: string, os: SupportedOS): {
  isValid: boolean;
  error?: string;
  suggestion?: string;
} {
  if (!path || path.trim().length === 0) {
    return {
      isValid: false,
      error: '安装路径不能为空',
      suggestion: `建议使用默认路径: ${getDefaultInstallPath(os)}`
    };
  }

  // Windows路径验证
  if (os === SupportedOS.WINDOWS) {
    if (!/^[A-Za-z]:\\/.test(path)) {
      return {
        isValid: false,
        error: 'Windows路径必须以盘符开头（如C:\\）',
        suggestion: '使用类似 C:\\MySQL\\8.0 的格式'
      };
    }

    if (path.includes('/')) {
      return {
        isValid: false,
        error: 'Windows路径应使用反斜杠（\\）',
        suggestion: '将路径中的 / 替换为 \\'
      };
    }
  }

  // Unix-like系统路径验证
  if (os === SupportedOS.MACOS || os === SupportedOS.LINUX) {
    if (!path.startsWith('/')) {
      return {
        isValid: false,
        error: 'Unix路径必须以 / 开头',
        suggestion: '使用绝对路径，如 /usr/local/mysql'
      };
    }

    if (path.includes('\\')) {
      return {
        isValid: false,
        error: 'Unix路径不应包含反斜杠（\\）',
        suggestion: '使用正斜杠（/）分隔路径'
      };
    }
  }

  // 检查路径长度
  if (path.length > 260) {
    return {
      isValid: false,
      error: '路径过长，可能导致安装问题',
      suggestion: '使用较短的路径名'
    };
  }

  // 检查特殊字符
  const invalidChars = /[<>:"|?*]/;
  if (invalidChars.test(path)) {
    return {
      isValid: false,
      error: '路径包含无效字符',
      suggestion: '避免使用 < > : " | ? * 等特殊字符'
    };
  }

  return { isValid: true };
}