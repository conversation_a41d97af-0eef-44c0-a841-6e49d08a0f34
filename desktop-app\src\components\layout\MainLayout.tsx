/**
 * MySQL安装器主布局组件
 * 简化的左右分栏布局，参考Python版本设计
 */

import React from 'react';
import TitleBar from './TitleBar';
import ConfigPanel from '../panels/ConfigPanel';
import InstallPanel from '../panels/InstallPanel';

export default function MainLayout() {
  return (
    <div className="flex flex-col h-screen bg-gray-50 desktop-app">
      {/* 自定义标题栏 */}
      <TitleBar />

      {/* 主内容区域 - 左右分栏布局 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧配置面板 */}
        <div className="w-1/2 border-r border-gray-200">
          <ConfigPanel />
        </div>

        {/* 右侧安装面板 */}
        <div className="w-1/2">
          <InstallPanel />
        </div>
      </div>
    </div>
  );
}