// MySQLAi.de - 知识库数据管理
// 提供知识库相关的数据结构、类型定义和数据访问函数
// 所有数据完全来自数据库，不再使用任何硬编码数据

import { KnowledgeCategory, KnowledgeItem } from '@/lib/types';
import { articlesApi, categoriesApi } from '@/lib/api/knowledge';
import { mapDatabaseArticlesToFrontend } from '@/lib/utils';

// 数据访问函数

/**
 * 获取热门知识点（基于相关项目数量）
 * 完全使用API调用获取真实数据，不再使用硬编码降级
 */
export async function getPopularKnowledgeItems(maxItems: number = 6): Promise<KnowledgeItem[]> {
  try {
    const response = await articlesApi.getAll({
      includeCodeExamples: true,
      includeRelated: true
    });

    if (response.success && response.data) {
      return response.data
        .sort((a, b) => a.order_index - b.order_index)
        .slice(0, maxItems);
    }
  } catch (error) {
    console.error('获取热门知识点失败:', error);
  }

  // 如果API调用失败，返回空数组（不再使用硬编码降级）
  return [];
}

/**
 * 获取最近更新的知识点
 * 完全使用API调用获取真实数据，不再使用硬编码降级
 */
export async function getRecentKnowledgeItems(maxItems: number = 6): Promise<KnowledgeItem[]> {
  try {
    const response = await articlesApi.getAll({
      includeCodeExamples: true,
      includeRelated: true
    });

    if (response.success && response.data) {
      return response.data
        .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())
        .slice(0, maxItems);
    }
  } catch (error) {
    console.error('获取最近更新知识点失败:', error);
  }

  // 如果API调用失败，返回空数组
  return [];
}

/**
 * 获取知识库分类
 * 完全使用API调用获取真实数据，不再使用硬编码降级
 */
export async function getKnowledgeCategories(): Promise<KnowledgeCategory[]> {
  try {
    const response = await categoriesApi.getAll(true);

    if (response.success && response.data) {
      return response.data
        .sort((a, b) => a.order_index - b.order_index);
    }
  } catch (error) {
    console.error('获取知识分类失败:', error);
  }

  // 如果API调用失败，返回空数组（不再使用硬编码降级）
  return [];
}

/**
 * 根据ID获取知识点
 * 完全使用API调用获取真实数据
 */
export async function getKnowledgeItem(itemId: string): Promise<KnowledgeItem | null> {
  try {
    const response = await articlesApi.getById(itemId, {
      includeCodeExamples: true,
      includeRelated: true
    });

    if (response.success && response.data) {
      const frontendArticles = mapDatabaseArticlesToFrontend([response.data]);
      return frontendArticles[0] || null;
    }
  } catch (error) {
    console.error('获取知识点失败:', error);
  }

  return null;
}

/**
 * 根据分类ID获取知识点列表
 * 完全使用API调用获取真实数据
 */
export async function getKnowledgeItemsByCategory(categoryId: string): Promise<KnowledgeItem[]> {
  try {
    const response = await articlesApi.getAll({
      category: categoryId,
      includeCodeExamples: true,
      includeRelated: true
    });

    if (response.success && response.data) {
      return response.data.sort((a, b) => a.order_index - b.order_index);
    }
  } catch (error) {
    console.error('获取分类知识点失败:', error);
  }

  return [];
}

/**
 * 搜索知识点
 * 完全使用API调用获取真实数据
 */
export async function searchKnowledgeItems(query: string): Promise<KnowledgeItem[]> {
  try {
    const response = await articlesApi.search(query);

    if (response.success && response.data) {
      return response.data;
    }
  } catch (error) {
    console.error('搜索知识点失败:', error);
  }

  return [];
}

/**
 * 获取相关知识点
 * 基于当前知识点的标签和分类获取相关内容
 */
export async function getRelatedKnowledgeItems(currentItem: KnowledgeItem, maxItems: number = 5): Promise<KnowledgeItem[]> {
  try {
    // 基于分类获取相关文章
    const response = await articlesApi.getAll({
      category: currentItem.category_id || undefined,
      includeCodeExamples: true,
      includeRelated: true
    });

    if (response.success && response.data) {
      return response.data
        .filter(item => item.id !== currentItem.id) // 排除当前文章
        .slice(0, maxItems);
    }
  } catch (error) {
    console.error('获取相关知识点失败:', error);
  }

  return [];
}

/**
 * 获取所有标签
 * 从API获取所有文章并提取标签
 */
export async function getAllTags(): Promise<string[]> {
  try {
    const response = await articlesApi.getAll();

    if (response.success && response.data) {
      const allTags = response.data.flatMap(item => item.tags || []);
      return Array.from(new Set(allTags)).sort();
    }
  } catch (error) {
    console.error('获取标签失败:', error);
  }

  return [];
}

/**
 * 获取所有知识点
 * 完全使用API调用获取真实数据
 */
export async function getKnowledgeItems(): Promise<KnowledgeItem[]> {
  try {
    const response = await articlesApi.getAll();

    if (response.success && response.data) {
      return response.data.sort((a, b) => a.order_index - b.order_index);
    }
  } catch (error) {
    console.error('获取知识点失败:', error);
  }

  return [];
}

/**
 * 根据标签获取知识点
 * 完全使用API调用获取真实数据
 */
export async function getKnowledgeItemsByTag(tag: string): Promise<KnowledgeItem[]> {
  try {
    // 使用搜索API查找包含特定标签的文章
    const response = await articlesApi.search(tag);

    if (response.success && response.data) {
      // 过滤出真正包含该标签的文章
      return response.data.filter(item =>
        item.tags?.some(itemTag => itemTag.toLowerCase().includes(tag.toLowerCase()))
      );
    }
  } catch (error) {
    console.error('根据标签获取知识点失败:', error);
  }

  return [];
}

/**
 * 获取知识库统计信息
 * 完全使用API调用获取真实数据
 */
export async function getKnowledgeStats() {
  try {
    const [categoriesResponse, articlesResponse] = await Promise.all([
      categoriesApi.getAll(true),
      articlesApi.getAll({ includeCodeExamples: false, includeRelated: false })
    ]);

    const categoriesCount = categoriesResponse.success ? categoriesResponse.data?.length || 0 : 0;
    const articlesCount = articlesResponse.success ? articlesResponse.data?.length || 0 : 0;

    return {
      categoriesCount,
      articlesCount,
      totalItems: articlesCount
    };
  } catch (error) {
    console.error('获取知识库统计失败:', error);
    return {
      categoriesCount: 0,
      articlesCount: 0,
      totalItems: 0
    };
  }
}

// 兼容性函数 - 为了保持向后兼容

/**
 * 根据ID获取知识点（兼容性函数）
 */
export async function getKnowledgeItemById(itemId: string): Promise<KnowledgeItem | null> {
  return getKnowledgeItem(itemId);
}

/**
 * 获取知识库分类（兼容性函数）
 */
export async function getKnowledgeCategory(categoryId: string): Promise<KnowledgeCategory | null> {
  try {
    const categories = await getKnowledgeCategories();
    return categories.find(category => category.id === categoryId) || null;
  } catch (error) {
    console.error('获取知识分类失败:', error);
    return null;
  }
}
