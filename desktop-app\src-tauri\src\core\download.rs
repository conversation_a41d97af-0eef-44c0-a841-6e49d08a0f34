use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex, Once};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::fs::File;
use tokio::io::AsyncWriteExt;
use futures_util::StreamExt;
use reqwest::Client;
use sha2::{Digest, Sha256};

/// 下载管理器常量
const SPEED_TEST_SIZE: usize = 1024 * 1024; // 1MB
const SPEED_TEST_TIMEOUT: u64 = 10; // 10秒
const CACHE_VALIDITY_PERIOD: u64 = 1800; // 30分钟
const MAX_RETRY_COUNT: u32 = 5;
const CHUNK_SIZE: usize = 8192; // 8KB

/// 下载源信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DownloadSource {
    pub name: String,
    pub url: String,
    pub region: String,
    pub priority: u32,
    pub available: bool,
    pub speed: Option<f64>, // KB/s
}

/// MySQL包信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MySQLPackage {
    pub version: String,
    pub os: String,
    pub architecture: String,
    pub filename: String,
    pub file_size: u64,
    pub download_sources: Vec<DownloadSource>,
    pub checksum: Option<String>,
    pub release_date: String,
}

/// 下载进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DownloadProgress {
    pub downloaded: u64,
    pub total: u64,
    pub speed: f64, // KB/s
    pub eta: Option<u64>, // 预计剩余时间（秒）
    pub percentage: f64,
    pub status: DownloadStatus,
}

/// 下载状态
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

/// 地理位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocationInfo {
    pub country: String,
    pub region: String,
    pub ip: String,
    pub is_china: bool,
}

/// 下载源缓存
#[derive(Debug)]
struct DownloadSourceCache {
    best_source: Option<DownloadSource>,
    sources_speed: HashMap<String, f64>,
    location_info: Option<LocationInfo>,
    timestamp: u64,
}

/// 智能下载管理器
pub struct DownloadManager {
    client: Client,
    cache: Arc<Mutex<DownloadSourceCache>>,
    mysql_packages: HashMap<String, MySQLPackage>,
}

static mut DOWNLOAD_MANAGER: Option<DownloadManager> = None;
static INIT: Once = Once::new();

impl DownloadManager {
    /// 获取全局下载管理器实例
    pub fn instance() -> &'static mut DownloadManager {
        unsafe {
            INIT.call_once(|| {
                DOWNLOAD_MANAGER = Some(DownloadManager::new());
            });
            DOWNLOAD_MANAGER.as_mut().unwrap()
        }
    }

    /// 创建新的下载管理器
    fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(60))
            .user_agent("MySQLAi-Desktop/1.0")
            .build()
            .expect("Failed to create HTTP client");

        let cache = Arc::new(Mutex::new(DownloadSourceCache {
            best_source: None,
            sources_speed: HashMap::new(),
            location_info: None,
            timestamp: 0,
        }));

        let mut manager = Self {
            client,
            cache,
            mysql_packages: HashMap::new(),
        };

        // 初始化MySQL包信息
        manager.initialize_mysql_packages();

        manager
    }

    /// 初始化MySQL包信息
    fn initialize_mysql_packages(&mut self) {
        // MySQL 8.0.36 - Windows x64
        let mysql_8_0_36_win_x64 = MySQLPackage {
            version: "8.0.36".to_string(),
            os: "windows".to_string(),
            architecture: "x64".to_string(),
            filename: "mysql-8.0.36-winx64.zip".to_string(),
            file_size: 398458880, // 约380MB
            download_sources: vec![
                DownloadSource {
                    name: "MySQL官方档案".to_string(),
                    url: "https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.36-winx64.zip".to_string(),
                    region: "global".to_string(),
                    priority: 1,
                    available: true,
                    speed: None,
                },
                DownloadSource {
                    name: "阿里云镜像".to_string(),
                    url: "https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.36-winx64.zip".to_string(),
                    region: "china".to_string(),
                    priority: 2,
                    available: true,
                    speed: None,
                },
                DownloadSource {
                    name: "清华大学镜像".to_string(),
                    url: "https://mirrors.tuna.tsinghua.edu.cn/mysql/downloads/MySQL-8.0/mysql-8.0.36-winx64.zip".to_string(),
                    region: "china".to_string(),
                    priority: 3,
                    available: true,
                    speed: None,
                },
            ],
            checksum: Some("sha256:...".to_string()),
            release_date: "2024-01-16".to_string(),
        };

        // MySQL 8.0.28 - Windows x64
        let mysql_8_0_28_win_x64 = MySQLPackage {
            version: "8.0.28".to_string(),
            os: "windows".to_string(),
            architecture: "x64".to_string(),
            filename: "mysql-8.0.28-winx64.zip".to_string(),
            file_size: 378458880, // 约361MB
            download_sources: vec![
                DownloadSource {
                    name: "MySQL官方档案".to_string(),
                    url: "https://downloads.mysql.com/archives/get/p/23/file/mysql-8.0.28-winx64.zip".to_string(),
                    region: "global".to_string(),
                    priority: 1,
                    available: true,
                    speed: None,
                },
                DownloadSource {
                    name: "阿里云镜像".to_string(),
                    url: "https://mirrors.aliyun.com/mysql/MySQL-8.0/mysql-8.0.28-winx64.zip".to_string(),
                    region: "china".to_string(),
                    priority: 2,
                    available: true,
                    speed: None,
                },
            ],
            checksum: Some("sha256:...".to_string()),
            release_date: "2022-01-18".to_string(),
        };

        // 添加到包映射
        self.mysql_packages.insert("8.0.36-windows-x64".to_string(), mysql_8_0_36_win_x64);
        self.mysql_packages.insert("8.0.28-windows-x64".to_string(), mysql_8_0_28_win_x64);
    }

    /// 检测用户地理位置
    pub async fn detect_user_location(&self) -> Result<LocationInfo, Box<dyn std::error::Error>> {
        // 检查缓存
        {
            let cache = self.cache.lock().unwrap();
            if let Some(ref location) = cache.location_info {
                let current_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                if current_time - cache.timestamp < CACHE_VALIDITY_PERIOD {
                    return Ok(location.clone());
                }
            }
        }

        // 尝试多个IP地理位置服务
        let services = vec![
            "http://ip-api.com/json/",
            "https://ipapi.co/json/",
            "https://ipinfo.io/json",
        ];

        for service_url in services {
            if let Ok(response) = self.client.get(service_url).send().await {
                if let Ok(data) = response.json::<serde_json::Value>().await {
                    let country = data["country"].as_str().unwrap_or("Unknown").to_string();
                    let region = if country == "CN" || country == "China" {
                        "china".to_string()
                    } else {
                        "global".to_string()
                    };
                    let ip = data["query"]
                        .as_str()
                        .or_else(|| data["ip"].as_str())
                        .unwrap_or("Unknown")
                        .to_string();
                    let is_china = region == "china";

                    let location_info = LocationInfo {
                        country,
                        region,
                        ip,
                        is_china,
                    };

                    // 更新缓存
                    {
                        let mut cache = self.cache.lock().unwrap();
                        cache.location_info = Some(location_info.clone());
                        cache.timestamp = SystemTime::now()
                            .duration_since(UNIX_EPOCH)
                            .unwrap()
                            .as_secs();
                    }

                    return Ok(location_info);
                }
            }
        }

        // 如果所有服务都失败，返回默认值
        Ok(LocationInfo {
            country: "Unknown".to_string(),
            region: "global".to_string(),
            ip: "Unknown".to_string(),
            is_china: false,
        })
    }

    /// 测试下载源速度
    pub async fn test_download_speed(&self, url: &str) -> f64 {
        let start_time = SystemTime::now();

        // 检查服务器是否支持Range请求
        match self.client.head(url).send().await {
            Ok(response) => {
                let supports_range = response
                    .headers()
                    .get("accept-ranges")
                    .and_then(|v| v.to_str().ok())
                    .map(|v| v == "bytes")
                    .unwrap_or(false);

                // 测试下载速度
                let request = if supports_range {
                    // 如果支持Range请求，只下载前1MB数据
                    self.client
                        .get(url)
                        .header("Range", format!("bytes=0-{}", SPEED_TEST_SIZE - 1))
                } else {
                    // 如果不支持Range请求，使用普通GET请求
                    self.client.get(url)
                };

                match request.send().await {
                    Ok(response) => {
                        let mut downloaded_size = 0;
                        let mut stream = response.bytes_stream();

                        while let Some(chunk) = stream.next().await {
                            if let Ok(chunk) = chunk {
                                downloaded_size += chunk.len();
                                if downloaded_size >= SPEED_TEST_SIZE {
                                    break;
                                }
                            }
                        }

                        let elapsed = start_time.elapsed().unwrap_or(Duration::from_secs(1));
                        let speed = (downloaded_size as f64) / (elapsed.as_secs_f64() * 1024.0); // KB/s
                        
                        log::info!("测试下载速度: {} -> {:.2} KB/s", url, speed);
                        speed
                    }
                    Err(e) => {
                        log::warn!("测试下载速度失败: {} -> {}", url, e);
                        0.0
                    }
                }
            }
            Err(e) => {
                log::warn!("HEAD请求失败: {} -> {}", url, e);
                0.0
            }
        }
    }

    /// 选择最佳下载源
    pub async fn select_best_source(&self, sources: &[DownloadSource]) -> Result<DownloadSource, Box<dyn std::error::Error>> {
        // 检查缓存
        {
            let cache = self.cache.lock().unwrap();
            if let Some(ref best_source) = cache.best_source {
                let current_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                if current_time - cache.timestamp < CACHE_VALIDITY_PERIOD {
                    if sources.iter().any(|s| s.url == best_source.url) {
                        log::info!("使用缓存的最佳下载源: {}", best_source.name);
                        return Ok(best_source.clone());
                    }
                }
            }
        }

        // 获取地理位置信息
        let location_info = self.detect_user_location().await?;
        log::info!("检测到用户位置: {} ({})", location_info.country, location_info.region);

        // 根据地区筛选下载源
        let filtered_sources: Vec<_> = if location_info.is_china {
            // 中国用户优先使用中国区域的下载源
            let china_sources: Vec<_> = sources.iter().filter(|s| s.region == "china").collect();
            if !china_sources.is_empty() {
                china_sources.into_iter().cloned().collect()
            } else {
                sources.to_vec()
            }
        } else {
            // 国际用户优先使用全球区域的下载源
            let global_sources: Vec<_> = sources.iter().filter(|s| s.region == "global").collect();
            if !global_sources.is_empty() {
                global_sources.into_iter().cloned().collect()
            } else {
                sources.to_vec()
            }
        };

        // 测试下载源速度
        let mut sources_with_speed = Vec::new();
        for source in filtered_sources {
            // 检查缓存中是否有该下载源的速度
            let cached_speed = {
                let cache = self.cache.lock().unwrap();
                cache.sources_speed.get(&source.url).copied()
            };

            let speed = if let Some(cached_speed) = cached_speed {
                log::info!("使用缓存的下载速度: {} -> {:.2} KB/s", source.name, cached_speed);
                cached_speed
            } else {
                let speed = self.test_download_speed(&source.url).await;
                // 更新缓存
                {
                    let mut cache = self.cache.lock().unwrap();
                    cache.sources_speed.insert(source.url.clone(), speed);
                }
                speed
            };

            // 计算综合得分（速度 * 优先级）
            let score = speed * (100.0 - source.priority as f64);
            let mut source_with_speed = source.clone();
            source_with_speed.speed = Some(speed);

            sources_with_speed.push((source_with_speed, score));
        }

        // 按综合得分排序
        sources_with_speed.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // 选择得分最高的下载源
        if let Some((best_source, score)) = sources_with_speed.first() {
            log::info!(
                "选择最佳下载源: {} -> 速度: {:.2} KB/s, 得分: {:.2}",
                best_source.name,
                best_source.speed.unwrap_or(0.0),
                score
            );

            // 更新缓存
            {
                let mut cache = self.cache.lock().unwrap();
                cache.best_source = Some(best_source.clone());
                cache.timestamp = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
            }

            Ok(best_source.clone())
        } else {
            // 如果没有可用的下载源，返回第一个下载源
            log::warn!("没有可用的下载源，使用第一个下载源");
            Ok(sources[0].clone())
        }
    }

    /// 下载文件
    pub async fn download_file<F>(
        &self,
        url: &str,
        file_path: &Path,
        progress_callback: F,
    ) -> Result<(), Box<dyn std::error::Error>>
    where
        F: Fn(DownloadProgress) + Send + Sync,
    {
        // 创建目录
        if let Some(parent) = file_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        // 检查是否支持断点续传
        let mut start_pos = 0u64;
        if file_path.exists() {
            let metadata = tokio::fs::metadata(file_path).await?;
            start_pos = metadata.len();
        }

        // 发送请求
        let mut request = self.client.get(url);
        if start_pos > 0 {
            request = request.header("Range", format!("bytes={}-", start_pos));
        }

        let response = request.send().await?;
        let total_size = response.content_length().unwrap_or(0) + start_pos;

        // 打开文件
        let mut file = if start_pos > 0 {
            File::options().append(true).open(file_path).await?
        } else {
            File::create(file_path).await?
        };

        // 下载文件
        let mut downloaded = start_pos;
        let mut stream = response.bytes_stream();
        let start_time = SystemTime::now();

        while let Some(chunk) = stream.next().await {
            let chunk = chunk?;
            file.write_all(&chunk).await?;
            downloaded += chunk.len() as u64;

            // 计算下载速度和ETA
            let elapsed = start_time.elapsed().unwrap_or(Duration::from_secs(1));
            let speed = (downloaded - start_pos) as f64 / (elapsed.as_secs_f64() * 1024.0); // KB/s
            let eta = if speed > 0.0 {
                Some(((total_size - downloaded) as f64 / (speed * 1024.0)) as u64)
            } else {
                None
            };

            // 调用进度回调
            let progress = DownloadProgress {
                downloaded,
                total: total_size,
                speed,
                eta,
                percentage: if total_size > 0 {
                    (downloaded as f64 / total_size as f64) * 100.0
                } else {
                    0.0
                },
                status: DownloadStatus::Downloading,
            };

            progress_callback(progress);
        }

        file.flush().await?;
        
        // 下载完成回调
        let final_progress = DownloadProgress {
            downloaded,
            total: total_size,
            speed: 0.0,
            eta: Some(0),
            percentage: 100.0,
            status: DownloadStatus::Completed,
        };
        progress_callback(final_progress);

        Ok(())
    }

    /// 带重试的下载文件
    pub async fn download_with_retry<F>(
        &self,
        url: &str,
        file_path: &Path,
        progress_callback: F,
    ) -> Result<(), Box<dyn std::error::Error>>
    where
        F: Fn(DownloadProgress) + Send + Sync + Clone,
    {
        let mut last_error = None;

        for attempt in 1..=MAX_RETRY_COUNT {
            log::info!("尝试下载 {} (尝试 {}/{})", url, attempt, MAX_RETRY_COUNT);

            match self.download_file(url, file_path, progress_callback.clone()).await {
                Ok(_) => {
                    log::info!("下载成功: {}", url);
                    return Ok(());
                }
                Err(e) => {
                    log::warn!("下载失败 (尝试 {}/{}): {}", attempt, MAX_RETRY_COUNT, e);
                    last_error = Some(e);

                    // 如果不是最后一次尝试，等待一段时间再重试
                    if attempt < MAX_RETRY_COUNT {
                        let retry_delay = std::cmp::min(30, 2_u64.pow(attempt - 1));
                        log::info!("等待 {} 秒后重试...", retry_delay);
                        tokio::time::sleep(Duration::from_secs(retry_delay)).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| "所有重试都失败".into()))
    }

    /// 获取MySQL包信息
    pub fn get_mysql_package(&self, version: &str, os: &str, architecture: &str) -> Option<&MySQLPackage> {
        let key = format!("{}-{}-{}", version, os, architecture);
        self.mysql_packages.get(&key)
    }

    /// 下载MySQL安装包
    pub async fn download_mysql<F>(
        &self,
        version: &str,
        os: &str,
        architecture: &str,
        download_dir: &Path,
        progress_callback: F,
    ) -> Result<PathBuf, Box<dyn std::error::Error>>
    where
        F: Fn(DownloadProgress) + Send + Sync + Clone,
    {
        // 获取MySQL包信息
        let package = self.get_mysql_package(version, os, architecture)
            .ok_or_else(|| format!("不支持的MySQL版本: {}-{}-{}", version, os, architecture))?;

        // 选择最佳下载源
        let best_source = self.select_best_source(&package.download_sources).await?;
        log::info!("使用下载源: {} ({})", best_source.name, best_source.url);

        // 构建下载路径
        let download_path = download_dir.join(&package.filename);

        // 下载文件
        self.download_with_retry(&best_source.url, &download_path, progress_callback).await?;

        // 验证文件大小
        let metadata = tokio::fs::metadata(&download_path).await?;
        if metadata.len() != package.file_size {
            return Err(format!(
                "文件大小不匹配: 期望 {} 字节，实际 {} 字节",
                package.file_size,
                metadata.len()
            ).into());
        }

        log::info!("MySQL {} 下载完成: {:?}", version, download_path);
        Ok(download_path)
    }

    /// 验证文件校验和
    pub async fn verify_checksum(&self, file_path: &Path, expected_checksum: &str) -> Result<bool, Box<dyn std::error::Error>> {
        if !expected_checksum.starts_with("sha256:") {
            return Err("不支持的校验和格式".into());
        }

        let expected_hash = &expected_checksum[7..]; // 移除 "sha256:" 前缀
        
        let file_content = tokio::fs::read(file_path).await?;
        let mut hasher = Sha256::new();
        hasher.update(&file_content);
        let actual_hash = format!("{:x}", hasher.finalize());

        Ok(actual_hash == expected_hash)
    }

    /// 获取支持的MySQL版本列表
    pub fn get_supported_versions(&self) -> Vec<(String, String, String)> {
        self.mysql_packages
            .keys()
            .map(|key| {
                let parts: Vec<&str> = key.split('-').collect();
                (parts[0].to_string(), parts[1].to_string(), parts[2].to_string())
            })
            .collect()
    }
}

/// 全局下载管理函数

/// 获取下载管理器实例
pub fn get_download_manager() -> &'static mut DownloadManager {
    DownloadManager::instance()
}

/// 检测用户地理位置
pub async fn detect_user_location() -> Result<LocationInfo, Box<dyn std::error::Error>> {
    let manager = get_download_manager();
    manager.detect_user_location().await
}

/// 获取MySQL包信息
pub fn get_mysql_package(version: &str, os: &str, architecture: &str) -> Option<MySQLPackage> {
    let manager = get_download_manager();
    manager.get_mysql_package(version, os, architecture).cloned()
}

/// 下载MySQL安装包
pub async fn download_mysql<F>(
    version: &str,
    os: &str,
    architecture: &str,
    download_dir: &Path,
    progress_callback: F,
) -> Result<PathBuf, Box<dyn std::error::Error>>
where
    F: Fn(DownloadProgress) + Send + Sync + Clone,
{
    let manager = get_download_manager();
    manager.download_mysql(version, os, architecture, download_dir, progress_callback).await
}

/// 获取支持的MySQL版本列表
pub fn get_supported_mysql_versions() -> Vec<(String, String, String)> {
    let manager = get_download_manager();
    manager.get_supported_versions()
}