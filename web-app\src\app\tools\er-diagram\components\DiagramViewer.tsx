'use client';

/**
 * ER图显示组件 - ER图生成工具
 * 集成GoJS图形库，实现图形渲染、数据更新、交互操作和视图控制功能
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  ZoomIn,
  ZoomOut,
  Maximize,
  RotateCcw,
  Download,
  Loader2,
  AlertCircle
} from 'lucide-react';
import * as go from 'gojs';
import { cn } from '@/lib/utils';
import type { DiagramViewerProps, TableInfo, RelationshipType, ForeignKeyInfo } from '../types/er-diagram';
import {
  createDiagramConfig,
  configureDiagramTemplates,
  applyThemeStyles,
  NODE_TYPES
} from '../lib/diagram-config';

/**
 * ER图显示组件 - 使用React.memo优化性能
 */
const DiagramViewer = React.memo(React.forwardRef<HTMLDivElement, DiagramViewerProps>(({
  tableData = [],
  isLoading = false,
  error = null,
  onDiagramUpdate,
  className,
  ...props
}, ref) => {
  const diagramRef = useRef<HTMLDivElement>(null);
  const diagramInstanceRef = useRef<go.Diagram | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);

  // 使用useMemo缓存图表统计信息计算结果
  /*
  const diagramStats = useMemo(() => {
    const entityTables = tableData.filter(table => {
      const foreignKeyCount = table.foreignKeys?.length || 0;
      const totalColumns = table.columns.length;
      return !(foreignKeyCount >= 2 && foreignKeyCount >= totalColumns * 0.5);
    });

    const relationshipTables = tableData.filter(table => {
      const foreignKeyCount = table.foreignKeys?.length || 0;
      const totalColumns = table.columns.length;
      return foreignKeyCount >= 2 && foreignKeyCount >= totalColumns * 0.5;
    });

    return {
      totalTables: tableData.length,
      entityTables: entityTables.length,
      relationshipTables: relationshipTables.length,
      totalColumns: tableData.reduce((sum, table) => sum + table.columns.length, 0),
      totalForeignKeys: tableData.reduce((sum, table) => sum + (table.foreignKeys?.length || 0), 0)
    };
  }, [tableData]);
  */

  /**
   * 清理GoJS图形
   */
  /*
  const cleanupDiagram = useCallback(() => {
    if (diagramInstanceRef.current) {
      try {
        // 正确清理GoJS图形实例
        diagramInstanceRef.current.div = null;
        diagramInstanceRef.current = null;
      } catch (err) {
        console.error('清理GoJS图形失败:', err);
      }
      setIsInitialized(false);
      console.log('GoJS图形已清理');
    }

    // 清理div内容，确保没有残留的GoJS元素
    if (diagramRef.current) {
      diagramRef.current.innerHTML = '';
    }
  }, []);
  */

  /**
   * IntersectionObserver延迟初始化 - 性能优化
   */
  useEffect(() => {
    if (!diagramRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect(); // 只需要观察一次
        }
      },
      {
        threshold: 0.1, // 当10%的区域可见时触发
        rootMargin: '50px' // 提前50px开始加载
      }
    );

    observer.observe(diagramRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  /**
   * 初始化GoJS图形
   */
  const initializeDiagram = useCallback(() => {
    if (!diagramRef.current || isInitialized) return;

    // 不要在这里清理图形，避免无限循环
    // if (diagramInstanceRef.current) {
    //   cleanupDiagram();
    // }

    try {
      // 清除之前的错误状态
      setInitError(null);

      const $ = go.GraphObject.make;

      // 确保div元素存在且没有被其他图形占用
      const divElement = diagramRef.current;
      if (!divElement) {
        throw new Error('Canvas div element not found');
      }

      // 清理div的任何现有内容
      divElement.innerHTML = '';

      // 创建GoJS图形实例并应用Chen ER图配置
      const diagram = $(go.Diagram, divElement, createDiagramConfig());

      // 配置Chen ER图标准模板
      configureDiagramTemplates(diagram);

      // 应用Chen ER图主题样式
      applyThemeStyles(diagram);

      // 监听缩放变化
      diagram.addDiagramListener('ViewportBoundsChanged', () => {
        setZoomLevel(diagram.scale);
      });

      // 监听选择变化
      diagram.addDiagramListener('ChangedSelection', () => {
        const selectedNodes = diagram.selection.count;
        console.log(`选中节点数: ${selectedNodes}`);
      });

      diagramInstanceRef.current = diagram;
      setIsInitialized(true);

      console.log('GoJS图形初始化成功');

      // 在开发模式下测试实体拖动功能
      if (process.env.NODE_ENV === 'development') {
        // 延迟测试，确保图形完全初始化
        setTimeout(async () => {
          try {
            const { testEntityDragBehavior } = await import('../lib/diagram-config');
            const testResult = testEntityDragBehavior(diagram);
            console.log('ER图拖动功能测试:', testResult.message);

            if (!testResult.success) {
              console.warn('ER图拖动功能测试详情:', testResult.details);
            }
          } catch (error) {
            console.warn('ER图拖动功能测试失败:', error);
          }
        }, 1000);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'GoJS图形初始化失败';
      console.error('GoJS图形初始化失败:', err);
      setInitError(errorMessage);
      setIsInitialized(false);
    }
  }, []); // 移除cleanupDiagram依赖，避免无限循环

  /**
   * 分析关系类型
   */
  const analyzeRelationshipType = (fk: ForeignKeyInfo, table: TableInfo): RelationshipType => {
    const fkColumn = table.columns.find(col => col.name === fk.columnName);
    if (!fkColumn) return '1:N'; // 默认为1:N

    return fkColumn.isUnique ? '1:1' : '1:N';
  };

  /**
   * 分析实体间直接关系
   */
  const analyzeDirectRelationships = (entityTables: TableInfo[]): Array<{
    fromTable: string;
    toTable: string;
    relationshipType: RelationshipType;
    foreignKey: ForeignKeyInfo;
  }> => {
    const relationships: Array<{
      fromTable: string;
      toTable: string;
      relationshipType: RelationshipType;
      foreignKey: ForeignKeyInfo;
    }> = [];

    entityTables.forEach(table => {
      table.foreignKeys?.forEach(fk => {
        if (fk.referenceTable) {
          relationships.push({
            fromTable: fk.referenceTable,
            toTable: table.tableName,
            relationshipType: analyzeRelationshipType(fk, table),
            foreignKey: fk
          });
        }
      });
    });

    return relationships;
  };

  /**
   * 转换表数据为GoJS模型数据 - Chen ER图标准
   */
  const convertTableDataToModel = useCallback((tables: TableInfo[]) => {
    const nodeDataArray: go.ObjectData[] = [];
    const linkDataArray: go.ObjectData[] = [];

    /**
     * 生成关系名称 - 基于外键列名进行智能语义分析
     * 根据外键列名中的关键词生成符合业务逻辑的中文关系名称
     */
    const generateRelationshipName = (rel: {
      fromTable: string;
      toTable: string;
      relationshipType: RelationshipType;
      foreignKey: ForeignKeyInfo;
    }): string => {
      const fkColumnName = rel.foreignKey.columnName.toLowerCase();

      if (rel.relationshipType === '1:1') {
        return '对应';
      } else if (rel.relationshipType === '1:N') {
        if (fkColumnName.includes('category')) return '分类';
        if (fkColumnName.includes('user') || fkColumnName.includes('managed')) return '管理';
        if (fkColumnName.includes('parent')) return '包含';
        return '关联';
      }
      return '关系';
    };

    /**
     * 获取基数标注 - 根据关系类型返回正确的基数标注对
     * 返回一个包含两个字符串的元组，分别表示关系两端的基数
     */
    const getCardinalityLabels = (relationshipType: RelationshipType): [string, string] => {
      switch (relationshipType) {
        case '1:1': return ['1', '1'];
        case '1:N': return ['1', 'N'];
        case 'N:M': return ['N', 'M'];
        default: return ['1', 'N'];
      }
    };

    // 识别关系表的函数
    const isRelationshipTable = (table: TableInfo): boolean => {
      // 关系表的特征：
      // 1. 有两个或更多外键
      // 2. 大部分列都是外键（外键列数 >= 总列数的50%）
      const foreignKeyCount = table.foreignKeys?.length || 0;
      const totalColumns = table.columns.length;

      return foreignKeyCount >= 2 && foreignKeyCount >= totalColumns * 0.5;
    };

    // 分离实体表和关系表
    const entityTables = tables.filter(table => !isRelationshipTable(table));
    const relationshipTables = tables.filter(table => isRelationshipTable(table));

    console.log(`识别结果: ${entityTables.length}个实体表, ${relationshipTables.length}个关系表`);
    console.log('实体表列表:', entityTables.map(t => t.tableName));
    console.log('关系表列表:', relationshipTables.map(t => t.tableName));

    // 处理实体表
    entityTables.forEach(table => {
      console.log(`处理实体表: ${table.tableName}, 注释: "${table.comment}"`);

      // 添加实体节点 - 使用表注释作为显示名称
      const entityNode = {
        category: NODE_TYPES.ENTITY,
        key: table.tableName,
        name: table.comment || table.tableName
      };
      nodeDataArray.push(entityNode);
      console.log('添加实体节点:', entityNode);

      // 添加属性节点（排除外键列）
      table.columns.forEach(col => {
        // 检查是否为外键列
        const isForeignKey = table.foreignKeys?.some(fk => fk.columnName === col.name);
        console.log(`列 ${col.name}: 注释="${col.comment}", 是否外键=${isForeignKey}, 是否主键=${table.primaryKeys.includes(col.name)}`);

        // 只为非外键列创建属性节点
        if (!isForeignKey) {
          const attrKey = `${table.tableName}-${col.name}`;
          const attrNode = {
            category: NODE_TYPES.ATTRIBUTE,
            key: attrKey,
            name: col.comment || col.name, // 使用列注释作为显示名称
            isPrimaryKey: table.primaryKeys.includes(col.name)
          };
          nodeDataArray.push(attrNode);
          console.log('添加属性节点:', attrNode);

          // 添加属性与实体的连接 - Chen ER图标准：实体到属性不需要基数标注
          linkDataArray.push({
            from: table.tableName,
            to: attrKey
          });
        } else {
          console.log(`跳过外键列: ${col.name}`);
        }
      });
    });

    // 处理关系表
    relationshipTables.forEach(table => {
      console.log(`处理关系表: ${table.tableName}, 注释: "${table.comment}"`);
      console.log('关系表外键:', table.foreignKeys);

      // 创建关系节点 - 使用表注释作为显示名称
      const relationshipKey = `${table.tableName}-relationship`;
      const relationshipNode = {
        category: NODE_TYPES.RELATIONSHIP,
        key: relationshipKey,
        name: table.comment || table.tableName
      };
      nodeDataArray.push(relationshipNode);
      console.log('添加关系节点:', relationshipNode);

      // 添加关系的非外键属性
      table.columns.forEach(col => {
        const isForeignKey = table.foreignKeys?.some(fk => fk.columnName === col.name);
        console.log(`关系表列 ${col.name}: 注释="${col.comment}", 是否外键=${isForeignKey}`);

        // 只为非外键列创建属性节点
        if (!isForeignKey) {
          const attrKey = `${relationshipKey}-${col.name}`;
          const attrNode = {
            category: NODE_TYPES.ATTRIBUTE,
            key: attrKey,
            name: col.comment || col.name,
            isPrimaryKey: table.primaryKeys.includes(col.name)
          };
          nodeDataArray.push(attrNode);
          console.log('添加关系属性节点:', attrNode);

          // 添加属性与关系的连接 - Chen ER图标准：关系到属性不需要基数标注
          linkDataArray.push({
            from: relationshipKey,
            to: attrKey
          });
        } else {
          console.log(`跳过关系表外键列: ${col.name}`);
        }
      });

      // 连接关系到相关实体 - 添加Chen ER图标准N:M基数标注
      table.foreignKeys?.forEach((fk, index) => {
        if (fk.referenceTable) {
          // 根据外键顺序分配N:M基数标注
          // 第一个外键标注为"N"，第二个外键标注为"M"
          const cardinality = index === 0 ? 'N' : 'M';
          const entityToRelationLink = {
            from: fk.referenceTable,
            to: relationshipKey,
            text: cardinality // Chen ER图标准：N:M关系的正确基数标注
          };
          linkDataArray.push(entityToRelationLink);
          console.log(`添加实体-关系连接(${cardinality}):`, entityToRelationLink);
        }
      });
    });

    // 分析并添加实体间的直接关系（1:1和1:N关系）
    const directRelationships = analyzeDirectRelationships(entityTables);
    console.log(`分析到${directRelationships.length}个直接关系:`, directRelationships);

    directRelationships.forEach((rel, index) => {
      console.log(`添加${rel.relationshipType}关系: ${rel.fromTable} -> ${rel.toTable}`);

      // 1. 创建关系节点（菱形） - 复用现有模式
      const relationshipKey = `${rel.fromTable}-${rel.toTable}-relationship-${index}`;
      const relationshipNode = {
        category: NODE_TYPES.RELATIONSHIP,
        key: relationshipKey,
        name: generateRelationshipName(rel)
      };
      nodeDataArray.push(relationshipNode);
      console.log('添加关系节点:', relationshipNode);

      // 2. 创建带基数标注的连接线
      const [fromCardinality, toCardinality] = getCardinalityLabels(rel.relationshipType);

      // 实体到关系的连接线
      linkDataArray.push({
        from: rel.fromTable,
        to: relationshipKey,
        text: fromCardinality
      });

      // 关系到实体的连接线
      linkDataArray.push({
        from: relationshipKey,
        to: rel.toTable,
        text: toCardinality
      });

      console.log(`添加Chen标准关系连接: ${rel.fromTable}(${fromCardinality}) -> ${relationshipKey} -> ${rel.toTable}(${toCardinality})`);
    });

    console.log(`生成模型数据: ${nodeDataArray.length}个节点, ${linkDataArray.length}个连接`);
    console.log('节点数据示例:', nodeDataArray.slice(0, 3));
    console.log('连接数据示例:', linkDataArray.slice(0, 3));
    return { nodeDataArray, linkDataArray };
  }, []);

  /**
   * 更新图形数据
   */
  const updateDiagram = useCallback((tables: TableInfo[]) => {
    if (!diagramInstanceRef.current || !isInitialized) return;

    try {
      const { nodeDataArray, linkDataArray } = convertTableDataToModel(tables);
      
      // 更新模型数据
      diagramInstanceRef.current.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);
      
      // 重新布局
      diagramInstanceRef.current.layoutDiagram(true);
      
      // 适应画布
      setTimeout(() => {
        if (diagramInstanceRef.current) {
          diagramInstanceRef.current.zoomToFit();
          diagramInstanceRef.current.contentAlignment = go.Spot.Center;
        }
      }, 100);
      
      console.log(`图形更新完成: ${nodeDataArray.length}个节点, ${linkDataArray.length}个连接`);
    } catch (err) {
      console.error('更新图形失败:', err);
    }
  }, [isInitialized, convertTableDataToModel]);

  /**
   * 缩放控制
   */
  const handleZoom = useCallback((direction: 'in' | 'out') => {
    if (!diagramInstanceRef.current) return;
    
    const currentScale = diagramInstanceRef.current.scale;
    const factor = direction === 'in' ? 1.2 : 0.8;
    const newScale = Math.max(0.1, Math.min(3, currentScale * factor));
    
    diagramInstanceRef.current.scale = newScale;
  }, []);

  /**
   * 适应画布
   */
  const handleFitToCanvas = useCallback(() => {
    if (!diagramInstanceRef.current) return;
    diagramInstanceRef.current.zoomToFit();
  }, []);

  /**
   * 重置布局
   */
  const handleResetLayout = useCallback(() => {
    if (!diagramInstanceRef.current) return;
    diagramInstanceRef.current.layoutDiagram(true);
    setTimeout(() => {
      if (diagramInstanceRef.current) {
        diagramInstanceRef.current.zoomToFit();
      }
    }, 100);
  }, []);

  /**
   * 导出图形
   */
  const handleExport = useCallback(() => {
    if (onDiagramUpdate && diagramInstanceRef.current) {
      onDiagramUpdate(diagramInstanceRef.current);
    }
  }, [onDiagramUpdate]);

  // 初始化图形 - 只有当组件进入视口时才初始化
  useEffect(() => {
    // 只有当组件进入视口时才开始初始化
    if (!isInView || isInitialized) return;

    // 使用requestAnimationFrame避免阻塞主线程
    const initFrame = requestAnimationFrame(() => {
      // 延迟初始化，确保DOM元素已经渲染
      const timer = setTimeout(() => {
        if (diagramRef.current && !isInitialized) {
          const rect = diagramRef.current.getBoundingClientRect();
          console.log('图形容器尺寸:', { width: rect.width, height: rect.height });

          if (rect.width > 0 && rect.height > 0) {
            // 使用requestIdleCallback进一步优化，在浏览器空闲时初始化
            if ('requestIdleCallback' in window) {
              requestIdleCallback(() => {
                initializeDiagram();
              });
            } else {
              // 降级处理
              setTimeout(() => {
                initializeDiagram();
              }, 0);
            }
          } else {
            console.warn('图形容器尺寸为0，延迟重试...');
            // 多次重试，确保容器有正确尺寸
            const retryTimer = setTimeout(() => {
              if (diagramRef.current && !isInitialized) {
                const retryRect = diagramRef.current.getBoundingClientRect();
                console.log('重试图形容器尺寸:', { width: retryRect.width, height: retryRect.height });
                if (retryRect.width > 0 && retryRect.height > 0) {
                  initializeDiagram();
                }
              }
            }, 1000);

            return () => clearTimeout(retryTimer);
          }
        }
      }, 200);

      return () => clearTimeout(timer);
    });

    return () => {
      cancelAnimationFrame(initFrame);
    };
  }, [isInView, isInitialized, initializeDiagram]); // 依赖isInView状态

  // 暂时注释掉组件卸载时的清理，避免无限循环
  // useEffect(() => {
  //   return () => {
  //     if (diagramInstanceRef.current) {
  //       // 直接清理，不依赖cleanupDiagram函数
  //       try {
  //         diagramInstanceRef.current.div = null;
  //         diagramInstanceRef.current = null;
  //         console.log('GoJS图形已清理');
  //       } catch (err) {
  //         console.error('清理GoJS图形失败:', err);
  //       }
  //       setIsInitialized(false);
  //     }
  //   };
  // }, []); // 空依赖数组，只在组件卸载时执行

  // 监听数据变化
  useEffect(() => {
    console.log('DiagramViewer数据变化:', {
      tableDataLength: tableData.length,
      isInitialized,
      tableData: tableData.slice(0, 2) // 只显示前2个表的数据用于调试
    });

    if (tableData.length > 0 && isInitialized) {
      console.log('开始更新图形，表数量:', tableData.length);
      updateDiagram(tableData);
    } else if (tableData.length > 0 && !isInitialized) {
      console.log('图形未初始化，等待初始化完成...');
    } else if (tableData.length === 0 && isInitialized) {
      console.log('没有表数据，清空图形');
      // 清空图形
      if (diagramInstanceRef.current) {
        diagramInstanceRef.current.model = new go.GraphLinksModel([], []);
      }
    }
  }, [tableData, isInitialized]); // 移除updateDiagram依赖，避免无限循环

  // 容器样式
  const containerStyles = cn(
    'relative w-full bg-white overflow-hidden',
    'transition-all duration-300',
    'min-h-[400px] h-full', // 确保最小高度
    className
  );

  // 工具栏样式
  const toolbarStyles = cn(
    'absolute top-4 right-4 z-10',
    'flex items-center space-x-2 p-2',
    'bg-white/95 backdrop-blur-sm rounded-lg border border-mysql-border shadow-xl'
  );

  // 按钮样式
  const buttonStyles = cn(
    'flex items-center justify-center w-8 h-8 rounded-lg',
    'bg-white border border-mysql-border shadow-sm',
    'hover:bg-mysql-primary hover:text-white hover:border-mysql-primary hover:shadow-md hover:scale-105',
    'focus:outline-none focus:ring-2 focus:ring-mysql-primary/30',
    'transition-all duration-300 ease-out',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none',
    'active:scale-95'
  );

  return (
    <div
      ref={ref}
      className={containerStyles}
      {...props}
    >
      {/* 工具栏 */}
      <div className={toolbarStyles}>
        <button
          type="button"
          onClick={() => handleZoom('in')}
          className={buttonStyles}
          title="放大"
          disabled={isLoading || !isInitialized}
        >
          <ZoomIn className="w-4 h-4" />
        </button>

        <button
          type="button"
          onClick={() => handleZoom('out')}
          className={buttonStyles}
          title="缩小"
          disabled={isLoading || !isInitialized}
        >
          <ZoomOut className="w-4 h-4" />
        </button>

        <button
          type="button"
          onClick={handleFitToCanvas}
          className={buttonStyles}
          title="适应画布"
          disabled={isLoading || !isInitialized}
        >
          <Maximize className="w-4 h-4" />
        </button>

        <button
          type="button"
          onClick={handleResetLayout}
          className={buttonStyles}
          title="重置布局"
          disabled={isLoading || !isInitialized}
        >
          <RotateCcw className="w-4 h-4" />
        </button>

        {onDiagramUpdate && (
          <button
            type="button"
            onClick={handleExport}
            className={buttonStyles}
            title="导出图形"
            disabled={isLoading || !isInitialized || tableData.length === 0}
          >
            <Download className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* 缩放指示器 */}
      {isInitialized && (
        <div className="absolute bottom-4 left-4 z-10">
          <div className="px-3 py-1 bg-white/90 backdrop-blur-sm rounded border border-mysql-border text-sm text-mysql-text">
            {Math.round(zoomLevel * 100)}%
          </div>
        </div>
      )}

      {/* 图形容器 */}
      <div
        ref={diagramRef}
        className="w-full h-full bg-white"
        style={{ minHeight: '500px', height: '100%' }}
      />

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm">
          <div className="flex items-center space-x-3 text-mysql-text">
            <Loader2 className="w-6 h-6 animate-spin text-mysql-primary" />
            <span className="text-lg font-medium">正在生成ER图...</span>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {(error || initError) && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm">
          <div className="flex flex-col items-center space-y-3 text-center max-w-md">
            <AlertCircle className="w-12 h-12 text-red-500" />
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-mysql-text">
                {initError ? '图形引擎加载失败' : '图形渲染失败'}
              </h3>
              <p className="text-sm text-mysql-text-light">{initError || error}</p>
              {initError && (
                <div className="mt-3">
                  <button
                    type="button"
                    onClick={() => {
                      setInitError(null);
                      setIsInitialized(false);
                      // 重新尝试初始化
                      setTimeout(() => {
                        if (diagramRef.current) {
                          initializeDiagram();
                        }
                      }, 100);
                    }}
                    className="px-4 py-2 bg-mysql-primary text-white rounded hover:bg-mysql-primary-dark transition-colors"
                  >
                    重新加载
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 懒加载占位符 - 未进入视口时显示 */}
      {!isInView && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto bg-mysql-primary-light rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-mysql-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="space-y-1">
              <h3 className="text-lg font-semibold text-mysql-text">图形引擎待加载</h3>
              <p className="text-sm text-mysql-text-light">滚动到此区域将自动加载图形引擎</p>
            </div>
          </div>
        </div>
      )}

      {/* 空状态 */}
      {isInView && !isLoading && !error && !initError && tableData.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto bg-mysql-primary-light rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-mysql-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="space-y-1">
              <h3 className="text-lg font-semibold text-mysql-text">暂无数据</h3>
              <p className="text-sm text-mysql-text-light">请输入SQL语句并解析以生成ER图</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}));

DiagramViewer.displayName = 'DiagramViewer';

export default DiagramViewer;
