'use client';

// MySQLAi.de - 统计概览组件
// 显示知识库的关键统计指标

import React from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  FolderOpen, 
  Code, 
  Search, 
  TrendingUp, 
  Users,
  Eye,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatsData {
  totalArticles: number;
  totalCategories: number;
  totalCodeExamples: number;
  totalSearches: number;
  totalViews: number;
  totalDownloads: number;
  articlesThisMonth: number;
  searchesThisMonth: number;
}

interface StatsOverviewProps {
  data: StatsData;
  loading?: boolean;
}

// 统计卡片数据
const getStatsCards = (data: StatsData) => [
  {
    title: '总文章数',
    value: data.totalArticles,
    change: data.articlesThisMonth,
    changeLabel: '本月新增',
    icon: FileText,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-200'
  },
  {
    title: '总分类数',
    value: data.totalCategories,
    change: 0,
    changeLabel: '本月新增',
    icon: FolderOpen,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200'
  },
  {
    title: '代码示例',
    value: data.totalCodeExamples,
    change: 0,
    changeLabel: '本月新增',
    icon: Code,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    borderColor: 'border-purple-200'
  },
  {
    title: '总搜索次数',
    value: data.totalSearches,
    change: data.searchesThisMonth,
    changeLabel: '本月搜索',
    icon: Search,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    borderColor: 'border-orange-200'
  },
  {
    title: '总浏览量',
    value: data.totalViews,
    change: 0,
    changeLabel: '本月浏览',
    icon: Eye,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
    borderColor: 'border-indigo-200'
  },
  {
    title: '总下载量',
    value: data.totalDownloads,
    change: 0,
    changeLabel: '本月下载',
    icon: Download,
    color: 'text-pink-600',
    bgColor: 'bg-pink-100',
    borderColor: 'border-pink-200'
  }
];

export default function StatsOverview({ data, loading = false }: StatsOverviewProps) {
  const statsCards = getStatsCards(data);

  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="bg-white rounded-lg shadow-md border border-mysql-border p-6 animate-pulse"
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {statsCards.map((card, index) => (
        <motion.div
          key={card.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className={cn(
            'bg-white rounded-lg shadow-md border p-6 hover:shadow-lg transition-all duration-300',
            card.borderColor
          )}
        >
          <div className="flex items-center space-x-4">
            {/* 图标 */}
            <div className={cn(
              'flex items-center justify-center w-12 h-12 rounded-lg',
              card.bgColor
            )}>
              <card.icon className={cn('w-6 h-6', card.color)} />
            </div>

            {/* 统计信息 */}
            <div className="flex-1">
              <p className="text-sm text-mysql-text-light mb-1">
                {card.title}
              </p>
              <p className="text-2xl font-bold text-mysql-text mb-1">
                {formatNumber(card.value)}
              </p>
              {card.change > 0 && (
                <div className="flex items-center space-x-1">
                  <TrendingUp className="w-3 h-3 text-green-600" />
                  <span className="text-xs text-green-600">
                    +{formatNumber(card.change)} {card.changeLabel}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 进度条（可选） */}
          {card.title === '总文章数' && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-xs text-mysql-text-light mb-1">
                <span>目标进度</span>
                <span>{Math.min(100, Math.round((data.totalArticles / 100) * 100))}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min(100, (data.totalArticles / 100) * 100)}%` }}
                ></div>
              </div>
            </div>
          )}
        </motion.div>
      ))}
    </div>
  );
}
