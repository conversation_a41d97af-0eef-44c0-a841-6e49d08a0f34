/**
 * 配置面板组件
 * 左侧面板，包含MySQL配置选项和激活状态
 */

import { useState } from 'react';
import { FolderOpen, Key, Database, Shield, CheckCircle, XCircle, Clock, Loader2 } from 'lucide-react';
import {
  useAppConfig,
  useActivationState,
  useAppStore
} from '../../store/appStore';
import { useActivationLogic } from '../../hooks/useActivationLogic';
import Button from '../ui/Button';

export default function ConfigPanel() {
  const config = useAppConfig();
  const activation = useActivationState();
  const {
    setMySQLVersion,
    setInstallPath,
    setRootPassword,
    setLicenseKey
  } = useAppStore();

  // 集成激活验证逻辑
  const {
    licenseKey: activationLicenseKey,
    setLicenseKey: setActivationLicenseKey,
    isActivating,
    successMessage,
    errorMessage,
    retryCount,
    handleActivateLicenseDirectly,
    clearMessages
  } = useActivationLogic();

  const [showPassword, setShowPassword] = useState(false);

  // MySQL版本选项
  const mysqlVersions = [
    '8.0.36',
    '8.0.35',
    '8.0.34',
    '5.7.44',
  ];

  // 激活状态显示
  const getActivationStatusDisplay = () => {
    switch (activation.status) {
      case 'activated':
        return {
          icon: <CheckCircle className="w-5 h-5 text-green-500" />,
          text: '已激活',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'expired':
        return {
          icon: <XCircle className="w-5 h-5 text-red-500" />,
          text: '已过期',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      case 'checking':
        return {
          icon: <Clock className="w-5 h-5 text-yellow-500" />,
          text: '验证中...',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200'
        };
      default:
        return {
          icon: <XCircle className="w-5 h-5 text-gray-500" />,
          text: '未激活',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const statusDisplay = getActivationStatusDisplay();

  // 条件渲染逻辑：只有在未激活或过期时才显示激活按钮
  const shouldShowActivationButton = activation.status === 'not_activated' || activation.status === 'expired';

  // 调试日志：追踪激活状态变化
  console.log('🎯 ConfigPanel render - activation state:', activation);
  console.log('🎯 ConfigPanel render - shouldShowActivationButton:', shouldShowActivationButton);
  console.log('🎯 ConfigPanel render - statusDisplay:', statusDisplay);

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 面板标题 */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <Database className="w-5 h-5 mr-2 text-blue-600" />
          MySQL 配置
        </h2>
      </div>

      {/* 配置内容 */}
      <div className="flex-1 p-6 space-y-6 overflow-auto">
        {/* 激活状态卡片 */}
        <div className={`p-4 rounded-lg border ${statusDisplay.bgColor} ${statusDisplay.borderColor}`}>
          {/* 全局错误提示 */}
          {errorMessage && errorMessage.includes('网络') && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-center space-x-2">
                <XCircle className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-700">
                  网络连接异常，请检查网络设置后重试
                </span>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              {statusDisplay.icon}
              <span className={`ml-2 font-medium ${statusDisplay.color}`}>
                {statusDisplay.text}
              </span>
            </div>
            {activation.remainingHours && (
              <span className="text-sm text-gray-500">
                剩余: {activation.remainingHours.toFixed(1)}小时
              </span>
            )}
          </div>
          
          {/* 激活码输入 - 条件渲染：只在未激活或过期时显示 */}
          {shouldShowActivationButton && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                激活码
              </label>
              <div className="relative">
                <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={activationLicenseKey}
                  onChange={(e) => {
                    setActivationLicenseKey(e.target.value);
                    setLicenseKey(e.target.value); // 同步到appStore
                    clearMessages(); // 清除之前的消息
                  }}
                  placeholder="请输入激活码"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isActivating}
                />
              </div>

              {/* 激活按钮 */}
              <Button
                variant="primary"
                size="sm"
                loading={isActivating}
                disabled={!activationLicenseKey.trim()}
                onClick={handleActivateLicenseDirectly}
                className="w-full mt-3"
              >
                激活许可证
              </Button>
            </div>
          )}

          {/* 加载状态显示 */}
          {isActivating && (
            <div className="mt-3">
              <div className="flex items-center justify-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                <span className="text-sm text-blue-700">
                  正在激活许可证...
                </span>
              </div>
            </div>
          )}

          {/* 成功消息显示 */}
          {!isActivating && successMessage && (
            <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-700">
                  {successMessage}
                </span>
              </div>
            </div>
          )}

          {/* 错误消息显示 */}
          {!isActivating && errorMessage && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center space-x-2">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm text-red-700">
                  {errorMessage}
                </span>
              </div>
                {/* 错误处理建议和重试信息 */}
                <div className="mt-2 text-xs text-red-600">
                  {errorMessage.includes('格式') && '请确保许可证密钥格式正确，通常为字母数字组合'}
                  {errorMessage.includes('网络') && '请检查网络连接，或稍后重试'}
                  {errorMessage.includes('权限') && '请确认许可证密钥有效，或联系技术支持'}
                  {errorMessage.includes('过期') && '许可证已过期，请联系技术支持获取新的激活码'}
                  {errorMessage.includes('次数') && '激活次数已达上限，请联系技术支持'}
                  {errorMessage.includes('未知错误') && '遇到未知错误，请联系技术支持'}
                  {retryCount > 0 && (
                    <div className="mt-1 text-xs text-orange-600">
                      已重试 {retryCount} 次，如问题持续请联系技术支持
                    </div>
                  )}
                </div>
              </div>
            )}
        </div>

        {/* MySQL版本选择 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            MySQL 版本
          </label>
          <select
            value={config.mysqlVersion}
            onChange={(e) => setMySQLVersion(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            aria-label="选择MySQL版本"
          >
            {mysqlVersions.map((version) => (
              <option key={version} value={version}>
                MySQL {version}
              </option>
            ))}
          </select>
        </div>

        {/* 安装路径 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            安装路径
          </label>
          <div className="relative">
            <FolderOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={config.installPath}
              onChange={(e) => setInstallPath(e.target.value)}
              placeholder="C:\MySQL"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500">
            MySQL将安装到此目录
          </p>
        </div>

        {/* Root密码 */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Root 密码
          </label>
          <div className="relative">
            <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type={showPassword ? "text" : "password"}
              value={config.rootPassword}
              onChange={(e) => setRootPassword(e.target.value)}
              placeholder="请设置Root密码"
              className="w-full pl-10 pr-12 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? '隐藏' : '显示'}
            </button>
          </div>
          <p className="text-xs text-gray-500">
            用于MySQL数据库管理员账户
          </p>
        </div>

        {/* 配置摘要 */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-2">配置摘要</h3>
          <div className="space-y-1 text-xs text-gray-600">
            <div>版本: MySQL {config.mysqlVersion}</div>
            <div>路径: {config.installPath}</div>
            <div>状态: {statusDisplay.text}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
