'use client';

// MySQLAi.de - 工具总览页面
// 展示所有可用的MySQL工具，包括ER图生成工具和MySQL安装工具
// 更新：2025-06-28

import Link from 'next/link';
import { motion } from 'framer-motion';
import { GitBranch, Download, ArrowRight, Wrench } from 'lucide-react';
import { cn } from '@/lib/utils';

// 页面元数据已移至layout或通过其他方式处理

// 工具数据配置
const TOOLS_DATA = [
  {
    id: 'er-diagram',
    title: 'ER图生成工具',
    description: '智能数据库关系图生成工具，可视化数据库结构，支持多种导出格式。',
    icon: GitBranch,
    href: '/tools/er-diagram',
    features: [
      '自动表关系识别',
      '可视化图形编辑',
      '多格式导出支持',
      '团队协作共享',
    ],
    color: 'purple',
    gradient: 'from-purple-500 to-purple-700',
  },
  {
    id: 'mysql-installer',
    title: 'MySQL安装工具',
    description: '一键自动安装和配置MySQL数据库，支持多版本管理和环境配置。',
    icon: Download,
    href: '/tools/mysql-installer',
    features: [
      '一键安装配置',
      '多版本管理',
      '环境自动检测',
      '安全配置优化',
    ],
    color: 'orange',
    gradient: 'from-orange-500 to-orange-700',
  },
] as const;

export default function ToolsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-mysql-primary-light via-white to-blue-50">
      {/* 面包屑导航 */}
      <div className="bg-white border-b border-mysql-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link 
              href="/" 
              className="text-mysql-text-light hover:text-mysql-primary transition-colors duration-200"
            >
              首页
            </Link>
            <span className="text-mysql-text-light">/</span>
            <span className="text-mysql-text font-medium">工具集</span>
          </nav>
        </div>
      </div>

      {/* 页面头部 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="flex items-center justify-center w-16 h-16 bg-mysql-primary rounded-2xl shadow-lg">
              <Wrench className="w-8 h-8 text-white" />
            </div>
          </motion.div>
          
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-mysql-text mb-6"
          >
            MySQL工具集
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed"
          >
            专业的MySQL工具集合，提升数据库开发和管理效率。
            从数据库设计到部署配置，我们为您提供全方位的工具支持。
          </motion.p>
        </div>

        {/* 工具卡片网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {TOOLS_DATA.map((tool, index) => {
            const IconComponent = tool.icon;
            
            return (
              <motion.div
                key={tool.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                className="group"
              >
                <Link href={tool.href}>
                  <div className={cn(
                    'relative bg-white rounded-2xl shadow-lg border border-mysql-border',
                    'hover:shadow-2xl hover:scale-105 transition-all duration-300',
                    'p-8 h-full cursor-pointer overflow-hidden'
                  )}>
                    {/* 渐变装饰条 */}
                    <div className={cn(
                      'absolute top-0 left-0 w-full h-1 bg-gradient-to-r',
                      tool.gradient
                    )} />
                    
                    {/* 工具图标 */}
                    <div className="flex items-center mb-6">
                      <div className={cn(
                        'flex items-center justify-center w-12 h-12 rounded-xl',
                        `bg-${tool.color}-100 group-hover:bg-${tool.color}-200`,
                        'transition-colors duration-300'
                      )}>
                        <IconComponent className={cn(
                          'w-6 h-6',
                          `text-${tool.color}-600`
                        )} />
                      </div>
                      <div className="ml-4 flex-1">
                        <h3 className="text-xl font-bold text-mysql-text group-hover:text-mysql-primary transition-colors duration-300">
                          {tool.title}
                        </h3>
                      </div>
                      <ArrowRight className="w-5 h-5 text-mysql-text-light group-hover:text-mysql-primary group-hover:translate-x-1 transition-all duration-300" />
                    </div>
                    
                    {/* 工具描述 */}
                    <p className="text-mysql-text-light mb-6 leading-relaxed">
                      {tool.description}
                    </p>
                    
                    {/* 功能特性列表 */}
                    <div className="space-y-2">
                      {tool.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center">
                          <div className={cn(
                            'w-2 h-2 rounded-full mr-3',
                            `bg-${tool.color}-500`
                          )} />
                          <span className="text-sm text-mysql-text-light">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </div>

        {/* 底部说明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center mt-16"
        >
          <p className="text-mysql-text-light">
            更多工具正在开发中，敬请期待...
          </p>
        </motion.div>
      </div>
    </div>
  );
}
