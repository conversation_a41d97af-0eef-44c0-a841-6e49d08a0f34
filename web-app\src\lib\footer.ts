// MySQLAi.de - 页脚数据配置文件
// 管理页脚相关的数据和配置

import { Github, Twitter, Linkedin, Mail, Phone, MapPin, Facebook, Youtube, Instagram } from 'lucide-react';

// 页脚导航数据
export const FOOTER_NAVIGATION = {
  products: {
    title: '产品服务',
    links: [
      { name: 'MySQL知识库', href: '/knowledge', description: '丰富的数据库知识分享' },
      { name: '项目管理', href: '/projects', description: '高效的项目任务管理' },
      { name: '报告展示', href: '/reports', description: '多媒体项目报告' },
      { name: 'AI智能分析', href: '/ai-analysis', description: 'AI驱动的性能分析' },
      { name: '性能监控', href: '/monitoring', description: '实时数据库监控' },
    ],
  },
  company: {
    title: '关于我们',
    links: [
      { name: '公司介绍', href: '/about', description: '了解MySQLAi.de' },
      { name: '团队成员', href: '/team', description: '专业的技术团队' },
      { name: '新闻动态', href: '/news', description: '最新公司动态' },
      { name: '招聘信息', href: '/careers', description: '加入我们的团队' },
      { name: '合作伙伴', href: '/partners', description: '战略合作伙伴' },
    ],
  },
  support: {
    title: '技术支持',
    links: [
      { name: '帮助中心', href: '/help', description: '常见问题解答' },
      { name: '技术文档', href: '/docs', description: '详细技术文档' },
      { name: '联系我们', href: '/contact', description: '7×24小时支持' },
      { name: '在线客服', href: '/chat', description: '实时在线咨询' },
      { name: '培训课程', href: '/training', description: 'MySQL专业培训' },
    ],
  },
  legal: {
    title: '法律声明',
    links: [
      { name: '服务条款', href: '/terms', description: '服务使用条款' },
      { name: '隐私政策', href: '/privacy', description: '用户隐私保护' },
      { name: '免责声明', href: '/disclaimer', description: '法律免责声明' },
      { name: 'Cookie政策', href: '/cookies', description: 'Cookie使用说明' },
      { name: '版权声明', href: '/copyright', description: '知识产权保护' },
    ],
  },
} as const;

// 社交媒体链接
export const SOCIAL_LINKS = [
  {
    name: 'GitHub',
    href: 'https://github.com/mysqlai',
    icon: 'Github',
    color: 'hover:text-gray-900',
    description: '开源项目和代码',
  },
  {
    name: 'Twitter',
    href: 'https://twitter.com/mysqlai',
    icon: 'Twitter',
    color: 'hover:text-blue-400',
    description: '最新动态和资讯',
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/company/mysqlai',
    icon: 'Linkedin',
    color: 'hover:text-blue-600',
    description: '专业网络和招聘',
  },
  {
    name: 'Email',
    href: 'mailto:<EMAIL>',
    icon: 'Mail',
    color: 'hover:text-mysql-primary',
    description: '邮件联系我们',
  },
  {
    name: 'Facebook',
    href: 'https://facebook.com/mysqlai',
    icon: 'Facebook',
    color: 'hover:text-blue-500',
    description: '社区和讨论',
  },
  {
    name: 'YouTube',
    href: 'https://youtube.com/mysqlai',
    icon: 'Youtube',
    color: 'hover:text-red-500',
    description: '技术视频和教程',
  },
] as const;

// 联系信息
export const CONTACT_INFO = [
  {
    icon: 'Phone',
    text: '+86 ************',
    href: 'tel:+8640088899999',
    description: '7×24小时技术支持热线',
  },
  {
    icon: 'Mail',
    text: '<EMAIL>',
    href: 'mailto:<EMAIL>',
    description: '商务合作和技术咨询',
  },
  {
    icon: 'MapPin',
    text: '北京市朝阳区科技园区',
    href: 'https://maps.google.com/?q=北京市朝阳区科技园区',
    description: '公司总部地址',
  },
] as const;

// 公司信息
export const COMPANY_INFO = {
  name: 'MySQLAi.de',
  fullName: 'MySQL智能分析专家',
  description: '专业的MySQL智能分析平台，为企业提供数据库优化、项目管理和报告展示的完整解决方案。',
  slogan: '专业的MySQL智能分析平台 | 企业级数据库解决方案',
  founded: '2020',
  employees: '50+',
  customers: '1000+',
  countries: '8',
} as const;

// 认证和资质
export const CERTIFICATIONS = [
  {
    name: 'ISO 27001',
    description: '信息安全管理体系认证',
    image: '/certifications/iso27001.png',
  },
  {
    name: 'MySQL认证',
    description: 'MySQL官方认证合作伙伴',
    image: '/certifications/mysql-certified.png',
  },
  {
    name: '高新技术企业',
    description: '国家高新技术企业认证',
    image: '/certifications/high-tech.png',
  },
] as const;

// 图标映射
export const FOOTER_ICON_MAP = {
  Github,
  Twitter,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Youtube,
  Instagram,
} as const;

// 工具函数

/**
 * 获取当前年份
 */
export function getCurrentYear(): number {
  return new Date().getFullYear();
}

/**
 * 获取版权信息
 */
export function getCopyrightText(): string {
  const year = getCurrentYear();
  return `© ${year} ${COMPANY_INFO.name}. 保留所有权利。`;
}

/**
 * 获取图标组件
 */
export function getFooterIconComponent(iconName: keyof typeof FOOTER_ICON_MAP) {
  return FOOTER_ICON_MAP[iconName];
}

/**
 * 根据分类获取导航链接
 */
export function getNavigationByCategory(category: keyof typeof FOOTER_NAVIGATION) {
  return FOOTER_NAVIGATION[category];
}

/**
 * 获取所有导航链接
 */
export function getAllNavigation() {
  return Object.values(FOOTER_NAVIGATION);
}

/**
 * 获取社交媒体链接
 */
export function getSocialLinks() {
  return SOCIAL_LINKS;
}

/**
 * 获取联系信息
 */
export function getContactInfo() {
  return CONTACT_INFO;
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证电话号码格式
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * 格式化链接URL
 */
export function formatUrl(url: string): string {
  if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('mailto:') && !url.startsWith('tel:')) {
    return `https://${url}`;
  }
  return url;
}

/**
 * 获取链接的显示文本
 */
export function getLinkDisplayText(link: { name: string; href: string }): string {
  return link.name;
}

/**
 * 检查链接是否为外部链接
 */
export function isExternalLink(href: string): boolean {
  return href.startsWith('http://') || href.startsWith('https://') || href.startsWith('mailto:') || href.startsWith('tel:');
}

/**
 * 获取链接的目标属性
 */
export function getLinkTarget(href: string): string {
  return isExternalLink(href) ? '_blank' : '_self';
}

/**
 * 获取链接的rel属性
 */
export function getLinkRel(href: string): string {
  return isExternalLink(href) ? 'noopener noreferrer' : '';
}

/**
 * 生成页脚的结构化数据
 */
export function generateFooterStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: COMPANY_INFO.name,
    description: COMPANY_INFO.description,
    url: 'https://mysqlai.de',
    logo: 'https://mysqlai.de/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: CONTACT_INFO[0].text,
      contactType: 'customer service',
      availableLanguage: ['Chinese', 'English'],
    },
    sameAs: SOCIAL_LINKS.map(link => link.href),
    address: {
      '@type': 'PostalAddress',
      addressLocality: '北京市',
      addressRegion: '朝阳区',
      addressCountry: 'CN',
    },
  };
}
