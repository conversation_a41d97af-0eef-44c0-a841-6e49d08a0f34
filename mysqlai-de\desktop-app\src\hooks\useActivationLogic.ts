/**
 * 激活验证逻辑Hook
 * 提供可重用的激活验证功能，包括状态同步、错误处理等
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useActivation } from './useActivation';
import { useAppStore } from '../store/appStore';
import { convertActivationStatusToState } from '../lib/utils';

export function useActivationLogic() {
  const activation = useActivation();
  const { updateActivation } = useAppStore();

  // 本地状态
  const [licenseKey, setLicenseKey] = useState('');
  const [isActivating, setIsActivating] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [retryCount, setRetryCount] = useState(0);

  // 用于防止重复同步的引用
  const lastSyncedStatus = useRef<any>(null);
  const isMountedRef = useRef(true);

  // 优化的状态同步函数
  const syncActivationState = useCallback((status: any) => {
    console.log('🔄 syncActivationState called with:', status);
    console.log('🔄 isMountedRef.current:', isMountedRef.current);
    console.log('🔄 lastSyncedStatus.current:', lastSyncedStatus.current);

    if (!isMountedRef.current || !status) {
      console.log('⏭️ Skipping sync - component unmounted or no status');
      return;
    }

    // 防止重复同步相同状态
    if (JSON.stringify(status) === JSON.stringify(lastSyncedStatus.current)) {
      console.log('⏭️ Skipping sync - same status as last sync');
      return;
    }

    try {
      console.log('🔄 Converting activation status to state...');
      const convertedState = convertActivationStatusToState(status);
      console.log('✅ Converted state:', convertedState);

      console.log('🔄 Updating appStore activation state...');
      updateActivation(convertedState);
      lastSyncedStatus.current = status;

      console.log('✅ Activation state synced successfully to appStore:', convertedState);

      // 验证同步结果
      setTimeout(() => {
        const currentStoreState = useAppStore.getState().activation;
        console.log('🔍 Current appStore activation state after sync:', currentStoreState);
      }, 100);

    } catch (error) {
      console.error('❌ Failed to sync activation state:', error);
    }
  }, [updateActivation]);

  // 强制状态同步函数 - 用于激活成功后确保状态更新
  const forceStateSync = useCallback((status: any) => {
    console.log('🚀 forceStateSync called - bypassing duplicate checks');

    if (!isMountedRef.current || !status) {
      console.log('⏭️ Skipping force sync - component unmounted or no status');
      return;
    }

    try {
      const convertedState = convertActivationStatusToState(status);
      console.log('🚀 Force converted state:', convertedState);

      // 强制更新，不检查重复
      updateActivation(convertedState);

      // 更新最后同步状态
      lastSyncedStatus.current = status;

      console.log('🚀 Force sync completed');
      console.log('📊 Current appStore state after force sync:', useAppStore.getState().activation);
    } catch (error) {
      console.error('❌ Error in force sync:', error);
    }
  }, [updateActivation]);

  // 错误分类和处理函数
  const categorizeError = useCallback((error: any): string => {
    const errorStr = error?.message || error?.toString() || '';

    if (errorStr.includes('network') || errorStr.includes('fetch') || errorStr.includes('timeout')) {
      return '网络连接异常，请检查网络设置后重试';
    }
    if (errorStr.includes('permission') || errorStr.includes('unauthorized')) {
      return '权限验证失败，请检查激活码是否正确';
    }
    if (errorStr.includes('expired') || errorStr.includes('invalid')) {
      return '激活码已过期或无效，请联系技术支持';
    }
    if (errorStr.includes('limit') || errorStr.includes('quota')) {
      return '激活次数已达上限，请联系技术支持';
    }
    if (errorStr.includes('format')) {
      return '激活码格式不正确，请检查输入';
    }

    return '激活过程中发生未知错误，请重试或联系技术支持';
  }, []);

  // 重试逻辑
  const attemptActivation = useCallback(async (key: string, attempt: number = 1): Promise<boolean> => {
    const maxRetries = 3;

    try {
      console.log(`Activation attempt ${attempt}/${maxRetries} for key: ${key.substring(0, 8)}...`);

      const success = await activation.activateLicense(key);

      if (success) {
        console.log('🎉 License activation successful!');

        // 刷新激活状态
        console.log('🔄 Refreshing activation status...');
        await activation.refreshStatus();
        console.log('✅ Activation status refreshed');
        console.log('📊 Current activation.activationStatus:', activation.activationStatus);

        // 同步激活状态到appStore
        console.log('🔄 Syncing activation state to appStore...');
        syncActivationState(activation.activationStatus);

        // 强制立即同步状态，确保UI更新
        console.log('🔄 Force syncing state immediately...');
        forceStateSync(activation.activationStatus);

        // 多次同步确保状态更新成功
        setTimeout(() => {
          console.log('🔄 Second sync attempt with latest status...');
          forceStateSync(activation.activationStatus);
        }, 100);

        setTimeout(() => {
          console.log('🔄 Third sync attempt with latest status...');
          forceStateSync(activation.activationStatus);
        }, 500);

        setTimeout(() => {
          console.log('🔄 Final sync attempt with latest status...');
          forceStateSync(activation.activationStatus);

          // 验证最终状态
          const finalState = useAppStore.getState().activation;
          console.log('🔍 Final appStore state after all syncs:', finalState);

          if (finalState.status !== 'activated') {
            console.warn('⚠️ State sync may have failed, forcing manual update...');
            // 手动强制更新状态
            updateActivation({
              status: 'activated',
              licenseKey: activation.activationStatus?.licenseKey,
              machineId: activation.activationStatus?.machineId,
              expiresAt: activation.activationStatus?.expiresAt,
              remainingHours: activation.activationStatus?.remainingHours,
              lastCheckTime: Date.now()
            });
          }
        }, 1000);

        setRetryCount(0);
        return true;
      } else {
        const errorMsg = activation.error || '许可证激活失败';

        // 如果是网络错误且还有重试次数，则自动重试
        if (attempt < maxRetries && errorMsg.includes('网络')) {
          console.log(`Network error detected, retrying in 2 seconds... (${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 2000));
          return attemptActivation(key, attempt + 1);
        }

        throw new Error(errorMsg);
      }
    } catch (error) {
      if (attempt < maxRetries && (error as Error)?.message?.includes('网络')) {
        console.log(`Network error detected, retrying in 2 seconds... (${attempt}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        return attemptActivation(key, attempt + 1);
      }

      throw error;
    }
  }, [activation, syncActivationState]);

  // 直接激活许可证 - 合并验证和激活逻辑，增强错误处理
  const handleActivateLicenseDirectly = useCallback(async () => {
    if (!licenseKey.trim()) {
      setErrorMessage('请输入激活码');
      return;
    }

    setIsActivating(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      const success = await attemptActivation(licenseKey.trim());

      if (success) {
        setSuccessMessage('许可证激活成功！现在可以安装MySQL。');
        setLicenseKey(''); // 清空输入框
      }
    } catch (error) {
      console.error('激活许可证时出错:', error);
      const categorizedError = categorizeError(error);
      setErrorMessage(categorizedError);
      setRetryCount(prev => prev + 1);
    } finally {
      setIsActivating(false);
    }
  }, [licenseKey, attemptActivation, categorizeError]);
  
  // 清除消息
  const clearMessages = useCallback(() => {
    setSuccessMessage('');
    setErrorMessage('');
    setRetryCount(0);
  }, []);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // 监听激活状态变化，同步到appStore（优化版本，避免无限循环）
  useEffect(() => {
    if (activation.activationStatus && isMountedRef.current) {
      // 使用setTimeout确保状态更新不会阻塞UI
      const timeoutId = setTimeout(() => {
        syncActivationState(activation.activationStatus);
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [activation.activationStatus, syncActivationState]);

  // 初始化许可证密钥
  useEffect(() => {
    if (activation.activationStatus?.licenseKey) {
      setLicenseKey(activation.activationStatus.licenseKey);
    }
  }, [activation.activationStatus?.licenseKey]);
  
  return {
    // 状态
    licenseKey,
    setLicenseKey,
    isActivating,
    successMessage,
    errorMessage,
    retryCount,

    // 操作
    handleActivateLicenseDirectly,
    clearMessages,

    // 激活状态
    activationStatus: activation.activationStatus,
    isLoading: activation.isLoading,
    error: activation.error,
    isActivated: activation.isActivated,

    // 状态同步和错误处理
    syncActivationState,
    categorizeError,

    // 调试信息
    lastSyncedStatus: lastSyncedStatus.current
  };
}
