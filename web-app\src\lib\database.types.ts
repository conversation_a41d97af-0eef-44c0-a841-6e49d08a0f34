// MySQLAi.de - Supabase 数据库类型定义
// 自动生成的类型定义，提供完整的类型安全

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // 用户表
      users: {
        Row: {
          id: string
          email: string
          name: string | null
          avatar_url: string | null
          bio: string | null
          website: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      
      // 知识库分类
      knowledge_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          icon: string | null
          color: string | null
          order_index: number
          created_at: string
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          icon?: string | null
          color?: string | null
          order_index?: number
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          icon?: string | null
          color?: string | null
          order_index?: number
          created_at?: string
        }
      }
      
      // 知识库文章
      knowledge_articles: {
        Row: {
          id: string
          title: string
          description: string | null
          content: string
          category_id: string | null
          tags: string[] | null
          difficulty: 'beginner' | 'intermediate' | 'advanced'
          order_index: number
          last_updated: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          title: string
          description?: string | null
          content: string
          category_id?: string | null
          tags?: string[] | null
          difficulty?: 'beginner' | 'intermediate' | 'advanced'
          order_index?: number
          last_updated?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          content?: string
          category_id?: string | null
          tags?: string[] | null
          difficulty?: 'beginner' | 'intermediate' | 'advanced'
          order_index?: number
          last_updated?: string
          created_at?: string
          updated_at?: string
        }
      }

      // 代码示例表
      code_examples: {
        Row: {
          id: string
          article_id: string
          title: string
          code: string
          language: string
          description: string | null
          order_index: number
          created_at: string
        }
        Insert: {
          id: string
          article_id: string
          title: string
          code: string
          language?: string
          description?: string | null
          order_index?: number
          created_at?: string
        }
        Update: {
          id?: string
          article_id?: string
          title?: string
          code?: string
          language?: string
          description?: string | null
          order_index?: number
          created_at?: string
        }
      }

      // 文章关联表
      article_relations: {
        Row: {
          id: string
          source_article_id: string
          target_article_id: string
          created_at: string
        }
        Insert: {
          id?: string
          source_article_id: string
          target_article_id: string
          created_at?: string
        }
        Update: {
          id?: string
          source_article_id?: string
          target_article_id?: string
          created_at?: string
        }
      }

      // 搜索历史表
      search_history: {
        Row: {
          id: string
          query: string
          results_count: number
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          query: string
          results_count?: number
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          query?: string
          results_count?: number
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
      }

      // ER图项目
      er_projects: {
        Row: {
          id: string
          name: string
          description: string | null
          sql_content: string
          diagram_data: Json | null
          user_id: string
          is_public: boolean
          view_count: number
          like_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          sql_content: string
          diagram_data?: Json | null
          user_id: string
          is_public?: boolean
          view_count?: number
          like_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          sql_content?: string
          diagram_data?: Json | null
          user_id?: string
          is_public?: boolean
          view_count?: number
          like_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      
      // 用户收藏
      user_favorites: {
        Row: {
          id: string
          user_id: string
          article_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          article_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          article_id?: string
          created_at?: string
        }
      }
      
      // 用户点赞
      user_likes: {
        Row: {
          id: string
          user_id: string
          article_id: string | null
          project_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          article_id?: string | null
          project_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          article_id?: string | null
          project_id?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      difficulty_level: 'beginner' | 'intermediate' | 'advanced'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
