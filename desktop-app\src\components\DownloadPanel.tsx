import React, { useState } from 'react';
import { 
  useDownload, 
  formatFileSize, 
  formatDownloadSpeed, 
  formatETA,
  getDownloadStatusText,
  getDownloadStatusColor,
  getRegionDisplayName,
  getOSDisplayName,
  getArchDisplayName,
  DownloadStatus
} from '../hooks/useDownload';

interface DownloadPanelProps {
  className?: string;
}

export const DownloadPanel: React.FC<DownloadPanelProps> = ({ className = '' }) => {
  const {
    userLocation,
    supportedVersions,
    selectedPackage,
    downloadProgress,
    isDownloading,
    isLoading,
    error,
    detectUserLocation,
    getMySQLPackage,
    downloadMySQLPackage,
    refreshAll
  } = useDownload();

  const [selectedVersion, setSelectedVersion] = useState('');
  const [selectedOS, setSelectedOS] = useState('');
  const [selectedArch, setSelectedArch] = useState('');
  const [downloadDir, setDownloadDir] = useState('C:\\MySQL\\Downloads');

  // 处理版本选择
  const handleVersionChange = async (version: string, os: string, arch: string) => {
    setSelectedVersion(version);
    setSelectedOS(os);
    setSelectedArch(arch);
    
    if (version && os && arch) {
      await getMySQLPackage(version, os, arch);
    }
  };

  // 处理下载
  const handleDownload = async () => {
    if (!selectedVersion || !selectedOS || !selectedArch) {
      return;
    }

    await downloadMySQLPackage(selectedVersion, selectedOS, selectedArch, downloadDir);
  };

  return (
    <div className={`p-6 bg-white rounded-lg shadow-md ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-800">下载管理</h2>
        <button
          type="button"
          onClick={refreshAll}
          disabled={isLoading}
          className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded hover:bg-gray-200 disabled:opacity-50"
        >
          {isLoading ? '刷新中...' : '刷新'}
        </button>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
          <div className="text-red-700 text-sm">{error}</div>
        </div>
      )}

      {/* 用户位置信息 */}
      {userLocation && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">地理位置</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600">国家/地区</div>
                <div className="font-medium">{userLocation.country}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">优化区域</div>
                <div className="font-medium">{getRegionDisplayName(userLocation.region)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">IP地址</div>
                <div className="font-mono text-sm">{userLocation.ip}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">下载优化</div>
                <div className={`font-medium ${userLocation.isChina ? 'text-green-600' : 'text-blue-600'}`}>
                  {userLocation.isChina ? '中国镜像优先' : '全球源优先'}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* MySQL版本选择 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">MySQL版本选择</h3>
        <div className="space-y-4">
          {/* 版本列表 */}
          {supportedVersions.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择MySQL版本
              </label>
              <div className="space-y-2">
                {supportedVersions.map(([version, os, arch], index) => (
                  <div key={index} className="flex items-center">
                    <input
                      type="radio"
                      id={`version-${index}`}
                      name="mysql-version"
                      value={`${version}-${os}-${arch}`}
                      checked={selectedVersion === version && selectedOS === os && selectedArch === arch}
                      onChange={() => handleVersionChange(version, os, arch)}
                      className="mr-3"
                    />
                    <label htmlFor={`version-${index}`} className="flex-1 cursor-pointer">
                      <div className="font-medium">
                        MySQL {version} - {getOSDisplayName(os)} {getArchDisplayName(arch)}
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 下载目录设置 */}
          <div>
            <label htmlFor="download-dir" className="block text-sm font-medium text-gray-700 mb-1">
              下载目录
            </label>
            <input
              id="download-dir"
              type="text"
              value={downloadDir}
              onChange={(e) => setDownloadDir(e.target.value)}
              placeholder="请输入下载目录路径"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isDownloading}
            />
          </div>
        </div>
      </div>

      {/* 选中的包信息 */}
      {selectedPackage && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">包信息</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600">文件名</div>
                <div className="font-mono text-sm">{selectedPackage.filename}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">文件大小</div>
                <div className="font-medium">{formatFileSize(selectedPackage.fileSize)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">发布日期</div>
                <div className="font-medium">{selectedPackage.releaseDate}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">下载源数量</div>
                <div className="font-medium">{selectedPackage.downloadSources.length} 个</div>
              </div>
            </div>

            {/* 下载源列表 */}
            <div className="mt-4">
              <div className="text-sm text-gray-600 mb-2">可用下载源</div>
              <div className="space-y-2">
                {selectedPackage.downloadSources.map((source, index) => (
                  <div key={index} className="flex items-center justify-between bg-white p-2 rounded border">
                    <div>
                      <div className="font-medium text-sm">{source.name}</div>
                      <div className="text-xs text-gray-600">{getRegionDisplayName(source.region)}</div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm ${source.available ? 'text-green-600' : 'text-red-600'}`}>
                        {source.available ? '可用' : '不可用'}
                      </div>
                      {source.speed && (
                        <div className="text-xs text-gray-600">
                          {formatDownloadSpeed(source.speed)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 下载进度 */}
      {downloadProgress && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">下载进度</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className={`font-medium ${getDownloadStatusColor(downloadProgress.status)}`}>
                {getDownloadStatusText(downloadProgress.status)}
              </div>
              <div className="text-sm text-gray-600">
                {downloadProgress.percentage.toFixed(1)}%
              </div>
            </div>

            {/* 进度条 */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  downloadProgress.status === DownloadStatus.Completed ? 'bg-green-500' :
                  downloadProgress.status === DownloadStatus.Failed ? 'bg-red-500' :
                  downloadProgress.status === DownloadStatus.Downloading ? 'bg-blue-500' : 'bg-gray-400'
                }`}
                style={{ width: `${downloadProgress.percentage}%` }}
              ></div>
            </div>

            {/* 下载详情 */}
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-gray-600">已下载</div>
                <div className="font-medium">{formatFileSize(downloadProgress.downloaded)}</div>
              </div>
              <div>
                <div className="text-gray-600">下载速度</div>
                <div className="font-medium">{formatDownloadSpeed(downloadProgress.speed)}</div>
              </div>
              <div>
                <div className="text-gray-600">剩余时间</div>
                <div className="font-medium">{formatETA(downloadProgress.eta)}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex space-x-3">
        <button
          type="button"
          onClick={handleDownload}
          disabled={!selectedPackage || isDownloading || !downloadDir.trim()}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isDownloading ? '下载中...' : '开始下载'}
        </button>

        <button
          type="button"
          onClick={() => setDownloadDir('C:\\MySQL\\Downloads')}
          disabled={isDownloading}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          重置目录
        </button>

        <button
          type="button"
          onClick={detectUserLocation}
          disabled={isLoading}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          重新检测位置
        </button>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="text-sm font-semibold text-blue-800 mb-2">下载说明</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 系统会自动检测您的地理位置并选择最优下载源</li>
          <li>• 支持断点续传，下载中断后可继续下载</li>
          <li>• 中国用户优先使用国内镜像源，提升下载速度</li>
          <li>• 下载完成后会自动验证文件完整性</li>
        </ul>
      </div>
    </div>
  );
};