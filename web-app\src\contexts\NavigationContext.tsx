'use client';

// MySQLAi.de - NavigationContext导航状态管理
// 管理知识库导航的全局状态，包含当前选中项、搜索状态、展开状态等

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { KnowledgeCategory, KnowledgeItem } from '@/lib/types';

// 导航状态接口
export interface NavigationState {
  // 侧边栏状态
  isSidebarOpen: boolean;
  
  // 当前选中状态
  currentCategory: string | null;
  currentItem: string | null;
  
  // 搜索状态
  searchQuery: string;
  isSearching: boolean;
  
  // 展开状态
  expandedCategories: Set<string>;
  
  // 视图模式
  viewMode: 'grid' | 'list';
}

// 导航操作接口
export interface NavigationActions {
  // 侧边栏控制
  toggleSidebar: () => void;
  openSidebar: () => void;
  closeSidebar: () => void;
  
  // 选中项控制
  setCurrentCategory: (categoryId: string | null) => void;
  setCurrentItem: (itemId: string | null) => void;
  
  // 搜索控制
  setSearchQuery: (query: string) => void;
  setIsSearching: (searching: boolean) => void;
  clearSearch: () => void;
  
  // 展开状态控制
  toggleCategory: (categoryId: string) => void;
  expandCategory: (categoryId: string) => void;
  collapseCategory: (categoryId: string) => void;
  expandAll: () => void;
  collapseAll: () => void;
  
  // 视图模式控制
  setViewMode: (mode: 'grid' | 'list') => void;
  
  // 重置状态
  resetNavigation: () => void;
}

// Context类型
export interface NavigationContextType extends NavigationState, NavigationActions {}

// 初始状态
const initialState: NavigationState = {
  isSidebarOpen: false,
  currentCategory: null,
  currentItem: null,
  searchQuery: '',
  isSearching: false,
  expandedCategories: new Set(),
  viewMode: 'grid',
};

// 创建Context
const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

// Provider组件Props
interface NavigationProviderProps {
  children: ReactNode;
  initialCategory?: string;
  initialItem?: string;
}

// Provider组件
export function NavigationProvider({ 
  children, 
  initialCategory, 
  initialItem 
}: NavigationProviderProps) {
  const [state, setState] = useState<NavigationState>({
    ...initialState,
    currentCategory: initialCategory || null,
    currentItem: initialItem || null,
    expandedCategories: initialCategory ? new Set([initialCategory]) : new Set(),
  });

  // 侧边栏控制
  const toggleSidebar = useCallback(() => {
    setState(prev => ({ ...prev, isSidebarOpen: !prev.isSidebarOpen }));
  }, []);

  const openSidebar = useCallback(() => {
    setState(prev => ({ ...prev, isSidebarOpen: true }));
  }, []);

  const closeSidebar = useCallback(() => {
    setState(prev => ({ ...prev, isSidebarOpen: false }));
  }, []);

  // 选中项控制
  const setCurrentCategory = useCallback((categoryId: string | null) => {
    setState(prev => ({ 
      ...prev, 
      currentCategory: categoryId,
      currentItem: null // 切换分类时清空当前知识点
    }));
  }, []);

  const setCurrentItem = useCallback((itemId: string | null) => {
    setState(prev => ({ ...prev, currentItem: itemId }));
  }, []);

  // 搜索控制
  const setSearchQuery = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
  }, []);

  const setIsSearching = useCallback((searching: boolean) => {
    setState(prev => ({ ...prev, isSearching: searching }));
  }, []);

  const clearSearch = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      searchQuery: '', 
      isSearching: false 
    }));
  }, []);

  // 展开状态控制
  const toggleCategory = useCallback((categoryId: string) => {
    setState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return { ...prev, expandedCategories: newExpanded };
    });
  }, []);

  const expandCategory = useCallback((categoryId: string) => {
    setState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      newExpanded.add(categoryId);
      return { ...prev, expandedCategories: newExpanded };
    });
  }, []);

  const collapseCategory = useCallback((categoryId: string) => {
    setState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      newExpanded.delete(categoryId);
      return { ...prev, expandedCategories: newExpanded };
    });
  }, []);

  const expandAll = useCallback(() => {
    // 这里需要获取所有分类ID，暂时使用空Set
    // 在实际使用时会传入所有分类ID
    setState(prev => ({ ...prev, expandedCategories: new Set() }));
  }, []);

  const collapseAll = useCallback(() => {
    setState(prev => ({ ...prev, expandedCategories: new Set() }));
  }, []);

  // 视图模式控制
  const setViewMode = useCallback((mode: 'grid' | 'list') => {
    setState(prev => ({ ...prev, viewMode: mode }));
  }, []);

  // 重置状态
  const resetNavigation = useCallback(() => {
    setState(initialState);
  }, []);

  // Context值
  const contextValue: NavigationContextType = {
    // 状态
    ...state,
    
    // 操作
    toggleSidebar,
    openSidebar,
    closeSidebar,
    setCurrentCategory,
    setCurrentItem,
    setSearchQuery,
    setIsSearching,
    clearSearch,
    toggleCategory,
    expandCategory,
    collapseCategory,
    expandAll,
    collapseAll,
    setViewMode,
    resetNavigation,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
}

// Hook for using navigation context
export function useNavigation(): NavigationContextType {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}

// 导出Context以供其他组件使用
export { NavigationContext };
