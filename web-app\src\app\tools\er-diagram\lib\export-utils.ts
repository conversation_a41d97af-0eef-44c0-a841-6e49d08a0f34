/**
 * ER图导出工具函数
 * 实现图形导出功能，支持PNG和JPEG格式导出，包括质量控制、背景设置和文件下载功能
 */

import * as go from 'gojs';
import { downloadFile } from '../../../../lib/utils';
import type { ExportOptions } from '../types/er-diagram';

/**
 * 质量设置映射
 */
const QUALITY_SETTINGS = {
  high: 2.0,    // 高质量 - 2倍缩放
  medium: 1.5,  // 中等质量 - 1.5倍缩放
  low: 1.0      // 低质量 - 1倍缩放
} as const;

/**
 * 默认导出选项
 */
const DEFAULT_EXPORT_OPTIONS: ExportOptions = {
  format: 'PNG',
  background: 'transparent',
  quality: 'high',
  filename: 'er-diagram'
};

/**
 * 生成带时间戳的文件名
 * @param filename 基础文件名
 * @param format 文件格式
 * @returns 完整的文件名
 */
export function generateFilename(filename: string, format: string): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  const cleanFilename = filename.replace(/[^a-zA-Z0-9_-]/g, '');
  return `${cleanFilename}_${timestamp}.${format}`;
}

/**
 * 获取图形边界和边距
 * @param diagram GoJS图形实例
 * @returns 边界信息
 */
function getDiagramBounds(diagram: go.Diagram) {
  const bounds = diagram.documentBounds.copy();
  const margin = 20; // 边距
  
  return {
    bounds,
    margin,
    width: bounds.width + 2 * margin,
    height: bounds.height + 2 * margin
  };
}

/**
 * 生成图片数据
 * @param diagram GoJS图形实例
 * @param options 导出选项
 * @returns 图片数据URL
 */
export function generateImageData(diagram: go.Diagram, options: ExportOptions): string {
  const finalOptions = { ...DEFAULT_EXPORT_OPTIONS, ...options };
  const { width, height } = getDiagramBounds(diagram);
  const scale = QUALITY_SETTINGS[finalOptions.quality];
  
  // 处理背景设置
  let background: string | go.Brush;
  if (finalOptions.background === 'transparent') {
    // 透明背景
    background = 'transparent';
  } else {
    // 白色背景
    background = 'white';
  }
  
  // 如果是JPEG格式，强制使用白色背景
  if (finalOptions.format === 'JPEG' && background === 'transparent') {
    background = 'white';
    console.warn('JPEG格式不支持透明背景，已自动设置为白色背景');
  }
  
  try {
    // 生成图片数据
    const imageData = diagram.makeImageData({
      scale: scale,
      background: background,
      type: `image/${finalOptions.format.toLowerCase()}`,
      size: new go.Size(width * scale, height * scale),
      maxSize: new go.Size(4096, 4096), // 最大尺寸限制
      returnType: 'string'
    });

    return imageData as string;
  } catch (error) {
    console.error('生成图片数据失败:', error);
    throw new Error('图片生成失败，请检查图形内容或降低质量设置');
  }
}

/**
 * 下载图片
 * @param dataUrl 图片数据URL
 * @param filename 文件名
 */
export function downloadImage(dataUrl: string, filename: string): void {
  try {
    // 验证数据URL格式
    if (!dataUrl || !dataUrl.startsWith('data:image/')) {
      throw new Error('无效的图片数据');
    }
    
    // 使用现有的downloadFile函数
    downloadFile(dataUrl, filename);
    
    console.log(`图片已下载: ${filename}`);
  } catch (error) {
    console.error('下载图片失败:', error);
    throw new Error('图片下载失败，请重试');
  }
}

/**
 * 验证导出选项
 * @param options 导出选项
 * @returns 验证结果
 */
function validateExportOptions(options: ExportOptions): { isValid: boolean; error?: string } {
  if (!options.filename || !options.filename.trim()) {
    return { isValid: false, error: '文件名不能为空' };
  }
  
  if (!/^[a-zA-Z0-9_-]+$/.test(options.filename.trim())) {
    return { isValid: false, error: '文件名只能包含字母、数字、下划线和连字符' };
  }
  
  if (!['PNG', 'JPEG'].includes(options.format)) {
    return { isValid: false, error: '不支持的图片格式' };
  }
  
  if (!['transparent', 'white'].includes(options.background)) {
    return { isValid: false, error: '不支持的背景选项' };
  }
  
  if (!['high', 'medium', 'low'].includes(options.quality)) {
    return { isValid: false, error: '不支持的质量设置' };
  }
  
  return { isValid: true };
}

/**
 * 主导出函数
 * @param diagram GoJS图形实例
 * @param options 导出选项
 * @returns Promise<void>
 */
export async function exportDiagram(diagram: go.Diagram, options: ExportOptions): Promise<void> {
  // 验证输入参数
  if (!diagram) {
    throw new Error('图形实例不能为空');
  }
  
  if (!diagram.nodes.count && !diagram.links.count) {
    throw new Error('图形内容为空，无法导出');
  }
  
  // 验证导出选项
  const validation = validateExportOptions(options);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }
  
  try {
    console.log('开始导出图形...', options);
    
    // 生成图片数据
    const imageData = generateImageData(diagram, options);
    
    // 生成文件名
    const filename = generateFilename(options.filename || 'er-diagram', options.format);
    
    // 下载图片
    downloadImage(imageData, filename);
    
    console.log('图形导出成功');
  } catch (error) {
    console.error('导出图形失败:', error);
    throw error;
  }
}

/**
 * 获取导出预览数据（可选功能）
 * @param diagram GoJS图形实例
 * @param options 导出选项
 * @returns 预览图片数据URL
 */
export function getExportPreview(diagram: go.Diagram, options: ExportOptions): string {
  const previewOptions = {
    ...options,
    quality: 'low' as const // 预览使用低质量以提高性能
  };
  
  return generateImageData(diagram, previewOptions);
}

/**
 * 获取导出信息
 * @param diagram GoJS图形实例
 * @param options 导出选项
 * @returns 导出信息
 */
export function getExportInfo(diagram: go.Diagram, options: ExportOptions) {
  const { width, height } = getDiagramBounds(diagram);
  const scale = QUALITY_SETTINGS[options.quality];
  const finalWidth = Math.round(width * scale);
  const finalHeight = Math.round(height * scale);
  const filename = generateFilename(options.filename || 'er-diagram', options.format);
  
  return {
    filename,
    dimensions: `${finalWidth} × ${finalHeight}`,
    scale: `${scale}x`,
    format: options.format.toUpperCase(),
    background: options.background === 'transparent' ? '透明' : '白色',
    estimatedSize: estimateFileSize(finalWidth, finalHeight, options.format)
  };
}

/**
 * 估算文件大小
 * @param width 图片宽度
 * @param height 图片高度
 * @param format 图片格式
 * @returns 估算的文件大小（KB）
 */
function estimateFileSize(width: number, height: number, format: string): string {
  const pixels = width * height;
  let bytesPerPixel: number;
  
  if (format === 'PNG') {
    bytesPerPixel = 4; // PNG通常每像素4字节
  } else {
    bytesPerPixel = 1.5; // JPEG通常每像素1.5字节（压缩后）
  }
  
  const estimatedBytes = pixels * bytesPerPixel;
  const estimatedKB = Math.round(estimatedBytes / 1024);
  
  if (estimatedKB > 1024) {
    return `${(estimatedKB / 1024).toFixed(1)} MB`;
  } else {
    return `${estimatedKB} KB`;
  }
}
