/**
 * 桌面应用类型定义入口文件
 * 重新导出共享类型，提供统一的类型访问
 */

// 导入共享类型
export * from '../../../web-app/shared/types';

// 导入共享常量类型
export type { ThemeColors } from '../../../web-app/shared/constants';

// 桌面应用特有类型
export interface TauriApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  autoUpdate: boolean;
  downloadPath: string;
  maxConcurrentDownloads: number;
}

export interface WindowState {
  width: number;
  height: number;
  x: number;
  y: number;
  maximized: boolean;
  minimized: boolean;
}

// 激活状态接口（与Rust后端保持一致）
export interface ActivationStatus {
  isActivated: boolean;
  licenseKey?: string;
  expiresAt?: string;
  machineId: string;
  isTrialMode?: boolean;
  remainingTrials?: number;
  remainingHours: number;
}