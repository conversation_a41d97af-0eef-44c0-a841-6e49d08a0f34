'use client';

/**
 * MySQL配置预览组件
 * 显示最终的配置摘要和生成的配置文件内容
 */

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Copy, 
  Download, 
  FileText, 
  Settings, 
  Database,
  Shield,
  Zap,
  Check,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { AdvancedConfig, SupportedOS } from '../types/mysql-installer';
import { generateMySQLConfig } from '../lib/config-generator';

interface ConfigPreviewProps {
  config: AdvancedConfig;
  onEdit?: () => void;
  onConfirm?: () => void;
  className?: string;
}

/**
 * 配置预览组件
 */
export default function ConfigPreview({
  config,
  onEdit,
  onConfirm,
  className
}: ConfigPreviewProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));
  const [copiedItem, setCopiedItem] = useState<string | null>(null);

  // 生成配置文件内容
  const configFileContent = generateMySQLConfig(config);

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string, itemId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(itemId);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  }, []);

  // 下载配置文件
  const downloadConfigFile = useCallback(() => {
    const filename = config.targetOS === SupportedOS.WINDOWS ? 'my.ini' : 'my.cnf';
    const blob = new Blob([configFileContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [configFileContent, config.targetOS]);

  // 切换展开状态
  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  }, []);

  // 渲染可折叠部分
  const renderCollapsibleSection = (
    id: string,
    title: string,
    icon: React.ReactNode,
    children: React.ReactNode
  ) => {
    const isExpanded = expandedSections.has(id);

    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <button
          onClick={() => toggleSection(id)}
          className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center space-x-3">
            {icon}
            <span className="font-semibold text-mysql-text">{title}</span>
          </div>
          
          {isExpanded ? (
            <ChevronDown className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-500" />
          )}
        </button>
        
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 bg-white">
              {children}
            </div>
          </motion.div>
        )}
      </div>
    );
  };

  // 渲染配置项
  const renderConfigItem = (label: string, value: string | number | boolean, unit?: string) => (
    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
      <span className="text-gray-600">{label}</span>
      <span className="font-medium">
        {value === true ? '是' : value === false ? '否' : value}
        {unit && ` ${unit}`}
      </span>
    </div>
  );

  return (
    <div className={cn('space-y-6', className)}>
      {/* 标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-mysql-text">配置预览</h2>
        
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={downloadConfigFile}
            icon={<Download className="w-4 h-4" />}
          >
            下载配置文件
          </Button>
          
          <Button
            variant="outline"
            onClick={onEdit}
            icon={<Settings className="w-4 h-4" />}
          >
            编辑配置
          </Button>
        </div>
      </div>

      {/* 基本配置 */}
      {renderCollapsibleSection(
        'basic',
        '基本配置',
        <Settings className="w-5 h-5 text-mysql-primary" />,
        <div className="space-y-2">
          {renderConfigItem('MySQL版本', config.version)}
          {renderConfigItem('目标系统', config.targetOS)}
          {renderConfigItem('安装路径', config.installPath)}
          {renderConfigItem('数据目录', config.dataPath)}
          {renderConfigItem('端口号', config.port)}
          {renderConfigItem('字符集', config.charset)}
        </div>
      )}

      {/* 数据库配置 */}
      {renderCollapsibleSection(
        'database',
        '数据库配置',
        <Database className="w-5 h-5 text-blue-600" />,
        <div className="space-y-2">
          {renderConfigItem('排序规则', config.collation)}
          {renderConfigItem('默认存储引擎', config.defaultStorageEngine)}
          {renderConfigItem('InnoDB缓冲池大小', config.innodbBufferPoolSize)}
          {renderConfigItem('最大连接数', config.maxConnections)}
          {renderConfigItem('查询缓存大小', config.queryCacheSize)}
        </div>
      )}

      {/* 安全配置 */}
      {renderCollapsibleSection(
        'security',
        '安全配置',
        <Shield className="w-5 h-5 text-green-600" />,
        <div className="space-y-2">
          {renderConfigItem('Root密码', '••••••••')}
          {renderConfigItem('启用SSL', config.enableSSL)}
          {renderConfigItem('禁用远程root登录', config.disableRemoteRoot)}
          {renderConfigItem('删除匿名用户', config.removeAnonymousUsers)}
          {renderConfigItem('删除测试数据库', config.removeTestDatabase)}
        </div>
      )}

      {/* 高级配置 */}
      {renderCollapsibleSection(
        'advanced',
        '高级配置',
        <Zap className="w-5 h-5 text-purple-600" />,
        <div className="space-y-2">
          {renderConfigItem('慢查询日志', config.slowQueryLog)}
          {renderConfigItem('慢查询时间阈值', config.longQueryTime, '秒')}
          {renderConfigItem('启用二进制日志', config.enableBinlog)}
          {renderConfigItem('启用错误日志', config.enableErrorLog)}
        </div>
      )}

      {/* 配置文件内容 */}
      {renderCollapsibleSection(
        'config-file',
        '配置文件内容',
        <FileText className="w-5 h-5 text-orange-600" />,
        <div>
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-600">
              {config.targetOS === SupportedOS.WINDOWS ? 'my.ini' : 'my.cnf'}
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => copyToClipboard(configFileContent, 'config-file')}
              icon={<Copy className="w-4 h-4" />}
            >
              {copiedItem === 'config-file' ? '已复制' : '复制'}
            </Button>
          </div>
          
          <div className="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
            <pre className="text-sm text-gray-300 whitespace-pre-wrap">
              {configFileContent}
            </pre>
          </div>
        </div>
      )}

      {/* 确认按钮 */}
      <div className="flex items-center justify-center pt-6">
        <Button
          size="lg"
          onClick={onConfirm}
          icon={<Check className="w-5 h-5" />}
          className="px-8 py-3"
        >
          确认配置并继续
        </Button>
      </div>

      {/* 配置摘要 */}
      <div className="bg-mysql-primary/5 border border-mysql-primary/20 rounded-lg p-6">
        <h3 className="font-semibold text-mysql-text mb-3">配置摘要</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-mysql-primary">{config.version}</div>
            <div className="text-gray-600">MySQL版本</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-mysql-primary">{config.port}</div>
            <div className="text-gray-600">端口号</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-mysql-primary">{config.maxConnections}</div>
            <div className="text-gray-600">最大连接数</div>
          </div>
        </div>
      </div>
    </div>
  );
}
