'use client';

// MySQLAi.de - Markdown编辑器组件
// 基于@uiw/react-md-editor的专业Markdown编辑器

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { motion } from 'framer-motion';
import { Eye, EyeOff, FileText, Maximize2, Minimize2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

// 动态导入Markdown编辑器，避免SSR问题
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor').then((mod) => mod.default),
  { ssr: false }
);

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: number;
  className?: string;
  disabled?: boolean;
  error?: string;
}

export default function MarkdownEditor({
  value,
  onChange,
  placeholder = '请输入文章内容...',
  height = 400,
  className,
  disabled = false,
  error
}: MarkdownEditorProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 确保组件已挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 处理值变化
  const handleChange = (val?: string) => {
    onChange(val || '');
  };

  // 切换预览模式
  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  // 切换全屏模式
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 如果还没有挂载，显示加载状态
  if (!mounted) {
    return (
      <div className={cn(
        'border-2 border-mysql-border rounded-lg bg-white',
        className
      )}>
        <div className="flex items-center justify-between p-3 border-b border-mysql-border bg-gray-50">
          <div className="flex items-center space-x-2">
            <FileText className="w-4 h-4 text-mysql-text-light" />
            <span className="text-sm font-medium text-mysql-text">Markdown编辑器</span>
          </div>
        </div>
        <div 
          className="flex items-center justify-center bg-gray-50"
          style={{ height: height }}
        >
          <div className="text-center">
            <div className="w-6 h-6 border-2 border-mysql-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm text-mysql-text-light">加载编辑器...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'relative',
        isFullscreen && 'fixed inset-0 z-50 bg-white',
        className
      )}
    >
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between p-3 border border-mysql-border border-b-0 rounded-t-lg bg-gray-50">
        <div className="flex items-center space-x-2">
          <FileText className="w-4 h-4 text-mysql-text-light" />
          <span className="text-sm font-medium text-mysql-text">Markdown编辑器</span>
          {error && (
            <span className="text-sm text-red-600">• {error}</span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* 预览模式切换 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={togglePreview}
            icon={isPreviewMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            className="text-mysql-text-light hover:text-mysql-primary"
            disabled={disabled}
          >
            {isPreviewMode ? '编辑' : '预览'}
          </Button>
          
          {/* 全屏模式切换 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFullscreen}
            icon={isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            className="text-mysql-text-light hover:text-mysql-primary"
            disabled={disabled}
          >
            {isFullscreen ? '退出全屏' : '全屏'}
          </Button>
        </div>
      </div>

      {/* Markdown编辑器 */}
      <div className={cn(
        'border border-mysql-border rounded-b-lg overflow-hidden',
        error && 'border-red-300',
        disabled && 'opacity-50 pointer-events-none'
      )}>
        <MDEditor
          value={value}
          onChange={handleChange}
          preview={isPreviewMode ? 'preview' : 'edit'}
          hideToolbar={false}
          visibleDragbar={false}
          height={isFullscreen ? window.innerHeight - 120 : height}
          data-color-mode="light"
          textareaProps={{
            placeholder,
            style: {
              fontSize: 14,
              lineHeight: 1.6,
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
            }
          }}
          style={{
            backgroundColor: 'white'
          }}
        />
      </div>

      {/* 全屏模式下的关闭按钮 */}
      {isFullscreen && (
        <div className="absolute top-4 right-4">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFullscreen}
            icon={<Minimize2 className="w-4 h-4" />}
            className="bg-white shadow-lg"
          >
            退出全屏
          </Button>
        </div>
      )}

      {/* 编辑器底部信息 */}
      <div className="flex items-center justify-between px-3 py-2 text-xs text-mysql-text-light bg-gray-50 border border-mysql-border border-t-0 rounded-b-lg">
        <div className="flex items-center space-x-4">
          <span>字符数: {value.length}</span>
          <span>行数: {value.split('\n').length}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>支持Markdown语法</span>
          <span>•</span>
          <span>Ctrl+S 保存</span>
        </div>
      </div>
    </motion.div>
  );
}
