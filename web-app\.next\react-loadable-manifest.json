{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/vendors.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app", "files": ["static/chunks/vendors.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error", "files": ["static/chunks/vendors.js"]}, "app\\tools\\er-diagram\\components\\DiagramViewer.tsx -> ../lib/diagram-config": {"id": "app\\tools\\er-diagram\\components\\DiagramViewer.tsx -> ../lib/diagram-config", "files": []}, "app\\tools\\er-diagram\\page.tsx -> ./components/DiagramViewer": {"id": "app\\tools\\er-diagram\\page.tsx -> ./components/DiagramViewer", "files": []}}