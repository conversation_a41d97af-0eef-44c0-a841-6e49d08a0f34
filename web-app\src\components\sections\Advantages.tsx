'use client';

// MySQLAi.de - Advantages优势展示组件
// 参考ePhone AI的布局设计，展示6个核心竞争优势

import React from 'react';
import { motion } from 'framer-motion';
import { Globe, Shield, Clock, Award, TrendingUp, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AdvantagesProps } from '@/lib/types';

interface AdvantagesComponentProps {
  className?: string;
}

// 优势数据配置
const ADVANTAGES_DATA = [
  {
    id: 'global-expertise',
    emoji: '🌍',
    title: '#1 MySQL专家',
    description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',
    details: '覆盖全球8个地区，超过5万用户信赖',
    stats: {
      customers: '1000+',
      regions: '8',
      users: '50000+',
    },
    icon: Globe,
    color: 'mysql-primary',
  },
  {
    id: 'compatibility-support',
    emoji: '📝',
    title: '兼容性与支持',
    description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',
    details: '支持MySQL 5.7到8.0的所有主流版本',
    versions: ['5.7', '8.0', '8.1', '8.2'],
    icon: Shield,
    color: 'mysql-accent',
  },
  {
    id: 'flexible-pricing',
    emoji: '💰',
    title: '灵活计费',
    description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',
    details: '透明计费，性价比最高的MySQL服务',
    pricing: {
      model: 'pay-as-you-go',
      transparency: '100%',
      hidden_fees: false,
    },
    icon: TrendingUp,
    color: 'mysql-success',
  },
  {
    id: 'global-deployment',
    emoji: '⚡',
    title: '全球布局',
    description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',
    details: '全球用户享受一致的高速服务体验',
    datacenters: 7,
    response_time: '<100ms',
    icon: Award,
    color: 'mysql-warning',
  },
  {
    id: 'service-guarantee',
    emoji: '⏰',
    title: '服务保障',
    description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',
    details: '专业运维团队，99.9%服务可用性保证',
    sla: '99.9%',
    support: '7x24',
    icon: Clock,
    color: 'mysql-error',
  },
  {
    id: 'transparent-billing',
    emoji: '🎈',
    title: '透明计费',
    description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',
    details: '无隐藏费用，按实际使用量计费',
    billing: {
      transparency: true,
      industry_standard: true,
      hidden_fees: false,
    },
    icon: Users,
    color: 'mysql-info',
  },
] as const;

export default function Advantages({ className }: AdvantagesComponentProps) {
  return (
    <section
      className={cn(
        'py-20 px-4 bg-gradient-to-b from-white to-mysql-primary-light/20',
        className
      )}
    >
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-mysql-text mb-6">
            核心优势
          </h2>
          <p className="text-lg sm:text-xl text-mysql-text-light max-w-3xl mx-auto leading-relaxed">
            专业的MySQL服务平台，为全球企业提供最优质的数据库解决方案
          </p>
        </motion.div>

        {/* 优势列表 */}
        <div className="space-y-8">
          {ADVANTAGES_DATA.map((advantage, index) => {
            const IconComponent = advantage.icon;
            const isEven = index % 2 === 0;

            return (
              <motion.div
                key={advantage.id}
                initial={{ opacity: 0, x: isEven ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ 
                  duration: 0.8, 
                  delay: index * 0.1,
                  ease: "easeOut" 
                }}
                viewport={{ once: true }}
                className="group"
              >
                <div className={cn(
                  'flex flex-col lg:flex-row items-center gap-8 p-8 bg-white rounded-2xl shadow-lg border border-mysql-border',
                  'hover:shadow-2xl hover:scale-102 transition-all duration-300 ease-out',
                  isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
                )}>
                  {/* 图标和emoji区域 */}
                  <div className="flex-shrink-0">
                    <div className="relative">
                      {/* 背景装饰圆圈 */}
                      <div className={cn(
                        'absolute inset-0 bg-gradient-to-br rounded-full opacity-20 scale-150',
                        `from-${advantage.color} to-${advantage.color}-dark`
                      )} />
                      
                      {/* 主图标容器 */}
                      <div className={cn(
                        'relative flex items-center justify-center w-24 h-24 rounded-full',
                        'bg-gradient-to-br shadow-lg group-hover:scale-110 transition-transform duration-300',
                        `from-${advantage.color} to-${advantage.color}-dark`
                      )}>
                        <IconComponent className="w-12 h-12 text-white" />
                      </div>
                      
                      {/* Emoji装饰 */}
                      <div className="absolute -top-2 -right-2 text-2xl bg-white rounded-full w-10 h-10 flex items-center justify-center shadow-md">
                        {advantage.emoji}
                      </div>
                    </div>
                  </div>

                  {/* 内容区域 */}
                  <div className={cn(
                    'flex-1 text-center lg:text-left',
                    !isEven && 'lg:text-right'
                  )}>
                    <h3 className="text-2xl font-bold text-mysql-text mb-3 group-hover:text-mysql-primary transition-colors duration-300">
                      {advantage.title}
                    </h3>
                    <p className="text-lg text-mysql-text-light mb-4 leading-relaxed">
                      {advantage.description}
                    </p>
                    <p className="text-sm text-mysql-text-light/80 italic">
                      {advantage.details}
                    </p>

                    {/* 统计数据展示 */}
                    {'stats' in advantage && advantage.stats && (
                      <div className={cn(
                        'flex gap-6 mt-4',
                        isEven ? 'justify-start' : 'lg:justify-end justify-center'
                      )}>
                        <div className="text-center">
                          <div className="text-xl font-bold text-mysql-primary">
                            {advantage.stats.customers}
                          </div>
                          <div className="text-xs text-mysql-text-light">企业客户</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-mysql-primary">
                            {advantage.stats.regions}
                          </div>
                          <div className="text-xs text-mysql-text-light">服务地区</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-mysql-primary">
                            {advantage.stats.users}
                          </div>
                          <div className="text-xs text-mysql-text-light">用户信赖</div>
                        </div>
                      </div>
                    )}

                    {/* 版本支持展示 */}
                    {'versions' in advantage && advantage.versions && (
                      <div className={cn(
                        'flex gap-2 mt-4',
                        isEven ? 'justify-start' : 'lg:justify-end justify-center'
                      )}>
                        {advantage.versions.map((version: string, vIndex: number) => (
                          <span
                            key={vIndex}
                            className="px-3 py-1 bg-mysql-primary-light text-mysql-primary text-xs font-medium rounded-full"
                          >
                            MySQL {version}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* 底部CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-mysql-text-light mb-6">
            准备体验专业的MySQL服务了吗？
          </p>
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className={cn(
              'inline-flex items-center px-8 py-4 bg-mysql-primary text-white',
              'text-lg font-semibold rounded-xl shadow-lg',
              'hover:bg-mysql-primary-dark hover:shadow-xl',
              'transition-all duration-300 ease-out',
              'focus:outline-none focus:ring-4 focus:ring-mysql-primary/30'
            )}
          >
            <span>立即开始</span>
            <TrendingUp className="ml-2 w-5 h-5" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
