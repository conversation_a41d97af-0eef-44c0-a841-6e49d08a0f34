
-- 测试激活码数据库初始化脚本
-- 用于MySQL桌面安装程序的激活测试

-- 创建license_keys表
CREATE TABLE IF NOT EXISTS license_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL UNIQUE,
    is_valid INTEGER DEFAULT 1,
    is_used INTEGER DEFAULT 0,
    duration_hours INTEGER DEFAULT 24,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建activations表
CREATE TABLE IF NOT EXISTS activations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL,
    machine_id TEXT NOT NULL,
    machine_fingerprint TEXT,
    activated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    is_active INTEGER DEFAULT 1,
    FOREIGN KEY (license_key) REFERENCES license_keys (license_key)
);

-- 插入测试激活码
INSERT OR REPLACE INTO license_keys (license_key, is_valid, is_used, duration_hours) 
   VALUES ('MYSQL-TEST-2024-DEMO-0001', 1, 0, 24); -- 24小时测试许可证
INSERT OR REPLACE INTO license_keys (license_key, is_valid, is_used, duration_hours) 
   VALUES ('MYSQL-PROF-2024-FULL-0001', 1, 0, 8760); -- 专业版年度许可证
INSERT OR REPLACE INTO license_keys (license_key, is_valid, is_used, duration_hours) 
   VALUES ('MYSQL-EVAL-2024-TRIAL-001', 1, 0, 168); -- 评估版周试用许可证
INSERT OR REPLACE INTO license_keys (license_key, is_valid, is_used, duration_hours) 
   VALUES ('MYSQL-3DFF-36D5-37DF-4258', 1, 0, 72); -- 随机生成的3天许可证

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_license_key ON activations (license_key);
CREATE INDEX IF NOT EXISTS idx_machine_id ON activations (machine_id);
CREATE INDEX IF NOT EXISTS idx_expires_at ON activations (expires_at);
