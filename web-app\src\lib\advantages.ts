// MySQLAi.de - 优势展示配置文件
// 管理网站优势展示相关的数据和配置

import { Globe, Shield, Clock, Award, TrendingUp, Users, Database, Zap, FileText, Settings } from 'lucide-react';

// 核心优势数据
export const CORE_ADVANTAGES = [
  {
    id: 'global-expertise',
    emoji: '🌍',
    title: '#1 MySQL专家',
    description: '100%专业的MySQL优化服务，已稳定服务1000+企业客户！',
    details: '覆盖全球8个地区，超过5万用户信赖',
    stats: {
      customers: '1000+',
      regions: '8',
      users: '50000+',
    },
    icon: 'Globe',
    color: 'mysql-primary',
  },
  {
    id: 'compatibility-support',
    emoji: '📝',
    title: '兼容性与支持',
    description: '完全兼容各种MySQL版本，确保无缝集成和迁移。',
    details: '支持MySQL 5.7到8.0的所有主流版本',
    versions: ['5.7', '8.0', '8.1', '8.2'],
    icon: 'Shield',
    color: 'mysql-accent',
  },
  {
    id: 'flexible-pricing',
    emoji: '💰',
    title: '灵活计费',
    description: '按需付费，无隐藏费用。MySQL性能优化，智能负载均衡。',
    details: '透明计费，性价比最高的MySQL服务',
    pricing: {
      model: 'pay-as-you-go',
      transparency: '100%',
      hidden_fees: false,
    },
    icon: 'TrendingUp',
    color: 'mysql-success',
  },
  {
    id: 'global-deployment',
    emoji: '⚡',
    title: '全球布局',
    description: '部署于全球7个数据中心，自动负载均衡确保快速响应。',
    details: '全球用户享受一致的高速服务体验',
    datacenters: 7,
    response_time: '<100ms',
    icon: 'Award',
    color: 'mysql-warning',
  },
  {
    id: 'service-guarantee',
    emoji: '⏰',
    title: '服务保障',
    description: '7*24小时技术支持，确保服务不间断，支持企业级SLA。',
    details: '专业运维团队，99.9%服务可用性保证',
    sla: '99.9%',
    support: '7x24',
    icon: 'Clock',
    color: 'mysql-error',
  },
  {
    id: 'transparent-billing',
    emoji: '🎈',
    title: '透明计费',
    description: '与行业标准同步，公平无猫腻，性价比最高的MySQL服务。',
    details: '无隐藏费用，按实际使用量计费',
    billing: {
      transparency: true,
      industry_standard: true,
      hidden_fees: false,
    },
    icon: 'Users',
    color: 'mysql-info',
  },
] as const;

// 技术优势数据
export const TECHNICAL_ADVANTAGES = [
  {
    id: 'ai-optimization',
    emoji: '🤖',
    title: 'AI智能优化',
    description: '基于机器学习的MySQL性能优化，自动识别瓶颈并提供解决方案。',
    details: '平均性能提升300%，故障预测准确率95%',
    icon: 'Zap',
    color: 'mysql-primary',
  },
  {
    id: 'real-time-monitoring',
    emoji: '📊',
    title: '实时监控',
    description: '全方位的数据库监控系统，实时掌握数据库运行状态。',
    details: '毫秒级监控精度，智能告警系统',
    icon: 'Database',
    color: 'mysql-accent',
  },
  {
    id: 'automated-backup',
    emoji: '💾',
    title: '自动备份',
    description: '智能备份策略，确保数据安全，支持多种恢复方案。',
    details: '增量备份，秒级恢复，99.99%数据安全保障',
    icon: 'Shield',
    color: 'mysql-success',
  },
  {
    id: 'performance-tuning',
    emoji: '🚀',
    title: '性能调优',
    description: '专业的MySQL性能调优服务，显著提升数据库响应速度。',
    details: '查询速度提升10倍，资源利用率优化50%',
    icon: 'TrendingUp',
    color: 'mysql-warning',
  },
] as const;

// 服务优势数据
export const SERVICE_ADVANTAGES = [
  {
    id: 'expert-team',
    emoji: '👥',
    title: '专家团队',
    description: '资深MySQL专家团队，平均15年数据库经验。',
    details: 'MySQL官方认证专家，大型项目实战经验',
    icon: 'Users',
    color: 'mysql-primary',
  },
  {
    id: 'custom-solutions',
    emoji: '🎯',
    title: '定制方案',
    description: '根据业务需求定制专属的MySQL解决方案。',
    details: '一对一咨询，量身定制，确保最佳效果',
    icon: 'Settings',
    color: 'mysql-accent',
  },
  {
    id: 'comprehensive-support',
    emoji: '🛠️',
    title: '全面支持',
    description: '从部署到维护的全生命周期技术支持服务。',
    details: '涵盖安装、配置、优化、故障排除全流程',
    icon: 'FileText',
    color: 'mysql-success',
  },
] as const;

// 图标映射
export const ADVANTAGE_ICON_MAP = {
  Globe,
  Shield,
  Clock,
  Award,
  TrendingUp,
  Users,
  Database,
  Zap,
  FileText,
  Settings,
} as const;

// 优势分类
export const ADVANTAGE_CATEGORIES = {
  core: '核心优势',
  technical: '技术优势',
  service: '服务优势',
} as const;

// 颜色主题映射
export const ADVANTAGE_COLOR_THEMES = {
  'mysql-primary': {
    bg: 'bg-mysql-primary',
    text: 'text-mysql-primary',
    border: 'border-mysql-primary',
    gradient: 'from-mysql-primary to-mysql-primary-dark',
  },
  'mysql-accent': {
    bg: 'bg-mysql-accent',
    text: 'text-mysql-accent',
    border: 'border-mysql-accent',
    gradient: 'from-mysql-accent to-blue-600',
  },
  'mysql-success': {
    bg: 'bg-mysql-success',
    text: 'text-mysql-success',
    border: 'border-mysql-success',
    gradient: 'from-mysql-success to-green-600',
  },
  'mysql-warning': {
    bg: 'bg-mysql-warning',
    text: 'text-mysql-warning',
    border: 'border-mysql-warning',
    gradient: 'from-mysql-warning to-yellow-600',
  },
  'mysql-error': {
    bg: 'bg-mysql-error',
    text: 'text-mysql-error',
    border: 'border-mysql-error',
    gradient: 'from-mysql-error to-red-600',
  },
  'mysql-info': {
    bg: 'bg-mysql-info',
    text: 'text-mysql-info',
    border: 'border-mysql-info',
    gradient: 'from-mysql-info to-blue-700',
  },
} as const;

// 工具函数

/**
 * 根据ID获取核心优势
 */
export function getCoreAdvantageById(id: string) {
  return CORE_ADVANTAGES.find(advantage => advantage.id === id);
}

/**
 * 根据ID获取技术优势
 */
export function getTechnicalAdvantageById(id: string) {
  return TECHNICAL_ADVANTAGES.find(advantage => advantage.id === id);
}

/**
 * 根据ID获取服务优势
 */
export function getServiceAdvantageById(id: string) {
  return SERVICE_ADVANTAGES.find(advantage => advantage.id === id);
}

/**
 * 获取图标组件
 */
export function getAdvantageIconComponent(iconName: keyof typeof ADVANTAGE_ICON_MAP) {
  return ADVANTAGE_ICON_MAP[iconName];
}

/**
 * 获取颜色主题
 */
export function getAdvantageColorTheme(colorName: keyof typeof ADVANTAGE_COLOR_THEMES) {
  return ADVANTAGE_COLOR_THEMES[colorName];
}

/**
 * 获取所有优势（按分类）
 */
export function getAllAdvantages() {
  return {
    core: CORE_ADVANTAGES,
    technical: TECHNICAL_ADVANTAGES,
    service: SERVICE_ADVANTAGES,
  };
}

/**
 * 获取随机优势
 */
export function getRandomAdvantages(count: number = 3, category: keyof typeof ADVANTAGE_CATEGORIES = 'core') {
  let advantages;
  switch (category) {
    case 'core':
      advantages = [...CORE_ADVANTAGES];
      break;
    case 'technical':
      advantages = [...TECHNICAL_ADVANTAGES];
      break;
    case 'service':
      advantages = [...SERVICE_ADVANTAGES];
      break;
    default:
      advantages = [...CORE_ADVANTAGES];
  }
  
  // 随机打乱数组
  for (let i = advantages.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [advantages[i], advantages[j]] = [advantages[j], advantages[i]];
  }
  
  return advantages.slice(0, count);
}

/**
 * 格式化统计数值
 */
export function formatAdvantageValue(value: string | number): string {
  if (typeof value === 'number') {
    return value.toLocaleString();
  }
  
  // 如果是字符串数字，添加千分位分隔符
  if (typeof value === 'string' && /^\d+$/.test(value)) {
    return parseInt(value).toLocaleString();
  }
  
  return value.toString();
}

/**
 * 验证优势数据完整性
 */
export function validateAdvantageData(advantage: any): boolean {
  const requiredFields = ['id', 'title', 'description', 'icon', 'color'];
  return requiredFields.every(field => advantage[field] !== undefined);
}

/**
 * 获取优势的显示标签
 */
export function getAdvantageDisplayLabel(advantage: any): string {
  if (advantage.emoji) {
    return `${advantage.emoji} ${advantage.title}`;
  }
  return advantage.title;
}
