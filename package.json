{"name": "mysq<PERSON>-monorepo", "version": "1.0.0", "description": "MySQLAi Monorepo - Web App and Desktop App", "private": true, "scripts": {"dev:web": "cd web-app && npm run dev", "dev:desktop": "cd desktop-app && npm run dev", "build:web": "cd web-app && npm run build", "build:desktop": "cd desktop-app && npm run build", "install:all": "cd web-app && npm install && cd ../desktop-app && npm install", "test:playwright": "cd web-app && npm run test:playwright"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "clsx": "^2.1.1", "lucide-react": "^0.525.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}