{"name": "mysq<PERSON>-monorepo", "version": "1.0.0", "description": "MySQLAi Monorepo - Web App and Desktop App", "private": true, "workspaces": ["web-app", "desktop-app"], "scripts": {"dev": "npm run dev --workspace=web-app", "dev:web": "npm run dev --workspace=web-app", "dev:desktop": "npm run dev --workspace=desktop-app", "build": "npm run build --workspaces", "build:web": "npm run build --workspace=web-app", "build:desktop": "npm run build --workspace=desktop-app", "lint": "npm run lint --workspaces", "type-check": "npm run type-check --workspaces", "clean": "npm run clean --workspaces", "test": "npm run test --workspaces", "test:playwright": "npm run test:playwright --workspace=web-app"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5", "tailwindcss": "^4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "@playwright/test": "^1.53.1", "eslint": "^9", "eslint-config-next": "15.3.4"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "clsx": "^2.1.1", "framer-motion": "^12.22.0", "lucide-react": "^0.525.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}