'use client';

/**
 * MySQL高级配置向导组件
 * 为需要自定义配置的用户提供多步骤向导式配置界面
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronLeft,
  ChevronRight,
  Database,
  Shield,
  Zap,
  Download,
  Upload,
  Check,
  AlertTriangle,
  Monitor,
  HardDrive
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { LoadingState } from '@/components/ui/StateComponents';
import {
  SupportedOS,
  MySQLVersion,
  AdvancedConfig
} from '../types/mysql-installer';
import { generateDefaultConfig, validateConfigParameters } from '../lib/config-generator';
import { getSystemInfo } from '../lib/system-detection';

interface AdvancedWizardProps {
  initialConfig?: Partial<AdvancedConfig>;
  onConfigComplete?: (config: AdvancedConfig) => void;
  onCancel?: () => void;
  onBackToQuick?: () => void;
  className?: string;
}

/**
 * 向导步骤定义
 */
const WIZARD_STEPS = [
  {
    id: 'system',
    title: '系统和版本',
    description: '选择目标系统和MySQL版本',
    icon: <Monitor className="w-5 h-5" />
  },
  {
    id: 'paths',
    title: '安装路径',
    description: '配置安装目录和数据目录',
    icon: <HardDrive className="w-5 h-5" />
  },
  {
    id: 'database',
    title: '数据库配置',
    description: '设置端口、字符集和存储引擎',
    icon: <Database className="w-5 h-5" />
  },
  {
    id: 'security',
    title: '安全设置',
    description: '配置密码和用户权限',
    icon: <Shield className="w-5 h-5" />
  },
  {
    id: 'advanced',
    title: '高级选项',
    description: '性能优化和高级配置',
    icon: <Zap className="w-5 h-5" />
  }
] as const;

type WizardStepId = typeof WIZARD_STEPS[number]['id'];

/**
 * 高级配置向导组件
 */
export default function AdvancedWizard({
  initialConfig,
  onConfigComplete,
  onCancel,
  onBackToQuick,
  className
}: AdvancedWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStepId>('system');
  const [config, setConfig] = useState<Partial<AdvancedConfig>>(() => {
    // 初始化配置
    const systemInfo = getSystemInfo();
    const defaultConfig = generateDefaultConfig(systemInfo.os);
    
    return {
      ...defaultConfig,
      // 高级配置默认值
      innodbBufferPoolSize: '512M',
      maxConnections: 300,
      queryCacheSize: '64M',
      slowQueryLog: false,
      longQueryTime: 2,
      customConfig: {},
      ...initialConfig
    };
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);

  // 获取当前步骤索引
  const currentStepIndex = WIZARD_STEPS.findIndex(step => step.id === currentStep);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === WIZARD_STEPS.length - 1;

  // 更新配置
  const updateConfig = useCallback((field: string, value: string | number | boolean) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除相关错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  // 验证当前步骤
  const validateCurrentStep = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    switch (currentStep) {
      case 'system':
        if (!config.version) {
          newErrors.version = '请选择MySQL版本';
        }
        if (!config.targetOS) {
          newErrors.targetOS = '请选择目标操作系统';
        }
        break;

      case 'paths':
        if (!config.installPath?.trim()) {
          newErrors.installPath = '安装路径不能为空';
        }
        if (!config.dataPath?.trim()) {
          newErrors.dataPath = '数据目录不能为空';
        }
        break;

      case 'database':
        if (!config.port || config.port < 1024 || config.port > 65535) {
          newErrors.port = '端口号必须在1024-65535范围内';
        }
        if (!config.charset?.trim()) {
          newErrors.charset = '请选择字符集';
        }
        break;

      case 'security':
        if (!config.rootPassword || config.rootPassword.length < 6) {
          newErrors.rootPassword = 'root密码长度至少6位';
        }
        break;

      case 'advanced':
        if (config.maxConnections && (config.maxConnections < 10 || config.maxConnections > 10000)) {
          newErrors.maxConnections = '最大连接数应在10-10000范围内';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [currentStep, config]);

  // 下一步
  const handleNext = useCallback(async () => {
    if (!validateCurrentStep()) {
      return;
    }

    if (isLastStep) {
      // 最后一步，完成配置
      setIsValidating(true);
      try {
        const fullConfig = config as AdvancedConfig;
        const validation = validateConfigParameters(fullConfig);

        if (!validation.isValid) {
          setErrors(validation.errors.reduce((acc, validationError, index) => {
            acc[`validation_${index}`] = validationError;
            return acc;
          }, {} as Record<string, string>));
          return;
        }

        onConfigComplete?.(fullConfig);
      } catch {
        setErrors({ general: '配置验证失败，请检查配置参数' });
      } finally {
        setIsValidating(false);
      }
    } else {
      // 进入下一步
      const nextStepIndex = currentStepIndex + 1;
      setCurrentStep(WIZARD_STEPS[nextStepIndex].id);
    }
  }, [validateCurrentStep, isLastStep, currentStepIndex, config, onConfigComplete]);

  // 上一步
  const handlePrevious = useCallback(() => {
    if (!isFirstStep) {
      const prevStepIndex = currentStepIndex - 1;
      setCurrentStep(WIZARD_STEPS[prevStepIndex].id);
    }
  }, [isFirstStep, currentStepIndex]);

  // 跳转到指定步骤
  const goToStep = useCallback((stepId: WizardStepId) => {
    setCurrentStep(stepId);
  }, []);

  // 导出配置
  const exportConfig = useCallback(() => {
    const configJson = JSON.stringify(config, null, 2);
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mysql-config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [config]);

  // 导入配置
  const importConfig = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedConfig = JSON.parse(e.target?.result as string);
        setConfig(prev => ({ ...prev, ...importedConfig }));
        setErrors({});
      } catch {
        setErrors({ import: '配置文件格式错误' });
      }
    };
    reader.readAsText(file);
  }, []);

  // 渲染步骤指示器
  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-4 mb-8">
      {WIZARD_STEPS.map((step, index) => {
        const isActive = step.id === currentStep;
        const isCompleted = index < currentStepIndex;
        const isClickable = index <= currentStepIndex;

        return (
          <div key={step.id} className="flex items-center">
            <button
              type="button"
              onClick={() => isClickable && goToStep(step.id)}
              disabled={!isClickable}
              className={cn(
                'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200',
                isActive 
                  ? 'border-mysql-primary bg-mysql-primary text-white' 
                  : isCompleted
                  ? 'border-green-500 bg-green-500 text-white'
                  : isClickable
                  ? 'border-gray-300 bg-white text-gray-600 hover:border-mysql-primary'
                  : 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed'
              )}
            >
              {isCompleted ? (
                <Check className="w-5 h-5" />
              ) : (
                <span className="text-sm font-medium">{index + 1}</span>
              )}
            </button>
            
            {index < WIZARD_STEPS.length - 1 && (
              <div className={cn(
                'w-12 h-0.5 mx-2',
                index < currentStepIndex ? 'bg-green-500' : 'bg-gray-200'
              )} />
            )}
          </div>
        );
      })}
    </div>
  );

  // 渲染步骤标题
  const renderStepHeader = () => {
    const step = WIZARD_STEPS[currentStepIndex];
    return (
      <div className="text-center mb-8">
        <div className="flex items-center justify-center space-x-3 mb-2">
          <div className="p-2 bg-mysql-primary/10 rounded-lg">
            {step.icon}
          </div>
          <h2 className="text-2xl font-bold text-mysql-text">{step.title}</h2>
        </div>
        <p className="text-gray-600">{step.description}</p>
      </div>
    );
  };

  // 渲染表单字段
  const renderFormField = (
    label: string,
    name: string,
    type: 'text' | 'number' | 'select' | 'password' | 'switch',
    options?: { label: string; value: string | number }[]
  ) => {
    const value = config[name as keyof typeof config];
    const error = errors[name];
    const hasError = Boolean(error);

    const baseInputClasses = cn(
      'w-full px-3 py-2 border rounded-lg transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-mysql-primary/20',
      hasError
        ? 'border-red-500 focus:border-red-500'
        : 'border-gray-300 focus:border-mysql-primary'
    );

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>

        {type === 'select' ? (
          <select
            value={value || ''}
            onChange={(e) => updateConfig(name, e.target.value)}
            className={baseInputClasses}
            aria-label={label}
          >
            <option value="">请选择...</option>
            {options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : type === 'switch' ? (
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={Boolean(value)}
              onChange={(e) => updateConfig(name, e.target.checked)}
              className="w-4 h-4 text-mysql-primary border-gray-300 rounded focus:ring-mysql-primary"
            />
            <span className="text-sm text-gray-600">启用此选项</span>
          </label>
        ) : (
          <input
            type={type}
            value={value || ''}
            onChange={(e) => updateConfig(name, type === 'number' ? Number(e.target.value) : e.target.value)}
            className={baseInputClasses}
            aria-label={label}
            placeholder={`请输入${label}`}
          />
        )}

        {hasError && (
          <p className="text-sm text-red-600 flex items-center space-x-1">
            <AlertTriangle className="w-4 h-4" />
            <span>{error}</span>
          </p>
        )}
      </div>
    );
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 'system':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {renderFormField(
                'MySQL版本',
                'version',
                'select',
                [
                  { label: 'MySQL 8.0.36 (推荐)', value: MySQLVersion.V8_0_36 },
                  { label: 'MySQL 8.0.35', value: MySQLVersion.V8_0_35 },
                  { label: 'MySQL 8.0.34', value: MySQLVersion.V8_0_34 }
                ]
              )}

              {renderFormField(
                '目标操作系统',
                'targetOS',
                'select',
                [
                  { label: 'Windows', value: SupportedOS.WINDOWS },
                  { label: 'macOS', value: SupportedOS.MACOS },
                  { label: 'Linux', value: SupportedOS.LINUX }
                ]
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">系统检测结果</h4>
              <p className="text-sm text-blue-700">
                当前检测到的系统：{getSystemInfo().osVersion} ({getSystemInfo().architecture})
              </p>
            </div>
          </div>
        );

      case 'paths':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {renderFormField('安装路径', 'installPath', 'text')}
              {renderFormField('数据目录', 'dataPath', 'text')}
              {renderFormField('日志目录', 'logPath', 'text')}
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">路径建议</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 确保路径具有足够的磁盘空间</li>
                <li>• 避免使用包含空格的路径</li>
                <li>• 数据目录应与安装目录分离</li>
              </ul>
            </div>
          </div>
        );

      case 'database':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {renderFormField('端口号', 'port', 'number')}

              {renderFormField(
                '字符集',
                'charset',
                'select',
                [
                  { label: 'utf8mb4 (推荐)', value: 'utf8mb4' },
                  { label: 'utf8', value: 'utf8' },
                  { label: 'latin1', value: 'latin1' }
                ]
              )}

              {renderFormField(
                '排序规则',
                'collation',
                'select',
                [
                  { label: 'utf8mb4_unicode_ci (推荐)', value: 'utf8mb4_unicode_ci' },
                  { label: 'utf8mb4_general_ci', value: 'utf8mb4_general_ci' },
                  { label: 'utf8_general_ci', value: 'utf8_general_ci' }
                ]
              )}

              {renderFormField(
                '默认存储引擎',
                'defaultStorageEngine',
                'select',
                [
                  { label: 'InnoDB (推荐)', value: 'InnoDB' },
                  { label: 'MyISAM', value: 'MyISAM' }
                ]
              )}
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {renderFormField('Root密码', 'rootPassword', 'password')}

              <div className="space-y-4">
                <h4 className="font-medium text-gray-800">安全选项</h4>
                <div className="space-y-3">
                  {renderFormField('启用SSL', 'enableSSL', 'switch')}
                  {renderFormField('禁用远程root登录', 'disableRemoteRoot', 'switch')}
                  {renderFormField('删除匿名用户', 'removeAnonymousUsers', 'switch')}
                  {renderFormField('删除测试数据库', 'removeTestDatabase', 'switch')}
                </div>
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-800 mb-2">安全提醒</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• 使用强密码，包含大小写字母、数字和特殊字符</li>
                <li>• 定期更换密码</li>
                <li>• 限制数据库访问权限</li>
              </ul>
            </div>
          </div>
        );

      case 'advanced':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {renderFormField('InnoDB缓冲池大小', 'innodbBufferPoolSize', 'text')}
              {renderFormField('最大连接数', 'maxConnections', 'number')}
              {renderFormField('查询缓存大小', 'queryCacheSize', 'text')}
              {renderFormField('慢查询时间阈值(秒)', 'longQueryTime', 'number')}
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-800">日志选项</h4>
              <div className="space-y-3">
                {renderFormField('启用慢查询日志', 'slowQueryLog', 'switch')}
                {renderFormField('启用二进制日志', 'enableBinlog', 'switch')}
                {renderFormField('启用错误日志', 'enableErrorLog', 'switch')}
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-800 mb-2">性能建议</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• InnoDB缓冲池大小建议设置为可用内存的70-80%</li>
                <li>• 最大连接数根据实际并发需求设置</li>
                <li>• 慢查询日志有助于性能优化</li>
              </ul>
            </div>
          </div>
        );

      default:
        return <div>未知步骤</div>;
    }
  };

  return (
    <div className={cn('max-w-4xl mx-auto', className)}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold text-mysql-text">高级配置向导</h1>
        
        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={exportConfig}
            icon={<Download className="w-4 h-4" />}
          >
            导出配置
          </Button>
          
          <label className="cursor-pointer">
            <Button
              variant="outline"
              size="sm"
              as="span"
              icon={<Upload className="w-4 h-4" />}
            >
              导入配置
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={importConfig}
              className="hidden"
            />
          </label>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onBackToQuick}
            icon={<ChevronLeft className="w-4 h-4" />}
          >
            返回一键安装
          </Button>
        </div>
      </div>

      {/* 步骤指示器 */}
      {renderStepIndicator()}

      {/* 步骤标题 */}
      {renderStepHeader()}

      {/* 错误提示 */}
      {Object.keys(errors).length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
        >
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="font-medium text-red-800">配置错误</span>
          </div>
          <ul className="text-sm text-red-700 space-y-1">
            {Object.values(errors).map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </motion.div>
      )}

      {/* 步骤内容 */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 mb-8">
        <AnimatePresence mode="wait">
          {isValidating ? (
            <LoadingState
              message="正在验证配置..."
              size="lg"
              variant="spinner"
              className="py-12"
            />
          ) : (
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* 渲染具体的步骤内容 */}
              {renderStepContent()}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 导航按钮 */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={isFirstStep ? onCancel : handlePrevious}
          icon={<ChevronLeft className="w-4 h-4" />}
          disabled={isValidating}
        >
          {isFirstStep ? '取消' : '上一步'}
        </Button>

        <div className="text-sm text-gray-500">
          第 {currentStepIndex + 1} 步，共 {WIZARD_STEPS.length} 步
        </div>

        <Button
          onClick={handleNext}
          icon={<ChevronRight className="w-4 h-4" />}
          disabled={isValidating}
          loading={isValidating}
        >
          {isLastStep ? '完成配置' : '下一步'}
        </Button>
      </div>
    </div>
  );
}
