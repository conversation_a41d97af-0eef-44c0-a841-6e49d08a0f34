// MySQLAi.de - 滚动相关工具函数
// 平滑滚动、锚点导航、滚动监听等功能

/**
 * 平滑滚动到指定元素
 */
export function scrollToElement(
  elementId: string,
  offset: number = 80,
  behavior: ScrollBehavior = 'smooth'
): void {
  const element = document.getElementById(elementId);
  if (!element) {
    console.warn(`Element with id "${elementId}" not found`);
    return;
  }

  const elementPosition = element.getBoundingClientRect().top;
  const offsetPosition = elementPosition + window.pageYOffset - offset;

  window.scrollTo({
    top: offsetPosition,
    behavior,
  });
}

/**
 * 平滑滚动到页面顶部
 */
export function scrollToTop(behavior: ScrollBehavior = 'smooth'): void {
  window.scrollTo({
    top: 0,
    behavior,
  });
}

/**
 * 平滑滚动到页面底部
 */
export function scrollToBottom(behavior: ScrollBehavior = 'smooth'): void {
  window.scrollTo({
    top: document.documentElement.scrollHeight,
    behavior,
  });
}

/**
 * 获取当前滚动位置
 */
export function getScrollPosition(): { x: number; y: number } {
  return {
    x: window.pageXOffset || document.documentElement.scrollLeft,
    y: window.pageYOffset || document.documentElement.scrollTop,
  };
}

/**
 * 获取页面总高度
 */
export function getPageHeight(): number {
  return Math.max(
    document.body.scrollHeight,
    document.body.offsetHeight,
    document.documentElement.clientHeight,
    document.documentElement.scrollHeight,
    document.documentElement.offsetHeight
  );
}

/**
 * 获取视窗高度
 */
export function getViewportHeight(): number {
  return window.innerHeight || document.documentElement.clientHeight;
}

/**
 * 计算滚动进度百分比
 */
export function getScrollProgress(): number {
  const scrollTop = getScrollPosition().y;
  const documentHeight = getPageHeight();
  const windowHeight = getViewportHeight();
  const scrollableHeight = documentHeight - windowHeight;
  
  if (scrollableHeight <= 0) return 0;
  
  return Math.min(Math.max(scrollTop / scrollableHeight, 0), 1);
}

/**
 * 检查元素是否在视窗中
 */
export function isElementInViewport(
  element: Element,
  threshold: number = 0
): boolean {
  const rect = element.getBoundingClientRect();
  const windowHeight = getViewportHeight();
  const windowWidth = window.innerWidth;

  return (
    rect.top >= -threshold &&
    rect.left >= -threshold &&
    rect.bottom <= windowHeight + threshold &&
    rect.right <= windowWidth + threshold
  );
}

/**
 * 获取当前可见的区域ID
 */
export function getCurrentSection(
  sectionIds: string[],
  offset: number = 100
): string | null {
  for (const id of sectionIds) {
    const element = document.getElementById(id);
    if (!element) continue;

    const rect = element.getBoundingClientRect();
    if (rect.top <= offset && rect.bottom > offset) {
      return id;
    }
  }
  return null;
}

/**
 * 滚动监听器类
 */
export class ScrollListener {
  private listeners: Array<{
    callback: (scrollData: ScrollData) => void;
    throttle?: number;
  }> = [];
  private isListening = false;
  private lastScrollTime = 0;

  constructor() {
    this.handleScroll = this.handleScroll.bind(this);
  }

  /**
   * 添加滚动监听器
   */
  addListener(
    callback: (scrollData: ScrollData) => void,
    throttle: number = 16
  ): () => void {
    const listener = { callback, throttle };
    this.listeners.push(listener);

    if (!this.isListening) {
      this.startListening();
    }

    // 返回移除监听器的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
      if (this.listeners.length === 0) {
        this.stopListening();
      }
    };
  }

  /**
   * 开始监听滚动
   */
  private startListening(): void {
    if (this.isListening) return;
    this.isListening = true;
    window.addEventListener('scroll', this.handleScroll, { passive: true });
  }

  /**
   * 停止监听滚动
   */
  private stopListening(): void {
    if (!this.isListening) return;
    this.isListening = false;
    window.removeEventListener('scroll', this.handleScroll);
  }

  /**
   * 处理滚动事件
   */
  private handleScroll(): void {
    const now = Date.now();
    
    this.listeners.forEach(({ callback, throttle = 16 }) => {
      if (now - this.lastScrollTime >= throttle) {
        const scrollData: ScrollData = {
          position: getScrollPosition(),
          progress: getScrollProgress(),
          direction: this.getScrollDirection(),
          isAtTop: getScrollPosition().y <= 10,
          isAtBottom: getScrollPosition().y >= getPageHeight() - getViewportHeight() - 10,
        };
        callback(scrollData);
      }
    });

    this.lastScrollTime = now;
  }

  /**
   * 获取滚动方向
   */
  private getScrollDirection(): 'up' | 'down' | 'none' {
    const currentY = getScrollPosition().y;
    const lastY = this.lastScrollY || 0;
    this.lastScrollY = currentY;

    if (currentY > lastY) return 'down';
    if (currentY < lastY) return 'up';
    return 'none';
  }

  private lastScrollY = 0;
}

/**
 * 滚动数据接口
 */
export interface ScrollData {
  position: { x: number; y: number };
  progress: number;
  direction: 'up' | 'down' | 'none';
  isAtTop: boolean;
  isAtBottom: boolean;
}

/**
 * 创建滚动监听器的React Hook
 */
export function useScrollListener(
  callback: (scrollData: ScrollData) => void,
  throttle: number = 16,
  deps: any[] = []
): void {
  // 注意：这个函数需要在React组件中使用React.useEffect
  // 这里只是提供函数签名，实际使用时需要导入React
}

/**
 * 锚点导航处理器
 */
export function handleAnchorNavigation(): void {
  // 处理页面加载时的锚点
  if (window.location.hash) {
    const elementId = window.location.hash.substring(1);
    setTimeout(() => {
      scrollToElement(elementId);
    }, 100);
  }

  // 处理锚点链接点击
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const link = target.closest('a[href^="#"]') as HTMLAnchorElement;
    
    if (link && link.hash) {
      e.preventDefault();
      const elementId = link.hash.substring(1);
      scrollToElement(elementId);
      
      // 更新URL但不触发页面跳转
      if (history.pushState) {
        history.pushState(null, '', link.hash);
      }
    }
  });
}

/**
 * 视差滚动效果
 */
export function createParallaxEffect(
  element: HTMLElement,
  speed: number = 0.5
): () => void {
  const scrollListener = new ScrollListener();
  
  const removeListener = scrollListener.addListener((scrollData) => {
    const yPos = scrollData.position.y * speed;
    element.style.transform = `translateY(${yPos}px)`;
  });

  return removeListener;
}

/**
 * 滚动进度条
 */
export function createScrollProgressBar(
  progressElement: HTMLElement
): () => void {
  const scrollListener = new ScrollListener();
  
  const removeListener = scrollListener.addListener((scrollData) => {
    const progress = scrollData.progress * 100;
    progressElement.style.width = `${progress}%`;
  });

  return removeListener;
}

/**
 * 滚动到元素可见时触发回调
 */
export function onElementVisible(
  elementId: string,
  callback: () => void,
  threshold: number = 0.1
): () => void {
  const element = document.getElementById(elementId);
  if (!element) {
    console.warn(`Element with id "${elementId}" not found`);
    return () => {};
  }

  const observer = new IntersectionObserver(
    ([entry]) => {
      if (entry.isIntersecting) {
        callback();
        observer.disconnect();
      }
    },
    { threshold }
  );

  observer.observe(element);

  return () => observer.disconnect();
}

/**
 * 防抖滚动处理器
 */
export function debounceScroll(
  callback: () => void,
  delay: number = 100
): () => void {
  let timeoutId: NodeJS.Timeout;
  
  const debouncedCallback = () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(callback, delay);
  };

  window.addEventListener('scroll', debouncedCallback, { passive: true });

  return () => {
    clearTimeout(timeoutId);
    window.removeEventListener('scroll', debouncedCallback);
  };
}

/**
 * 节流滚动处理器
 */
export function throttleScroll(
  callback: () => void,
  delay: number = 16
): () => void {
  let isThrottled = false;
  
  const throttledCallback = () => {
    if (isThrottled) return;
    
    isThrottled = true;
    requestAnimationFrame(() => {
      callback();
      isThrottled = false;
    });
  };

  window.addEventListener('scroll', throttledCallback, { passive: true });

  return () => {
    window.removeEventListener('scroll', throttledCallback);
  };
}
