// MySQLAi.de - 工具函数库
// 提供通用的工具函数和辅助方法

import { type ClassValue, clsx } from 'clsx';
import type { Database } from './database.types';
import type { KnowledgeCategory, KnowledgeItem, CodeExample } from './types';

// 图标名称到 emoji 的映射
const ICON_MAP: Record<string, string> = {
  'Database': '🗄️',
  'Server': '🖥️',
  'Table': '📋',
  'Edit': '✏️',
  'Search': '🔍',
  'Settings': '⚙️',
  'BookOpen': '📖',
  'Code': '💻',
  'FileText': '📄',
  'FolderOpen': '📁',
  'Users': '👥',
  'Mail': '📧',
  'Home': '🏠',
  'BarChart3': '📊',
  'Wrench': '🔧',
  'GitBranch': '🌿',
  'Download': '⬇️'
};

// 数据库类型别名
type DbKnowledgeCategory = Database['public']['Tables']['knowledge_categories']['Row'];
type DbKnowledgeArticle = Database['public']['Tables']['knowledge_articles']['Row'];
type DbCodeExample = Database['public']['Tables']['code_examples']['Row'];

/**
 * 将图标名称转换为对应的 emoji
 */
export function getIconEmoji(iconName: string): string {
  return ICON_MAP[iconName] || '📁';
}

// CSS类名合并工具（兼容Tailwind CSS）
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

// 延迟执行工具
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// 格式化日期
export function formatDate(
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Asia/Shanghai', // 中国标准时间
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat('zh-CN', finalOptions).format(dateObj);
}

// 格式化数字
export function formatNumber(
  num: number,
  options: Intl.NumberFormatOptions = {}
): string {
  return new Intl.NumberFormat('zh-CN', options).format(num);
}

// 生成随机ID
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证手机号格式（中国大陆）
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 截断文本
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// 首字母大写
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// 转换为短横线命名
export function kebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

// 转换为驼峰命名
export function camelCase(str: string): string {
  return str
    .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
    .replace(/^[A-Z]/, char => char.toLowerCase());
}

// 深度克隆对象
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
}

// 获取对象深层属性值
export function getNestedValue(obj: any, path: string, defaultValue: any = undefined): any {
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === null || result === undefined || !(key in result)) {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result;
}

// 设置对象深层属性值
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  let current = obj;
  
  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[lastKey] = value;
}

// 数组去重
export function uniqueArray<T>(array: T[], key?: keyof T): T[] {
  if (!key) {
    return [...new Set(array)];
  }
  
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}

// 数组分组
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = String(item[key]);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

// 检查是否为移动设备
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768;
}

// 检查是否为暗色主题
export function isDarkMode(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
}

// 平滑滚动到元素
export function scrollToElement(
  elementId: string,
  offset: number = 0,
  behavior: ScrollBehavior = 'smooth'
): void {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.offsetTop - offset;
    window.scrollTo({
      top: elementPosition,
      behavior,
    });
  }
}

// 复制文本到剪贴板
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('复制失败:', err);
    return false;
  }
}

// 下载文件
export function downloadFile(url: string, filename: string): void {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 生成颜色变体
export function generateColorVariants(baseColor: string): {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
} {
  // 这是一个简化版本，实际项目中可能需要更复杂的颜色计算
  return {
    50: `${baseColor}0D`,   // 5% opacity
    100: `${baseColor}1A`,  // 10% opacity
    200: `${baseColor}33`,  // 20% opacity
    300: `${baseColor}4D`,  // 30% opacity
    400: `${baseColor}66`,  // 40% opacity
    500: baseColor,         // base color
    600: `${baseColor}CC`,  // 80% opacity
    700: `${baseColor}B3`,  // 70% opacity
    800: `${baseColor}99`,  // 60% opacity
    900: `${baseColor}80`,  // 50% opacity
  };
}

// ============================================================================
// 数据库类型映射工具函数
// ============================================================================

/**
 * 将数据库的 knowledge_categories 行映射到前端 KnowledgeCategory 类型
 */
export function mapDatabaseCategoryToFrontend(dbCategory: DbKnowledgeCategory): KnowledgeCategory {
  return {
    id: dbCategory.id,
    name: dbCategory.name,
    description: dbCategory.description,
    icon: dbCategory.icon,
    color: dbCategory.color,
    order_index: dbCategory.order_index,
    created_at: dbCategory.created_at,
  };
}

/**
 * 将前端 KnowledgeCategory 类型映射到数据库插入格式
 */
export function mapFrontendCategoryToDatabase(
  category: Omit<KnowledgeCategory, 'id'> & { id?: string }
): Database['public']['Tables']['knowledge_categories']['Insert'] {
  return {
    id: category.id || generateId('cat'),
    name: category.name,
    description: category.description,
    icon: category.icon,
    color: category.color,
    order_index: category.order_index,
  };
}

/**
 * 将数据库的 code_examples 行映射到前端 CodeExample 类型
 */
export function mapDatabaseCodeExampleToFrontend(dbCodeExample: DbCodeExample): CodeExample {
  return {
    id: dbCodeExample.id,
    article_id: dbCodeExample.article_id,
    title: dbCodeExample.title,
    code: dbCodeExample.code,
    language: dbCodeExample.language,
    description: dbCodeExample.description,
    order_index: dbCodeExample.order_index,
    created_at: dbCodeExample.created_at,
  };
}

/**
 * 将前端 CodeExample 类型映射到数据库插入格式
 */
export function mapFrontendCodeExampleToDatabase(
  codeExample: Omit<CodeExample, 'id'> & { id?: string; articleId: string }
): Database['public']['Tables']['code_examples']['Insert'] {
  return {
    id: codeExample.id || generateId('code'),
    article_id: codeExample.articleId, // 字段名转换：articleId → article_id
    title: codeExample.title,
    code: codeExample.code,
    language: codeExample.language,
    description: codeExample.description || null,
    order_index: 0, // 默认排序
  };
}

/**
 * 将数据库的 knowledge_articles 行映射到前端 KnowledgeItem 类型
 * 注意：此函数不包含关联数据（codeExamples, relatedItems），需要单独查询
 */
export function mapDatabaseArticleToFrontend(
  dbArticle: DbKnowledgeArticle
): KnowledgeItem {
  return {
    id: dbArticle.id,
    title: dbArticle.title,
    description: dbArticle.description,
    content: dbArticle.content,
    category_id: dbArticle.category_id,
    tags: dbArticle.tags,
    difficulty: dbArticle.difficulty,
    order_index: dbArticle.order_index,
    last_updated: dbArticle.last_updated,
    created_at: dbArticle.created_at,
    updated_at: dbArticle.updated_at,
  };
}

/**
 * 将前端 KnowledgeItem 类型映射到数据库插入格式
 */
export function mapFrontendArticleToDatabase(
  article: Omit<KnowledgeItem, 'id'> & { id?: string }
): Database['public']['Tables']['knowledge_articles']['Insert'] {
  return {
    id: article.id || generateId('article'),
    title: article.title,
    description: article.description,
    content: article.content,
    category_id: article.category_id,
    tags: article.tags && article.tags.length > 0 ? article.tags : null,
    difficulty: article.difficulty,
    order_index: article.order_index,
    last_updated: article.last_updated,
  };
}

// ============================================================================
// 批量映射工具函数
// ============================================================================

/**
 * 批量映射数据库分类到前端格式
 */
export function mapDatabaseCategoriesToFrontend(dbCategories: DbKnowledgeCategory[]): KnowledgeCategory[] {
  return dbCategories.map(mapDatabaseCategoryToFrontend);
}

/**
 * 批量映射数据库文章到前端格式
 */
export function mapDatabaseArticlesToFrontend(dbArticles: DbKnowledgeArticle[]): KnowledgeItem[] {
  return dbArticles.map(article => mapDatabaseArticleToFrontend(article));
}

/**
 * 批量映射数据库代码示例到前端格式
 */
export function mapDatabaseCodeExamplesToFrontend(dbCodeExamples: DbCodeExample[]): CodeExample[] {
  return dbCodeExamples.map(mapDatabaseCodeExampleToFrontend);
}

/**
 * 安全的字段映射函数 - 处理可能的 null/undefined 值
 */
export function safeFieldMap<T, R>(
  value: T | null | undefined,
  mapper: (value: T) => R,
  defaultValue: R
): R {
  return value !== null && value !== undefined ? mapper(value) : defaultValue;
}

/**
 * 验证映射结果的完整性
 */
export function validateMappedData<T extends { id: string }>(
  data: T[],
  requiredFields: (keyof T)[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  data.forEach((item, index) => {
    requiredFields.forEach(field => {
      if (!item[field]) {
        errors.push(`Item ${index} missing required field: ${String(field)}`);
      }
    });
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}
