'use client';

/**
 * 错误边界组件 - ER图生成工具
 * 捕获和处理React组件错误，提供友好的错误界面和恢复机制
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import Link from 'next/link';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * 错误边界组件
 * 捕获子组件中的JavaScript错误，记录错误并显示友好的错误界面
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新state以显示错误界面
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // 这里可以将错误信息发送到错误报告服务
    // reportErrorToService(error, errorInfo);
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误界面
      return (
        <div className="min-h-screen bg-gradient-to-br from-mysql-primary-light via-white to-blue-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-2xl shadow-xl border border-mysql-border p-8 text-center">
            {/* 错误图标 */}
            <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-6">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>

            {/* 错误标题 */}
            <h1 className="text-2xl font-bold text-mysql-text mb-4">
              出现了一些问题
            </h1>

            {/* 错误描述 */}
            <p className="text-mysql-text-light mb-6 leading-relaxed">
              ER图生成工具遇到了意外错误。我们已经记录了这个问题，请尝试以下解决方案：
            </p>

            {/* 解决方案建议 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold text-mysql-text mb-2">建议的解决方案：</h3>
              <ul className="text-sm text-mysql-text-light space-y-1">
                <li>• 点击下方&quot;重新加载&quot;按钮重试</li>
                <li>• 检查您的SQL语句格式是否正确</li>
                <li>• 清除浏览器缓存后重新访问</li>
                <li>• 如果问题持续存在，请联系技术支持</li>
              </ul>
            </div>

            {/* 错误详情（开发环境显示） */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
                <h3 className="text-sm font-medium text-red-800 mb-2">错误详情：</h3>
                <pre className="text-xs text-red-700 overflow-auto max-h-32">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                type="button"
                onClick={this.handleReset}
                className="inline-flex items-center justify-center px-6 py-3 bg-mysql-primary text-white rounded-lg hover:bg-mysql-primary-dark transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                重新加载
              </button>

              <button
                type="button"
                onClick={() => window.location.reload()}
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新页面
              </button>

              <Link
                href="/tools"
                className="inline-flex items-center justify-center px-6 py-3 border border-mysql-primary text-mysql-primary rounded-lg hover:bg-mysql-primary hover:text-white transition-all duration-300 hover:scale-105 shadow-lg"
              >
                <Home className="w-4 h-4 mr-2" />
                返回工具集
              </Link>
            </div>

            {/* 帮助信息 */}
            <div className="mt-8 pt-6 border-t border-mysql-border">
              <p className="text-sm text-mysql-text-light">
                如果问题持续存在，请
                <Link href="/contact" className="text-mysql-primary hover:underline ml-1">
                  联系我们
                </Link>
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
