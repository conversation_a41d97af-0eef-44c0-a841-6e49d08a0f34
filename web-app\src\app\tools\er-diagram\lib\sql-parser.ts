/**
 * SQL解析器 - 将SQL DDL语句解析为ER图数据结构
 * 基于node-sql-parser库实现，支持MySQL语法
 */

import { Parser } from 'node-sql-parser';
import type {
  TableInfo,
  ColumnInfo,
  ForeignKeyInfo,
  SqlParseResult,
  ParseError
} from '../types/er-diagram';

// node-sql-parser AST类型定义
interface CreateTableAST {
  type: string;
  keyword: string;
  table: Array<{ db: string | null; table: string; name?: string }>;
  create_definitions: CreateDefinition[];
  table_options?: unknown[] | Record<string, unknown>;
}

interface CreateDefinition {
  column?: { column: string };
  definition?: ColumnDefinition | CreateDefinition[];
  resource: string;
  primary_key?: string;
  constraint_type?: string;
  reference_definition?: ReferenceDefinition;
}

interface ColumnDefinition {
  dataType: string;
  length?: number | number[];
  constraint?: ConstraintDefinition[];
}

interface ConstraintDefinition {
  type: string;
  value?: string | number;
}

interface ReferenceDefinition {
  table?: { table: string };
  definition?: Array<{ column: string }>;
}

/**
 * SQL解析器类
 */
export class SqlParser {
  private parser: Parser;

  constructor() {
    // 初始化SQL解析器，指定MySQL方言
    this.parser = new Parser();
  }

  /**
   * 解析SQL DDL语句，返回表信息数组
   * @param sql SQL DDL语句字符串
   * @returns 解析结果
   */
  public async parseSql(sql: string): Promise<SqlParseResult> {
    try {
      console.log('开始解析SQL:', sql.substring(0, 100) + '...');

      // 预处理SQL，移除不支持的语句
      const processedSql = this.preprocessSql(sql);
      
      // 分割多个SQL语句
      const statements = this.splitSqlStatements(processedSql);
      console.log(`成功分割SQL语句，共有${statements.length}条语句`);

      const tables: TableInfo[] = [];
      const warnings: string[] = [];

      for (const statement of statements) {
        try {
          if (this.isCreateTableStatement(statement)) {
            console.log('解析CREATE TABLE语句:', statement.substring(0, 50) + '...');
            const tableInfo = await this.parseCreateTable(statement);
            if (tableInfo) {
              tables.push(tableInfo);
            }
          } else {
            console.log('跳过非CREATE TABLE语句');
          }
        } catch (error) {
          const warning = `解析语句失败: ${error instanceof Error ? error.message : '未知错误'}`;
          console.warn(warning);
          warnings.push(warning);
        }
      }

      const result: SqlParseResult = {
        tables,
        warnings,
        stats: {
          tableCount: tables.length,
          columnCount: tables.reduce((sum, table) => sum + table.columns.length, 0),
          foreignKeyCount: tables.reduce((sum, table) => sum + table.foreignKeys.length, 0)
        }
      };

      console.log(`SQL解析完成，共解析出${tables.length}个表`);
      return result;

    } catch (error) {
      console.error('SQL解析错误:', error);
      throw this.createParseError(error instanceof Error ? error.message : '未知解析错误');
    }
  }

  /**
   * 预处理SQL，移除不支持的语句和注释
   * @param sql 原始SQL
   * @returns 处理后的SQL
   */
  private preprocessSql(sql: string): string {
    const lines = sql.split('\n');
    const processedLines: string[] = [];

    for (const line of lines) {
      const trimmedLine = line.trim().toUpperCase();
      
      // 跳过空行和注释
      if (!trimmedLine || trimmedLine.startsWith('--') || trimmedLine.startsWith('#')) {
        continue;
      }
      
      // 跳过不支持的语句
      if (
        trimmedLine.includes('UNIQUE INDEX') ||
        trimmedLine.includes('CREATE INDEX') ||
        trimmedLine.includes('ALTER TABLE') ||
        trimmedLine.startsWith('USE ') ||
        trimmedLine.startsWith('SET ')
      ) {
        continue;
      }

      processedLines.push(line);
    }

    return processedLines.join('\n');
  }

  /**
   * 分割多个SQL语句
   * @param sql SQL字符串
   * @returns 语句数组
   */
  private splitSqlStatements(sql: string): string[] {
    // 简单的分号分割，后续可以改进为更智能的分割
    return sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
  }

  /**
   * 检查是否为CREATE TABLE语句
   * @param statement SQL语句
   * @returns 是否为CREATE TABLE语句
   */
  private isCreateTableStatement(statement: string): boolean {
    return statement.trim().toUpperCase().startsWith('CREATE TABLE');
  }

  /**
   * 解析单个CREATE TABLE语句
   * @param statement CREATE TABLE语句
   * @returns 表信息
   */
  private async parseCreateTable(statement: string): Promise<TableInfo | null> {
    try {
      // 首先尝试使用node-sql-parser解析
      let tableInfo = await this.parseWithNodeSqlParser(statement);

      // 如果node-sql-parser失败，尝试使用正则表达式解析
      if (!tableInfo) {
        console.log('node-sql-parser解析失败，尝试正则表达式解析...');
        tableInfo = this.parseWithRegex(statement);
      }

      return tableInfo;

    } catch (error) {
      console.error(`解析CREATE TABLE语句失败:`, error);
      return null;
    }
  }

  /**
   * 使用node-sql-parser解析CREATE TABLE语句
   * @param statement CREATE TABLE语句
   * @returns 表信息
   */
  private async parseWithNodeSqlParser(statement: string): Promise<TableInfo | null> {
    try {
      const ast = this.parser.astify(statement, { database: 'MySQL' });

      if (!ast || typeof ast !== 'object' || !('type' in ast) || ast.type !== 'create') {
        throw new Error('不是有效的CREATE TABLE语句');
      }

      const createAst = ast as unknown as CreateTableAST;
      const tableName = this.extractTableName(createAst);

      if (!tableName) {
        throw new Error('无法提取表名');
      }

      console.log(`解析表: ${tableName}`);

      const columns = this.extractColumns(createAst, statement);
      const primaryKeys = this.extractPrimaryKeys(createAst, columns);
      const foreignKeys = this.extractForeignKeys(createAst, statement);
      const comment = this.extractTableComment(createAst, statement);

      const tableInfo: TableInfo = {
        tableName,
        columns,
        primaryKeys,
        foreignKeys,
        comment
      };

      console.log(`表${tableName}解析完成: ${columns.length}列, ${primaryKeys.length}个主键, ${foreignKeys.length}个外键`);
      return tableInfo;

    } catch (error) {
      console.error(`node-sql-parser解析失败:`, error);
      return null;
    }
  }

  /**
   * 使用正则表达式解析CREATE TABLE语句（备用方案）
   * @param statement CREATE TABLE语句
   * @returns 表信息
   */
  private parseWithRegex(statement: string): TableInfo | null {
    try {
      // 提取表名
      const tableNameMatch = statement.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i);
      if (!tableNameMatch) {
        throw new Error('无法提取表名');
      }

      const tableName = tableNameMatch[1];
      console.log(`正则表达式解析表: ${tableName}`);

      // 提取表注释 - 应该在语句最后，在括号外面，排除外键注释
      const tableCommentMatch = statement.match(/\)\s*COMMENT\s*['"]([^'"]*)['"]\s*;?\s*$/i);
      const comment = tableCommentMatch ? tableCommentMatch[1] : undefined;
      console.log(`表注释匹配结果: ${tableCommentMatch ? tableCommentMatch[1] : '未找到'}`);
      console.log(`表注释正则匹配: ${tableCommentMatch}`);

      // 提取列定义部分
      const columnSectionMatch = statement.match(/\(([\s\S]*)\)/);
      if (!columnSectionMatch) {
        throw new Error('无法提取列定义');
      }

      const columnSection = columnSectionMatch[1];
      const columns = this.parseColumnsWithRegex(columnSection);
      const primaryKeys = this.parsePrimaryKeysWithRegex(columnSection);
      const foreignKeys = this.parseForeignKeysWithRegex(columnSection);

      const tableInfo: TableInfo = {
        tableName,
        columns,
        primaryKeys,
        foreignKeys,
        comment
      };

      console.log(`正则表达式解析完成: ${tableName} - ${columns.length}列, ${primaryKeys.length}个主键, ${foreignKeys.length}个外键`);
      return tableInfo;

    } catch (error) {
      console.error('正则表达式解析失败:', error);
      return null;
    }
  }

  /**
   * 提取表名
   * @param ast AST对象
   * @returns 表名
   */
  private extractTableName(ast: CreateTableAST): string | null {
    try {
      if (ast.table && ast.table.length > 0) {
        const tableObj = ast.table[0];
        return tableObj.table || null;
      }
      return null;
    } catch (error) {
      console.error('提取表名失败:', error);
      return null;
    }
  }

  /**
   * 提取列信息
   * @param ast AST对象
   * @param statement 原始SQL语句
   * @returns 列信息数组
   */
  private extractColumns(ast: CreateTableAST, statement: string): ColumnInfo[] {
    console.log(`🔍 extractColumns方法被调用了！`);
    const columns: ColumnInfo[] = [];

    try {
      console.log(`AST结构检查:`, ast);
      console.log(`create_definitions存在:`, !!ast.create_definitions);
      console.log(`create_definitions长度:`, ast.create_definitions?.length);

      if (!ast.create_definitions) {
        console.log(`没有create_definitions，返回空列数组`);
        return columns;
      }

      // 首先提取表级UNIQUE约束
      const tableUniqueColumns = this.extractTableLevelUniqueFromAST(ast);

      for (const def of ast.create_definitions) {
        console.log(`检查定义:`, def);
        if (def.resource === 'column' && def.column && def.definition && !Array.isArray(def.definition)) {
          const columnDef = def.definition as ColumnDefinition;
          // 从原始SQL语句中提取列注释
          const columnComment = this.extractColumnCommentFromSql(def.column.column, statement);

          // 检查是否有UNIQUE约束（列级或表级）
          console.log(`列 ${def.column.column} 约束结构:`, columnDef.constraint);
          console.log(`列 ${def.column.column} def.unique值:`, (def as any).unique);
          const hasConstraintUnique = this.hasConstraint(columnDef, 'UNIQUE');
          const hasDefUnique = (def as any).unique === 'unique';
          const columnLevelUnique = hasConstraintUnique || hasDefUnique;
          const tableLevelUnique = tableUniqueColumns.has(def.column.column);
          const isUnique = columnLevelUnique || tableLevelUnique;
          console.log(`列 ${def.column.column} UNIQUE约束检测: hasConstraint=${hasConstraintUnique}, defUnique=${hasDefUnique}, 列级=${columnLevelUnique}, 表级=${tableLevelUnique}, 最终=${isUnique}`);

          const column: ColumnInfo = {
            name: def.column.column,
            type: this.formatDataType(columnDef),
            nullable: !this.hasConstraint(columnDef, 'NOT NULL'),
            defaultValue: this.extractDefaultValue(columnDef),
            comment: columnComment || this.extractColumnComment(columnDef),
            isUnique
          };

          columns.push(column);
          console.log(`提取列: ${column.name} (${column.type}), 注释: "${column.comment}"`);
          console.log(`列定义结构:`, columnDef);
        }
      }
    } catch (error) {
      console.error('提取列信息失败:', error);
    }

    return columns;
  }

  /**
   * 格式化数据类型
   * @param definition 列定义
   * @returns 格式化的数据类型
   */
  private formatDataType(definition: ColumnDefinition): string {
    try {
      if (definition.dataType) {
        let type = definition.dataType.toUpperCase();
        
        // 添加长度信息
        if (definition.length) {
          if (Array.isArray(definition.length)) {
            type += `(${definition.length.join(',')})`;
          } else {
            type += `(${definition.length})`;
          }
        }
        
        return type;
      }
      return 'UNKNOWN';
    } catch (error) {
      console.error('格式化数据类型失败:', error);
      return 'UNKNOWN';
    }
  }

  /**
   * 检查是否有特定约束
   * @param definition 列定义
   * @param constraint 约束名称
   * @returns 是否有约束
   */
  private hasConstraint(definition: ColumnDefinition, constraint: string): boolean {
    try {
      if (definition.constraint) {
        return definition.constraint.some((c: any) => 
          c.type && c.type.toUpperCase() === constraint.toUpperCase()
        );
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 提取默认值
   * @param definition 列定义
   * @returns 默认值
   */
  private extractDefaultValue(definition: ColumnDefinition): string | undefined {
    try {
      const defaultConstraint = definition.constraint?.find((c: ConstraintDefinition) =>
        c.type && c.type.toUpperCase() === 'DEFAULT'
      );
      
      if (defaultConstraint && defaultConstraint.value) {
        return String(defaultConstraint.value);
      }
      
      return undefined;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * 提取列注释
   * @param definition 列定义
   * @returns 列注释
   */
  private extractColumnComment(definition: ColumnDefinition): string | undefined {
    try {
      // 方法1：检查constraint数组中的COMMENT
      const commentConstraint = definition.constraint?.find((c: ConstraintDefinition) =>
        c.type && c.type.toUpperCase() === 'COMMENT'
      );

      if (commentConstraint && commentConstraint.value) {
        return this.removeQuotes(String(commentConstraint.value));
      }

      // 方法2：检查suffix数组中的COMMENT（使用any类型避免TypeScript错误）
      const defAny = definition as any;
      if (defAny.suffix && Array.isArray(defAny.suffix)) {
        for (let i = 0; i < defAny.suffix.length; i++) {
          const item = defAny.suffix[i];
          if (item && typeof item === 'object' && item.type === 'COMMENT' && item.value) {
            return this.removeQuotes(String(item.value));
          }
        }
      }

      // 方法3：检查comment字段
      if (defAny.comment) {
        return this.removeQuotes(String(defAny.comment));
      }

      return undefined;
    } catch (error) {
      console.error('提取列注释失败:', error);
      return undefined;
    }
  }

  /**
   * 提取主键
   * @param ast AST对象
   * @param _columns 列信息数组（暂未使用）
   * @returns 主键列名数组
   */
  private extractPrimaryKeys(ast: CreateTableAST, _columns: ColumnInfo[]): string[] {
    const primaryKeys: string[] = [];

    try {
      // 检查列级主键约束
      if (ast.create_definitions) {
        for (const def of ast.create_definitions) {
          if (def.resource === 'column' && def.primary_key) {
            if (def.column?.column) {
              primaryKeys.push(def.column.column);
              console.log(`找到列级主键: ${def.column.column}`);
            }
          }
        }
      }

      // 检查表级主键约束
      if (ast.create_definitions) {
        for (const def of ast.create_definitions) {
          if (def.resource === 'constraint' && def.constraint_type === 'primary key') {
            if (Array.isArray(def.definition)) {
              primaryKeys.push(...def.definition.map((col: CreateDefinition) => col.column?.column || '').filter(Boolean));
            }
          }
        }
      }
    } catch (err) {
      console.error('提取主键失败:', err);
    }

    return Array.from(new Set(primaryKeys)); // 去重
  }

  /**
   * 提取外键关系
   * @param ast AST对象
   * @param originalSql 原始SQL语句（用于备用正则表达式解析）
   * @returns 外键关系数组
   */
  private extractForeignKeys(ast: CreateTableAST, originalSql?: string): ForeignKeyInfo[] {
    const foreignKeys: ForeignKeyInfo[] = [];
    console.log(`🔍 extractForeignKeys方法被调用了！`);

    try {
      if (ast.create_definitions) {
        for (const def of ast.create_definitions) {
          // 检查外键约束，不限制resource类型
          if (def.constraint_type === 'FOREIGN KEY' || def.constraint_type === 'foreign key') {
            console.log(`发现外键约束定义:`, def);
            console.log(`外键定义详细结构:`, JSON.stringify(def, null, 2));

            // 处理表级外键约束
            if (Array.isArray(def.definition) && def.definition.length > 0) {
              const columnDef = def.definition[0];
              console.log(`外键列定义:`, columnDef);

              // 尝试多种方式提取外键信息
              let columnName = '';
              let referenceTable = '';
              let referenceColumn = '';

              // 提取外键列名 - 尝试多种路径
              if (columnDef.column?.column) {
                columnName = columnDef.column.column;
              } else if (typeof columnDef.column === 'string') {
                columnName = columnDef.column;
              } else if (typeof columnDef === 'string') {
                columnName = columnDef;
              }

              const refDef = (def as any).reference_definition;
              console.log(`引用定义:`, refDef);

              // 提取引用表名 - 修复逻辑，直接访问数组元素
              console.log(`引用表结构详细分析:`, refDef?.table);
              console.log(`是否为数组:`, Array.isArray(refDef?.table));

              // 直接尝试访问table数组的第一个元素
              if (refDef?.table && refDef.table.length > 0) {
                console.log(`数组第一个元素:`, refDef.table[0]);
                const tableObj = refDef.table[0];
                if (tableObj && typeof tableObj === 'object' && tableObj.table) {
                  referenceTable = tableObj.table;
                  console.log(`✅ 从对象.table提取的表名:`, referenceTable);
                } else if (typeof tableObj === 'string') {
                  referenceTable = tableObj;
                  console.log(`✅ 从字符串提取的表名:`, referenceTable);
                }
              } else if (refDef?.table?.[0]?.table) {
                referenceTable = refDef.table[0].table;
                console.log(`✅ 从table[0].table提取的表名:`, referenceTable);
              } else if (refDef?.table?.table) {
                referenceTable = refDef.table.table;
                console.log(`✅ 从table.table提取的表名:`, referenceTable);
              } else if (typeof refDef?.table === 'string') {
                referenceTable = refDef.table;
                console.log(`✅ 从字符串table提取的表名:`, referenceTable);
              } else if (refDef?.table?.name) {
                referenceTable = refDef.table.name;
                console.log(`✅ 从table.name提取的表名:`, referenceTable);
              }
              console.log(`🎯 最终提取的引用表名:`, referenceTable);

              // 提取引用列名 - 尝试多种路径
              if (refDef?.definition?.[0]?.column) {
                referenceColumn = refDef.definition[0].column;
              } else if (typeof refDef?.definition?.[0] === 'string') {
                referenceColumn = refDef.definition[0];
              } else if (refDef?.columns?.[0]) {
                referenceColumn = refDef.columns[0];
              }

              const fk: ForeignKeyInfo = {
                columnName,
                referenceTable,
                referenceColumn
              };

              console.log(`提取的外键信息:`, fk);

              if (fk.columnName && fk.referenceTable && fk.referenceColumn) {
                foreignKeys.push(fk);
                console.log(`✅ 成功提取外键: ${fk.columnName} -> ${fk.referenceTable}.${fk.referenceColumn}`);
              } else {
                console.log(`❌ 外键信息不完整:`, fk);
              }
            } else {
              console.log(`外键定义格式不正确:`, def.definition);
            }
          }
        }
      }
    } catch (err) {
      console.error('提取外键失败:', err);
    }

    // 如果AST解析没有找到外键，尝试使用正则表达式作为备用方案
    if (foreignKeys.length === 0) {
      console.log(`AST解析未找到外键，尝试正则表达式解析...`);
      try {
        // 使用原始SQL语句进行正则表达式解析
        const sqlText = originalSql || '';
        const regexForeignKeys = this.parseForeignKeysWithRegex(sqlText);
        if (regexForeignKeys.length > 0) {
          console.log(`✅ 正则表达式解析成功找到${regexForeignKeys.length}个外键`);
          foreignKeys.push(...regexForeignKeys);
        }
      } catch (regexErr) {
        console.error('正则表达式解析外键也失败:', regexErr);
      }
    }

    return foreignKeys;
  }

  /**
   * 从原始SQL语句中提取列注释
   * @param columnName 列名
   * @param statement 原始SQL语句
   * @returns 列注释
   */
  private extractColumnCommentFromSql(columnName: string, statement: string): string | undefined {
    try {
      // 方法1：精确匹配列定义中的注释（处理各种数据类型）
      const patterns = [
        // 匹配 columnName datatype COMMENT '注释'
        new RegExp(`\\b${columnName}\\s+\\w+(?:\\([^)]*\\))?\\s+(?:PRIMARY\\s+KEY\\s+)?COMMENT\\s+['"]([^'"]*)['"']`, 'i'),
        // 匹配 columnName datatype(size) COMMENT '注释'
        new RegExp(`\\b${columnName}\\s+\\w+\\([^)]*\\)\\s+COMMENT\\s+['"]([^'"]*)['"']`, 'i'),
        // 匹配 columnName datatype PRIMARY KEY COMMENT '注释'
        new RegExp(`\\b${columnName}\\s+\\w+\\s+PRIMARY\\s+KEY\\s+COMMENT\\s+['"]([^'"]*)['"']`, 'i'),
        // 通用匹配
        new RegExp(`\\b${columnName}\\s+[^,()]*?COMMENT\\s+['"]([^'"]*)['"']`, 'i')
      ];

      for (const regex of patterns) {
        const match = statement.match(regex);
        if (match && match[1]) {
          console.log(`从SQL提取列${columnName}注释: ${match[1]}`);
          return match[1];
        }
      }

      // 方法2：如果上面都没匹配到，尝试更宽松的匹配
      const looseRegex = new RegExp(`${columnName}[^,]*?COMMENT\\s+['"]([^'"]*)['"']`, 'i');
      const looseMatch = statement.match(looseRegex);
      if (looseMatch && looseMatch[1]) {
        console.log(`从SQL宽松匹配列${columnName}注释: ${looseMatch[1]}`);
        return looseMatch[1];
      }

      console.log(`未找到列${columnName}的注释`);
      return undefined;
    } catch (error) {
      console.error(`提取列${columnName}注释失败:`, error);
      return undefined;
    }
  }

  /**
   * 提取表注释
   * @param ast AST对象
   * @param statement 原始SQL语句
   * @returns 表注释
   */
  private extractTableComment(ast: CreateTableAST, statement: string): string | undefined {
    try {
      // 尝试从AST中提取
      if (ast.table_options && typeof ast.table_options === 'object' && 'comment' in ast.table_options) {
        return this.removeQuotes(String(ast.table_options.comment));
      }

      // 使用正则表达式从原始语句中提取表注释（在括号外面的最后一个COMMENT）
      const tableCommentMatch = statement.match(/\)\s*COMMENT\s*['"]([^'"]*)['"]\s*;?\s*$/i);
      if (tableCommentMatch) {
        console.log(`从原始语句提取表注释: ${tableCommentMatch[1]}`);
        return tableCommentMatch[1];
      }

      console.log('未找到表注释');
      return undefined;
    } catch (err) {
      console.error('提取表注释失败:', err);
      return undefined;
    }
  }

  /**
   * 使用正则表达式解析列定义
   * @param columnSection 列定义部分
   * @returns 列信息数组
   */
  private parseColumnsWithRegex(columnSection: string): ColumnInfo[] {
    const columns: ColumnInfo[] = [];

    try {
      // 首先提取表级UNIQUE约束
      const tableUniqueColumns = this.extractTableLevelUniqueConstraints(columnSection);

      // 分割列定义，处理逗号分隔
      const lines = columnSection.split('\n').map(line => line.trim()).filter(line => line);
      let currentColumn = '';

      for (const line of lines) {
        if (line.startsWith('PRIMARY KEY') || line.startsWith('FOREIGN KEY') || line.startsWith('KEY') || line.startsWith('INDEX')) {
          continue;
        }

        currentColumn += ' ' + line;

        if (line.endsWith(',') || lines.indexOf(line) === lines.length - 1) {
          const columnDef = currentColumn.trim().replace(/,$/, '');
          const column = this.parseColumnDefinitionWithRegex(columnDef);
          if (column) {
            // 如果列在表级UNIQUE约束中，设置isUnique为true
            if (tableUniqueColumns.has(column.name)) {
              column.isUnique = true;
              console.log(`列 ${column.name} 通过表级UNIQUE约束设置为unique`);
            }
            columns.push(column);
          }
          currentColumn = '';
        }
      }
    } catch (error) {
      console.error('正则表达式解析列失败:', error);
    }

    return columns;
  }

  /**
   * 解析单个列定义
   * @param columnDef 列定义字符串
   * @returns 列信息
   */
  private parseColumnDefinitionWithRegex(columnDef: string): ColumnInfo | null {
    try {
      // 匹配列名和数据类型
      const match = columnDef.match(/^`?(\w+)`?\s+(\w+(?:\([^)]*\))?)/i);
      if (!match) {
        return null;
      }

      const name = match[1];
      const type = match[2].toUpperCase();

      // 检查是否可为空
      const nullable = !columnDef.toUpperCase().includes('NOT NULL');

      // 提取默认值
      const defaultMatch = columnDef.match(/DEFAULT\s+([^,\s]+)/i);
      const defaultValue = defaultMatch ? defaultMatch[1].replace(/['"]/g, '') : undefined;

      // 提取注释
      const commentMatch = columnDef.match(/COMMENT\s+['"]([^'"]*)['"]/i);
      const comment = commentMatch ? commentMatch[1] : undefined;
      console.log(`列 ${name} 注释解析: 定义="${columnDef}", 匹配="${commentMatch}", 注释="${comment}"`);

      // 检查是否有UNIQUE约束
      const isUnique = columnDef.toUpperCase().includes('UNIQUE');
      console.log(`列 ${name} UNIQUE约束检测: 定义="${columnDef}", isUnique=${isUnique}`);

      return {
        name,
        type,
        nullable,
        defaultValue,
        comment,
        isUnique
      };
    } catch (error) {
      console.error('解析列定义失败:', error);
      return null;
    }
  }

  /**
   * 从AST中提取表级UNIQUE约束
   * @param ast CREATE TABLE AST
   * @returns 具有UNIQUE约束的列名集合
   */
  private extractTableLevelUniqueFromAST(ast: CreateTableAST): Set<string> {
    const uniqueColumns = new Set<string>();

    try {
      if (!ast.create_definitions) {
        return uniqueColumns;
      }

      for (const def of ast.create_definitions) {
        // 查找UNIQUE约束定义
        if (def.resource === 'constraint' && def.constraint_type === 'unique') {
          // 处理UNIQUE约束
          if (def.definition && Array.isArray(def.definition)) {
            for (const col of def.definition) {
              if (col && typeof col === 'object' && 'column' in col && col.column) {
                const columnName = (col as any).column;
                uniqueColumns.add(columnName);
                console.log(`发现AST表级UNIQUE约束: ${columnName}`);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('从AST提取表级UNIQUE约束失败:', error);
    }

    return uniqueColumns;
  }

  /**
   * 提取表级UNIQUE约束
   * @param columnSection 列定义部分
   * @returns 具有UNIQUE约束的列名集合
   */
  private extractTableLevelUniqueConstraints(columnSection: string): Set<string> {
    const uniqueColumns = new Set<string>();

    try {
      // 匹配 UNIQUE KEY (column_name) 格式
      const uniqueKeyRegex = /UNIQUE\s+KEY\s*\(\s*`?(\w+)`?\s*\)/gi;
      let match;
      while ((match = uniqueKeyRegex.exec(columnSection)) !== null) {
        uniqueColumns.add(match[1]);
        console.log(`发现表级UNIQUE KEY约束: ${match[1]}`);
      }

      // 匹配 UNIQUE (column_name) 格式
      const uniqueRegex = /UNIQUE\s*\(\s*`?(\w+)`?\s*\)/gi;
      while ((match = uniqueRegex.exec(columnSection)) !== null) {
        uniqueColumns.add(match[1]);
        console.log(`发现表级UNIQUE约束: ${match[1]}`);
      }
    } catch (error) {
      console.error('提取表级UNIQUE约束失败:', error);
    }

    return uniqueColumns;
  }

  /**
   * 使用正则表达式解析主键
   * @param columnSection 列定义部分
   * @returns 主键列名数组
   */
  private parsePrimaryKeysWithRegex(columnSection: string): string[] {
    const primaryKeys: string[] = [];

    try {
      // 查找列级主键
      const columnPrimaryKeyRegex = /`?(\w+)`?\s+[^,]*PRIMARY\s+KEY/gi;
      let match;
      while ((match = columnPrimaryKeyRegex.exec(columnSection)) !== null) {
        primaryKeys.push(match[1]);
      }

      // 查找表级主键
      const tablePrimaryKeyMatch = columnSection.match(/PRIMARY\s+KEY\s*\(\s*`?(\w+)`?\s*\)/i);
      if (tablePrimaryKeyMatch) {
        primaryKeys.push(tablePrimaryKeyMatch[1]);
      }
    } catch (error) {
      console.error('正则表达式解析主键失败:', error);
    }

    return Array.from(new Set(primaryKeys));
  }

  /**
   * 使用正则表达式解析外键
   * @param columnSection 列定义部分
   * @returns 外键关系数组
   */
  private parseForeignKeysWithRegex(columnSection: string): ForeignKeyInfo[] {
    const foreignKeys: ForeignKeyInfo[] = [];

    try {
      const foreignKeyRegex = /FOREIGN\s+KEY\s*\(\s*`?(\w+)`?\s*\)\s+REFERENCES\s+`?(\w+)`?\s*\(\s*`?(\w+)`?\s*\)/gi;
      let match;

      while ((match = foreignKeyRegex.exec(columnSection)) !== null) {
        foreignKeys.push({
          columnName: match[1],
          referenceTable: match[2],
          referenceColumn: match[3]
        });
      }
    } catch (error) {
      console.error('正则表达式解析外键失败:', error);
    }

    return foreignKeys;
  }

  /**
   * 移除字符串两端的引号
   * @param str 字符串
   * @returns 移除引号后的字符串
   */
  private removeQuotes(str: string): string {
    return str.replace(/^['"]|['"]$/g, '');
  }

  /**
   * 创建解析错误对象
   * @param message 错误消息
   * @returns 解析错误对象
   */
  private createParseError(message: string): ParseError {
    return {
      message,
      type: 'syntax'
    };
  }
}

/**
 * 创建SQL解析器实例
 * @returns SQL解析器实例
 */
export function createSqlParser(): SqlParser {
  return new SqlParser();
}

/**
 * 便捷的SQL解析函数
 * @param sql SQL DDL语句
 * @returns 解析结果
 */
export async function parseSql(sql: string): Promise<SqlParseResult> {
  const parser = createSqlParser();
  return parser.parseSql(sql);
}
