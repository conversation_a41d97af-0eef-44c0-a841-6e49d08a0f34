// MySQLAi.de - Next.js 中间件
// 提供管理员路由保护和认证验证

import { NextRequest, NextResponse } from 'next/server';
import { handleRouteInMiddleware } from '@/lib/route-utils';

// 需要保护的管理员路由
const PROTECTED_ADMIN_ROUTES = [
  '/admin',
  // 知识库模块路由
  '/admin/knowledge',
  '/admin/knowledge/articles',
  '/admin/knowledge/categories',
  '/admin/knowledge/code-examples',
  // 原有路由（保持向后兼容）
  '/admin/articles',
  '/admin/categories',
  '/admin/code-examples',
  '/admin/stats'
];

// 管理员认证相关路由
const ADMIN_AUTH_ROUTES = [
  '/admin/login'
];

// 简化的令牌验证（避免在中间件中进行API调用）
function verifyAdminToken(token: string): boolean {
  try {
    // 解析令牌
    const decoded = Buffer.from(token, 'base64').toString();
    const [username, timestamp] = decoded.split(':');

    // 验证令牌格式和用户名
    if (username !== '17742495540') {
      return false;
    }

    // 检查令牌是否过期（24小时）
    const tokenTime = parseInt(timestamp);
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;

    return (now - tokenTime) <= twentyFourHours;
  } catch (error) {
    console.error('Token verification failed:', error);
    return false;
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是管理员相关路由
  const isAdminRoute = pathname.startsWith('/admin');

  if (!isAdminRoute) {
    // 非管理员路由，直接通过
    return NextResponse.next();
  }

  // 处理知识库路由重定向（在认证检查之前）
  const routeRedirectResponse = handleRouteInMiddleware(request);
  if (routeRedirectResponse) {
    return routeRedirectResponse;
  }

  // 检查是否是认证路由（登录页面）
  const isAuthRoute = ADMIN_AUTH_ROUTES.some(route => pathname.startsWith(route));

  if (isAuthRoute) {
    // 如果已经认证，重定向到管理后台
    const token = request.cookies.get('mysql_admin_token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (token) {
      const isValid = verifyAdminToken(token);
      if (isValid) {
        return NextResponse.redirect(new URL('/admin', request.url));
      }
    }

    // 未认证或认证无效，允许访问登录页面
    return NextResponse.next();
  }

  // 检查是否是受保护的管理员路由
  const isProtectedRoute = PROTECTED_ADMIN_ROUTES.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  );

  if (isProtectedRoute) {
    // 从 cookie 或 header 获取认证令牌
    const token = request.cookies.get('mysql_admin_token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      // 没有令牌，重定向到登录页面
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }

    // 验证令牌有效性
    const isValid = verifyAdminToken(token);

    if (!isValid) {
      // 令牌无效，重定向到登录页面
      const response = NextResponse.redirect(new URL('/admin/login', request.url));
      // 清除无效的 cookie
      response.cookies.delete('mysql_admin_token');
      return response;
    }

    // 令牌有效，允许访问
    return NextResponse.next();
  }

  // 其他管理员路由，直接通过
  return NextResponse.next();
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有管理员路由，除了:
     * - api 路由 (已经有自己的保护)
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     */
    '/admin/:path*'
  ]
};
