'use client';

// MySQLAi.de - 加载状态组件
// 通用的加载指示器，支持多种样式和大小

import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

export default function LoadingSpinner({
  size = 'md',
  variant = 'spinner',
  color = 'primary',
  text,
  className,
  fullScreen = false
}: LoadingSpinnerProps) {

  // 尺寸配置
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  // 颜色配置
  const colorClasses = {
    primary: 'text-mysql-primary',
    secondary: 'text-mysql-secondary',
    white: 'text-white',
    gray: 'text-gray-500'
  };

  // 渲染不同类型的加载器
  const renderSpinner = () => {
    const baseClasses = cn(
      sizeClasses[size],
      colorClasses[color],
      'animate-spin'
    );

    switch (variant) {
      case 'spinner':
        return <Loader2 className={baseClasses} />;
      
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className={cn(
                  'rounded-full',
                  size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                  colorClasses[color].replace('text-', 'bg-')
                )}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 0.6,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <motion.div
            className={cn(
              'rounded-full border-2',
              sizeClasses[size],
              colorClasses[color].replace('text-', 'border-')
            )}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [1, 0.5, 1]
            }}
            transition={{
              duration: 1,
              repeat: Infinity
            }}
          />
        );

      case 'bars':
        return (
          <div className="flex space-x-1 items-end">
            {[0, 1, 2, 3].map((i) => (
              <motion.div
                key={i}
                className={cn(
                  'rounded-sm',
                  size === 'sm' ? 'w-1' : size === 'md' ? 'w-1.5' : size === 'lg' ? 'w-2' : 'w-3',
                  colorClasses[color].replace('text-', 'bg-')
                )}
                style={{
                  height: size === 'sm' ? '8px' : size === 'md' ? '12px' : size === 'lg' ? '16px' : '24px'
                }}
                animate={{
                  scaleY: [1, 2, 1]
                }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  delay: i * 0.1
                }}
              />
            ))}
          </div>
        );

      default:
        return <Loader2 className={baseClasses} />;
    }
  };

  // 全屏加载器
  if (fullScreen) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center"
      >
        <div className="text-center">
          {renderSpinner()}
          {text && (
            <p className="mt-4 text-sm text-mysql-text-light">
              {text}
            </p>
          )}
        </div>
      </motion.div>
    );
  }

  // 普通加载器
  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex items-center space-x-2">
        {renderSpinner()}
        {text && (
          <span className="text-sm text-mysql-text-light">
            {text}
          </span>
        )}
      </div>
    </div>
  );
}
