/**
 * 激活状态同步测试
 * 用于调试和验证激活状态在不同组件间的同步问题
 */

import { useAppStore } from '../store/appStore';
import { convertActivationStatusToState } from '../lib/utils';

export interface StateSyncTestResult {
  testName: string;
  passed: boolean;
  description: string;
  actualResult: any;
  expectedResult: any;
  details?: string;
}

/**
 * 模拟激活成功的状态数据
 */
const mockSuccessfulActivationStatus = {
  isActivated: true,
  licenseKey: 'TEST-ACTIVATION-SUCCESS-123',
  machineId: 'test-machine-id',
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后过期
  remainingHours: 720, // 30天 * 24小时
  isTrialMode: false
};

/**
 * 测试状态转换函数
 */
export function testStateConversion(): StateSyncTestResult[] {
  const results: StateSyncTestResult[] = [];

  // 测试1: 成功激活状态转换
  const convertedState = convertActivationStatusToState(mockSuccessfulActivationStatus);
  
  results.push({
    testName: '激活成功状态转换测试',
    passed: convertedState.status === 'activated',
    description: '验证激活成功的状态是否正确转换为appStore格式',
    actualResult: convertedState,
    expectedResult: { status: 'activated', licenseKey: 'TEST-ACTIVATION-SUCCESS-123' },
    details: `转换后状态: ${convertedState.status}, 许可证密钥: ${convertedState.licenseKey}`
  });

  // 测试2: 空状态处理
  const emptyState = convertActivationStatusToState(null);
  
  results.push({
    testName: '空状态处理测试',
    passed: emptyState.status === 'not_activated',
    description: '验证空状态是否正确转换为未激活状态',
    actualResult: emptyState,
    expectedResult: { status: 'not_activated' },
    details: `空状态转换结果: ${emptyState.status}`
  });

  // 测试3: 过期状态转换
  const expiredStatus = {
    ...mockSuccessfulActivationStatus,
    expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 昨天过期
    remainingHours: 0
  };
  
  const expiredState = convertActivationStatusToState(expiredStatus);
  
  results.push({
    testName: '过期状态转换测试',
    passed: expiredState.status === 'expired',
    description: '验证过期状态是否正确识别',
    actualResult: expiredState,
    expectedResult: { status: 'expired' },
    details: `过期状态转换结果: ${expiredState.status}`
  });

  return results;
}

/**
 * 测试appStore状态更新
 */
export function testAppStoreUpdate(): StateSyncTestResult[] {
  const results: StateSyncTestResult[] = [];
  const store = useAppStore.getState();

  // 保存初始状态
  const initialState = { ...store.activation };

  // 测试1: 更新为激活状态
  const activatedState = convertActivationStatusToState(mockSuccessfulActivationStatus);
  store.updateActivation(activatedState);
  
  const updatedState = useAppStore.getState().activation;
  
  results.push({
    testName: 'appStore激活状态更新测试',
    passed: updatedState.status === 'activated',
    description: '验证appStore是否正确更新为激活状态',
    actualResult: updatedState,
    expectedResult: { status: 'activated' },
    details: `更新后状态: ${updatedState.status}, 许可证: ${updatedState.licenseKey}`
  });

  // 测试2: UI条件渲染逻辑
  const shouldShowButton = updatedState.status === 'not_activated' || updatedState.status === 'expired';
  
  results.push({
    testName: 'UI条件渲染逻辑测试',
    passed: !shouldShowButton, // 激活后应该隐藏按钮
    description: '验证激活成功后激活按钮是否正确隐藏',
    actualResult: { shouldShowButton, status: updatedState.status },
    expectedResult: { shouldShowButton: false, status: 'activated' },
    details: `激活状态: ${updatedState.status}, 显示按钮: ${shouldShowButton}`
  });

  // 恢复初始状态
  store.updateActivation(initialState);

  return results;
}

/**
 * 模拟完整的激活流程测试
 */
export function simulateActivationFlow(): StateSyncTestResult[] {
  const results: StateSyncTestResult[] = [];
  const store = useAppStore.getState();

  console.log('🧪 开始模拟激活流程测试...');

  // 步骤1: 初始状态
  console.log('📍 步骤1: 设置初始未激活状态');
  store.updateActivation({ status: 'not_activated' });
  let currentState = useAppStore.getState().activation;
  
  results.push({
    testName: '初始状态设置',
    passed: currentState.status === 'not_activated',
    description: '验证初始状态是否为未激活',
    actualResult: currentState.status,
    expectedResult: 'not_activated'
  });

  // 步骤2: 激活中状态
  console.log('📍 步骤2: 设置激活中状态');
  store.updateActivation({ status: 'checking' });
  currentState = useAppStore.getState().activation;
  
  results.push({
    testName: '激活中状态设置',
    passed: currentState.status === 'checking',
    description: '验证激活中状态是否正确设置',
    actualResult: currentState.status,
    expectedResult: 'checking'
  });

  // 步骤3: 激活成功
  console.log('📍 步骤3: 模拟激活成功');
  const successState = convertActivationStatusToState(mockSuccessfulActivationStatus);
  store.updateActivation(successState);
  currentState = useAppStore.getState().activation;
  
  results.push({
    testName: '激活成功状态更新',
    passed: currentState.status === 'activated',
    description: '验证激活成功后状态是否正确更新',
    actualResult: currentState,
    expectedResult: { status: 'activated' },
    details: `最终状态: ${JSON.stringify(currentState, null, 2)}`
  });

  // 步骤4: UI响应验证
  const shouldShowAfterActivation = currentState.status === 'not_activated' || currentState.status === 'expired';
  
  results.push({
    testName: '激活后UI响应验证',
    passed: !shouldShowAfterActivation,
    description: '验证激活成功后UI是否正确响应（隐藏激活按钮）',
    actualResult: shouldShowAfterActivation,
    expectedResult: false,
    details: `激活后显示按钮: ${shouldShowAfterActivation}`
  });

  return results;
}

/**
 * 运行所有状态同步测试
 */
export function runAllStateSyncTests(): void {
  console.log('🧪 开始运行激活状态同步测试套件...\n');

  const allResults = [
    ...testStateConversion(),
    ...testAppStoreUpdate(),
    ...simulateActivationFlow()
  ];

  let passedCount = 0;
  let failedCount = 0;

  allResults.forEach((result, index) => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`\n测试 ${index + 1}: ${result.testName}`);
    console.log(`状态: ${status}`);
    console.log(`描述: ${result.description}`);
    console.log(`期望结果: ${JSON.stringify(result.expectedResult)}`);
    console.log(`实际结果: ${JSON.stringify(result.actualResult)}`);
    
    if (result.details) {
      console.log(`详细信息: ${result.details}`);
    }

    if (result.passed) {
      passedCount++;
    } else {
      failedCount++;
    }
  });

  console.log(`\n📊 测试总结:`);
  console.log(`✅ 通过: ${passedCount}`);
  console.log(`❌ 失败: ${failedCount}`);
  console.log(`📈 通过率: ${((passedCount / allResults.length) * 100).toFixed(1)}%`);

  if (failedCount === 0) {
    console.log('🎉 所有状态同步测试通过！');
  } else {
    console.log('⚠️ 有测试失败，请检查状态同步逻辑！');
  }
}

// 导出测试函数
export default {
  testStateConversion,
  testAppStoreUpdate,
  simulateActivationFlow,
  runAllStateSyncTests
};
